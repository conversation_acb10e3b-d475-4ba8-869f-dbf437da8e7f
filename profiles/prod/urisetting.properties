#develop
#beforeurl=https://dev.cloudpense.com/product/password-reset.html?locale=
#registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
#oauthurl=https://devapi.cloudpense.com/iExpenseManager/oauth/token
#finance=https://dev.cloudpense.com/product/finance-detail.html?id=

#qa
beforeurl=https://qa.cloudpense.com/product/password-reset.html?locale=
registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
oauthurl=http://localhost:8081/iExpenseManager/oauth/token
finance=https://qa.cloudpense.com/product/finance-detail.html?id=
workUrl=https://qa.cloudpense.com/tmp/portal.html?path=document&id=
projectUrl=https://www.cloudpense.com
projectOauthUrl=https://api.cloudpense.com/iExpenseManager

#product
#beforeurl=https://www.cloudpense.com/product/password-reset.html?locale=
#registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
#oauthurl=https://api.cloudpense.com/iExpenseManager/oauth/token
#finance=https://www.cloudpense.com/product/finance-detail.html?id=
#workUrl=https://www.cloudpense.com/tmp/portal.html?path=document&id=
#nippon uri by cloudpense
global.env=prod
nippon.tmc=https://api.cloudpense.com/iExpenseManager/open/third/order/split/taxexpid
nippon.tmc.ticket=https://api.cloudpense.com/iExpenseManager/open/third/order/flight/ticketno
nippon.sso_page=https://www.cloudpense.com/redirect/nippon-login.html
nippon.get_cp_token=https://api.cloudpense.com/iExpenseManager/open/oauth/token
nippon.cp_sso=https://api.cloudpense.com/iExpenseManager/sso/login
nippon.grant_type=client_credentials
nippon.client_id=lbfniuq3wi4e52wos02po
nippon.client_secret=VJwq5O98uiyhjm2wmki876ytg
nippon.encrypt_secret=g4Jff50BfieY70Ge
nippon.encrypt_corp_id=c13de52307ef9973
nippon.travel_time_check=https://openapi.cloudpense.com/nippon/esb/travelTimeCheck/unAuth
#nippon sso login
nippon.wechat_auth=http://m-eportal.nipponpaint.com.cn/BasicV2/Wechat/GetTicket
nippon.pc_auth=https://npsso-ida.nipponpaint.com.cn/esc-sso/oauth2.0/accessToken
nippon.pc_auth_profile=https://npsso-ida.nipponpaint.com.cn/esc-sso/oauth2.0/profile
nippon.sso.client_id=RXC9lF5Rdp
nippon.sso.client_secret=6e6acb79-8749-48b7-8ed4-770b022a2277
nippon.sso.grant_type=authorization_code
nippon.wechat_ntf=http://wxapprove.nipponpaint.com.cn/Service/MpService.asmx
#nippon data sync uri by nippon
nippon.sent_status=http://beep.nipponpaint.com.cn/webapp/Reimbursement/DisplayState.aspx
nippon.credit=http://beep2.nipponpaint.com.cn:8011/TravelExpenseService.asmx
nippon.invoice_update=http://beep2.nipponpaint.com.cn:8011/TravelExpenseService.asmx
nippon.claim_push=http://beep2.nipponpaint.com.cn:8011/TravelExpenseService.asmx
nippon.exchange_rate=http://cloudpense.nipponpaint.com.cn/np8000/sap/bc/srt/rfc/sap/bapi_exchangerate_getdetail_ws/500/bapi_exchangerate_getdetail_ws/bapi_exchangerate_getdetail_ws
nippon.company=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WebFI_PRD:ch_SOAP_web_CompanyCode_out&version=3.0&Sender.Service=BS_WebFI_PRD&Interface=http%3A%2F%2Fwww.neusoft.com%2F%5EZCompanyCdReq
nippon.cost_center=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=SI_FK2ERP_COSTANDPROFITCENTER_T&interfaceNamespace=http%3A%2F%2Fwww.pactera.com%2Fnippon
nippon.phone_cost_limit=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WebFI_PRD:CC_WebExpenseLimit_SOAP_OUT&version=3.0&Sender.Service=BS_WebFI_PRD&Interface=http%3A%2F%2Fwww.neusoft.com%2F%5EZWebExpenseLimit
nippon.frame_order=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WEB:CC_012_SOAP_SENDER_WEB_ECC_FRAME_ORDER&version=3.0&Sender.Service=BS_WEB&Interface=http%3A%2F%2Fwww.pactera.com%2FreimburseII_masterdata%5EMI_012_O_WEB_ECC_FRAME_ORDER
nippon.internal_order=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=ZIntOrderReq&interfaceNamespace=http://www.neusoft.com/
nippon.wbs_project=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=ZPrjReq&interfaceNamespace=http://www.neusoft.com/
nippon.absence_sync=http://cloudpense.nipponpaint.com.cn/np8000/sap/bc/srt/rfc/sap/zwshr_absence/500/zwshr_absence/zwshr_absence
nippon.customer=http://cloudpense.nipponpaint.com.cn/np8001/sap/bc/srt/rfc/sap/zfm_sd0005_ws/500/zfm_sd0005_ws/zfm_sd0005_ws
nippon.supplier=http://cloudpense.nipponpaint.com.cn/np8000/sap/bc/srt/rfc/sap/zws_get_lifnr_undeleted/500/zws_get_lifnr_undeleted/zws_get_lifnr_undeleted
nippon.training=http://notesapp.nipponpaint.com.cn:80/boot/group/HrTraining.nsf/TrainingMasterData?OpenWebService
nippon.on_business=http://notesapp.nipponpaint.com.cn:80/Boot/Application/Hr_station.nsf/hrmasterdata?OpenWebService
nippon.budget=http://notesapp4.nipponpaint.com.cn:80/bunsha/TU/application/EnginOrderApproval.nsf/FeeMasterData?OpenWebService
nippon.forwarding_server=http://beep2.nipponpaint.com.cn:8080/NipponData/api/
nippon.approve.email.moreApprove=http://eportal.nipponpaint.com.cn/Pages/Frame.aspx?MenuCode=Index&Url=%2f_layouts%2f15%2fPortal%2fTodoTask%2fTodoList.aspx%3FRefresh%3Dtrue
nippon.customer_sendmail=http://*************:8248/nippon/nippon/notifyEmail
#hsf
hsf.local_file_path=/home/<USER>/hsf
hsf.registerCode=3002
hsf.bindUrl=http://**************/seeyon/rest/thirdpartyUserMapper/binding
hsf.msgUrl=http://**************/seeyon/rest/thirdpartyMessage/receive/singleMessage
hsf.taskUrl=http://**************/seeyon/rest/thirdpartyPending/receive/pendings
hsf.updateUrl=http://**************/seeyon/rest/thirdpartyPending/updatePendingState
hsf.tokenUrl=http://**************/seeyon/rest/token/feikon/fk123456
hsf.ftp_user_name=fk
hsf.ftp_password=Fk122
hsf.ftp_server_ip=**************
hsf.ftp_server_port=123
hsf.claim_push=http://**************:1880/service/XChangeServlet?account=nknc&groupcode=HKJT
hsf.claim_push.operator=1001B1100000008I2X8Z
#het-env
het.env = prod
#het-qa
qa.het.payment.orgCode = 33057
qa.het.payment.secretKey=0236fc5d82c075cb55fc5eb7ae7e6e2cd0125fb91daf937a28d7470a4abaa752ef7a4f9bdba20743deea7d2263fdf078
qa.het.payment.url=https://gphn.cloudpense.com/het/payment/
#het-prod
prod.het.payment.orgCode = 36132
prod.het.payment.secretKey = 6e944305d19040b9c5604e4db237bfd3c7555642304f32edfe26d53b64bb3db85d719dcffa51d3d096be231b415b267b1d5bb5c27cb1aeff644e865d7560e12b
prod.het.payment.url = https://caiziapp.jinzay.com.cn:6443/
#yofc
yofc.env =prod
prod.yofc.postUrl = https://gphn.cloudpense.com/project/yofc/cbs/v2/
prod.yofc.key =123456
prod.yofc.voucherUrl = https://gphn.cloudpense.com/project/yofc/erp/generalLedger/add?apikey=tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp
prod.yofc.voucherUrlNew = https://gphn.cloudpense.com/project/yofc/erp/generalLedger/addBatch?apikey=tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp

#evcard
evcardSap=http://sap-po.gcsrental.com/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_CGXT_P&receiverParty=&receiverService=&interface=SI_OT2SAP_REQ&interfaceNamespace=http%3A%2F%2Fwww.evcard.com%2Fint
evcardSso=http://sso.evcard.vip/evcard-sso/api/validateToken
evcardSrm=http://srm-itf.gcsrental.com/itf/modules/ws_eitf/PUR_REQ/eitf_pur_req_import_server.svc
basePush=http://**************:86/pushMessage.do
cloudpenseevcard=https://www.cloudpense.com/redirect/evcard-oa-mobile.html

#evcard
kehua.k3VoucherPush=http://*************:8247/kehuashengwu/voucher/voucher
kehua.k3VoucherPreview=http://*************:8247/kehuashengwu/voucher/preview

#wshh
#æ¥éåæ¨é
wshh.claimPush=http://**************:9008/api/v1/certificate/upload?token=864a2239-408d-4c53-a6be-6b186f31bd6e
getTokenurl=http://oa.gcsrental.com/login/gettoken.jsp?userid=
jumpOaUrl=http://oa.gcsrental.com/login/autologin.jsp?ssoToken=

heraeus.industrialCode=1171

liandian.todoCount.expireTime=1800000
liandian.todoCount.capacity=1000

phoenix.iPortalTravelUrl=http://api.phoenixcontact.com.cn:8083/iPortal/startFlow
phoenix.iPortalUrl=https://api.phoenixcontact.com.cn:18443/iPortal/lbpmResult

cloudpense.evcard.open_api=https://openapi.cloudpense.com
cloudpense.evcard.client_id=evcard-ljka24isropqzx
cloudpense.evcard.client_secret=K3IUkhsnoiu2fgKJkjkfwel3erfdxcv

redirect.url.document.update.16904=https://openapi.cloudpense.com/evcard/claim/createAsync

helishiCustomerUrl=https://openapi.cloudpense.com/heraeus/voucher/pushVoucherToSap
helishi.tw.tokenUrl=https://openapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=cloudpense119529600f6&client_secret=4b7fb2ccf3328b98b6c9f9456120cc13420c8be324ca52fa77e149329b03123f1c3dee10
helishi.shanghai.tokenUrl=https://openapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=hlsInvoice3i209zbLPw3SAmbtY1&client_secret=hlsKF59e30956eEInvo9DD5C3F3161D35D66171
helishi.diance.tokenUrl=https://openapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=hlsInvoice3i209zbLPw3SAmbtY&client_secret=hlsKF59e30956eEInvo9DD5C3F3161D35D6617
helishi.tomcatPath=/home/<USER>/softwares/apache-tomcat-8.0.21

#åéæ¶æ¯æå¡æ¥å£
message.host=https://openapi.cloudpense.com/cproxy/message/send/unAuth