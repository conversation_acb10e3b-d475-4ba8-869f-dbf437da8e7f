#develop
#beforeurl=https://dev.cloudpense.com/product/password-reset.html?locale=
#registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
#oauthurl=https://devapi.cloudpense.com/iExpenseManager/oauth/token
#finance=https://dev.cloudpense.com/product/finance-detail.html?id=

#qa
beforeurl=https://qa.cloudpense.com/product/password-reset.html?locale=
registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
oauthurl=http://localhost:8081/iExpenseManager/oauth/token
finance=https://qa.cloudpense.com/product/finance-detail.html?id=
workUrl=https://qa.cloudpense.com/tmp/portal.html?path=document&id=
projectUrl=https://qa.cloudpense.com
projectOauthUrl=https://devapi.cloudpense.com/iExpenseManager

#product
#beforeurl=https://www.cloudpense.com/product/password-reset.html?locale=
#registerurl=https://www.cloudpense.com/product/password-emailverify.html?locale=
#oauthurl=https://api.cloudpense.com/iExpenseManager/oauth/token
#finance=https://www.cloudpense.com/product/finance-detail.html?id=
#workUrl=https://www.cloudpense.com/tmp/portal.html?path=document&id=
#nippon
#nippon uri by cloudpense
global.env=qa
nippon.tmc=https://qaapi.cloudpense.com/iExpenseManager/open/third/order/split/taxexpid
nippon.tmc.ticket=https://devapi.cloudpense.com/iExpenseManager/open/third/order/flight/ticketno
nippon.sso_page=https://qa.cloudpense.com/redirect/nippon-login.html
nippon.get_cp_token=https://gphn.cloudpense.com/iExpenseSqftp/open/oauth/token
nippon.grant_type=client_credentials
nippon.client_id=lbfniuq3wi4e52wos02po
nippon.client_secret=VJwq5O98uiyhjm2wmki876ytg
nippon.cp_sso=https://qaapi.cloudpense.com/iExpenseManager/sso/login
nippon.encrypt_secret=g4Jff50BfieY70Ge
nippon.encrypt_corp_id=c13de52307ef9973
#nippon sso login
nippon.wechat_auth=http://m-eportal-uat.nipponpaint.com.cn/BasicV2/Wechat/GetTicket
nippon.pc_auth=https://ssotest.nipponpaint.com.cn/profile/oauth2/accessToken
nippon.pc_auth_profile=https://ssotest.nipponpaint.com.cn/profile/oauth2/profile
nippon.sso.client_id=RXC9lF5Rdp
nippon.sso.client_secret=6e6acb79-8749-48b7-8ed4-770b022a2277
nippon.sso.grant_type=authorization_code
#nippon data sync uri by nippon
nippon.sent_status=http://beep.nipponpaint.com.cn/webapp/Reimbursement/DisplayState.aspx
nippon.wechat_ntf=http://WXAPPROVETEST.NIPPONPAINT.COM.CN/Service/MpService.asmx
nippon.company=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WebFI_PRD:ch_SOAP_web_CompanyCode_out&version=3.0&Sender.Service=BS_WebFI_PRD&Interface=http%3A%2F%2Fwww.neusoft.com%2F%5EZCompanyCdReq
nippon.cost_center=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=SI_FK2ERP_COSTANDPROFITCENTER_T&interfaceNamespace=http%3A%2F%2Fwww.pactera.com%2Fnippon
nippon.supplier=http://cloudpense.nipponpaint.com.cn/np8000/sap/bc/srt/rfc/sap/zws_get_lifnr_undeleted/500/zws_get_lifnr_undeleted/zws_get_lifnr_undeleted
nippon.customer=http://cloudpense.nipponpaint.com.cn/np8001/sap/bc/srt/rfc/sap/zfm_sd0005_ws/500/zfm_sd0005_ws/zfm_sd0005_ws
nippon.phone_cost_limit=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WebFI_PRD:CC_WebExpenseLimit_SOAP_OUT&version=3.0&Sender.Service=BS_WebFI_PRD&Interface=http%3A%2F%2Fwww.neusoft.com%2F%5EZWebExpenseLimit
nippon.frame_order=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?channel=:BS_WEB:CC_012_SOAP_SENDER_WEB_ECC_FRAME_ORDER&version=3.0&Sender.Service=BS_WEB&Interface=http%3A%2F%2Fwww.pactera.com%2FreimburseII_masterdata%5EMI_012_O_WEB_ECC_FRAME_ORDER
nippon.forwarding_server=http://beep2.nipponpaint.com.cn:8080/NipponData/api/
nippon.internal_order=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=ZIntOrderReq&interfaceNamespace=http://www.neusoft.com/
nippon.training=http://notesapp.nipponpaint.com.cn:80/boot/group/HrTraining.nsf/TrainingMasterData?OpenWebService
nippon.on_business=http://notesapp.nipponpaint.com.cn:80/Boot/Application/Hr_station.nsf/hrmasterdata?OpenWebService
nippon.credit=http://feikongtest.nipponpaint.com.cn:8007/TravelExpenseService.asmx
nippon.exchange_rate=http://saperpqas.nipponpaint.com.cn:8000/sap/bc/srt/rfc/sap/bapi_exchangerate_getdetail_ws/510/bapi_exchangerate_getdetail_ws/bapi_exchangerate_getdetail_ws
nippon.wbs_project=http://p1p.nipponpaint.com.cn:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_WebFI_PRD&receiverParty=&receiverService=&interface=ZPrjReq&interfaceNamespace=http://www.neusoft.com/
nippon.claim_push=http://fkdev.nipponpaint.com.cn/TravelExpenseService.asmx
nippon.absence_sync=http://saperpqas.nipponpaint.com.cn:8000/sap/bc/srt/rfc/sap/zwshr_absence/510/zwshr_absence/zwshr_absence
nippon.budget=http://notesdev.nipponpaint.com.cn:80/bunsha/tu/application/enginorderapproval.nsf/FeeMasterData?OpenWebService
nippon.invoice_update=http://feikongtest.nipponpaint.com.cn:8007/TravelExpenseService.asmx
nippon.approve.email.moreApprove=http://eportal.nipponpaint.com.cn/Pages/Frame.aspx?MenuCode=Index&Url=%2f_layouts%2f15%2fPortal%2fTodoTask%2fTodoList.aspx%3FRefresh%3Dtrue
nippon.customer_sendmail=http://*************:8248/nippon/nippon/notifyEmail
#hsf
hsf.local_file_path=/home/<USER>/hsf
hsf.registerCode=3002
hsf.bindUrl=https://www.hsf-dj.cn/seeyon/rest/thirdpartyUserMapper/binding
hsf.msgUrl=https://www.hsf-dj.cn/seeyon/rest/thirdpartyMessage/receive/singleMessage
hsf.taskUrl=https://www.hsf-dj.cn/seeyon/rest/thirdpartyPending/receive/pendings
hsf.updateUrl=https://www.hsf-dj.cn/seeyon/rest/thirdpartyPending/updatePendingState
hsf.tokenUrl=https://www.hsf-dj.cn/seeyon/rest/token/feikon/fk123456
hsf.ftp_user_name=fk
hsf.ftp_password=Fk122
hsf.ftp_server_ip=**************
hsf.ftp_server_port=123
hsf.claim_push=http://**************:91/service/XChangeServlet?account=nctest&groupcode=HKJT
#het-env
het.env = qa
#het-qa
qa.het.payment.orgCode = 33057
qa.het.payment.secretKey = 0236fc5d82c075cb55fc5eb7ae7e6e2cd0125fb91daf937a28d7470a4abaa752ef7a4f9bdba20743deea7d2263fdf078
qa.het.payment.url=https://gphn.cloudpense.com/het/payment/
#het-prod
prod.het.payment.orgCode = 36132
prod.het.payment.secretKey = 6e944305d19040b9c5604e4db237bfd3c7555642304f32edfe26d53b64bb3db85d719dcffa51d3d096be231b415b267b1d5bb5c27cb1aeff644e865d7560e12b
prod.het.payment.url = https://caiziapp.jinzay.com.cn:6443/
#yofc
yofc.env =qa
#qa.yofc.postUrl = https://gphn.cloudpense.com/project/yofc/cbs_domain/apikey=tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp
qa.yofc.postUrl = https://gphn.cloudpense.com/project/yofc/v3_test/
#qa.yofc.postUrl = https://openapis.yofc.com:8443/v3_test/
#qa.yofc.postUrl = https://gphn.cloudpense.com/project/yofc/v3_test/tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp
#qa.yofc.key =123456
qa.yofc.key =********
qa.yofc.voucherUrl = https://gphn.cloudpense.com/project/yofc/erp/generalLedger/add?apikey=tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp
qa.yofc.voucherUrlNew = https://gphn.cloudpense.com/project/yofc/erp/test/generalLedger/addBatch?apikey=tpga32ebxcl0mrkd84xbp7iy3a0ynhht64vkbpvp

#evcard
evcardSap=http://sap-po-dev.gcsrental.com/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_CGXT&receiverParty=&receiverService=&interface=SI_OT2SAP_REQ&interfaceNamespace=http%3A%2F%2Fwww.evcard.com%2Fint
evcardSso=http://csms-st.evcard.vip:180/evcard-sso/api/validateToken
evcardSrm=http://**************:18090/itf/modules/ws_eitf/PUR_REQ/eitf_pur_req_import_server.svc

basePush=http://oam-test.gcsrental.com/pushMessage.do
cloudpenseevcard=https://qa.cloudpense.com/redirect/evcard-oa-mobile.html
wshh.claimPush=http://**************:9008/api/v1/certificate/upload?token=864a2239-408d-4c53-a6be-6b186f31bd6e
getTokenurl=http://oa-test.gcsrental.com/login/gettoken.jsp?userid=
jumpOaUrl=http://oa-test.gcsrental.com/login/autologin.jsp?ssoToken=

#kehua
kehua.k3VoucherPush=http://*************:8247/kehuashengwu/voucher/voucher
kehua.k3VoucherPreview=http://*************:8247/kehuashengwu/voucher/preview

heraeus.industrialCode=1171

liandian.todoCount.expireTime=60000
liandian.todoCount.capacity=5

phoenix.iPortalTravelUrl=http://api-test.phoenixcontact.com.cn:8085/iPortal/startFlow
phoenix.iPortalUrl=http://api-test.phoenixcontact.com.cn:8085/iPortal/lbpmResult

#helishiCustomerUrl=https://topenapi.cloudpense.com/heraeus/voucher/pushVoucherToSap
helishiCustomerUrl=http://localhost:8218/heraeus/voucher/pushVoucherToSap
helishi.tw.tokenUrl=https://topenapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=cloudpense110893696bb&client_secret=47dd92e0c8d69a5a63154c27636c26c946aaa8461ba1cb4613a544c4a00b251fc0fd6faf
helishi.shanghai.tokenUrl=https://topenapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=cloudpense9352473662&client_secret=4b55b8062728c860078f5c8382339f9748cd92c442cfec6be4124184b896fd49b6e3d0e0
helishi.diance.tokenUrl=https://topenapi.cloudpense.com/common/unAuth/tokens/get?grant_type=client_credentials&client_id=cloudpense119898240af&client_secret=40159096d782c266fccb45265c0593814198b2e81e0710139e624dae91d093e0293c5e6a

#åéæ¶æ¯æå¡æ¥å£
message.host=https://topenapi.cloudpense.com/cproxy/message/send/unAuth