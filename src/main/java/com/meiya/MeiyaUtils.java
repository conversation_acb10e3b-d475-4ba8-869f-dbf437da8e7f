package com.meiya;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import net.sf.json.JSONSerializer;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

//package io.rong.util;
import java.security.MessageDigest;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class MeiyaUtils {
    public static final String host = "http://***************:6888";
    public static final String hosturl = host + "/ormrpc/services/WSToFIDataFacade";
    public static final String loginurl = host + "/ormrpc/services/EASLogin";

    public static String top = "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\"http://webservice.app.fi.custom.eas.kingdee.com\">";
    public static String bottom = "</soapenv:Envelope>";
    public static String header = "<soapenv:Header/>";

    public static String logIn = "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:log=\"http://login.webservice.bos.kingdee.com\"><soapenv:Header/><soapenv:Body><log:login soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\"><userName xsi:type=\"xsd:string\">MYCF</userName><password xsi:type=\"xsd:string\">MYCF</password><slnName xsi:type=\"xsd:string\">eas</slnName><dcName xsi:type=\"xsd:string\">My2020</dcName><language xsi:type=\"xsd:string\">L2</language><dbType xsi:type=\"xsd:int\">1</dbType></log:login></soapenv:Body></soapenv:Envelope>";
    public static String logOut = "<soapenv:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:log=\"http://login.webservice.bos.kingdee.com\"><soapenv:Header/><soapenv:Body><log:logout soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\"><userName xsi:type=\"xsd:string\">MYCF</userName><slnName xsi:type=\"xsd:string\">eas</slnName><dcName xsi:type=\"xsd:string\">My2020</dcName><language xsi:type=\"xsd:string\">L2</language></log:logout></soapenv:Body></soapenv:Envelope>";

    private static final Logger logger = LoggerFactory.getLogger(MeiyaUtils.class);
    public static String dbQueryRequest(String data) {
        return top + header +
                "<soapenv:Body><web:dbQuery soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\"><sql xsi:type=\"xsd:string\"><![CDATA[" +
                data +
                "]]></sql></web:dbQuery></soapenv:Body>" +
                bottom;
    }

    public static String dbCreateVoucherRequest(String data) {
        return top + header +
                "<soapenv:Body><web:createVoucher soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\"><xmlData>" +
                StringEscapeUtils.escapeHtml("<xmlData>" + data + "</xmlData>") +
                "</xmlData></web:createVoucher></soapenv:Body>" +
                bottom;
    }

    public static String dbQueryVoucherRequest(String data) {
        return top + header +
                "<soapenv:Body><web:queryVoucher soapenv:encodingStyle=\"http://schemas.xmlsoap.org/soap/encoding/\">" +
                data +
                "</web:queryVoucher></soapenv:Body>" +
                bottom;
    }

    public static String dbQueryResponse(String data) {
        data = StringEscapeUtils.unescapeHtml(data);
//        System.out.println(data);
        int startIndex = data.indexOf("<xmlData>");
        int endIndex = data.indexOf("</xmlData>");
        System.out.println(startIndex);
        System.out.println(endIndex);
        return data.substring(startIndex, endIndex + 10);
    }

    public static String HttpPostString(String url, Map<String, String> headers, String data) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(40000).setConnectTimeout(40000).build();
        httpPost.setConfig(requestConfig);

        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }

        try {
            StringEntity requestEntity = new StringEntity(data, "utf-8");
            httpPost.setEntity(requestEntity);

            response = httpClient.execute(httpPost, new BasicHttpContext());
//            if (response.getStatusLine().getStatusCode() != 200) {
//
//                System.out.println("request url failed, http code=" + response.getStatusLine().getStatusCode()
//                        + ", url=" + url);
//                return response;
//            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");
                logger.info("result str==>" + resultStr);
                return resultStr;
            }
        } catch (IOException e) {
            logger.info("request url=" + url + ", exception, msg=" + e.getMessage());
            e.printStackTrace();
            throw new Exception(e.getMessage());
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
    public static String hexSHA1(String value) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(value.getBytes("utf-8"));
            byte[] digest = md.digest();
            return byteToHexString(digest);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }
    public static String byteToHexString(byte[] bytes) {
        return String.valueOf(Hex.encodeHex(bytes));
    }



    public static void main(String[] arg0) {
//        String data = dbQueryRequest("select * from t_bd_person where rownum<=10;");
//        System.out.println(data);
//        Map<String, String> header = new HashMap<>();
//        header.put("Content-Type", "application/xml");
//        header.put("SOAPAction", " ");
//        try {
//            String abc = HttpPostString(hosturl, header, data);
////            System.out.println(StringEscapeUtils.unescapeHtml(abc));
//            System.out.println(dbQueryResponse(abc));
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("start", "bnew");
        XMLSerializer xmlSerializer = new XMLSerializer();
        System.out.println(xmlSerializer.write(JSONSerializer.toJSON(jsonObject.toJSONString())));
//        String test = "<dbQueryReturn><![CDATA[xmlData]]></dbQueryReturn><body>";
//        System.out.println(dbQueryResponse(test));

    }

}
