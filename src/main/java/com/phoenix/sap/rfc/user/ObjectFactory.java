
package com.phoenix.sap.rfc.user;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.sap.document.sap.rfc.functions package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.sap.document.sap.rfc.functions
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link ZHRCCSYNCPERSON_Type }
     * 
     */
    public ZHRCCSYNCPERSON_Type createZHRCCSYNCPERSON_Type() {
        return new ZHRCCSYNCPERSON_Type();
    }

    /**
     * Create an instance of {@link ZHRCCSYNCPERSONResponse }
     * 
     */
    public ZHRCCSYNCPERSONResponse createZHRCCSYNCPERSONResponse() {
        return new ZHRCCSYNCPERSONResponse();
    }

    /**
     * Create an instance of {@link ZHRTCCPERSON }
     * 
     */
    public ZHRTCCPERSON createZHRTCCPERSON() {
        return new ZHRTCCPERSON();
    }

    /**
     * Create an instance of {@link ZHRSCCPERSON }
     * 
     */
    public ZHRSCCPERSON createZHRSCCPERSON() {
        return new ZHRSCCPERSON();
    }

}
