
package com.phoenix.sap.rfc.user;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ET_OUTPERSON" type="{urn:sap-com:document:sap:rfc:functions}ZHR_T_CC_PERSON"/&gt;
 *         &lt;element name="EV_MESSG" type="{urn:sap-com:document:sap:rfc:functions}char500"/&gt;
 *         &lt;element name="EV_MSGTY" type="{urn:sap-com:document:sap:rfc:functions}char1"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "etoutperson",
    "evmessg",
    "evmsgty"
})
@XmlRootElement(name = "ZHR_CC_SYNC_PERSONResponse")
public class ZHRCCSYNCPERSONResponse {

    @XmlElement(name = "ET_OUTPERSON", required = true)
    protected ZHRTCCPERSON etoutperson;
    @XmlElement(name = "EV_MESSG", required = true)
    protected String evmessg;
    @XmlElement(name = "EV_MSGTY", required = true)
    protected String evmsgty;

    /**
     * Gets the value of the etoutperson property.
     * 
     * @return
     *     possible object is
     *     {@link ZHRTCCPERSON }
     *     
     */
    public ZHRTCCPERSON getETOUTPERSON() {
        return etoutperson;
    }

    /**
     * Sets the value of the etoutperson property.
     * 
     * @param value
     *     allowed object is
     *     {@link ZHRTCCPERSON }
     *     
     */
    public void setETOUTPERSON(ZHRTCCPERSON value) {
        this.etoutperson = value;
    }

    /**
     * Gets the value of the evmessg property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEVMESSG() {
        return evmessg;
    }

    /**
     * Sets the value of the evmessg property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEVMESSG(String value) {
        this.evmessg = value;
    }

    /**
     * Gets the value of the evmsgty property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEVMSGTY() {
        return evmsgty;
    }

    /**
     * Sets the value of the evmsgty property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEVMSGTY(String value) {
        this.evmsgty = value;
    }

}
