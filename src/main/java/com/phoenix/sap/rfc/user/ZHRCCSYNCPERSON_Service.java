package com.phoenix.sap.rfc.user;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-15T08:51:10.672Z
 * Generated source version: 3.2.6
 *
 */
@WebServiceClient(name = "ZHR_CC_SYNC_PERSON",
                  wsdlLocation = "file:人员.xml",
                  targetNamespace = "urn:sap-com:document:sap:rfc:functions")
public class ZHRCCSYNCPERSON_Service extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("urn:sap-com:document:sap:rfc:functions", "ZHR_CC_SYNC_PERSON");
    public final static QName BindingSoap12 = new QName("urn:sap-com:document:sap:rfc:functions", "binding_soap12");
    public final static QName Binding = new QName("urn:sap-com:document:sap:rfc:functions", "binding");
    static {
        URL url = null;
        try {
            url = new URL("file:人员.xml");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ZHRCCSYNCPERSON_Service.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "file:人员.xml");
        }
        WSDL_LOCATION = url;
    }

    public ZHRCCSYNCPERSON_Service(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ZHRCCSYNCPERSON_Service(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ZHRCCSYNCPERSON_Service() {
        super(WSDL_LOCATION, SERVICE);
    }

    public ZHRCCSYNCPERSON_Service(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ZHRCCSYNCPERSON_Service(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ZHRCCSYNCPERSON_Service(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns ZhrCcSyncPerson
     */
    @WebEndpoint(name = "binding_soap12")
    public ZhrCcSyncPerson getBindingSoap12() {
        return super.getPort(BindingSoap12, ZhrCcSyncPerson.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ZhrCcSyncPerson
     */
    @WebEndpoint(name = "binding_soap12")
    public ZhrCcSyncPerson getBindingSoap12(WebServiceFeature... features) {
        return super.getPort(BindingSoap12, ZhrCcSyncPerson.class, features);
    }


    /**
     *
     * @return
     *     returns ZhrCcSyncPerson
     */
    @WebEndpoint(name = "binding")
    public ZhrCcSyncPerson getBinding() {
        return super.getPort(Binding, ZhrCcSyncPerson.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ZhrCcSyncPerson
     */
    @WebEndpoint(name = "binding")
    public ZhrCcSyncPerson getBinding(WebServiceFeature... features) {
        return super.getPort(Binding, ZhrCcSyncPerson.class, features);
    }

}
