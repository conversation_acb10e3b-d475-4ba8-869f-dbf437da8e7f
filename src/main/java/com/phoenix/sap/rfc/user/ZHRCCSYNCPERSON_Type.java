
package com.phoenix.sap.rfc.user;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="IV_BEGDA" type="{urn:sap-com:document:sap:rfc:functions}date10"/&gt;
 *         &lt;element name="IV_BUKRS" type="{urn:sap-com:document:sap:rfc:functions}char4"/&gt;
 *         &lt;element name="IV_ENDDA" type="{urn:sap-com:document:sap:rfc:functions}date10"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "ivbegda",
    "ivbukrs",
    "ivendda"
})
@XmlRootElement(name = "ZHR_CC_SYNC_PERSON")
public class ZHRCCSYNCPERSON_Type {

    @XmlElement(name = "IV_BEGDA", required = true)
    protected String ivbegda;
    @XmlElement(name = "IV_BUKRS", required = true)
    protected String ivbukrs;
    @XmlElement(name = "IV_ENDDA", required = true)
    protected String ivendda;

    /**
     * Gets the value of the ivbegda property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIVBEGDA() {
        return ivbegda;
    }

    /**
     * Sets the value of the ivbegda property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIVBEGDA(String value) {
        this.ivbegda = value;
    }

    /**
     * Gets the value of the ivbukrs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIVBUKRS() {
        return ivbukrs;
    }

    /**
     * Sets the value of the ivbukrs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIVBUKRS(String value) {
        this.ivbukrs = value;
    }

    /**
     * Gets the value of the ivendda property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getIVENDDA() {
        return ivendda;
    }

    /**
     * Sets the value of the ivendda property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setIVENDDA(String value) {
        this.ivendda = value;
    }

}
