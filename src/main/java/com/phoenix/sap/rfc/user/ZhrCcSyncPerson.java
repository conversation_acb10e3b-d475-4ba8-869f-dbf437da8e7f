package com.phoenix.sap.rfc.user;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-15T08:51:10.633Z
 * Generated source version: 3.2.6
 *
 */
@WebService(targetNamespace = "urn:sap-com:document:sap:rfc:functions", name = "zhr_cc_sync_person")
@XmlSeeAlso({ObjectFactory.class})
public interface ZhrCcSyncPerson {

    @WebMethod(operationName = "ZHR_CC_SYNC_PERSON", action = "urn:sap-com:document:sap:rfc:functions:zhr_cc_sync_person:ZHR_CC_SYNC_PERSONRequest")
    @RequestWrapper(localName = "ZHR_CC_SYNC_PERSON", targetNamespace = "urn:sap-com:document:sap:rfc:functions", className = "com.sap.document.sap.rfc.functions.ZHRCCSYNCPERSON_Type")
    @ResponseWrapper(localName = "ZHR_CC_SYNC_PERSONResponse", targetNamespace = "urn:sap-com:document:sap:rfc:functions", className = "com.sap.document.sap.rfc.functions.ZHRCCSYNCPERSONResponse")
    public void zhrCCSYNCPERSON(
        @WebParam(name = "IV_BEGDA", targetNamespace = "")
        java.lang.String ivBEGDA,
        @WebParam(name = "IV_BUKRS", targetNamespace = "")
        java.lang.String ivBUKRS,
        @WebParam(name = "IV_ENDDA", targetNamespace = "")
        java.lang.String ivENDDA,
        @WebParam(mode = WebParam.Mode.OUT, name = "ET_OUTPERSON", targetNamespace = "")
        javax.xml.ws.Holder<ZHRTCCPERSON> etOUTPERSON,
        @WebParam(mode = WebParam.Mode.OUT, name = "EV_MESSG", targetNamespace = "")
        javax.xml.ws.Holder<java.lang.String> evMESSG,
        @WebParam(mode = WebParam.Mode.OUT, name = "EV_MSGTY", targetNamespace = "")
        javax.xml.ws.Holder<java.lang.String> evMSGTY
    );
}
