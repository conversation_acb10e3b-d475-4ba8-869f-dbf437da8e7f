
package com.phoenix.sap.rfc.user;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for ZHR_S_CC_PERSON complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="ZHR_S_CC_PERSON"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="PERNR" type="{urn:sap-com:document:sap:rfc:functions}numeric8"/&gt;
 *         &lt;element name="SNAME" type="{urn:sap-com:document:sap:rfc:functions}char30"/&gt;
 *         &lt;element name="BUKRS" type="{urn:sap-com:document:sap:rfc:functions}char4"/&gt;
 *         &lt;element name="ORGEH" type="{urn:sap-com:document:sap:rfc:functions}numeric8"/&gt;
 *         &lt;element name="KOSTL" type="{urn:sap-com:document:sap:rfc:functions}char10"/&gt;
 *         &lt;element name="GESCH" type="{urn:sap-com:document:sap:rfc:functions}char1"/&gt;
 *         &lt;element name="EMAIL" type="{urn:sap-com:document:sap:rfc:functions}char50"/&gt;
 *         &lt;element name="PHONE" type="{urn:sap-com:document:sap:rfc:functions}char50"/&gt;
 *         &lt;element name="STAT2" type="{urn:sap-com:document:sap:rfc:functions}char20"/&gt;
 *         &lt;element name="ZHRZJ" type="{urn:sap-com:document:sap:rfc:functions}char20"/&gt;
 *         &lt;element name="PLSTX" type="{urn:sap-com:document:sap:rfc:functions}char25"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ZHR_S_CC_PERSON", propOrder = {
    "pernr",
    "sname",
    "bukrs",
    "orgeh",
    "kostl",
    "gesch",
    "email",
    "phone",
    "stat2",
    "zhrzj",
    "plstx"
})
public class ZHRSCCPERSON {

    @XmlElement(name = "PERNR", required = true)
    protected String pernr;
    @XmlElement(name = "SNAME", required = true)
    protected String sname;
    @XmlElement(name = "BUKRS", required = true)
    protected String bukrs;
    @XmlElement(name = "ORGEH", required = true)
    protected String orgeh;
    @XmlElement(name = "KOSTL", required = true)
    protected String kostl;
    @XmlElement(name = "GESCH", required = true)
    protected String gesch;
    @XmlElement(name = "EMAIL", required = true)
    protected String email;
    @XmlElement(name = "PHONE", required = true)
    protected String phone;
    @XmlElement(name = "STAT2", required = true)
    protected String stat2;
    @XmlElement(name = "ZHRZJ", required = true)
    protected String zhrzj;
    @XmlElement(name = "PLSTX", required = true)
    protected String plstx;

    /**
     * Gets the value of the pernr property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPERNR() {
        return pernr;
    }

    /**
     * Sets the value of the pernr property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPERNR(String value) {
        this.pernr = value;
    }

    /**
     * Gets the value of the sname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSNAME() {
        return sname;
    }

    /**
     * Sets the value of the sname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSNAME(String value) {
        this.sname = value;
    }

    /**
     * Gets the value of the bukrs property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBUKRS() {
        return bukrs;
    }

    /**
     * Sets the value of the bukrs property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBUKRS(String value) {
        this.bukrs = value;
    }

    /**
     * Gets the value of the orgeh property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getORGEH() {
        return orgeh;
    }

    /**
     * Sets the value of the orgeh property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setORGEH(String value) {
        this.orgeh = value;
    }

    /**
     * Gets the value of the kostl property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKOSTL() {
        return kostl;
    }

    /**
     * Sets the value of the kostl property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKOSTL(String value) {
        this.kostl = value;
    }

    /**
     * Gets the value of the gesch property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getGESCH() {
        return gesch;
    }

    /**
     * Sets the value of the gesch property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setGESCH(String value) {
        this.gesch = value;
    }

    /**
     * Gets the value of the email property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEMAIL() {
        return email;
    }

    /**
     * Sets the value of the email property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEMAIL(String value) {
        this.email = value;
    }

    /**
     * Gets the value of the phone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPHONE() {
        return phone;
    }

    /**
     * Sets the value of the phone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPHONE(String value) {
        this.phone = value;
    }

    /**
     * Gets the value of the stat2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSTAT2() {
        return stat2;
    }

    /**
     * Sets the value of the stat2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSTAT2(String value) {
        this.stat2 = value;
    }

    /**
     * Gets the value of the zhrzj property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getZHRZJ() {
        return zhrzj;
    }

    /**
     * Sets the value of the zhrzj property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setZHRZJ(String value) {
        this.zhrzj = value;
    }

    /**
     * Gets the value of the plstx property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPLSTX() {
        return plstx;
    }

    /**
     * Sets the value of the plstx property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPLSTX(String value) {
        this.plstx = value;
    }

}
