package com.Kehua;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.regions.ServiceAbbreviations;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.util.GUIDUtil;
import com.cloudpense.expman.util.S3.S3Constants;

import java.io.File;
import java.net.URL;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * 科华凭证附件的S3操作
 */
public class KehuaS3 {
    public boolean upload(String filePath,String uuidName){
        String use_id = new String();
        String use_secret = new String();
        String use_name = new String();
        String server = CustomerContextHolder.getCustomerType();
        /*定义密钥信息*/

        use_id = S3Constants.S3_LIST.get(server).getKeyID();
        use_secret = S3Constants.S3_LIST.get(server).getKey();
        use_name = S3Constants.S3_LIST.get(server).getName();

        AWSCredentials credentials = new BasicAWSCredentials(use_id, use_secret);
        AmazonS3 s3Client = new AmazonS3Client(credentials);
        Region region = Region.getRegion(Regions.CN_NORTH_1);
        s3Client.setRegion(region);
        final String serviceEndpoint = region.getServiceEndpoint(ServiceAbbreviations.S3);
        s3Client.setEndpoint(serviceEndpoint);

        File file = new File(filePath);
        ObjectMetadata disposition = new ObjectMetadata();
//        disposition.setContentDisposition("attachment;filename="+uuidName);
        if(uuidName.endsWith("pdf")) {
            s3Client.putObject(new PutObjectRequest(use_name, uuidName, file).withMetadata(disposition));
        }else {
            // set to public
            s3Client.putObject(new PutObjectRequest(use_name, uuidName, file));
        }
        return true;
    }



    public String getS3URL(String name) throws Exception {

        String use_id = new String();
        String use_secret = new String();
        String use_name = new String();
        String server = CustomerContextHolder.getCustomerType();
        use_id = S3Constants.S3_LIST.get(server).getKeyID();
        use_secret = S3Constants.S3_LIST.get(server).getKey();
        use_name = S3Constants.S3_LIST.get(server).getName();

        AWSCredentials credentials = new BasicAWSCredentials(use_id, use_secret);
        AmazonS3 s3Client = new AmazonS3Client(credentials);
        Region region = Region.getRegion(Regions.CN_NORTH_1);
        s3Client.setRegion(region);
        final String serviceEndpoint = region.getServiceEndpoint(ServiceAbbreviations.S3);
        s3Client.setEndpoint(serviceEndpoint);

        // Create a UTC Gregorian calendar value.
        GregorianCalendar calendar = new GregorianCalendar(TimeZone.getTimeZone("UTC"));

        // Specify the current time as the start time for the shared access signature
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR, 2);
        Date expirationDate = calendar.getTime();

        URL url = s3Client.generatePresignedUrl(use_name, name, expirationDate);

        return url.toString();
    }

    /*获取文件名的拓展名*/
    public static String getExtentionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1).toLowerCase();
            }
        }

        return filename;
    }

    public static void main(String[] s) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
//        new KehuaS3().upload("./csv/","kehua/","xzjsEBbkDmxDehB-t-001.jpg");
//        System.out.println(new KehuaS3().getS3URL("kehua/xzjsEBbkDmxDehB-t-001.jpg"));
        //c7795026-44d2-443b-9f69-15d5cb1e796c.jpg
//        System.out.println(new KehuaS3().getS3URL("c7795026-44d2-443b-9f69-15d5cb1e796c.jpg"));

        String name = "kehua/"+ GUIDUtil.generateGUID() + "." + KehuaS3.getExtentionName("132567_0_19年补-北京特科-GXBJ20170626.pdf");
        boolean a =new KehuaS3().upload("C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\kehua\\attachmentFile\\132567_0_19年补-北京特科-GXBJ20170626.pdf",name);
        System.out.println(a);
        System.out.println(new KehuaS3().getS3URL(name));
    }
}
