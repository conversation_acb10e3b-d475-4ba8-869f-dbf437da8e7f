package com.Kehua;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.antscity.fsm.api.ApiException;
import com.antscity.fsm.api.util.HashMapEx;
import com.antscity.fsm.api.util.HttpUtils;
import com.antscity.fsm.api.util.SignUtils;
import com.cloudpense.expman.util.Constants.KehuaConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class KehuaUtils {

    private static Logger logger = LoggerFactory.getLogger(KehuaUtils.class);

    public static String doPost(JSONObject jsonObject, String methods) throws ApiException, IOException {
        String LOG_KEY = jsonObject.getString("LOG_KEY");
        KehuaFsmClient client = new KehuaFsmClient(KehuaConstants.URL, KehuaConstants.SECRET_KEY);
        client.login(KehuaConstants.USERNAME, KehuaConstants.PASSWORD);
        String token = client.token;
        HashMapEx parameters = new HashMapEx();
        parameters.put(KehuaFsmClient.DATA, jsonObject.toJSONString());
        parameters.put(KehuaFsmClient.METHOD, methods);
        parameters.put(KehuaFsmClient.TOKEN, token);
        parameters.put(KehuaFsmClient.TIMESTAMP, new Date());
        parameters.put(KehuaFsmClient.VERSION, KehuaConstants.VERSION);
        parameters.put(KehuaFsmClient.SIGN, SignUtils.signRequest(parameters, KehuaConstants.SECRET_KEY));
        logger.info("{}今修接口{}参数：{}", LOG_KEY, methods, parameters);
        String response = HttpUtils.sendPostRequest(KehuaConstants.URL, parameters);
        logger.info("{}今修接口{}返回结果：{}", LOG_KEY, methods, response);
        return response;
    }

    /**
     * 根据资源号查询userCode
     * @param resourceCode 资源号
     * @return userCode
     */
    public static String findUserCodeByResourceCode(String resourceCode){
        JSONObject jsonObject1 = new JSONObject();
        String userCode="";
        jsonObject1.put("methods","resourceFindByResourceCodes");
        List<String> list = new ArrayList<>();
        list.add(resourceCode);
        jsonObject1.put("resourceCodeList",list);
        JSONObject resultJson1 = null;
        try {
            resultJson1 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject1,jsonObject1.getString("methods")));
        } catch (ApiException | IOException e) {
            e.printStackTrace();
        }
        if (resultJson1 != null) {
             userCode = JSONArray.parseArray(resultJson1.getString("data")).getJSONObject(0).getString("userCode");
        }

        return userCode;
    }
    /**
     * 根据userCodes查询userName
     */
    public static String findUserNameByUserCode(String userCode){
        String userName ="";
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("methods","userExGetByUserCode");
        jsonObject2.put("userCode",userCode);
        JSONObject resultJson2 = null;
        try {
            resultJson2 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject2,jsonObject2.getString("methods")));
        } catch (ApiException | IOException e) {
            e.printStackTrace();
        }
        if (resultJson2 != null) {
            userName =JSONArray.parseArray(resultJson2.getString("data")).getJSONObject(0).getString("userName");
        }

        return userName;
    }

    public static String findCityByWorkOrderNo(String planSourceNo){
        String city ="";
        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("methods","workOrderGetByNo");
        jsonObject3.put("workOrderNo",planSourceNo);
        JSONObject resultJson3 = null;
        try {
            resultJson3 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject3,jsonObject3.getString("methods")));
        } catch (ApiException | IOException e) {
            e.printStackTrace();
        }
        if (resultJson3 != null) {
            city = JSONObject.parseObject(resultJson3.getString("data")).getString("city");
        }

        return city;
    }


    public static JSONObject updateSingleOrderByWorkOrderNo(String workOrderNo ){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("methods","resourcePlanSearch");
        jsonObject.put("workOrderNo",workOrderNo);
        jsonObject.put("excludeDirector","true");
        JSONObject resultJson = null;
        try {
            resultJson = JSONObject.parseObject(KehuaUtils.doPost(jsonObject,jsonObject.getString("methods")));
        } catch (ApiException | IOException e) {
            e.printStackTrace();
        }

        JSONObject inputJson = new JSONObject();
        if (resultJson != null) {
            inputJson.put("resourcePlanNo",resultJson.getString("resourcePlanNo"));
            inputJson.put("planSourceNo",resultJson.getString("planSourceNo"));
            inputJson.put("planBeginTime",resultJson.getString("planBeginTime").replace(" +0000",""));
            inputJson.put("planEndTime",resultJson.getString("planEndTime").replace(" +0000",""));
            inputJson.put("resourceDeleted",resultJson.getBoolean("resourceDeleted"));
            inputJson.put("planStatus",resultJson.getInteger("planStatus"));
            //下面查询工人工号和城市名
            String resourceCode = resultJson.getString("resourceCode");
            String planSourceNo = resultJson.getString("planSourceNo");
            String userCode = findUserCodeByResourceCode(resourceCode);
            String userName = findUserNameByUserCode(userCode);
            String city = findCityByWorkOrderNo(planSourceNo);

            //组装
            inputJson.put("PersonNo",userName.replace("A",""));
            inputJson.put("city",city.replace("市",""));
            return inputJson;
        }

        return inputJson;

    }








    public static void main(String[] s){
        JSONObject jsonObject0 = new JSONObject();
        jsonObject0.put("methods","workOrderGetById");
        jsonObject0.put("workOrderId","413");
        jsonObject0.put("","");
//        jsonObject0.put("","");
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("methods","workOrderSearch");
        jsonObject1.put("typeCodes","10,12");
        jsonObject1.put("statusCodes","10,12");
        jsonObject1.put("dateType","CREATE_TIME");
        jsonObject1.put("endTime","2019-01-16 17:08:00");

        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("methods","customerSearch");
        jsonObject2.put("searchText","32");

        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("methods","resourcePlanSearch");
        jsonObject3.put("beginTime","2019-05-31 11:00:00");
        jsonObject3.put("endTime","2019-05-31 11:43:00");

        JSONObject jsonObject4 = new JSONObject();
        jsonObject4.put("methods","resourceFindByResourceCodes");
        List<String> list = new ArrayList<>();
//        list.add("1000361");
        list.add("1000132");
        jsonObject4.put("resourceCodeList",list);

        JSONObject jsonObject5 = new JSONObject();
        jsonObject5.put("methods","userExGetByUserCode");
        jsonObject5.put("userCode","1212");


        JSONObject jsonObject6 = new JSONObject();
        jsonObject6.put("methods","workOrderGetByNo");
        jsonObject6.put("workOrderNo","412190107009306");


        JSONObject jsonObject7 = new JSONObject();
        jsonObject7.put("methods","resourcePlanSearch");
        jsonObject7.put("workOrderNo","412190319010761");
        jsonObject7.put("excludeDirector",true);


        JSONObject jsonObjects = jsonObject3;
        System.out.println(jsonObjects.toJSONString());
        try {
           String result = doPost(jsonObjects,jsonObjects.getString("methods"));
           System.out.println(result);
        } catch (ApiException | IOException e) {
            e.printStackTrace();
        }
    }
}
