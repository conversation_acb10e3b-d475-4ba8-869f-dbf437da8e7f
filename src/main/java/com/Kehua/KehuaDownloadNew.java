package com.Kehua;

import org.apache.commons.net.PrintCommandListener;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
public class KehuaDownloadNew extends Thread {

    private static Logger logger = LoggerFactory.getLogger(KehuaDownloadNew.class);

    /**
     *  枚举类DownloadStatus代码
     */
    public enum DownloadStatus {
        //远程文件不存在
        Remote_File_Noexist,
        //本地文件大于远程文件
        Local_Bigger_Remote,
        //断点下载文件成功
        Download_From_Break_Success,
        //断点下载文件失败
        Download_From_Break_Failed,
        //全新下载文件成功
        Download_New_Success,
        //全新下载文件失败
        Download_New_Failed;
    }

    private FTPClient ftpClient;
    private String ftpURL;
    private String username;
    private String pwd;
    private String ftpport;
    private String file1;
    private String file2;
    private String charset;
    private Boolean success=false;
    private CountDownLatch countDownLatch;
    public void setParams(String ftpURL,String username,String pwd,String ftpport,
                          String file1,String file2,String charset,CountDownLatch countDownLatch ){
        //设置将过程中使用到的命令输出到控制台
        this.ftpURL = ftpURL;
        this.username = username;
        this.pwd = pwd;
        this.ftpport = ftpport;
        this.file1 = file1;
        this.file2 = file2;
        this.charset =charset;
        this.countDownLatch=countDownLatch;
        this.ftpClient.addProtocolCommandListener(new PrintCommandListener(new PrintWriter(System.out)));
    }

    public boolean getStatus(){
        return success;
    }

    public void setFtpClient(FTPClient ftpClient) {
        this.ftpClient = ftpClient;
    }

    /**
     * 连接到FTP服务器
     * @param hostname 主机名
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return 是否连接成功
     */
    private boolean connect(String hostname, int port, String username, String password) throws IOException{
        ftpClient.connect(hostname, port);
        ftpClient.setControlEncoding(charset);
        if(FTPReply.isPositiveCompletion(ftpClient.getReplyCode())){
            if(ftpClient.login(username, password)){
//                if(FTPReply.isPositiveCompletion(ftpClient.sendCommand("OPTS UTF8", "ON"))) {
//                    ftpClient.setControlEncoding("UTF-8");
//                }
                return true;
            }
        }
        disconnect();
        return false;
    }

    /**
     * 断开连接
     */
    private void disconnect() throws IOException{
        if(ftpClient.isConnected()){
            ftpClient.disconnect();
        }
    }


    /**
     * 从FTP服务器上下载文件,支持断点续传，上传百分比汇报
     * @param remote 远程文件路径
     * @param local 本地文件路径
     * @return 上传的状态
     */
    public DownloadStatus download(String remote,String local) throws IOException{
        //设置被动模式
        ftpClient.enterLocalPassiveMode();
        //设置以二进制方式传输
        ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
        DownloadStatus result;
        //检查远程文件是否存在
        FTPFile[] files = ftpClient.listFiles(new String(remote.getBytes(charset),"iso-8859-1"));
        if(files.length != 1){
            logger.error("async download remote 科华合同接口，远程文件不存在, 远程文件名称是:{}", remote);
            return DownloadStatus.Remote_File_Noexist;
        }
        // 获取远程文件大小
        long lRemoteSize = files[0].getSize();
        File f = new File(local);
        //本地存在文件，进行断点下载
        if(f.exists()){
            long localSize = f.length();
            //判断本地文件大小是否大于远程文件大小
            if(localSize >= lRemoteSize){
                logger.error("本地文件大于远程文件，下载中止");
                return DownloadStatus.Local_Bigger_Remote;
            }
            //进行断点续传，并记录状态
            FileOutputStream out = new FileOutputStream(f,true);
            ftpClient.setRestartOffset(localSize);
            InputStream in = ftpClient.retrieveFileStream(new String(remote.getBytes(charset),"iso-8859-1"));
            byte[] bytes = new byte[1024];
            long step = lRemoteSize /100;
            long process=localSize /step;
            int c;
            while((c = in.read(bytes))!= -1){
                out.write(bytes,0,c);
                localSize+=c;
                long nowProcess = localSize /step;
                if(nowProcess > process){
                    process = nowProcess;
                    if(process % 10 == 0)
                        logger.info("下载进度："+process);
                    //TODO 更新文件下载进度,值存放在process变量中
                }
            }
            in.close();
            out.close();
            boolean isDo = ftpClient.completePendingCommand();
            if(isDo){
                result = DownloadStatus.Download_From_Break_Success;
            }else {
                result = DownloadStatus.Download_From_Break_Failed;
            }
        }else {
            OutputStream out = new FileOutputStream(f);
            InputStream in= ftpClient.retrieveFileStream(new String(remote.getBytes(charset),"iso-8859-1"));
            byte[] bytes = new byte[1024];
            long step = lRemoteSize /100;
            long process=0;
            long localSize = 0L;
            int c;
            while((c = in.read(bytes))!= -1){
                out.write(bytes, 0, c);
                localSize+=c;
                long nowProcess = localSize /step;
                if(nowProcess > process){
                    process = nowProcess;
                    if(process % 10 == 0)
                        logger.info("下载进度："+process);
                    //TODO 更新文件下载进度,值存放在process变量中
                }
            }
            in.close();
            out.close();
            boolean upNewStatus = ftpClient.completePendingCommand();
            if(upNewStatus){
                result = DownloadStatus.Download_New_Success;
            }else {
                result = DownloadStatus.Download_New_Failed;
            }
        }
        return result;
    }

    @Override
    public void run() {
        try {
//            this.connect(ftpURL, new java.lang.Integer(ftpport), username, pwd);
            // file1：远程 ftp 服务器文件地址  file2：本地服务器文件地址
            DownloadStatus result = this.download(file1, file2);
            logger.info("远程文件: {}, 本地文件:{}, 下载结果是:{}", file1, file2, result);
            switch (result){
                //远程文件不存在
                case Remote_File_Noexist:
                    this.success =false;
                    break;
                //本地文件大于远程文件
                case Local_Bigger_Remote:
                    this.success =true;
                    break;
                //断点下载文件成功
                case Download_From_Break_Success:
                    this.success =true;
                    break;
                //断点下载文件失败
                case Download_From_Break_Failed:
                    this.success =false;
                    break;
                //全新下载文件成功
                case Download_New_Success:
                    this.success =true;
                    break;
                //全新下载文件失败
                case Download_New_Failed:
                    this.success =false;
                    break;
                default:
            }
//            this.disconnect();
        } catch (IOException e) {
            logger.error("连接FTP出错：", e);
        }finally {
            countDownLatch.countDown();
        }
    }


}
