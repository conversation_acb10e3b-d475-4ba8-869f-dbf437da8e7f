package com.Kehua;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.apache.commons.net.ftp.FTPClient;

import java.io.IOException;
import java.io.OutputStream;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;


/**
 * 对于科华合同接口中几个耗时比较长的下载操作,改写为多线程
 * <AUTHOR>
 */
@Slf4j
public class KehuaAsynchronous1 {

    private CountDownLatch countDownLatch;
    private CountDownLatch countDownLatch2;


    private static ScheduledExecutorService threadPool = new ScheduledThreadPoolExecutor(5,
            new BasicThreadFactory.Builder().namingPattern("example-schedule-pool-%d").daemon(true).build());

    private static ScheduledExecutorService threadPool2 = new ScheduledThreadPoolExecutor(2,
            new BasicThreadFactory.Builder().namingPattern("example-schedule-pool-%d").daemon(true).build());


    boolean saycDownload(OutputStream output, String aimFile, CountDownLatch countDownLatch, FTPClient ftpClient){
        boolean result = true;
        FtpDownloadThread t1 = new FtpDownloadThread();
        t1.setOutput(output);
        t1.setAimFile(aimFile);
        t1.setFtpClient(ftpClient);
        try {
            this.countDownLatch =countDownLatch;
            threadPool.execute(t1);
            result = false;
        } catch (Exception e) {
            log.error("6.1 ftp kh file down error, file error_message{}", t1, e);
        }
        return result;
    }

    void shutDown(){
        threadPool.shutdown();
    }

    private class FtpDownloadThread extends Thread {
        private JSONObject input;
        private OutputStream output;
        private String aimFile;
        private FTPClient ftpClient;

        @Override
        public void run(){
            FTPClient ftp = ftpClient;
            try {
                // 连接服务器
//                ftp = KehuaFtp.ftpConcat();
                log.info("ftp download file 远程服务器上的文件名称是:{}", aimFile);
                boolean result = ftp.retrieveFile(aimFile, output);
                log.info("ftp download file 文件为:{}, 写入本地工作流的执行结果是:{}", aimFile, result);
                output.close();
                // 退出
//                ftp.logout();
                // 断开连接
//                ftp.disconnect();
            } catch (IOException e) {
                log.error("科华合同同步接口，合同文件流写入异常，异常信息是:", e);
            }finally {
                countDownLatch.countDown();
                log.info("技术器是:{}", countDownLatch.toString());
            }
        }

        public void setInput(JSONObject input){
            this.input=input;
        }

        public void setOutput(OutputStream output){
            this.output=output;
        }

        void setAimFile(String aimFile){
            this.aimFile =aimFile;
        }

        public void setFtpClient(FTPClient ftpClient) {
            this.ftpClient = ftpClient;
        }
    }


}
