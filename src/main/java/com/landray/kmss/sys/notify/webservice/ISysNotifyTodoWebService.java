package com.landray.kmss.sys.notify.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-31T01:08:01.413Z
 * Generated source version: 3.2.6
 *
 */
@WebService(targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", name = "ISysNotifyTodoWebService")
@XmlSeeAlso({ObjectFactory.class})
public interface ISysNotifyTodoWebService {

    @WebMethod
    @RequestWrapper(localName = "getTodoCount", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.GetTodoCount")
    @ResponseWrapper(localName = "getTodoCountResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.GetTodoCountResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult getTodoCount(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoGetCountContext arg0
    ) throws Exception_Exception;

    @WebMethod
    @RequestWrapper(localName = "deleteTodo", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.DeleteTodo")
    @ResponseWrapper(localName = "deleteTodoResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.DeleteTodoResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult deleteTodo(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoRemoveContext arg0
    ) throws Exception_Exception;

    @WebMethod
    @RequestWrapper(localName = "setTodoDone", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.SetTodoDone")
    @ResponseWrapper(localName = "setTodoDoneResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.SetTodoDoneResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult setTodoDone(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoRemoveContext arg0
    ) throws Exception_Exception;

    @WebMethod
    @RequestWrapper(localName = "getTodo", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.GetTodo")
    @ResponseWrapper(localName = "getTodoResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.GetTodoResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult getTodo(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoGetContext arg0
    ) throws Exception_Exception;

    @WebMethod
    @RequestWrapper(localName = "sendTodo", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.SendTodo")
    @ResponseWrapper(localName = "sendTodoResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.SendTodoResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult sendTodo(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoSendContext arg0
    ) throws Exception_Exception;

    @WebMethod
    @RequestWrapper(localName = "updateTodo", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.UpdateTodo")
    @ResponseWrapper(localName = "updateTodoResponse", targetNamespace = "http://webservice.notify.sys.kmss.landray.com/", className = "com.landray.kmss.sys.notify.webservice.UpdateTodoResponse")
    @WebResult(name = "return", targetNamespace = "")
    public com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult updateTodo(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.sys.notify.webservice.NotifyTodoUpdateContext arg0
    ) throws Exception_Exception;
}
