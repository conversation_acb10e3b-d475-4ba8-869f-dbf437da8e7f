package com.landray.kmss.sys.notify.webservice;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-31T01:08:01.437Z
 * Generated source version: 3.2.6
 *
 */
@WebServiceClient(name = "ISysNotifyTodoWebServiceService",
                  wsdlLocation = "http://10.10.6.166/sys/webservice/sysNotifyTodoWebService?wsdl",
                  targetNamespace = "http://webservice.notify.sys.kmss.landray.com/")
public class ISysNotifyTodoWebServiceService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://webservice.notify.sys.kmss.landray.com/", "ISysNotifyTodoWebServiceService");
    public final static QName ISysNotifyTodoWebServicePort = new QName("http://webservice.notify.sys.kmss.landray.com/", "ISysNotifyTodoWebServicePort");
    static {
        URL url = null;
        try {
            url = new URL("http://10.10.6.166/sys/webservice/sysNotifyTodoWebService?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ISysNotifyTodoWebServiceService.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "http://10.10.6.166/sys/webservice/sysNotifyTodoWebService?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ISysNotifyTodoWebServiceService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ISysNotifyTodoWebServiceService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ISysNotifyTodoWebServiceService() {
        super(WSDL_LOCATION, SERVICE);
    }

    public ISysNotifyTodoWebServiceService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ISysNotifyTodoWebServiceService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ISysNotifyTodoWebServiceService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns ISysNotifyTodoWebService
     */
    @WebEndpoint(name = "ISysNotifyTodoWebServicePort")
    public ISysNotifyTodoWebService getISysNotifyTodoWebServicePort() {
        return super.getPort(ISysNotifyTodoWebServicePort, ISysNotifyTodoWebService.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ISysNotifyTodoWebService
     */
    @WebEndpoint(name = "ISysNotifyTodoWebServicePort")
    public ISysNotifyTodoWebService getISysNotifyTodoWebServicePort(WebServiceFeature... features) {
        return super.getPort(ISysNotifyTodoWebServicePort, ISysNotifyTodoWebService.class, features);
    }

}
