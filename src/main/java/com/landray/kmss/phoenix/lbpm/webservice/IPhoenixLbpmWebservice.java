package com.landray.kmss.phoenix.lbpm.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-29T06:01:01.284Z
 * Generated source version: 3.2.6
 *
 */
@WebService(targetNamespace = "http://webservice.lbpm.phoenix.kmss.landray.com/", name = "IPhoenixLbpmWebservice")
@XmlSeeAlso({ObjectFactory.class})
public interface IPhoenixLbpmWebservice {

    @WebMethod
    @RequestWrapper(localName = "approve", targetNamespace = "http://webservice.lbpm.phoenix.kmss.landray.com/", className = "com.landray.kmss.phoenix.lbpm.webservice.Approve")
    @ResponseWrapper(localName = "approveResponse", targetNamespace = "http://webservice.lbpm.phoenix.kmss.landray.com/", className = "com.landray.kmss.phoenix.lbpm.webservice.ApproveResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String approve(
        @WebParam(name = "approveObject", targetNamespace = "")
        com.landray.kmss.phoenix.lbpm.webservice.ApproveObject approveObject
    );
}
