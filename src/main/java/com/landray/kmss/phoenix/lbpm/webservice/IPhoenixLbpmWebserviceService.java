package com.landray.kmss.phoenix.lbpm.webservice;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-29T06:01:01.342Z
 * Generated source version: 3.2.6
 *
 */
@WebServiceClient(name = "IPhoenixLbpmWebserviceService",
                  wsdlLocation = "http://***********/sys/webservice/phoenixLbpmWebservice?wsdl",
                  targetNamespace = "http://webservice.lbpm.phoenix.kmss.landray.com/")
public class IPhoenixLbpmWebserviceService extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://webservice.lbpm.phoenix.kmss.landray.com/", "IPhoenixLbpmWebserviceService");
    public final static QName IPhoenixLbpmWebservicePort = new QName("http://webservice.lbpm.phoenix.kmss.landray.com/", "IPhoenixLbpmWebservicePort");
    static {
        URL url = null;
        try {
            url = new URL("http://***********/sys/webservice/phoenixLbpmWebservice?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(IPhoenixLbpmWebserviceService.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "http://***********/sys/webservice/phoenixLbpmWebservice?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public IPhoenixLbpmWebserviceService(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public IPhoenixLbpmWebserviceService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public IPhoenixLbpmWebserviceService() {
        super(WSDL_LOCATION, SERVICE);
    }

    public IPhoenixLbpmWebserviceService(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public IPhoenixLbpmWebserviceService(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public IPhoenixLbpmWebserviceService(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns IPhoenixLbpmWebservice
     */
    @WebEndpoint(name = "IPhoenixLbpmWebservicePort")
    public IPhoenixLbpmWebservice getIPhoenixLbpmWebservicePort() {
        return super.getPort(IPhoenixLbpmWebservicePort, IPhoenixLbpmWebservice.class);
    }

    /**
     *
     * @param features
     *     A list of {@link javax.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns IPhoenixLbpmWebservice
     */
    @WebEndpoint(name = "IPhoenixLbpmWebservicePort")
    public IPhoenixLbpmWebservice getIPhoenixLbpmWebservicePort(WebServiceFeature... features) {
        return super.getPort(IPhoenixLbpmWebservicePort, IPhoenixLbpmWebservice.class, features);
    }

}
