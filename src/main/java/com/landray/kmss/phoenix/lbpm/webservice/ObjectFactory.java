
package com.landray.kmss.phoenix.lbpm.webservice;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.landray.kmss.phoenix.lbpm.webservice package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Approve_QNAME = new QName("http://webservice.lbpm.phoenix.kmss.landray.com/", "approve");
    private final static QName _ApproveResponse_QNAME = new QName("http://webservice.lbpm.phoenix.kmss.landray.com/", "approveResponse");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.landray.kmss.phoenix.lbpm.webservice
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Approve }
     * 
     */
    public Approve createApprove() {
        return new Approve();
    }

    /**
     * Create an instance of {@link ApproveResponse }
     * 
     */
    public ApproveResponse createApproveResponse() {
        return new ApproveResponse();
    }

    /**
     * Create an instance of {@link ApproveObject }
     * 
     */
    public ApproveObject createApproveObject() {
        return new ApproveObject();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Approve }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.lbpm.phoenix.kmss.landray.com/", name = "approve")
    public JAXBElement<Approve> createApprove(Approve value) {
        return new JAXBElement<Approve>(_Approve_QNAME, Approve.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link ApproveResponse }{@code >}}
     * 
     */
    @XmlElementDecl(namespace = "http://webservice.lbpm.phoenix.kmss.landray.com/", name = "approveResponse")
    public JAXBElement<ApproveResponse> createApproveResponse(ApproveResponse value) {
        return new JAXBElement<ApproveResponse>(_ApproveResponse_QNAME, ApproveResponse.class, null, value);
    }

}
