
package com.landray.kmss.phoenix.lbpm.webservice;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for approve complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="approve"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="approveObject" type="{http://webservice.lbpm.phoenix.kmss.landray.com/}approveObject" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "approve", propOrder = {
    "approveObject"
})
public class Approve {

    protected ApproveObject approveObject;

    /**
     * Gets the value of the approveObject property.
     * 
     * @return
     *     possible object is
     *     {@link ApproveObject }
     *     
     */
    public ApproveObject getApproveObject() {
        return approveObject;
    }

    /**
     * Sets the value of the approveObject property.
     * 
     * @param value
     *     allowed object is
     *     {@link ApproveObject }
     *     
     */
    public void setApproveObject(ApproveObject value) {
        this.approveObject = value;
    }

}
