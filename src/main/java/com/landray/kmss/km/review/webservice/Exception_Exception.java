
package com.landray.kmss.km.review.webservice;

import javax.xml.ws.WebFault;


/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-30T01:16:23.113Z
 * Generated source version: 3.2.6
 */

@WebFault(name = "Exception", targetNamespace = "http://webservice.review.km.kmss.landray.com/")
public class Exception_Exception extends java.lang.Exception {

    private com.landray.kmss.km.review.webservice.Exception exception;

    public Exception_Exception() {
        super();
    }

    public Exception_Exception(String message) {
        super(message);
    }

    public Exception_Exception(String message, java.lang.Throwable cause) {
        super(message, cause);
    }

    public Exception_Exception(String message, com.landray.kmss.km.review.webservice.Exception exception) {
        super(message);
        this.exception = exception;
    }

    public Exception_Exception(String message, com.landray.kmss.km.review.webservice.Exception exception, java.lang.Throwable cause) {
        super(message, cause);
        this.exception = exception;
    }

    public com.landray.kmss.km.review.webservice.Exception getFaultInfo() {
        return this.exception;
    }
}
