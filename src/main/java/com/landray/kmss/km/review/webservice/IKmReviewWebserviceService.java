package com.landray.kmss.km.review.webservice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.2.6
 * 2018-10-30T01:16:23.157Z
 * Generated source version: 3.2.6
 *
 */
@WebService(targetNamespace = "http://webservice.review.km.kmss.landray.com/", name = "IKmReviewWebserviceService")
@XmlSeeAlso({ObjectFactory.class})
public interface IKmReviewWebserviceService {

    @WebMethod
    @RequestWrapper(localName = "addReview", targetNamespace = "http://webservice.review.km.kmss.landray.com/", className = "com.landray.kmss.km.review.webservice.AddReview")
    @ResponseWrapper(localName = "addReviewResponse", targetNamespace = "http://webservice.review.km.kmss.landray.com/", className = "com.landray.kmss.km.review.webservice.AddReviewResponse")
    @WebResult(name = "return", targetNamespace = "")
    public java.lang.String addReview(
        @WebParam(name = "arg0", targetNamespace = "")
        com.landray.kmss.km.review.webservice.KmReviewParamterForm arg0
    ) throws Exception_Exception;
}
