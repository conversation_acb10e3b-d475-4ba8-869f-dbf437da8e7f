package com.cloudpense.expman.dataSource;

import org.springframework.util.StringUtils;

/**
 * Created by futurefall on 2016/12/20.
 */
public class CustomerContextHolder {

    public static final String A = "cpprd";
    public static final String B = "cpuae";
    public static final String C = "cprmy";
    public static final String D = "cpdcj";
    public static final String E = "cppxc";
    public static final String F = "cphsf";
    public static final String NPP = "cpnpp";
    public static final String WHX = "cpwhx";
    public static final String CJLR = "cpjrc";
    public static final String CFG = "cpcfg";
    public static final String LGG = "cplgg";
    public static final String EVC = "cpevc";
    public static final String SSF = "cpssf";
    public static final String SHW = "cpshw";
    public static final String CEA = "cpcea";
    public static final String CPKHB = "cpkhb";
    public static final String CPHRSTW = "cphrstw";
    public static final String CPPRD03 = "cpprd03";

    //用ThreadLocal来设置当前线程使用哪个dataSource
    private static final ThreadLocal<String> contextHolder = new ThreadLocal<String>();

    public static void setCustomerType(String customerType) {
        contextHolder.set(customerType);
    }

    public static String getCustomerType() {
        return contextHolder.get();
    }

    public static void clearCustomerType() {
        contextHolder.remove();
    }

}
