package com.cloudpense.expman.webService.cjlr;

import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.webService.cjlr.entity.EmployeeEntity;
import com.cloudpense.expman.webService.phoenix.ForPhoenixResponse;
import com.fasterxml.jackson.core.JsonProcessingException;

import javax.jws.WebParam;
import javax.jws.WebService;

@WebService
public interface EmployeeData {
    /**
     *
     * @param employeeEntity
     * @return 返回报错信息
     * @throws ValidationException
     */
    public ForPhoenixResponse employeeData(@WebParam(name = "EmployeeEntity") EmployeeEntity employeeEntity) throws ValidationException, JsonProcessingException;
}
