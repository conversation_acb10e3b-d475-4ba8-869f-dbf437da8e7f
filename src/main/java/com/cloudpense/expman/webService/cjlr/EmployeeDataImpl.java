package com.cloudpense.expman.webService.cjlr;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.CJLRMapper;
import com.cloudpense.expman.util.TranslationUtil;
import com.cloudpense.expman.webService.cjlr.entity.EmployeeEntity;
import com.cloudpense.expman.webService.phoenix.ForPhoenixResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jws.WebMethod;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public class EmployeeDataImpl implements EmployeeData {
    private static final Logger logger = LoggerFactory.getLogger(EmployeeDataImpl.class);
    @Autowired
    private CJLRMapper cjlrMapper;
    @Autowired
    private TranslationUtil translationUtil;
    private static final String ERROR ="E";

    @Override
    @WebMethod
    public ForPhoenixResponse employeeData(EmployeeEntity employeeEntity) throws ValidationException, JsonProcessingException {
        logger.info("CJLR_EMPLOYEE_DATA==>"+ JSONObject.toJSONString(employeeEntity));
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString="";
        jsonString =objectMapper.writeValueAsString(employeeEntity);
        FndQuery fndQuery = new FndQuery();
        fndQuery.setInput(jsonString);
        logger.info("jsonString==>"+ jsonString);
        fndQuery.setType("EmployeeData");
        fndQuery.setCompanyId(14968);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CJLR);
        cjlrMapper.cheryJLRUpdateData(fndQuery);
        cjlrMapper.normalInsertLog(14968,"CJLR_EmployeeData_logs",jsonString);
        String resultCode ;
        String resultMessage ;
        ForPhoenixResponse forPhoenixResponse=new ForPhoenixResponse();
        if (ERROR.equals(fndQuery.getReturnCode())) {
            resultCode = ERROR;
            resultMessage =  translationUtil.getMessage(resultCode,fndQuery.getReturnMessage(),"zh_CN");
        } else {
            resultCode = "S";
            resultMessage = fndQuery.getReturnMessage();
        }
        forPhoenixResponse.RESPONSE_STATUS = resultCode;
        forPhoenixResponse.RESPONSE_MESSAGE =resultMessage;
        return forPhoenixResponse;
    }
}
