/**
 * FeeMasterDataServiceStub.java
 * <p>
 * This file was auto-generated from WSDL
 * by the Apache Axis2 version: 1.7.9  Built on : Nov 16, 2018 (12:05:37 GMT)
 */
package com.cloudpense.expman.webService.nippon;


/*
 *  FeeMasterDataServiceStub java implementation
 */
public class BudgetStub extends org.apache.axis2.client.Stub {
    private static int counter = 0;
    protected org.apache.axis2.description.AxisOperation[] _operations;

    //hashmaps to keep the fault mapping
    private java.util.HashMap faultExceptionNameMap = new java.util.HashMap();
    private java.util.HashMap faultExceptionClassNameMap = new java.util.HashMap();
    private java.util.HashMap faultMessageMap = new java.util.HashMap();
    private javax.xml.namespace.QName[] opNameArray = null;

    /**
     * Constructor that takes in a configContext
     */
    public BudgetStub(
            org.apache.axis2.context.ConfigurationContext configurationContext,
            String targetEndpoint) throws org.apache.axis2.AxisFault {
        this(configurationContext, targetEndpoint, false);
    }

    /**
     * Constructor that takes in a configContext  and useseperate listner
     */
    public BudgetStub(
            org.apache.axis2.context.ConfigurationContext configurationContext,
            String targetEndpoint, boolean useSeparateListener)
            throws org.apache.axis2.AxisFault {
        //To populate AxisService
        populateAxisService();
        populateFaults();

        _serviceClient = new org.apache.axis2.client.ServiceClient(configurationContext,
                _service);

        _serviceClient.getOptions()
                .setTo(new org.apache.axis2.addressing.EndpointReference(
                        targetEndpoint));
        _serviceClient.getOptions().setUseSeparateListener(useSeparateListener);
    }

    /**
     * Default Constructor
     */
    public BudgetStub(
            org.apache.axis2.context.ConfigurationContext configurationContext)
            throws org.apache.axis2.AxisFault {
        this(configurationContext,
                "http://notesapp4.nipponpaint.com.cn:80/bunsha/TU/application/EnginOrderApproval.nsf/FeeMasterData?OpenWebService");
    }

    /**
     * Default Constructor
     */
    public BudgetStub() throws org.apache.axis2.AxisFault {
        this(
                "http://notesapp4.nipponpaint.com.cn:80/bunsha/TU/application/EnginOrderApproval.nsf/FeeMasterData?OpenWebService");
    }

    /**
     * Constructor taking the target endpoint
     */
    public BudgetStub(String targetEndpoint)
            throws org.apache.axis2.AxisFault {
        this(null, targetEndpoint);
    }

    private static synchronized String getUniqueSuffix() {
        // reset the counter if it is greater than 99999
        if (counter > 99999) {
            counter = 0;
        }

        counter = counter + 1;

        return Long.toString(System.currentTimeMillis()) +
                "_" + counter;
    }

    private void populateAxisService() throws org.apache.axis2.AxisFault {
        //creating the Service with a unique name
        _service = new org.apache.axis2.description.AxisService(
                "FeeMasterDataService" + getUniqueSuffix());
        addAnonymousOperations();

        //creating the operations
        org.apache.axis2.description.AxisOperation __operation;

        _operations = new org.apache.axis2.description.AxisOperation[2];

        __operation = new org.apache.axis2.description.OutInAxisOperation();

        __operation.setName(new javax.xml.namespace.QName(
                "urn:DefaultNamespace", "gETBUDGETLIST"));
        _service.addOperation(__operation);

        _operations[0] = __operation;

        __operation = new org.apache.axis2.description.OutInAxisOperation();

        __operation.setName(new javax.xml.namespace.QName(
                "urn:DefaultNamespace", "gETLIST"));
        _service.addOperation(__operation);

        _operations[1] = __operation;
    }

    //populates the faults
    private void populateFaults() {
    }

    /**
     * Auto generated method signature
     *
     * @param sTRYEAR
     * @see com.cloudpense.expman.webService.nippon.FeeMasterDataService#gETBUDGETLIST
     */
    public GETBUDGETLISTReturn gETBUDGETLIST(
            STRYEAR sTRYEAR)
            throws java.rmi.RemoteException {
        org.apache.axis2.context.MessageContext _messageContext = new org.apache.axis2.context.MessageContext();

        try {
            org.apache.axis2.client.OperationClient _operationClient = _serviceClient.createClient(_operations[0].getName());
            _operationClient.getOptions().setAction("GETBUDGETLIST");
            _operationClient.getOptions().setExceptionToBeThrownOnSOAPFault(true);

            addPropertyToOperationClient(_operationClient,
                    org.apache.axis2.description.WSDL2Constants.ATTR_WHTTP_QUERY_PARAMETER_SEPARATOR,
                    "&");

            // create SOAP envelope with that payload
            org.apache.axiom.soap.SOAPEnvelope env = null;

            env = toEnvelope(getFactory(_operationClient.getOptions()
                            .getSoapVersionURI()),
                    sTRYEAR,
                    optimizeContent(
                            new javax.xml.namespace.QName("urn:DefaultNamespace",
                                    "gETBUDGETLIST")),
                    new javax.xml.namespace.QName("urn:DefaultNamespace",
                            "STRYEAR"));

            //adding SOAP soap_headers
            _serviceClient.addHeadersToEnvelope(env);
            // set the message context with that soap envelope
            _messageContext.setEnvelope(env);

            // add the message contxt to the operation client
            _operationClient.addMessageContext(_messageContext);

            //execute the operation client
            _operationClient.execute(true);

            org.apache.axis2.context.MessageContext _returnMessageContext = _operationClient.getMessageContext(org.apache.axis2.wsdl.WSDLConstants.MESSAGE_LABEL_IN_VALUE);
            org.apache.axiom.soap.SOAPEnvelope _returnEnv = _returnMessageContext.getEnvelope();

            Object object = fromOM(_returnEnv.getBody()
                            .getFirstElement(),
                    GETBUDGETLISTReturn.class);

            return (GETBUDGETLISTReturn) object;
        } catch (org.apache.axis2.AxisFault f) {
            org.apache.axiom.om.OMElement faultElt = f.getDetail();

            if (faultElt != null) {
                if (faultExceptionNameMap.containsKey(
                        new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETBUDGETLIST"))) {
                    //make the fault by reflection
                    try {
                        String exceptionClassName = (String) faultExceptionClassNameMap.get(new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETBUDGETLIST"));
                        Class exceptionClass = Class.forName(exceptionClassName);
                        java.lang.reflect.Constructor constructor = exceptionClass.getConstructor(String.class);
                        Exception ex = (Exception) constructor.newInstance(f.getMessage());

                        //message class
                        String messageClassName = (String) faultMessageMap.get(new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETBUDGETLIST"));
                        Class messageClass = Class.forName(messageClassName);
                        Object messageObject = fromOM(faultElt,
                                messageClass);
                        java.lang.reflect.Method m = exceptionClass.getMethod("setFaultMessage",
                                new Class[]{messageClass});
                        m.invoke(ex, new Object[]{messageObject});

                        throw new java.rmi.RemoteException(ex.getMessage(), ex);
                    } catch (ClassCastException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (ClassNotFoundException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (NoSuchMethodException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (java.lang.reflect.InvocationTargetException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (IllegalAccessException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (InstantiationException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    }
                } else {
                    throw f;
                }
            } else {
                throw f;
            }
        } finally {
            if (_messageContext.getTransportOut() != null) {
                _messageContext.getTransportOut().getSender()
                        .cleanup(_messageContext);
            }
        }
    }

    /**
     * Auto generated method signature
     *
     * @param sTRYEARMONTH
     * @see com.cloudpense.expman.webService.nippon.FeeMasterDataService#gETLIST
     */
    public GETLISTReturn gETLIST(
            STRYEARMONTH sTRYEARMONTH)
            throws java.rmi.RemoteException {
        org.apache.axis2.context.MessageContext _messageContext = new org.apache.axis2.context.MessageContext();

        try {
            org.apache.axis2.client.OperationClient _operationClient = _serviceClient.createClient(_operations[1].getName());
            _operationClient.getOptions().setAction("GETLIST");
            _operationClient.getOptions().setExceptionToBeThrownOnSOAPFault(true);

            addPropertyToOperationClient(_operationClient,
                    org.apache.axis2.description.WSDL2Constants.ATTR_WHTTP_QUERY_PARAMETER_SEPARATOR,
                    "&");

            // create SOAP envelope with that payload
            org.apache.axiom.soap.SOAPEnvelope env = null;

            env = toEnvelope(getFactory(_operationClient.getOptions()
                            .getSoapVersionURI()),
                    sTRYEARMONTH,
                    optimizeContent(
                            new javax.xml.namespace.QName("urn:DefaultNamespace",
                                    "gETLIST")),
                    new javax.xml.namespace.QName("urn:DefaultNamespace",
                            "STRYEARMONTH"));

            //adding SOAP soap_headers
            _serviceClient.addHeadersToEnvelope(env);
            // set the message context with that soap envelope
            _messageContext.setEnvelope(env);

            // add the message contxt to the operation client
            _operationClient.addMessageContext(_messageContext);

            //execute the operation client
            _operationClient.execute(true);

            org.apache.axis2.context.MessageContext _returnMessageContext = _operationClient.getMessageContext(org.apache.axis2.wsdl.WSDLConstants.MESSAGE_LABEL_IN_VALUE);
            org.apache.axiom.soap.SOAPEnvelope _returnEnv = _returnMessageContext.getEnvelope();

            Object object = fromOM(_returnEnv.getBody()
                            .getFirstElement(),
                    GETLISTReturn.class);

            return (GETLISTReturn) object;
        } catch (org.apache.axis2.AxisFault f) {
            org.apache.axiom.om.OMElement faultElt = f.getDetail();

            if (faultElt != null) {
                if (faultExceptionNameMap.containsKey(
                        new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETLIST"))) {
                    //make the fault by reflection
                    try {
                        String exceptionClassName = (String) faultExceptionClassNameMap.get(new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETLIST"));
                        Class exceptionClass = Class.forName(exceptionClassName);
                        java.lang.reflect.Constructor constructor = exceptionClass.getConstructor(String.class);
                        Exception ex = (Exception) constructor.newInstance(f.getMessage());

                        //message class
                        String messageClassName = (String) faultMessageMap.get(new org.apache.axis2.client.FaultMapKey(
                                faultElt.getQName(), "GETLIST"));
                        Class messageClass = Class.forName(messageClassName);
                        Object messageObject = fromOM(faultElt,
                                messageClass);
                        java.lang.reflect.Method m = exceptionClass.getMethod("setFaultMessage",
                                new Class[]{messageClass});
                        m.invoke(ex, new Object[]{messageObject});

                        throw new java.rmi.RemoteException(ex.getMessage(), ex);
                    } catch (ClassCastException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (ClassNotFoundException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (NoSuchMethodException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (java.lang.reflect.InvocationTargetException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (IllegalAccessException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    } catch (InstantiationException e) {
                        // we cannot intantiate the class - throw the original Axis fault
                        throw f;
                    }
                } else {
                    throw f;
                }
            } else {
                throw f;
            }
        } finally {
            if (_messageContext.getTransportOut() != null) {
                _messageContext.getTransportOut().getSender()
                        .cleanup(_messageContext);
            }
        }
    }

    private boolean optimizeContent(javax.xml.namespace.QName opName) {
        if (opNameArray == null) {
            return false;
        }

        for (int i = 0; i < opNameArray.length; i++) {
            if (opName.equals(opNameArray[i])) {
                return true;
            }
        }

        return false;
    }

    private org.apache.axiom.om.OMElement toOM(
            STRYEAR param,
            boolean optimizeContent) throws org.apache.axis2.AxisFault {
        try {
            return param.getOMElement(STRYEAR.MY_QNAME,
                    org.apache.axiom.om.OMAbstractFactory.getOMFactory());
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    private org.apache.axiom.om.OMElement toOM(
            GETBUDGETLISTReturn param,
            boolean optimizeContent) throws org.apache.axis2.AxisFault {
        try {
            return param.getOMElement(GETBUDGETLISTReturn.MY_QNAME,
                    org.apache.axiom.om.OMAbstractFactory.getOMFactory());
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    private org.apache.axiom.om.OMElement toOM(
            STRYEARMONTH param,
            boolean optimizeContent) throws org.apache.axis2.AxisFault {
        try {
            return param.getOMElement(STRYEARMONTH.MY_QNAME,
                    org.apache.axiom.om.OMAbstractFactory.getOMFactory());
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    private org.apache.axiom.om.OMElement toOM(
            GETLISTReturn param,
            boolean optimizeContent) throws org.apache.axis2.AxisFault {
        try {
            return param.getOMElement(GETLISTReturn.MY_QNAME,
                    org.apache.axiom.om.OMAbstractFactory.getOMFactory());
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    private org.apache.axiom.soap.SOAPEnvelope toEnvelope(
            org.apache.axiom.soap.SOAPFactory factory,
            STRYEAR param,
            boolean optimizeContent, javax.xml.namespace.QName elementQName)
            throws org.apache.axis2.AxisFault {
        try {
            org.apache.axiom.soap.SOAPEnvelope emptyEnvelope = factory.getDefaultEnvelope();
            emptyEnvelope.getBody()
                    .addChild(param.getOMElement(
                            STRYEAR.MY_QNAME,
                            factory));

            return emptyEnvelope;
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    /* methods to provide back word compatibility */
    private org.apache.axiom.soap.SOAPEnvelope toEnvelope(
            org.apache.axiom.soap.SOAPFactory factory,
            STRYEARMONTH param,
            boolean optimizeContent, javax.xml.namespace.QName elementQName)
            throws org.apache.axis2.AxisFault {
        try {
            org.apache.axiom.soap.SOAPEnvelope emptyEnvelope = factory.getDefaultEnvelope();
            emptyEnvelope.getBody()
                    .addChild(param.getOMElement(
                            STRYEARMONTH.MY_QNAME,
                            factory));

            return emptyEnvelope;
        } catch (org.apache.axis2.databinding.ADBException e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }
    }

    /* methods to provide back word compatibility */

    /**
     * get the default envelope
     */
    private org.apache.axiom.soap.SOAPEnvelope toEnvelope(
            org.apache.axiom.soap.SOAPFactory factory) {
        return factory.getDefaultEnvelope();
    }

    private Object fromOM(org.apache.axiom.om.OMElement param,
                          Class type) throws org.apache.axis2.AxisFault {
        try {
            if (GETBUDGETLISTReturn.class.equals(
                    type)) {
                return GETBUDGETLISTReturn.Factory.parse(param.getXMLStreamReaderWithoutCaching());
            }

            if (GETLISTReturn.class.equals(
                    type)) {
                return GETLISTReturn.Factory.parse(param.getXMLStreamReaderWithoutCaching());
            }

            if (STRYEAR.class.equals(
                    type)) {
                return STRYEAR.Factory.parse(param.getXMLStreamReaderWithoutCaching());
            }

            if (STRYEARMONTH.class.equals(
                    type)) {
                return STRYEARMONTH.Factory.parse(param.getXMLStreamReaderWithoutCaching());
            }
        } catch (Exception e) {
            throw org.apache.axis2.AxisFault.makeFault(e);
        }

        return null;
    }

    //http://notesdev.nipponpaint.com.cn:80/bunsha/TU/application/EnginOrderApproval.nsf/FeeMasterData?OpenWebService
    public static class CLASS_BUDGETLIST implements org.apache.axis2.databinding.ADBBean {
        /* This type was generated from the piece of schema that had
           name = CLASS_BUDGETLIST
           Namespace URI = urn:DefaultNamespace
           Namespace Prefix = ns1
         */

        /**
         * field for RESULT
         */
        protected String localRESULT;

        /**
         * field for MSG
         */
        protected String localMSG;

        /**
         * field for YEAR_LIST
         * This was an Array!
         */
        protected String[] localYEAR_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localYEAR_LISTTracker = false;

        /**
         * field for MONTH_LIST
         * This was an Array!
         */
        protected String[] localMONTH_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localMONTH_LISTTracker = false;

        /**
         * field for COSTCENTERCODE_LIST
         * This was an Array!
         */
        protected String[] localCOSTCENTERCODE_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localCOSTCENTERCODE_LISTTracker = false;

        /**
         * field for COSTCENTERNAME_LIST
         * This was an Array!
         */
        protected String[] localCOSTCENTERNAME_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localCOSTCENTERNAME_LISTTracker = false;

        /**
         * field for REGION_LIST
         * This was an Array!
         */
        protected String[] localREGION_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localREGION_LISTTracker = false;

        /**
         * field for BUDGET_LIST
         * This was an Array!
         */
        protected String[] localBUDGET_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localBUDGET_LISTTracker = false;

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getRESULT() {
            return localRESULT;
        }

        /**
         * Auto generated setter method
         *
         * @param param RESULT
         */
        public void setRESULT(String param) {
            this.localRESULT = param;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getMSG() {
            return localMSG;
        }

        /**
         * Auto generated setter method
         *
         * @param param MSG
         */
        public void setMSG(String param) {
            this.localMSG = param;
        }

        public boolean isYEAR_LISTSpecified() {
            return localYEAR_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getYEAR_LIST() {
            return localYEAR_LIST;
        }

        /**
         * validate the array for YEAR_LIST
         */
        protected void validateYEAR_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param YEAR_LIST
         */
        public void setYEAR_LIST(String[] param) {
            validateYEAR_LIST(param);

            localYEAR_LISTTracker = param != null;

            this.localYEAR_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addYEAR_LIST(String param) {
            if (localYEAR_LIST == null) {
                localYEAR_LIST = new String[]{};
            }

            //update the setting tracker
            localYEAR_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localYEAR_LIST);
            list.add(param);
            this.localYEAR_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isMONTH_LISTSpecified() {
            return localMONTH_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getMONTH_LIST() {
            return localMONTH_LIST;
        }

        /**
         * validate the array for MONTH_LIST
         */
        protected void validateMONTH_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param MONTH_LIST
         */
        public void setMONTH_LIST(String[] param) {
            validateMONTH_LIST(param);

            localMONTH_LISTTracker = param != null;

            this.localMONTH_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addMONTH_LIST(String param) {
            if (localMONTH_LIST == null) {
                localMONTH_LIST = new String[]{};
            }

            //update the setting tracker
            localMONTH_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localMONTH_LIST);
            list.add(param);
            this.localMONTH_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isCOSTCENTERCODE_LISTSpecified() {
            return localCOSTCENTERCODE_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getCOSTCENTERCODE_LIST() {
            return localCOSTCENTERCODE_LIST;
        }

        /**
         * validate the array for COSTCENTERCODE_LIST
         */
        protected void validateCOSTCENTERCODE_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param COSTCENTERCODE_LIST
         */
        public void setCOSTCENTERCODE_LIST(String[] param) {
            validateCOSTCENTERCODE_LIST(param);

            localCOSTCENTERCODE_LISTTracker = param != null;

            this.localCOSTCENTERCODE_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addCOSTCENTERCODE_LIST(String param) {
            if (localCOSTCENTERCODE_LIST == null) {
                localCOSTCENTERCODE_LIST = new String[]{};
            }

            //update the setting tracker
            localCOSTCENTERCODE_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localCOSTCENTERCODE_LIST);
            list.add(param);
            this.localCOSTCENTERCODE_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isCOSTCENTERNAME_LISTSpecified() {
            return localCOSTCENTERNAME_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getCOSTCENTERNAME_LIST() {
            return localCOSTCENTERNAME_LIST;
        }

        /**
         * validate the array for COSTCENTERNAME_LIST
         */
        protected void validateCOSTCENTERNAME_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param COSTCENTERNAME_LIST
         */
        public void setCOSTCENTERNAME_LIST(String[] param) {
            validateCOSTCENTERNAME_LIST(param);

            localCOSTCENTERNAME_LISTTracker = param != null;

            this.localCOSTCENTERNAME_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addCOSTCENTERNAME_LIST(String param) {
            if (localCOSTCENTERNAME_LIST == null) {
                localCOSTCENTERNAME_LIST = new String[]{};
            }

            //update the setting tracker
            localCOSTCENTERNAME_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localCOSTCENTERNAME_LIST);
            list.add(param);
            this.localCOSTCENTERNAME_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isREGION_LISTSpecified() {
            return localREGION_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getREGION_LIST() {
            return localREGION_LIST;
        }

        /**
         * validate the array for REGION_LIST
         */
        protected void validateREGION_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param REGION_LIST
         */
        public void setREGION_LIST(String[] param) {
            validateREGION_LIST(param);

            localREGION_LISTTracker = param != null;

            this.localREGION_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addREGION_LIST(String param) {
            if (localREGION_LIST == null) {
                localREGION_LIST = new String[]{};
            }

            //update the setting tracker
            localREGION_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localREGION_LIST);
            list.add(param);
            this.localREGION_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isBUDGET_LISTSpecified() {
            return localBUDGET_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getBUDGET_LIST() {
            return localBUDGET_LIST;
        }

        /**
         * validate the array for BUDGET_LIST
         */
        protected void validateBUDGET_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param BUDGET_LIST
         */
        public void setBUDGET_LIST(String[] param) {
            validateBUDGET_LIST(param);

            localBUDGET_LISTTracker = param != null;

            this.localBUDGET_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addBUDGET_LIST(String param) {
            if (localBUDGET_LIST == null) {
                localBUDGET_LIST = new String[]{};
            }

            //update the setting tracker
            localBUDGET_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localBUDGET_LIST);
            list.add(param);
            this.localBUDGET_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, parentQName));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            String prefix = null;
            String namespace = null;

            prefix = parentQName.getPrefix();
            namespace = parentQName.getNamespaceURI();
            writeStartElement(prefix, namespace, parentQName.getLocalPart(),
                    xmlWriter);

            if (serializeType) {
                String namespacePrefix = registerPrefix(xmlWriter,
                        "urn:DefaultNamespace");

                if ((namespacePrefix != null) &&
                        (namespacePrefix.trim().length() > 0)) {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            namespacePrefix + ":CLASS_BUDGETLIST", xmlWriter);
                } else {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            "CLASS_BUDGETLIST", xmlWriter);
                }
            }

            namespace = "";
            writeStartElement(null, namespace, "RESULT", xmlWriter);

            if (localRESULT == null) {
                // write the nil attribute
                throw new org.apache.axis2.databinding.ADBException(
                        "RESULT cannot be null!!");
            } else {
                xmlWriter.writeCharacters(localRESULT);
            }

            xmlWriter.writeEndElement();

            namespace = "";
            writeStartElement(null, namespace, "MSG", xmlWriter);

            if (localMSG == null) {
                // write the nil attribute
                throw new org.apache.axis2.databinding.ADBException(
                        "MSG cannot be null!!");
            } else {
                xmlWriter.writeCharacters(localMSG);
            }

            xmlWriter.writeEndElement();

            if (localYEAR_LISTTracker) {
                if (localYEAR_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localYEAR_LIST.length; i++) {
                        if (localYEAR_LIST[i] != null) {
                            writeStartElement(null, namespace, "YEAR_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localYEAR_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "YEAR_LIST cannot be null!!");
                }
            }

            if (localMONTH_LISTTracker) {
                if (localMONTH_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localMONTH_LIST.length; i++) {
                        if (localMONTH_LIST[i] != null) {
                            writeStartElement(null, namespace, "MONTH_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localMONTH_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "MONTH_LIST cannot be null!!");
                }
            }

            if (localCOSTCENTERCODE_LISTTracker) {
                if (localCOSTCENTERCODE_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localCOSTCENTERCODE_LIST.length; i++) {
                        if (localCOSTCENTERCODE_LIST[i] != null) {
                            writeStartElement(null, namespace,
                                    "COSTCENTERCODE_LIST", xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localCOSTCENTERCODE_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "COSTCENTERCODE_LIST cannot be null!!");
                }
            }

            if (localCOSTCENTERNAME_LISTTracker) {
                if (localCOSTCENTERNAME_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localCOSTCENTERNAME_LIST.length; i++) {
                        if (localCOSTCENTERNAME_LIST[i] != null) {
                            writeStartElement(null, namespace,
                                    "COSTCENTERNAME_LIST", xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localCOSTCENTERNAME_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "COSTCENTERNAME_LIST cannot be null!!");
                }
            }

            if (localREGION_LISTTracker) {
                if (localREGION_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localREGION_LIST.length; i++) {
                        if (localREGION_LIST[i] != null) {
                            writeStartElement(null, namespace, "REGION_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localREGION_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "REGION_LIST cannot be null!!");
                }
            }

            if (localBUDGET_LISTTracker) {
                if (localBUDGET_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localBUDGET_LIST.length; i++) {
                        if (localBUDGET_LIST[i] != null) {
                            writeStartElement(null, namespace, "BUDGET_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localBUDGET_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "BUDGET_LIST cannot be null!!");
                }
            }

            xmlWriter.writeEndElement();
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static CLASS_BUDGETLIST parse(
                    javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                CLASS_BUDGETLIST object = new CLASS_BUDGETLIST();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    if (reader.getAttributeValue(
                            "http://www.w3.org/2001/XMLSchema-instance",
                            "type") != null) {
                        String fullTypeName = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "type");

                        if (fullTypeName != null) {
                            String nsPrefix = null;

                            if (fullTypeName.indexOf(":") > -1) {
                                nsPrefix = fullTypeName.substring(0,
                                        fullTypeName.indexOf(":"));
                            }

                            nsPrefix = (nsPrefix == null) ? "" : nsPrefix;

                            String type = fullTypeName.substring(fullTypeName.indexOf(
                                    ":") + 1);

                            if (!"CLASS_BUDGETLIST".equals(type)) {
                                //find namespace for the prefix
                                String nsUri = reader.getNamespaceContext()
                                        .getNamespaceURI(nsPrefix);

                                return (CLASS_BUDGETLIST) ExtensionMapper.getTypeObject(nsUri,
                                        type, reader);
                            }
                        }
                    }

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    reader.next();

                    java.util.ArrayList list3 = new java.util.ArrayList();

                    java.util.ArrayList list4 = new java.util.ArrayList();

                    java.util.ArrayList list5 = new java.util.ArrayList();

                    java.util.ArrayList list6 = new java.util.ArrayList();

                    java.util.ArrayList list7 = new java.util.ArrayList();

                    java.util.ArrayList list8 = new java.util.ArrayList();

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "RESULT").equals(
                                    reader.getName())) {
                        nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "nil");

                        if ("true".equals(nillableValue) ||
                                "1".equals(nillableValue)) {
                            throw new org.apache.axis2.databinding.ADBException(
                                    "The element: " + "RESULT" +
                                            "  cannot be null");
                        }

                        String content = reader.getElementText();

                        object.setRESULT(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                content));

                        reader.next();
                    } // End of if for expected property start element

                    else {
                        // 1 - A start element we are not expecting indicates an invalid parameter was passed
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "MSG").equals(
                                    reader.getName())) {
                        nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "nil");

                        if ("true".equals(nillableValue) ||
                                "1".equals(nillableValue)) {
                            throw new org.apache.axis2.databinding.ADBException(
                                    "The element: " + "MSG" + "  cannot be null");
                        }

                        String content = reader.getElementText();

                        object.setMSG(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                content));

                        reader.next();
                    } // End of if for expected property start element

                    else {
                        // 1 - A start element we are not expecting indicates an invalid parameter was passed
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "YEAR_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list3.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone3 = false;

                        while (!loopDone3) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone3 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "YEAR_LIST").equals(
                                        reader.getName())) {
                                    list3.add(reader.getElementText());
                                } else {
                                    loopDone3 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setYEAR_LIST((String[]) list3.toArray(
                                new String[list3.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "MONTH_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list4.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone4 = false;

                        while (!loopDone4) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone4 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "MONTH_LIST").equals(
                                        reader.getName())) {
                                    list4.add(reader.getElementText());
                                } else {
                                    loopDone4 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setMONTH_LIST((String[]) list4.toArray(
                                new String[list4.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("",
                                    "COSTCENTERCODE_LIST").equals(reader.getName())) {
                        // Process the array and step past its final element's end.
                        list5.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone5 = false;

                        while (!loopDone5) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone5 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "COSTCENTERCODE_LIST").equals(
                                        reader.getName())) {
                                    list5.add(reader.getElementText());
                                } else {
                                    loopDone5 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setCOSTCENTERCODE_LIST((String[]) list5.toArray(
                                new String[list5.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("",
                                    "COSTCENTERNAME_LIST").equals(reader.getName())) {
                        // Process the array and step past its final element's end.
                        list6.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone6 = false;

                        while (!loopDone6) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone6 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "COSTCENTERNAME_LIST").equals(
                                        reader.getName())) {
                                    list6.add(reader.getElementText());
                                } else {
                                    loopDone6 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setCOSTCENTERNAME_LIST((String[]) list6.toArray(
                                new String[list6.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "REGION_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list7.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone7 = false;

                        while (!loopDone7) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone7 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "REGION_LIST").equals(
                                        reader.getName())) {
                                    list7.add(reader.getElementText());
                                } else {
                                    loopDone7 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setREGION_LIST((String[]) list7.toArray(
                                new String[list7.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "BUDGET_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list8.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone8 = false;

                        while (!loopDone8) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone8 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "BUDGET_LIST").equals(
                                        reader.getName())) {
                                    list8.add(reader.getElementText());
                                } else {
                                    loopDone8 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setBUDGET_LIST((String[]) list8.toArray(
                                new String[list8.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement()) {
                        // 2 - A start element we are not expecting indicates a trailing invalid property
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }

    public static class ExtensionMapper {
        public static Object getTypeObject(
                String namespaceURI, String typeName,
                javax.xml.stream.XMLStreamReader reader) throws Exception {
            if ("urn:DefaultNamespace".equals(namespaceURI) &&
                    "CLASS_BUDGETLIST".equals(typeName)) {
                return CLASS_BUDGETLIST.Factory.parse(reader);
            }

            if ("urn:DefaultNamespace".equals(namespaceURI) &&
                    "CLASS_FEELIST".equals(typeName)) {
                return CLASS_FEELIST.Factory.parse(reader);
            }

            throw new org.apache.axis2.databinding.ADBException(
                    "Unsupported type " + namespaceURI + " " + typeName);
        }
    }

    public static class STRYEARMONTH implements org.apache.axis2.databinding.ADBBean {
        public static final javax.xml.namespace.QName MY_QNAME = new javax.xml.namespace.QName("urn:DefaultNamespace",
                "STRYEARMONTH", "ns1");

        /**
         * field for STRYEARMONTH
         */
        protected String localSTRYEARMONTH;

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getSTRYEARMONTH() {
            return localSTRYEARMONTH;
        }

        /**
         * Auto generated setter method
         *
         * @param param STRYEARMONTH
         */
        public void setSTRYEARMONTH(String param) {
            this.localSTRYEARMONTH = param;
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, MY_QNAME));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            //We can safely assume an element has only one type associated with it
            String namespace = "urn:DefaultNamespace";
            String _localName = "STRYEARMONTH";

            writeStartElement(null, namespace, _localName, xmlWriter);

            // add the type details if this is used in a simple type
            if (serializeType) {
                String namespacePrefix = registerPrefix(xmlWriter,
                        "urn:DefaultNamespace");

                if ((namespacePrefix != null) &&
                        (namespacePrefix.trim().length() > 0)) {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            namespacePrefix + ":STRYEARMONTH", xmlWriter);
                } else {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            "STRYEARMONTH", xmlWriter);
                }
            }

            if (localSTRYEARMONTH == null) {
                throw new org.apache.axis2.databinding.ADBException(
                        "STRYEARMONTH cannot be null !!");
            } else {
                xmlWriter.writeCharacters(localSTRYEARMONTH);
            }

            xmlWriter.writeEndElement();
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static STRYEARMONTH parse(
                    javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                STRYEARMONTH object = new STRYEARMONTH();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    while (!reader.isEndElement()) {
                        if (reader.isStartElement()) {
                            if (reader.isStartElement() &&
                                    new javax.xml.namespace.QName(
                                            "urn:DefaultNamespace", "STRYEARMONTH").equals(
                                            reader.getName())) {
                                nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                        "nil");

                                if ("true".equals(nillableValue) ||
                                        "1".equals(nillableValue)) {
                                    throw new org.apache.axis2.databinding.ADBException(
                                            "The element: " + "STRYEARMONTH" +
                                                    "  cannot be null");
                                }

                                String content = reader.getElementText();

                                object.setSTRYEARMONTH(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                        content));
                            } // End of if for expected property start element

                            else {
                                // 3 - A start element we are not expecting indicates an invalid parameter was passed
                                throw new org.apache.axis2.databinding.ADBException(
                                        "Unexpected subelement " +
                                                reader.getName());
                            }
                        } else {
                            reader.next();
                        }
                    } // end of while loop
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }

    public static class GETBUDGETLISTReturn implements org.apache.axis2.databinding.ADBBean {
        public static final javax.xml.namespace.QName MY_QNAME = new javax.xml.namespace.QName("urn:DefaultNamespace",
                "GETBUDGETLISTReturn", "ns1");

        /**
         * field for GETBUDGETLISTReturn
         */
        protected CLASS_BUDGETLIST localGETBUDGETLISTReturn;

        /**
         * Auto generated getter method
         *
         * @return CLASS_BUDGETLIST
         */
        public CLASS_BUDGETLIST getGETBUDGETLISTReturn() {
            return localGETBUDGETLISTReturn;
        }

        /**
         * Auto generated setter method
         *
         * @param param GETBUDGETLISTReturn
         */
        public void setGETBUDGETLISTReturn(CLASS_BUDGETLIST param) {
            this.localGETBUDGETLISTReturn = param;
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, MY_QNAME));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            //We can safely assume an element has only one type associated with it
            if (localGETBUDGETLISTReturn == null) {
                throw new org.apache.axis2.databinding.ADBException(
                        "GETBUDGETLISTReturn cannot be null!");
            }

            localGETBUDGETLISTReturn.serialize(MY_QNAME, xmlWriter);
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static GETBUDGETLISTReturn parse(
                    javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                GETBUDGETLISTReturn object = new GETBUDGETLISTReturn();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    while (!reader.isEndElement()) {
                        if (reader.isStartElement()) {
                            if (reader.isStartElement() &&
                                    new javax.xml.namespace.QName(
                                            "urn:DefaultNamespace",
                                            "GETBUDGETLISTReturn").equals(
                                            reader.getName())) {
                                object.setGETBUDGETLISTReturn(CLASS_BUDGETLIST.Factory.parse(
                                        reader));
                            } // End of if for expected property start element

                            else {
                                // 3 - A start element we are not expecting indicates an invalid parameter was passed
                                throw new org.apache.axis2.databinding.ADBException(
                                        "Unexpected subelement " +
                                                reader.getName());
                            }
                        } else {
                            reader.next();
                        }
                    } // end of while loop
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }

    public static class STRYEAR implements org.apache.axis2.databinding.ADBBean {
        public static final javax.xml.namespace.QName MY_QNAME = new javax.xml.namespace.QName("urn:DefaultNamespace",
                "STRYEAR", "ns1");

        /**
         * field for STRYEAR
         */
        protected String localSTRYEAR;

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getSTRYEAR() {
            return localSTRYEAR;
        }

        /**
         * Auto generated setter method
         *
         * @param param STRYEAR
         */
        public void setSTRYEAR(String param) {
            this.localSTRYEAR = param;
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, MY_QNAME));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            //We can safely assume an element has only one type associated with it
            String namespace = "urn:DefaultNamespace";
            String _localName = "STRYEAR";

            writeStartElement(null, namespace, _localName, xmlWriter);

            // add the type details if this is used in a simple type
            if (serializeType) {
                String namespacePrefix = registerPrefix(xmlWriter,
                        "urn:DefaultNamespace");

                if ((namespacePrefix != null) &&
                        (namespacePrefix.trim().length() > 0)) {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            namespacePrefix + ":STRYEAR", xmlWriter);
                } else {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            "STRYEAR", xmlWriter);
                }
            }

            if (localSTRYEAR == null) {
                throw new org.apache.axis2.databinding.ADBException(
                        "STRYEAR cannot be null !!");
            } else {
                xmlWriter.writeCharacters(localSTRYEAR);
            }

            xmlWriter.writeEndElement();
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static STRYEAR parse(javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                STRYEAR object = new STRYEAR();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    while (!reader.isEndElement()) {
                        if (reader.isStartElement()) {
                            if (reader.isStartElement() &&
                                    new javax.xml.namespace.QName(
                                            "urn:DefaultNamespace", "STRYEAR").equals(
                                            reader.getName())) {
                                nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                        "nil");

                                if ("true".equals(nillableValue) ||
                                        "1".equals(nillableValue)) {
                                    throw new org.apache.axis2.databinding.ADBException(
                                            "The element: " + "STRYEAR" +
                                                    "  cannot be null");
                                }

                                String content = reader.getElementText();

                                object.setSTRYEAR(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                        content));
                            } // End of if for expected property start element

                            else {
                                // 3 - A start element we are not expecting indicates an invalid parameter was passed
                                throw new org.apache.axis2.databinding.ADBException(
                                        "Unexpected subelement " +
                                                reader.getName());
                            }
                        } else {
                            reader.next();
                        }
                    } // end of while loop
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }

    public static class CLASS_FEELIST implements org.apache.axis2.databinding.ADBBean {
        /* This type was generated from the piece of schema that had
           name = CLASS_FEELIST
           Namespace URI = urn:DefaultNamespace
           Namespace Prefix = ns1
         */

        /**
         * field for RESULT
         */
        protected String localRESULT;

        /**
         * field for MSG
         */
        protected String localMSG;

        /**
         * field for REQUESTID_LIST
         * This was an Array!
         */
        protected String[] localREQUESTID_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localREQUESTID_LISTTracker = false;

        /**
         * field for YEAR_LIST
         * This was an Array!
         */
        protected String[] localYEAR_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localYEAR_LISTTracker = false;

        /**
         * field for MONTH_LIST
         * This was an Array!
         */
        protected String[] localMONTH_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localMONTH_LISTTracker = false;

        /**
         * field for COSTCENTERCODE_LIST
         * This was an Array!
         */
        protected String[] localCOSTCENTERCODE_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localCOSTCENTERCODE_LISTTracker = false;

        /**
         * field for COSTCENTERNAME_LIST
         * This was an Array!
         */
        protected String[] localCOSTCENTERNAME_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localCOSTCENTERNAME_LISTTracker = false;

        /**
         * field for REGION_LIST
         * This was an Array!
         */
        protected String[] localREGION_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localREGION_LISTTracker = false;

        /**
         * field for TARGET_LIST
         * This was an Array!
         */
        protected String[] localTARGET_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localTARGET_LISTTracker = false;

        /**
         * field for SUBJECT_LIST
         * This was an Array!
         */
        protected String[] localSUBJECT_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localSUBJECT_LISTTracker = false;

        /**
         * field for AMOUNT_LIST
         * This was an Array!
         */
        protected String[] localAMOUNT_LIST;

        /*  This tracker boolean wil be used to detect whether the user called the set method
         *   for this attribute. It will be used to determine whether to include this field
         *   in the serialized XML
         */
        protected boolean localAMOUNT_LISTTracker = false;

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getRESULT() {
            return localRESULT;
        }

        /**
         * Auto generated setter method
         *
         * @param param RESULT
         */
        public void setRESULT(String param) {
            this.localRESULT = param;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String
         */
        public String getMSG() {
            return localMSG;
        }

        /**
         * Auto generated setter method
         *
         * @param param MSG
         */
        public void setMSG(String param) {
            this.localMSG = param;
        }

        public boolean isREQUESTID_LISTSpecified() {
            return localREQUESTID_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getREQUESTID_LIST() {
            return localREQUESTID_LIST;
        }

        /**
         * validate the array for REQUESTID_LIST
         */
        protected void validateREQUESTID_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param REQUESTID_LIST
         */
        public void setREQUESTID_LIST(String[] param) {
            validateREQUESTID_LIST(param);

            localREQUESTID_LISTTracker = param != null;

            this.localREQUESTID_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addREQUESTID_LIST(String param) {
            if (localREQUESTID_LIST == null) {
                localREQUESTID_LIST = new String[]{};
            }

            //update the setting tracker
            localREQUESTID_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localREQUESTID_LIST);
            list.add(param);
            this.localREQUESTID_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isYEAR_LISTSpecified() {
            return localYEAR_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getYEAR_LIST() {
            return localYEAR_LIST;
        }

        /**
         * validate the array for YEAR_LIST
         */
        protected void validateYEAR_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param YEAR_LIST
         */
        public void setYEAR_LIST(String[] param) {
            validateYEAR_LIST(param);

            localYEAR_LISTTracker = param != null;

            this.localYEAR_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addYEAR_LIST(String param) {
            if (localYEAR_LIST == null) {
                localYEAR_LIST = new String[]{};
            }

            //update the setting tracker
            localYEAR_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localYEAR_LIST);
            list.add(param);
            this.localYEAR_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isMONTH_LISTSpecified() {
            return localMONTH_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getMONTH_LIST() {
            return localMONTH_LIST;
        }

        /**
         * validate the array for MONTH_LIST
         */
        protected void validateMONTH_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param MONTH_LIST
         */
        public void setMONTH_LIST(String[] param) {
            validateMONTH_LIST(param);

            localMONTH_LISTTracker = param != null;

            this.localMONTH_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addMONTH_LIST(String param) {
            if (localMONTH_LIST == null) {
                localMONTH_LIST = new String[]{};
            }

            //update the setting tracker
            localMONTH_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localMONTH_LIST);
            list.add(param);
            this.localMONTH_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isCOSTCENTERCODE_LISTSpecified() {
            return localCOSTCENTERCODE_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getCOSTCENTERCODE_LIST() {
            return localCOSTCENTERCODE_LIST;
        }

        /**
         * validate the array for COSTCENTERCODE_LIST
         */
        protected void validateCOSTCENTERCODE_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param COSTCENTERCODE_LIST
         */
        public void setCOSTCENTERCODE_LIST(String[] param) {
            validateCOSTCENTERCODE_LIST(param);

            localCOSTCENTERCODE_LISTTracker = param != null;

            this.localCOSTCENTERCODE_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addCOSTCENTERCODE_LIST(String param) {
            if (localCOSTCENTERCODE_LIST == null) {
                localCOSTCENTERCODE_LIST = new String[]{};
            }

            //update the setting tracker
            localCOSTCENTERCODE_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localCOSTCENTERCODE_LIST);
            list.add(param);
            this.localCOSTCENTERCODE_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isCOSTCENTERNAME_LISTSpecified() {
            return localCOSTCENTERNAME_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getCOSTCENTERNAME_LIST() {
            return localCOSTCENTERNAME_LIST;
        }

        /**
         * validate the array for COSTCENTERNAME_LIST
         */
        protected void validateCOSTCENTERNAME_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param COSTCENTERNAME_LIST
         */
        public void setCOSTCENTERNAME_LIST(String[] param) {
            validateCOSTCENTERNAME_LIST(param);

            localCOSTCENTERNAME_LISTTracker = param != null;

            this.localCOSTCENTERNAME_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addCOSTCENTERNAME_LIST(String param) {
            if (localCOSTCENTERNAME_LIST == null) {
                localCOSTCENTERNAME_LIST = new String[]{};
            }

            //update the setting tracker
            localCOSTCENTERNAME_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localCOSTCENTERNAME_LIST);
            list.add(param);
            this.localCOSTCENTERNAME_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isREGION_LISTSpecified() {
            return localREGION_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getREGION_LIST() {
            return localREGION_LIST;
        }

        /**
         * validate the array for REGION_LIST
         */
        protected void validateREGION_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param REGION_LIST
         */
        public void setREGION_LIST(String[] param) {
            validateREGION_LIST(param);

            localREGION_LISTTracker = param != null;

            this.localREGION_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addREGION_LIST(String param) {
            if (localREGION_LIST == null) {
                localREGION_LIST = new String[]{};
            }

            //update the setting tracker
            localREGION_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localREGION_LIST);
            list.add(param);
            this.localREGION_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isTARGET_LISTSpecified() {
            return localTARGET_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getTARGET_LIST() {
            return localTARGET_LIST;
        }

        /**
         * validate the array for TARGET_LIST
         */
        protected void validateTARGET_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param TARGET_LIST
         */
        public void setTARGET_LIST(String[] param) {
            validateTARGET_LIST(param);

            localTARGET_LISTTracker = param != null;

            this.localTARGET_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addTARGET_LIST(String param) {
            if (localTARGET_LIST == null) {
                localTARGET_LIST = new String[]{};
            }

            //update the setting tracker
            localTARGET_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localTARGET_LIST);
            list.add(param);
            this.localTARGET_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isSUBJECT_LISTSpecified() {
            return localSUBJECT_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getSUBJECT_LIST() {
            return localSUBJECT_LIST;
        }

        /**
         * validate the array for SUBJECT_LIST
         */
        protected void validateSUBJECT_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param SUBJECT_LIST
         */
        public void setSUBJECT_LIST(String[] param) {
            validateSUBJECT_LIST(param);

            localSUBJECT_LISTTracker = param != null;

            this.localSUBJECT_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addSUBJECT_LIST(String param) {
            if (localSUBJECT_LIST == null) {
                localSUBJECT_LIST = new String[]{};
            }

            //update the setting tracker
            localSUBJECT_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localSUBJECT_LIST);
            list.add(param);
            this.localSUBJECT_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        public boolean isAMOUNT_LISTSpecified() {
            return localAMOUNT_LISTTracker;
        }

        /**
         * Auto generated getter method
         *
         * @return java.lang.String[]
         */
        public String[] getAMOUNT_LIST() {
            return localAMOUNT_LIST;
        }

        /**
         * validate the array for AMOUNT_LIST
         */
        protected void validateAMOUNT_LIST(String[] param) {
        }

        /**
         * Auto generated setter method
         *
         * @param param AMOUNT_LIST
         */
        public void setAMOUNT_LIST(String[] param) {
            validateAMOUNT_LIST(param);

            localAMOUNT_LISTTracker = param != null;

            this.localAMOUNT_LIST = param;
        }

        /**
         * Auto generated add method for the array for convenience
         *
         * @param param java.lang.String
         */
        public void addAMOUNT_LIST(String param) {
            if (localAMOUNT_LIST == null) {
                localAMOUNT_LIST = new String[]{};
            }

            //update the setting tracker
            localAMOUNT_LISTTracker = true;

            java.util.List list = org.apache.axis2.databinding.utils.ConverterUtil.toList(localAMOUNT_LIST);
            list.add(param);
            this.localAMOUNT_LIST = (String[]) list.toArray(new String[list.size()]);
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, parentQName));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            String prefix = null;
            String namespace = null;

            prefix = parentQName.getPrefix();
            namespace = parentQName.getNamespaceURI();
            writeStartElement(prefix, namespace, parentQName.getLocalPart(),
                    xmlWriter);

            if (serializeType) {
                String namespacePrefix = registerPrefix(xmlWriter,
                        "urn:DefaultNamespace");

                if ((namespacePrefix != null) &&
                        (namespacePrefix.trim().length() > 0)) {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            namespacePrefix + ":CLASS_FEELIST", xmlWriter);
                } else {
                    writeAttribute("xsi",
                            "http://www.w3.org/2001/XMLSchema-instance", "type",
                            "CLASS_FEELIST", xmlWriter);
                }
            }

            namespace = "";
            writeStartElement(null, namespace, "RESULT", xmlWriter);

            if (localRESULT == null) {
                // write the nil attribute
                throw new org.apache.axis2.databinding.ADBException(
                        "RESULT cannot be null!!");
            } else {
                xmlWriter.writeCharacters(localRESULT);
            }

            xmlWriter.writeEndElement();

            namespace = "";
            writeStartElement(null, namespace, "MSG", xmlWriter);

            if (localMSG == null) {
                // write the nil attribute
                throw new org.apache.axis2.databinding.ADBException(
                        "MSG cannot be null!!");
            } else {
                xmlWriter.writeCharacters(localMSG);
            }

            xmlWriter.writeEndElement();

            if (localREQUESTID_LISTTracker) {
                if (localREQUESTID_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localREQUESTID_LIST.length; i++) {
                        if (localREQUESTID_LIST[i] != null) {
                            writeStartElement(null, namespace,
                                    "REQUESTID_LIST", xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localREQUESTID_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "REQUESTID_LIST cannot be null!!");
                }
            }

            if (localYEAR_LISTTracker) {
                if (localYEAR_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localYEAR_LIST.length; i++) {
                        if (localYEAR_LIST[i] != null) {
                            writeStartElement(null, namespace, "YEAR_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localYEAR_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "YEAR_LIST cannot be null!!");
                }
            }

            if (localMONTH_LISTTracker) {
                if (localMONTH_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localMONTH_LIST.length; i++) {
                        if (localMONTH_LIST[i] != null) {
                            writeStartElement(null, namespace, "MONTH_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localMONTH_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "MONTH_LIST cannot be null!!");
                }
            }

            if (localCOSTCENTERCODE_LISTTracker) {
                if (localCOSTCENTERCODE_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localCOSTCENTERCODE_LIST.length; i++) {
                        if (localCOSTCENTERCODE_LIST[i] != null) {
                            writeStartElement(null, namespace,
                                    "COSTCENTERCODE_LIST", xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localCOSTCENTERCODE_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "COSTCENTERCODE_LIST cannot be null!!");
                }
            }

            if (localCOSTCENTERNAME_LISTTracker) {
                if (localCOSTCENTERNAME_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localCOSTCENTERNAME_LIST.length; i++) {
                        if (localCOSTCENTERNAME_LIST[i] != null) {
                            writeStartElement(null, namespace,
                                    "COSTCENTERNAME_LIST", xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localCOSTCENTERNAME_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "COSTCENTERNAME_LIST cannot be null!!");
                }
            }

            if (localREGION_LISTTracker) {
                if (localREGION_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localREGION_LIST.length; i++) {
                        if (localREGION_LIST[i] != null) {
                            writeStartElement(null, namespace, "REGION_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localREGION_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "REGION_LIST cannot be null!!");
                }
            }

            if (localTARGET_LISTTracker) {
                if (localTARGET_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localTARGET_LIST.length; i++) {
                        if (localTARGET_LIST[i] != null) {
                            writeStartElement(null, namespace, "TARGET_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localTARGET_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "TARGET_LIST cannot be null!!");
                }
            }

            if (localSUBJECT_LISTTracker) {
                if (localSUBJECT_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localSUBJECT_LIST.length; i++) {
                        if (localSUBJECT_LIST[i] != null) {
                            writeStartElement(null, namespace, "SUBJECT_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localSUBJECT_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "SUBJECT_LIST cannot be null!!");
                }
            }

            if (localAMOUNT_LISTTracker) {
                if (localAMOUNT_LIST != null) {
                    namespace = "";

                    for (int i = 0; i < localAMOUNT_LIST.length; i++) {
                        if (localAMOUNT_LIST[i] != null) {
                            writeStartElement(null, namespace, "AMOUNT_LIST",
                                    xmlWriter);

                            xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    localAMOUNT_LIST[i]));

                            xmlWriter.writeEndElement();
                        } else {
                            // we have to do nothing since minOccurs is zero
                        }
                    }
                } else {
                    throw new org.apache.axis2.databinding.ADBException(
                            "AMOUNT_LIST cannot be null!!");
                }
            }

            xmlWriter.writeEndElement();
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static CLASS_FEELIST parse(
                    javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                CLASS_FEELIST object = new CLASS_FEELIST();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    if (reader.getAttributeValue(
                            "http://www.w3.org/2001/XMLSchema-instance",
                            "type") != null) {
                        String fullTypeName = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "type");

                        if (fullTypeName != null) {
                            String nsPrefix = null;

                            if (fullTypeName.indexOf(":") > -1) {
                                nsPrefix = fullTypeName.substring(0,
                                        fullTypeName.indexOf(":"));
                            }

                            nsPrefix = (nsPrefix == null) ? "" : nsPrefix;

                            String type = fullTypeName.substring(fullTypeName.indexOf(
                                    ":") + 1);

                            if (!"CLASS_FEELIST".equals(type)) {
                                //find namespace for the prefix
                                String nsUri = reader.getNamespaceContext()
                                        .getNamespaceURI(nsPrefix);

                                return (CLASS_FEELIST) ExtensionMapper.getTypeObject(nsUri,
                                        type, reader);
                            }
                        }
                    }

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    reader.next();

                    java.util.ArrayList list3 = new java.util.ArrayList();

                    java.util.ArrayList list4 = new java.util.ArrayList();

                    java.util.ArrayList list5 = new java.util.ArrayList();

                    java.util.ArrayList list6 = new java.util.ArrayList();

                    java.util.ArrayList list7 = new java.util.ArrayList();

                    java.util.ArrayList list8 = new java.util.ArrayList();

                    java.util.ArrayList list9 = new java.util.ArrayList();

                    java.util.ArrayList list10 = new java.util.ArrayList();

                    java.util.ArrayList list11 = new java.util.ArrayList();

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "RESULT").equals(
                                    reader.getName())) {
                        nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "nil");

                        if ("true".equals(nillableValue) ||
                                "1".equals(nillableValue)) {
                            throw new org.apache.axis2.databinding.ADBException(
                                    "The element: " + "RESULT" +
                                            "  cannot be null");
                        }

                        String content = reader.getElementText();

                        object.setRESULT(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                content));

                        reader.next();
                    } // End of if for expected property start element

                    else {
                        // 1 - A start element we are not expecting indicates an invalid parameter was passed
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "MSG").equals(
                                    reader.getName())) {
                        nillableValue = reader.getAttributeValue("http://www.w3.org/2001/XMLSchema-instance",
                                "nil");

                        if ("true".equals(nillableValue) ||
                                "1".equals(nillableValue)) {
                            throw new org.apache.axis2.databinding.ADBException(
                                    "The element: " + "MSG" + "  cannot be null");
                        }

                        String content = reader.getElementText();

                        object.setMSG(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                content));

                        reader.next();
                    } // End of if for expected property start element

                    else {
                        // 1 - A start element we are not expecting indicates an invalid parameter was passed
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "REQUESTID_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list3.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone3 = false;

                        while (!loopDone3) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone3 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "REQUESTID_LIST").equals(
                                        reader.getName())) {
                                    list3.add(reader.getElementText());
                                } else {
                                    loopDone3 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setREQUESTID_LIST((String[]) list3.toArray(
                                new String[list3.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "YEAR_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list4.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone4 = false;

                        while (!loopDone4) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone4 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "YEAR_LIST").equals(
                                        reader.getName())) {
                                    list4.add(reader.getElementText());
                                } else {
                                    loopDone4 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setYEAR_LIST((String[]) list4.toArray(
                                new String[list4.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "MONTH_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list5.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone5 = false;

                        while (!loopDone5) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone5 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "MONTH_LIST").equals(
                                        reader.getName())) {
                                    list5.add(reader.getElementText());
                                } else {
                                    loopDone5 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setMONTH_LIST((String[]) list5.toArray(
                                new String[list5.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("",
                                    "COSTCENTERCODE_LIST").equals(reader.getName())) {
                        // Process the array and step past its final element's end.
                        list6.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone6 = false;

                        while (!loopDone6) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone6 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "COSTCENTERCODE_LIST").equals(
                                        reader.getName())) {
                                    list6.add(reader.getElementText());
                                } else {
                                    loopDone6 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setCOSTCENTERCODE_LIST((String[]) list6.toArray(
                                new String[list6.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("",
                                    "COSTCENTERNAME_LIST").equals(reader.getName())) {
                        // Process the array and step past its final element's end.
                        list7.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone7 = false;

                        while (!loopDone7) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone7 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "COSTCENTERNAME_LIST").equals(
                                        reader.getName())) {
                                    list7.add(reader.getElementText());
                                } else {
                                    loopDone7 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setCOSTCENTERNAME_LIST((String[]) list7.toArray(
                                new String[list7.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "REGION_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list8.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone8 = false;

                        while (!loopDone8) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone8 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "REGION_LIST").equals(
                                        reader.getName())) {
                                    list8.add(reader.getElementText());
                                } else {
                                    loopDone8 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setREGION_LIST((String[]) list8.toArray(
                                new String[list8.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "TARGET_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list9.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone9 = false;

                        while (!loopDone9) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone9 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "TARGET_LIST").equals(
                                        reader.getName())) {
                                    list9.add(reader.getElementText());
                                } else {
                                    loopDone9 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setTARGET_LIST((String[]) list9.toArray(
                                new String[list9.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "SUBJECT_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list10.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone10 = false;

                        while (!loopDone10) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone10 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "SUBJECT_LIST").equals(
                                        reader.getName())) {
                                    list10.add(reader.getElementText());
                                } else {
                                    loopDone10 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setSUBJECT_LIST((String[]) list10.toArray(
                                new String[list10.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement() &&
                            new javax.xml.namespace.QName("", "AMOUNT_LIST").equals(
                                    reader.getName())) {
                        // Process the array and step past its final element's end.
                        list11.add(reader.getElementText());

                        //loop until we find a start element that is not part of this array
                        boolean loopDone11 = false;

                        while (!loopDone11) {
                            // Ensure we are at the EndElement
                            while (!reader.isEndElement()) {
                                reader.next();
                            }

                            // Step out of this element
                            reader.next();

                            // Step to next element event.
                            while (!reader.isStartElement() &&
                                    !reader.isEndElement())
                                reader.next();

                            if (reader.isEndElement()) {
                                //two continuous end elements means we are exiting the xml structure
                                loopDone11 = true;
                            } else {
                                if (new javax.xml.namespace.QName("",
                                        "AMOUNT_LIST").equals(
                                        reader.getName())) {
                                    list11.add(reader.getElementText());
                                } else {
                                    loopDone11 = true;
                                }
                            }
                        }

                        // call the converter utility  to convert and set the array
                        object.setAMOUNT_LIST((String[]) list11.toArray(
                                new String[list11.size()]));
                    } // End of if for expected property start element

                    else {
                    }

                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    if (reader.isStartElement()) {
                        // 2 - A start element we are not expecting indicates a trailing invalid property
                        throw new org.apache.axis2.databinding.ADBException(
                                "Unexpected subelement " + reader.getName());
                    }
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }

    public static class GETLISTReturn implements org.apache.axis2.databinding.ADBBean {
        public static final javax.xml.namespace.QName MY_QNAME = new javax.xml.namespace.QName("urn:DefaultNamespace",
                "GETLISTReturn", "ns1");

        /**
         * field for GETLISTReturn
         */
        protected CLASS_FEELIST localGETLISTReturn;

        /**
         * Auto generated getter method
         *
         * @return CLASS_FEELIST
         */
        public CLASS_FEELIST getGETLISTReturn() {
            return localGETLISTReturn;
        }

        /**
         * Auto generated setter method
         *
         * @param param GETLISTReturn
         */
        public void setGETLISTReturn(CLASS_FEELIST param) {
            this.localGETLISTReturn = param;
        }

        /**
         * @param parentQName
         * @param factory
         * @return org.apache.axiom.om.OMElement
         */
        public org.apache.axiom.om.OMElement getOMElement(
                final javax.xml.namespace.QName parentQName,
                final org.apache.axiom.om.OMFactory factory)
                throws org.apache.axis2.databinding.ADBException {
            return factory.createOMElement(new org.apache.axis2.databinding.ADBDataSource(
                    this, MY_QNAME));
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            serialize(parentQName, xmlWriter, false);
        }

        public void serialize(final javax.xml.namespace.QName parentQName,
                              javax.xml.stream.XMLStreamWriter xmlWriter, boolean serializeType)
                throws javax.xml.stream.XMLStreamException,
                org.apache.axis2.databinding.ADBException {
            //We can safely assume an element has only one type associated with it
            if (localGETLISTReturn == null) {
                throw new org.apache.axis2.databinding.ADBException(
                        "GETLISTReturn cannot be null!");
            }

            localGETLISTReturn.serialize(MY_QNAME, xmlWriter);
        }

        private static String generatePrefix(
                String namespace) {
            if (namespace.equals("urn:DefaultNamespace")) {
                return "ns1";
            }

            return org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
        }

        /**
         * Utility method to write an element start tag.
         */
        private void writeStartElement(String prefix,
                                       String namespace, String localPart,
                                       javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeStartElement(writerPrefix, localPart, namespace);
            } else {
                if (namespace.length() == 0) {
                    prefix = "";
                } else if (prefix == null) {
                    prefix = generatePrefix(namespace);
                }

                xmlWriter.writeStartElement(prefix, localPart, namespace);
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }
        }

        /**
         * Util method to write an attribute with the ns prefix
         */
        private void writeAttribute(String prefix,
                                    String namespace, String attName,
                                    String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String writerPrefix = xmlWriter.getPrefix(namespace);

            if (writerPrefix != null) {
                xmlWriter.writeAttribute(writerPrefix, namespace, attName,
                        attValue);
            } else {
                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
                xmlWriter.writeAttribute(prefix, namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeAttribute(String namespace,
                                    String attName, String attValue,
                                    javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attValue);
            } else {
                xmlWriter.writeAttribute(registerPrefix(xmlWriter, namespace),
                        namespace, attName, attValue);
            }
        }

        /**
         * Util method to write an attribute without the ns prefix
         */
        private void writeQNameAttribute(String namespace,
                                         String attName, javax.xml.namespace.QName qname,
                                         javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String attributeNamespace = qname.getNamespaceURI();
            String attributePrefix = xmlWriter.getPrefix(attributeNamespace);

            if (attributePrefix == null) {
                attributePrefix = registerPrefix(xmlWriter, attributeNamespace);
            }

            String attributeValue;

            if (attributePrefix.trim().length() > 0) {
                attributeValue = attributePrefix + ":" + qname.getLocalPart();
            } else {
                attributeValue = qname.getLocalPart();
            }

            if (namespace.equals("")) {
                xmlWriter.writeAttribute(attName, attributeValue);
            } else {
                registerPrefix(xmlWriter, namespace);
                xmlWriter.writeAttribute(attributePrefix, namespace, attName,
                        attributeValue);
            }
        }

        /**
         * method to handle Qnames
         */
        private void writeQName(javax.xml.namespace.QName qname,
                                javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            String namespaceURI = qname.getNamespaceURI();

            if (namespaceURI != null) {
                String prefix = xmlWriter.getPrefix(namespaceURI);

                if (prefix == null) {
                    prefix = generatePrefix(namespaceURI);
                    xmlWriter.writeNamespace(prefix, namespaceURI);
                    xmlWriter.setPrefix(prefix, namespaceURI);
                }

                if (prefix.trim().length() > 0) {
                    xmlWriter.writeCharacters(prefix + ":" +
                            org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qname));
                } else {
                    // i.e this is the default namespace
                    xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                            qname));
                }
            } else {
                xmlWriter.writeCharacters(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                        qname));
            }
        }

        private void writeQNames(javax.xml.namespace.QName[] qnames,
                                 javax.xml.stream.XMLStreamWriter xmlWriter)
                throws javax.xml.stream.XMLStreamException {
            if (qnames != null) {
                // we have to store this data until last moment since it is not possible to write any
                // namespace data after writing the charactor data
                StringBuffer stringToWrite = new StringBuffer();
                String namespaceURI = null;
                String prefix = null;

                for (int i = 0; i < qnames.length; i++) {
                    if (i > 0) {
                        stringToWrite.append(" ");
                    }

                    namespaceURI = qnames[i].getNamespaceURI();

                    if (namespaceURI != null) {
                        prefix = xmlWriter.getPrefix(namespaceURI);

                        if ((prefix == null) || (prefix.length() == 0)) {
                            prefix = generatePrefix(namespaceURI);
                            xmlWriter.writeNamespace(prefix, namespaceURI);
                            xmlWriter.setPrefix(prefix, namespaceURI);
                        }

                        if (prefix.trim().length() > 0) {
                            stringToWrite.append(prefix).append(":")
                                    .append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                            qnames[i]));
                        } else {
                            stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                    qnames[i]));
                        }
                    } else {
                        stringToWrite.append(org.apache.axis2.databinding.utils.ConverterUtil.convertToString(
                                qnames[i]));
                    }
                }

                xmlWriter.writeCharacters(stringToWrite.toString());
            }
        }

        /**
         * Register a namespace prefix
         */
        private String registerPrefix(
                javax.xml.stream.XMLStreamWriter xmlWriter,
                String namespace)
                throws javax.xml.stream.XMLStreamException {
            String prefix = xmlWriter.getPrefix(namespace);

            if (prefix == null) {
                prefix = generatePrefix(namespace);

                javax.xml.namespace.NamespaceContext nsContext = xmlWriter.getNamespaceContext();

                while (true) {
                    String uri = nsContext.getNamespaceURI(prefix);

                    if ((uri == null) || (uri.length() == 0)) {
                        break;
                    }

                    prefix = org.apache.axis2.databinding.utils.BeanUtil.getUniquePrefix();
                }

                xmlWriter.writeNamespace(prefix, namespace);
                xmlWriter.setPrefix(prefix, namespace);
            }

            return prefix;
        }

        /**
         * Factory class that keeps the parse method
         */
        public static class Factory {
            private static org.apache.commons.logging.Log log = org.apache.commons.logging.LogFactory.getLog(Factory.class);

            /**
             * static method to create the object
             * Precondition:  If this object is an element, the current or next start element starts this object and any intervening reader events are ignorable
             * If this object is not an element, it is a complex type and the reader is at the event just after the outer start element
             * Postcondition: If this object is an element, the reader is positioned at its end element
             * If this object is a complex type, the reader is positioned at the end element of its outer element
             */
            public static GETLISTReturn parse(
                    javax.xml.stream.XMLStreamReader reader)
                    throws Exception {
                GETLISTReturn object = new GETLISTReturn();

                int event;
                javax.xml.namespace.QName currentQName = null;
                String nillableValue = null;
                String prefix = "";
                String namespaceuri = "";

                try {
                    while (!reader.isStartElement() && !reader.isEndElement())
                        reader.next();

                    currentQName = reader.getName();

                    // Note all attributes that were handled. Used to differ normal attributes
                    // from anyAttributes.
                    java.util.Vector handledAttributes = new java.util.Vector();

                    while (!reader.isEndElement()) {
                        if (reader.isStartElement()) {
                            if (reader.isStartElement() &&
                                    new javax.xml.namespace.QName(
                                            "urn:DefaultNamespace", "GETLISTReturn").equals(
                                            reader.getName())) {
                                object.setGETLISTReturn(CLASS_FEELIST.Factory.parse(
                                        reader));
                            } // End of if for expected property start element

                            else {
                                // 3 - A start element we are not expecting indicates an invalid parameter was passed
                                throw new org.apache.axis2.databinding.ADBException(
                                        "Unexpected subelement " +
                                                reader.getName());
                            }
                        } else {
                            reader.next();
                        }
                    } // end of while loop
                } catch (javax.xml.stream.XMLStreamException e) {
                    throw new Exception(e);
                }

                return object;
            }
        } //end of factory class
    }
}
