package com.cloudpense.expman.webService.nippon.server;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.mapper.NipponMapper;
import com.cloudpense.expman.util.MQProducer;
import com.cloudpense.expman.vo.nippon.ExpClaimHeaderWithBLOBsDto;
import com.cloudpense.expman.webService.nippon.server.entity.ClaimStatusRequest;
import com.cloudpense.expman.webService.nippon.server.entity.ClaimStatusResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

public class ClaimStatusUpdateImpl implements ClaimStatusUpdate {

    private static Logger logger = LoggerFactory.getLogger(ClaimStatusUpdateImpl.class);

    private NipponMapper nipponMapper;

    @Autowired
    private MQProducer mqProducer;

    @Autowired
    public ClaimStatusUpdateImpl(NipponMapper nipponMapper) {
        this.nipponMapper = nipponMapper;
    }

    @Override
    public List<ClaimStatusResponse> claimStatusUpdate(List<ClaimStatusRequest> request) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        List<ClaimStatusResponse> response = new ArrayList<>();
        ClaimStatusResponse resp = null;
        ExpClaimHeader header;
        String desc, finStatus = null, sendStatus = null, externalStatus = "success";
        JSONObject input;
        int userId, pathId;
        try {
            logger.info("claim_update传入数据{}", JSONArray.toJSONString(request));
        } catch (Exception ignored) {
        }
        for (ClaimStatusRequest req : request
        ) {
            logger.info("claim_update处理中的数据为{}", JSONArray.toJSONString(req));
            try {
                input = new JSONObject();
                desc = req.getDescription();
                sendStatus = req.getSendStatus();
                header = nipponMapper.queryClaimHeaderByDocNum1(req.getClaimID());
                userId = 171000;
                pathId = (nipponMapper.queryWorkFlow2(header.getHeaderId()) != null) ? nipponMapper.queryWorkFlow2(header.getHeaderId()) : 0;


                ExpClaimHeader document1 = nipponMapper.queryClaimHeaderByDocNum1(req.getClaimID());
                if(("approved").equals(document1.getStatus())){
                    try {
                        ExpClaimHeaderWithBLOBsDto doc1 = new ExpClaimHeaderWithBLOBsDto();
                        doc1.setCode(document1.getDocumentNum());
                        doc1.setCreatedBy(Long.valueOf(document1.getCreatedBy()));
                        doc1.setId(document1.getHeaderId());
                        mqProducer.sendMQ(doc1,"document_approved", 15220L,null,
                                header.getStatus());
                    }catch (Exception e){
                        logger.info("执行报错{}",e.getMessage(),e);
                        logger.info("111");
                    }
                }

                if (sendStatus == null) {
                    sendStatus = "";
                }
                input.put("total_pay_amount", header.getTotalPayAmount());
                input.put("travel_method", header.getTravelMethod());
                input.put("business_purpose", header.getBusinessPurpose());
                input.put("leave_type", header.getLeaveType());
                input.put("leave_day", header.getLeaveDay());
                input.put("total_amount", header.getTotalAmount());
                input.put("advance_amount", header.getAdvanceAmount());
                input.put("currency_code", header.getCurrencyCode());
                if (header.getStartDatetime() != null) {
                    input.put("start_datetime", header.getStartDatetime().getTime());
                }
                if (header.getEndDatetime() != null) {
                    input.put("end_datetime", header.getEndDatetime().getTime());
                }
                input.put("destination_city", header.getDestinationCity());
                input.put("destination_city_to", header.getDestinationCityTo());
                input.put("description", header.getDescription());
                input.put("finance_description", header.getFinanceDescription());
                input.put("product_name", header.getProductName());
                input.put("project_name", header.getProjectName());
                input.put("customer_name", header.getCustomerName());
                input.put("supplier_id", header.getSupplierId());
                if (header.getPlanStartDatetime() != null) {
                    input.put("plan_start_datetime", header.getPlanStartDatetime().getTime());
                }

                if (header.getPlanEndDatetime() != null) {
                    input.put("plan_end_datetime", header.getPlanEndDatetime().getTime());
                }
                input.put("branch_id", header.getBranchId());
                input.put("submit_department", header.getSubmitDepartment());
                input.put("charge_department", header.getChargeDepartment());
                input.put("submit_user", header.getSubmitUser());
                input.put("charge_user", header.getChargeUser());
                input.put("pay_object", header.getPayObject());
                input.put("pay_user", header.getPayUser());
                input.put("invoice_flag", header.getInvoiceFlag());
                if (header.getPaymentDate() != null) {
                    input.put("payment_date", header.getPaymentDate().getTime());
                }
                input.put("user_account_id", header.getUserAccountId());
                input.put("supplier_account_id", header.getSupplierAccountId());
                input.put("branch_account_id", header.getBranchAccountId());
                input.put("gl_period", header.getGlPeriod());
                input.put("priority", header.getPriority());
                input.put("column1", header.getColumn1());
                input.put("column2", header.getColumn2());
                input.put("column3", header.getColumn3());
                input.put("column4", header.getColumn4());
                input.put("column5", header.getColumn5());
                input.put("column6", header.getColumn6());
                input.put("column7", header.getColumn7());
                input.put("column8", header.getColumn8());
                input.put("column9", header.getColumn9());
                input.put("column10", header.getColumn10());
                input.put("column11", header.getColumn11());
                input.put("column12", header.getColumn12());
                input.put("column13", header.getColumn13());
                input.put("column14", header.getColumn14());
                input.put("column15", header.getColumn15());
                input.put("column16", header.getColumn16());
                input.put("column17", header.getColumn17());
                input.put("column18", header.getColumn18());
                input.put("column19", header.getColumn19());
                input.put("column20", header.getColumn20());
                input.put("column21", header.getColumn21());
                input.put("column22", header.getColumn22());
                input.put("column23", header.getColumn23());
                input.put("column24", header.getColumn24());
                input.put("column25", header.getColumn25());
                input.put("column26", header.getColumn26());
                input.put("column27", header.getColumn27());
                input.put("column28", header.getColumn28());
                input.put("column29", header.getColumn29());
                input.put("column30", header.getColumn30());
                input.put("column31", header.getColumn31());
                input.put("column32", header.getColumn32());
                input.put("column33", header.getColumn33());
                input.put("column34", header.getColumn34());
                input.put("column35", header.getColumn35());
                input.put("column36", header.getColumn36());
                input.put("column37", header.getColumn37());
                input.put("column38", header.getColumn38());
                input.put("column39", header.getColumn39());
                input.put("column40", header.getColumn40());
                input.put("column41", header.getColumn41());
                input.put("column42", header.getColumn42());
                input.put("column43", header.getColumn43());
                input.put("column44", header.getColumn44());
                input.put("column45", header.getColumn45());
                input.put("column46", header.getColumn46());
                input.put("column47", header.getColumn47());
                input.put("column48", header.getColumn48());
                input.put("column49", header.getColumn49());
                input.put("column50", header.getColumn50());

                switch (req.getStatus()) {
                    case "06":
                        finStatus = "初审退单";
                        externalStatus = "rejected";
                        sendStatus = "暂无";
                        if (desc == null || "".equals(desc)) {
                            desc = "初审退单";
                        } else {
                            desc = "初审退单:" + desc;
                        }
                        input.put("note", desc);
                        input.put("path_status", "rejected");
                        input.put("status", "rejected");
                        break;
                    case "11":
                        finStatus = "记账退单";
                        externalStatus = "rejected";
                        sendStatus = "暂无";
                        if (desc == null || "".equals(desc)) {
                            desc = "记账退单";
                        } else {
                            desc = "记账退单:" + desc;
                        }
                        input.put("note", desc);
                        input.put("path_status", "rejected");
                        input.put("status", "rejected");
                        break;
                    case "98":
                        finStatus = "系统拒绝";
                        externalStatus = "rejected";
                        sendStatus = "暂无";
                        if (desc == null || "".equals(desc)) {
                            desc = "系统拒绝";
                        } else {
                            desc = "系统拒绝:" + desc;
                        }
                        input.put("note", desc);
                        input.put("path_status", "rejected");
                        input.put("status", "rejected");
                        break;
                    case "07":
                        finStatus = "打回修改";
                        externalStatus = "modifying";
                        sendStatus = "暂无";
                        if (desc == null || "".equals(desc)) {
                            desc = "打回修改:请修改单据信息!";
                        } else {
                            desc = "打回修改:" + desc;
                        }
                        input.put("note", desc);
                        input.put("path_status", "modifying");
                        input.put("status", "modifying");
                        break;
                    case "12":
                        finStatus = "已完成";
                        externalStatus = "success";
                        //已完成
                        desc = "本单已完成";
                        input.put("note", desc);
                        input.put("path_status", "approved");
                        input.put("status", "approved");
                        break;
                    case "01":
                        finStatus = "暂存";
                        sendStatus = "暂无";
                        break;
                    case "02":
                        finStatus = "待审批";
                        sendStatus = "暂无";
                        break;
                    case "03":
                        finStatus = "已审批";
                        sendStatus = "暂无";
                        break;
                    case "04":
                        finStatus = "审批拒绝";
                        sendStatus = "暂无";
                        break;
                    case "05":
                        finStatus = "已过账";
                        nipponMapper.updatePostingDate(header.getHeaderId(), "I", req.getPostDate());
                        break;
                    case "08":
                        finStatus = "已修改";
                        break;
                    case "09":
                        finStatus = "审核退单";
                        externalStatus = "rejected";
                        sendStatus = "暂无";
                        if (desc == null || "".equals(desc)) {
                            desc = "审核退单";
                        } else {
                            desc = "审核退单:" + desc;
                        }
                        input.put("note", desc);
                        input.put("path_status", "rejected");
                        input.put("status", "rejected");
                        break;
                    case "10":
                        finStatus = "已付款";
                        break;
                }

                if (pathId != 0 && input.getString("status") != null) {
                    try {
                        if ("rejected".equals(input.getString("status"))) {
                            nipponMapper.updatePostingDate(header.getHeaderId(), "D", null);
                        }
                    } catch (Exception e) {
                    }
                    input.put("user_id", userId);
                    input.put("fin_status", finStatus);
                    input.put("path_id", pathId);
                    header.setInput(input.toJSONString());
                    header.setUserId(userId);
                    header.setCompanyId(15220);
                    if (header.getLanguage() == null || ("".equals(header.getLanguage()))) {
                        header.setLanguage("zh_CN");
                    }
                    nipponMapper.updateWorkFlow2(header.getHeaderId());
                    System.out.println(header.getInput());
//                    if(!"12".equals(req.getStatus())){
//                        nipponMapper.unlockReceipt(header.getHeaderId());
//                    }
                    nipponMapper.saveWithFinanceInfoNew(header);
                    System.out.println(header.getReturnCode() + header.getReturnMessage());

                    resp = new ClaimStatusResponse();
                    resp.setClaimID(req.getClaimID());
                    if ("S".equals(header.getReturnCode())) {
                        resp.setErrMsg("");
                        resp.setStatus("OK");
                        ExpClaimHeader document = nipponMapper.queryClaimHeaderByDocNum1(req.getClaimID());
                        if(("approved").equals(document.getStatus())){
                            ExpClaimHeaderWithBLOBsDto doc = new ExpClaimHeaderWithBLOBsDto();
                            doc.setCode(document.getDocumentNum());
                            doc.setCreatedBy(Long.valueOf(document.getCreatedBy()));
                            doc.setId(document.getHeaderId());
                            mqProducer.sendMQ(doc,"document_approved", 15220L,null,
                                    header.getStatus());
                        }
                    } else {
                        resp.setErrMsg(header.getReturnMessage());
                        resp.setStatus("error");
                        logger.info("单据状态更新出错----单据" + req.getClaimID() + "费控回退状态更新失败!\n请求详情:\n" + JSON.toJSONString(req) + "\n错误详情:\n" + header.getReturnMessage());
                    }
                    response.add(resp);
                }
                nipponMapper.updateNipponStatus(header.getHeaderId(), finStatus, sendStatus, externalStatus);
                if (resp == null) {
                    resp = new ClaimStatusResponse();
                    resp.setClaimID(req.getClaimID());
                    resp.setErrMsg("");
                    resp.setStatus("OK");
                    response.add(resp);
                }

            } catch (Exception e) {
                e.printStackTrace();
                resp = new ClaimStatusResponse();
                resp.setClaimID(req.getClaimID());
                resp.setErrMsg(e.getMessage());
                resp.setStatus("error");
                response.add(resp);
                logger.error("单据状态更新出错----单据" + req.getClaimID() + "费控回退状态更新失败!\n请求详情:\n" + JSON.toJSONString(req) + "\n错误详情:\n", e);
            }
        }
        return response;
    }
}
