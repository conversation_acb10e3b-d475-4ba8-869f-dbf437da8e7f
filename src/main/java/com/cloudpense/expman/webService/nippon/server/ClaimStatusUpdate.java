package com.cloudpense.expman.webService.nippon.server;

import com.cloudpense.expman.webService.nippon.server.entity.ClaimStatusRequest;
import com.cloudpense.expman.webService.nippon.server.entity.ClaimStatusResponse;

import javax.jws.WebParam;
import javax.jws.WebService;
import javax.jws.soap.SOAPBinding;
import java.util.List;

@WebService
@SOAPBinding(style = SOAPBinding.Style.RPC)
public interface ClaimStatusUpdate {
    public List<ClaimStatusResponse> claimStatusUpdate(@WebParam(name = "updateRequest") List<ClaimStatusRequest> request);
}
