package com.cloudpense.expman.webService.hsf;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceFeature;
import javax.xml.ws.Service;

/**
 * This class was generated by Apache CXF 3.4.1
 * 2020-12-29T17:06:37.340+08:00
 * Generated source version: 3.4.1
 *
 */
@WebServiceClient(name = "ItfGetInfoOfHR",
                  wsdlLocation = "http://220.174.235.16:92/uapws/service/ItfGetInfoOfHR?wsdl",
                  targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
public class ItfGetInfoOfHR extends Service {

    public final static URL WSDL_LOCATION;

    public final static QName SERVICE = new QName("http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", "ItfGetInfoOfHR");
    public final static QName ItfGetInfoOfHRSOAP11PortHttp = new QName("http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", "ItfGetInfoOfHRSOAP11port_http");
    static {
        URL url = null;
        try {
            url = new URL("http://220.174.235.16:92/uapws/service/ItfGetInfoOfHR?wsdl");
        } catch (MalformedURLException e) {
            java.util.logging.Logger.getLogger(ItfGetInfoOfHR.class.getName())
                .log(java.util.logging.Level.INFO,
                     "Can not initialize the default wsdl from {0}", "http://220.174.235.16:92/uapws/service/ItfGetInfoOfHR?wsdl");
        }
        WSDL_LOCATION = url;
    }

    public ItfGetInfoOfHR(URL wsdlLocation) {
        super(wsdlLocation, SERVICE);
    }

    public ItfGetInfoOfHR(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ItfGetInfoOfHR() {
        super(WSDL_LOCATION, SERVICE);
    }

    public ItfGetInfoOfHR(WebServiceFeature ... features) {
        super(WSDL_LOCATION, SERVICE, features);
    }

    public ItfGetInfoOfHR(URL wsdlLocation, WebServiceFeature ... features) {
        super(wsdlLocation, SERVICE, features);
    }

    public ItfGetInfoOfHR(URL wsdlLocation, QName serviceName, WebServiceFeature ... features) {
        super(wsdlLocation, serviceName, features);
    }




    /**
     *
     * @return
     *     returns ItfGetInfoOfHRPortType
     */
    @WebEndpoint(name = "ItfGetInfoOfHRSOAP11port_http")
    public ItfGetInfoOfHRPortType getItfGetInfoOfHRSOAP11PortHttp() {
        return super.getPort(ItfGetInfoOfHRSOAP11PortHttp, ItfGetInfoOfHRPortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ItfGetInfoOfHRPortType
     */
    @WebEndpoint(name = "ItfGetInfoOfHRSOAP11port_http")
    public ItfGetInfoOfHRPortType getItfGetInfoOfHRSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(ItfGetInfoOfHRSOAP11PortHttp, ItfGetInfoOfHRPortType.class, features);
    }

}
