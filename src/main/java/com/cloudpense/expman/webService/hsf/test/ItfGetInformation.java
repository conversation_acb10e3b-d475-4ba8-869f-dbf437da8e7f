
package com.cloudpense.expman.webService.hsf.test;

import javax.xml.namespace.QName;
import javax.xml.ws.*;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "ItfGetInformation", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", wsdlLocation = "http://220.174.235.27:91/uapws/service/ItfGetInformation?wsdl")
public class ItfGetInformation
    extends Service
{

    private final static URL ITFGETINFORMATION_WSDL_LOCATION;
    private final static WebServiceException ITFGETINFORMATION_EXCEPTION;
    private final static QName ITFGETINFORMATION_QNAME = new QName("http://getinfo.hna.plg.itf.nc/ItfGetInformation", "ItfGetInformation");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("http://220.174.235.27:91/uapws/service/ItfGetInformation?wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        ITFGETINFORMATION_WSDL_LOCATION = url;
        ITFGETINFORMATION_EXCEPTION = e;
    }

    public ItfGetInformation() {
        super(__getWsdlLocation(), ITFGETINFORMATION_QNAME);
    }

    public ItfGetInformation(WebServiceFeature... features) {
        super(__getWsdlLocation(), ITFGETINFORMATION_QNAME, features);
    }

    public ItfGetInformation(URL wsdlLocation) {
        super(wsdlLocation, ITFGETINFORMATION_QNAME);
    }

    public ItfGetInformation(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, ITFGETINFORMATION_QNAME, features);
    }

    public ItfGetInformation(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public ItfGetInformation(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    private static URL __getWsdlLocation() {
        if (ITFGETINFORMATION_EXCEPTION!= null) {
            throw ITFGETINFORMATION_EXCEPTION;
        }
        return ITFGETINFORMATION_WSDL_LOCATION;
    }

    /**
     *
     * @return
     *     returns ItfGetInformationPortType
     */
    @WebEndpoint(name = "ItfGetInformationSOAP11port_http")
    public ItfGetInformationPortType getItfGetInformationSOAP11PortHttp() {
        return super.getPort(new QName("http://getinfo.hna.plg.itf.nc/ItfGetInformation", "ItfGetInformationSOAP11port_http"), ItfGetInformationPortType.class);
    }

    /**
     *
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns ItfGetInformationPortType
     */
    @WebEndpoint(name = "ItfGetInformationSOAP11port_http")
    public ItfGetInformationPortType getItfGetInformationSOAP11PortHttp(WebServiceFeature... features) {
        return super.getPort(new QName("http://getinfo.hna.plg.itf.nc/ItfGetInformation", "ItfGetInformationSOAP11port_http"), ItfGetInformationPortType.class, features);
    }

}
