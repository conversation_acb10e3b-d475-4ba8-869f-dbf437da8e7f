
package com.cloudpense.expman.webService.hsf;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.*;


@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "_return"
})
@XmlRootElement(name = "getDefDocInfoResponse")
public class GetDefDocInfoResponse {

    @XmlElementRef(name = "return", namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", type = JAXBElement.class, required = false)
    protected JAXBElement<String> _return;

    /**
     * @return possible object is
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public JAXBElement<String> getReturn() {
        return _return;
    }

    /**
     * @param value allowed object is
     *              {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public void setReturn(JAXBElement<String> value) {
        this._return = value;
    }

}
