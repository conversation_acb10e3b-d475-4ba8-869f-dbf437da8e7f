
package com.cloudpense.expman.webService.hsf;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.*;


/**
 * <p>anonymous complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="xmljson" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "xmljson"
})
@XmlRootElement(name = "getDefDocInfo")
public class GetDefDocInfo {

    @XmlElementRef(name = "xmljson", namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", type = JAXBElement.class, required = false)
    protected JAXBElement<String> xmljson;

    /**
     * 获取xmljson属性的值。
     *
     * @return possible object is
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public JAXBElement<String> getXmljson() {
        return xmljson;
    }

    /**
     * 设置xmljson属性的值。
     *
     * @param value allowed object is
     *              {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public void setXmljson(JAXBElement<String> value) {
        this.xmljson = value;
    }

}
