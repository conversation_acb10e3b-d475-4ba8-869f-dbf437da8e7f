
package com.cloudpense.expman.webService.hsf;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebService(name = "ItfGetInformationPortType", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
@XmlSeeAlso({
        ObjectFactory.class
})
public interface ItfGetInformationPortType {


    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getFctInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getFctInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetFctInfo")
    @ResponseWrapper(localName = "getFctInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetFctInfoResponse")
    public String getFctInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getDefDocInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getDefDocInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetDefDocInfo")
    @ResponseWrapper(localName = "getDefDocInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetDefDocInfoResponse")
    public String getDefDocInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getDefDocListInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getDefDocListInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetDefDocListInfo")
    @ResponseWrapper(localName = "getDefDocListInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetDefDocListInfoResponse")
    public String getDefDocListInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getPsnbankInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getPsnbankInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetPsnbankInfo")
    @ResponseWrapper(localName = "getPsnbankInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetPsnbankInfoResponse")
    public String getPsnbankInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getSupplierInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getSupplierInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetSupplierInfo")
    @ResponseWrapper(localName = "getSupplierInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetSupplierInfoResponse")
    public String getSupplierInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

    /**
     * @param xmljson
     * @return returns java.lang.String
     */
    @WebMethod(action = "urn:getInoutbusiclassInfo")
    @WebResult(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
    @RequestWrapper(localName = "getInoutbusiclassInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetInoutbusiclassInfo")
    @ResponseWrapper(localName = "getInoutbusiclassInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation", className = "nc.itf.plg.hna.getinfo.GetInoutbusiclassInfoResponse")
    public String getInoutbusiclassInfo(
            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInformation")
                    String xmljson);

}
