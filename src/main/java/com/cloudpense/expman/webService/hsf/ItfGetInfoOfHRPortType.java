package com.cloudpense.expman.webService.hsf;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.ws.Action;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;

/**
 * This class was generated by Apache CXF 3.4.1
 * 2020-12-29T17:06:37.314+08:00
 * Generated source version: 3.4.1
 *
 */
@WebService(targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "ItfGetInfoOfHRPortType")
@XmlSeeAlso({ObjectFactory.class})
public interface ItfGetInfoOfHRPortType {

    @WebMethod(action = "urn:getOrgInfo")
    @Action(input = "urn:getOrgInfo", output = "urn:getOrgInfoResponse")
    @RequestWrapper(localName = "getOrgInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetOrgInfo")
    @ResponseWrapper(localName = "getOrgInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetOrgInfoResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getOrgInfo(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );

    @WebMethod(action = "urn:getPostInfo")
    @Action(input = "urn:getPostInfo", output = "urn:getPostInfoResponse")
    @RequestWrapper(localName = "getPostInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPostInfo")
    @ResponseWrapper(localName = "getPostInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPostInfoResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getPostInfo(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );

    @WebMethod(action = "urn:getDeptInfo")
    @Action(input = "urn:getDeptInfo", output = "urn:getDeptInfoResponse")
    @RequestWrapper(localName = "getDeptInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetDeptInfo")
    @ResponseWrapper(localName = "getDeptInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetDeptInfoResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getDeptInfo(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );

    @WebMethod(operationName = "getPsnInfo_A", action = "urn:getPsnInfo_A")
    @Action(input = "urn:getPsnInfo_A", output = "urn:getPsnInfo_AResponse")
    @RequestWrapper(localName = "getPsnInfo_A", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPsnInfoA")
    @ResponseWrapper(localName = "getPsnInfo_AResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPsnInfoAResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getPsnInfoA(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );

    @WebMethod(action = "urn:getPsnInfo")
    @Action(input = "urn:getPsnInfo", output = "urn:getPsnInfoResponse")
    @RequestWrapper(localName = "getPsnInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPsnInfo")
    @ResponseWrapper(localName = "getPsnInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetPsnInfoResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getPsnInfo(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );

    @WebMethod(action = "urn:getJobGradeInfo")
    @Action(input = "urn:getJobGradeInfo", output = "urn:getJobGradeInfoResponse")
    @RequestWrapper(localName = "getJobGradeInfo", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetJobGradeInfo")
    @ResponseWrapper(localName = "getJobGradeInfoResponse", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", className = "nc.itf.plg.hna.getinfo.GetJobGradeInfoResponse")
    @WebResult(name = "return", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
    public String getJobGradeInfo(

            @WebParam(name = "xmljson", targetNamespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR")
                    String xmljson
    );
}
