
package com.cloudpense.expman.webService.hsf.prd;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the nc.itf.plg.hna.getinfo package. 
 * &lt;p&gt;An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _GetJobGradeInfoXmljson_QNAME = new QName("http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", "xmljson");
    private final static QName _GetJobGradeInfoResponseReturn_QNAME = new QName("http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", "return");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: nc.itf.plg.hna.getinfo
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link GetJobGradeInfo }
     * 
     */
    public GetJobGradeInfo createGetJobGradeInfo() {
        return new GetJobGradeInfo();
    }

    /**
     * Create an instance of {@link GetJobGradeInfoResponse }
     * 
     */
    public GetJobGradeInfoResponse createGetJobGradeInfoResponse() {
        return new GetJobGradeInfoResponse();
    }

    /**
     * Create an instance of {@link GetPostInfo }
     * 
     */
    public GetPostInfo createGetPostInfo() {
        return new GetPostInfo();
    }

    /**
     * Create an instance of {@link GetPostInfoResponse }
     * 
     */
    public GetPostInfoResponse createGetPostInfoResponse() {
        return new GetPostInfoResponse();
    }

    /**
     * Create an instance of {@link GetOrgInfo }
     * 
     */
    public GetOrgInfo createGetOrgInfo() {
        return new GetOrgInfo();
    }

    /**
     * Create an instance of {@link GetOrgInfoResponse }
     * 
     */
    public GetOrgInfoResponse createGetOrgInfoResponse() {
        return new GetOrgInfoResponse();
    }

    /**
     * Create an instance of {@link GetDeptInfo }
     * 
     */
    public GetDeptInfo createGetDeptInfo() {
        return new GetDeptInfo();
    }

    /**
     * Create an instance of {@link GetDeptInfoResponse }
     * 
     */
    public GetDeptInfoResponse createGetDeptInfoResponse() {
        return new GetDeptInfoResponse();
    }

    /**
     * Create an instance of {@link GetPsnInfo }
     * 
     */
    public GetPsnInfo createGetPsnInfo() {
        return new GetPsnInfo();
    }

    /**
     * Create an instance of {@link GetPsnInfoResponse }
     * 
     */
    public GetPsnInfoResponse createGetPsnInfoResponse() {
        return new GetPsnInfoResponse();
    }

    /**
     * Create an instance of {@link GetPsnInfoA }
     * 
     */
    public GetPsnInfoA createGetPsnInfoA() {
        return new GetPsnInfoA();
    }

    /**
     * Create an instance of {@link GetPsnInfoAResponse }
     * 
     */
    public GetPsnInfoAResponse createGetPsnInfoAResponse() {
        return new GetPsnInfoAResponse();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetJobGradeInfo.class)
    public JAXBElement<String> createGetJobGradeInfoXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetJobGradeInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetJobGradeInfoResponse.class)
    public JAXBElement<String> createGetJobGradeInfoResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetJobGradeInfoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetPostInfo.class)
    public JAXBElement<String> createGetPostInfoXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetPostInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetPostInfoResponse.class)
    public JAXBElement<String> createGetPostInfoResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetPostInfoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetOrgInfo.class)
    public JAXBElement<String> createGetOrgInfoXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetOrgInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetOrgInfoResponse.class)
    public JAXBElement<String> createGetOrgInfoResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetOrgInfoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetDeptInfo.class)
    public JAXBElement<String> createGetDeptInfoXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetDeptInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetDeptInfoResponse.class)
    public JAXBElement<String> createGetDeptInfoResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetDeptInfoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetPsnInfo.class)
    public JAXBElement<String> createGetPsnInfoXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetPsnInfo.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetPsnInfoResponse.class)
    public JAXBElement<String> createGetPsnInfoResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetPsnInfoResponse.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "xmljson", scope = GetPsnInfoA.class)
    public JAXBElement<String> createGetPsnInfoAXmljson(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoXmljson_QNAME, String.class, GetPsnInfoA.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     * 
     * @param value
     *     Java instance representing xml element's value.
     * @return
     *     the new instance of {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    @XmlElementDecl(namespace = "http://getinfo.hna.plg.itf.nc/ItfGetInfoOfHR", name = "return", scope = GetPsnInfoAResponse.class)
    public JAXBElement<String> createGetPsnInfoAResponseReturn(String value) {
        return new JAXBElement<String>(_GetJobGradeInfoResponseReturn_QNAME, String.class, GetPsnInfoAResponse.class, value);
    }

}
