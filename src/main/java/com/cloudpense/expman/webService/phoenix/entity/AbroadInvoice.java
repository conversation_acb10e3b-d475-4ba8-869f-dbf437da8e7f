package com.cloudpense.expman.webService.phoenix.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class AbroadInvoice {
//    @JsonProperty(value = "column34")
    public String iPortalNum;
//    @JsonProperty(value = "branch_id")
    public String companyCode;
//    @JsonProperty(value = "submit_user")
    public String proposerCode;
//    @JsonProperty(value = "submit_department")
    public String proposerDepartment;
    public String proposerCostcenter;
//    @JsonProperty(value = "column17")
    public String destinationCountry;
//    @JsonProperty(value = "column37")
    public String destinationCity;
//    @JsonProperty(value = "column18")
    public String retourCountry;
//    @JsonProperty(value = "column19")
    public String retourCity;
//    @JsonProperty(value = "start_datetime")
    public String departTime;
//    @JsonProperty(value = "column23")
    public String arriveTime;
//    @JsonProperty(value = "column24")
    public String leaveTime;
//    @JsonProperty(value = "end_datetime")
    public String backHomeTime;
//    @JsonProperty(value = "column20")
    public String holidayBegins;
//    @JsonProperty(value = "column21")
    public String holidayEnds;
    /**
     * 出国补助天数
     */
//    @JsonProperty(value = "quantity")
    public String abroadDays;
//    @JsonProperty(value = "column22")
    public String abroadRegion;
    /**
     * 出国补助金额
     */
//    @JsonProperty(value = "receipt_amount")
    public String abroadSubsidy;
    /**
     * 出国补助类别
     */
//    @JsonProperty(value = "column33")
    public String abroadCategory;
//    @JsonProperty(value = "receipt_currency")
    public String currency;


}
