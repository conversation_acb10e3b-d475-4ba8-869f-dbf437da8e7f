package com.cloudpense.expman.webService.phoenix.entity;


import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * <AUTHOR>
 */
public class PromotionalToolsInvoice {
//    @JsonProperty(value = "column34")
    public	String	 PTANum;
//    @JsonProperty(value = "branch_id")
    public	String	companyName;
//    @JsonProperty(value = "")
    public	String	proposerName;
//    @JsonProperty(value = "submit_user")
    public	String	proposerCode;
//    @JsonProperty(value = "submit_department")
    public	String	proposerDepartment;
//    @JsonProperty(value = "")
    public	String	proposerCostcenter;
//    @JsonProperty(value = "column13")
    public	String	isMarketActivity;
//    @JsonProperty(value = "column14")
    public	String	marketActivityCode;
    @JsonProperty(value = "")
    public	String	project;
//    @JsonProperty(value = "total_amount")
    public	String	totalAmount;
//    @JsonProperty(value = "column27")
    public	String	poNum;
//    @JsonProperty(value = "")
    public	String	status;
//    @JsonProperty(value = "claim_line_group")
    public  PromotionalToolsGroup promotionalToolsGroup;

}
