package com.cloudpense.expman.webService.phoenix.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 */
public class IndirectPurchaseInvoice {
//    @JsonProperty(value = "column34")
    public String iprNum;
//    @JsonProperty(value = "branch_id")
    public String companyName;
    @JsonProperty(value = "")
    public String proposerName;
//    @JsonProperty(value = "submit_user")
    public String proposerCode;
//    @JsonProperty(value = "submit_department")
    public String proposerDepartment;
    @JsonProperty(value = "")
    public String proposerCostcenter;
    @JsonProperty(value = "")
    public String truePayerName;
//    @JsonProperty(value = "charge_user")
    public String truePayerCode;
    @JsonProperty(value = "")
    public String truePayerDepartment;
//    @JsonProperty(value = "charge_department")
    public String truePayerCostcenter;
//    @JsonProperty(value = "column13")
    public String isMarketActivity;
//    @JsonProperty(value = "column14")
    public String marketActivityCode;
//    @JsonProperty(value = "column25")
    public String firstCategory;
//    @JsonProperty(value = "column26")
    public String secondCategory;
//    @JsonProperty(value = "total_amount")
    public String totalAmountOfTax;
//    @JsonProperty(value = "column27")
    public String poNum;
//    @JsonProperty(value = "")
    public String isInStock;
//    @JsonProperty(value = "")
    public String status;
    /**
     * 供应商编码(新加)
     */
    public String supplierCode;

//    @JsonProperty(value = "claim_line_group")
    public IndirectPurchaseGroup indirectPurchaseGroup;
}
