package com.cloudpense.expman.webService.phoenix;

import cn.hutool.core.util.StrUtil;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.util.TranslationUtil;
import com.cloudpense.expman.webService.phoenix.entity.AbroadInvoice;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import com.fasterxml.jackson.databind.ObjectMapper;
import javax.jws.WebMethod;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public class AbroadApplicationImpl implements AbroadApplication {
    private static final Logger logger = LoggerFactory.getLogger(AbroadApplicationImpl.class);
    @Autowired
    private PhoenixMapper mapper;
    @Autowired
    private TranslationUtil translationUtil;

    private static final String ERROR ="E";
    @Override
    @WebMethod
    public ForPhoenixResponse abroadApplication( AbroadInvoice abroadInvoice)  {
        String LOG_KEY = "abroadApplication===";
//        setDefaultValue(abroadInvoice);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString="";
        try {
             jsonString= objectMapper.writeValueAsString(abroadInvoice);
             logger.info("{}入参={}", LOG_KEY, jsonString);
        } catch (JsonProcessingException e) {
//            e.printStackTrace();
              logger.error("JsonProcessingException==>", e);
        }
        FndQuery fndQuery = new FndQuery();
        fndQuery.setInput(jsonString);
        fndQuery.setType("Abroad");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        mapper.savePhoenixInvoice(fndQuery);
        ForPhoenixResponse forPhoenixResponse=new ForPhoenixResponse();
        logger.info("returncode==>" + fndQuery.getReturnCode()+ "returnmessage==>"+fndQuery.getReturnMessage());
        String resultCode = "Y";
        String resultMessage = "success！";
        if (ERROR.equals(fndQuery.getReturnCode())) {
            resultCode = "E";
            resultMessage =  translationUtil.getMessage(resultCode,fndQuery.getReturnMessage(),"zh_CN");
        }
        forPhoenixResponse.RESPONSE_STATUS = resultCode;
        forPhoenixResponse.RESPONSE_MESSAGE =resultMessage;

        return forPhoenixResponse;
    }

    private void setDefaultValue(AbroadInvoice abroadInvoice) {
        if(StrUtil.isEmpty(abroadInvoice.arriveTime)) {
            abroadInvoice.arriveTime = null;
        }
        if(StrUtil.isEmpty(abroadInvoice.leaveTime)) {
            abroadInvoice.leaveTime = null;
        }
        if(StrUtil.isEmpty(abroadInvoice.holidayBegins)) {
            abroadInvoice.holidayBegins = null;
        }
        if(StrUtil.isEmpty(abroadInvoice.holidayEnds)) {
            abroadInvoice.holidayEnds = null;
        }
    }


    public static void main(String[] s){}

}
