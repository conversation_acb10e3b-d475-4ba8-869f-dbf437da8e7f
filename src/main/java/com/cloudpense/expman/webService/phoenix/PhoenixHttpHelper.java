package com.cloudpense.expman.webService.phoenix;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.exception.FormatException;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Map;

public class PhoenixHttpHelper {
    private static final Logger logger = LoggerFactory.getLogger(PhoenixHttpHelper.class);
    public static String PhoenixHttpPostIBMP(String url, Map<String, String> headers, JSONObject data) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");

        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }

        try {
            StringEntity requestEntity = new StringEntity(JSON.toJSONString(data), "utf-8");
            httpPost.setEntity(requestEntity);

            response = httpClient.execute(httpPost, new BasicHttpContext());
            if (response.getStatusLine().getStatusCode() != 200) {

                logger.info("iBMP request url failed, http code==>" + + response.getStatusLine().getStatusCode()
                        + ", url==>" + url);
                return null;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");

                return resultStr;
            }
        } catch (IOException e) {
            logger.info("request url==>" + url + ", exception, msg==>" + e.getMessage());
            logger.error("IOException==>", e);
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("IOException==>", e);
            }
        }

        return null;
    }

    public static String PhoenixHttpPostString(String url, Map<String, String> headers, String data) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(60000).setConnectTimeout(60000).build();
        httpPost.setConfig(requestConfig);

        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }

        try {
            StringEntity requestEntity = new StringEntity(data, "utf-8");
            httpPost.setEntity(requestEntity);

            response = httpClient.execute(httpPost, new BasicHttpContext());
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.info("request url failed, http code==>" + response.getStatusLine().getStatusCode()
                        + ", url==>" + url);
                return null;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");

                return resultStr;
            }
        } catch (IOException e) {
            logger.info("request url==>" + url + ", exception, msg==>" + e.getMessage());
            logger.error("IOException==>", e);
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("IOException==>", e);
            }
        }

        return null;
    }
}
