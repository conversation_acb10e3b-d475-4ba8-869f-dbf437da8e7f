package com.cloudpense.expman.webService.phoenix.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PromotionalToolsLines {
//    @JsonProperty(value = "type_id")
    public	String	subProject;
//    @JsonProperty(value = "comments")
    public	String	toolsName;
//    @JsonProperty(value = "quantity")
    public	String	toolsQuantity;
//    @JsonProperty(value = "receipt_amount")
    public	String	amountOfMoney;
//    @JsonProperty(value = "column50")
    public String lineNumber;
}
