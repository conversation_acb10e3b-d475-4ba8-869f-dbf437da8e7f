package com.cloudpense.expman.webService.phoenix;

import com.cloudpense.expman.webService.phoenix.entity.IndirectPurchaseInvoice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public interface IndirectPurchaseApplication {
    /**
     * MRO采购申请
     * @param indirectPurchaseInvoice  MRO采购申请单据
     * @return
     */
    @WebMethod
    public ForPhoenixResponse indirectPurchaseApplication(@WebParam(name = "IndirectPurchaseInvoice") IndirectPurchaseInvoice indirectPurchaseInvoice);
}
