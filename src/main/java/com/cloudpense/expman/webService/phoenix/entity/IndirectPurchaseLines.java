package com.cloudpense.expman.webService.phoenix.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

public class IndirectPurchaseLines {
//    @JsonProperty(value = "type_id")
    public String typeOfExpense;
//    @JsonProperty(value = "column6")
    public String thirdCategory;
//    @JsonProperty(value = "column7")
    public String specification;

    public String quantity;
//    @JsonProperty(value = "column8")
    public String pretaxPrice;
//    @JsonProperty(value = "tax_code_id")
    public String taxRate;
//    @JsonProperty(value = "receipt_amount")
    public String amountOfMoney;
//    @JsonProperty(value = "column50")
    public String lineNumber;
//    @JsonProperty(value = "column27")
    public String po_column27;
}
