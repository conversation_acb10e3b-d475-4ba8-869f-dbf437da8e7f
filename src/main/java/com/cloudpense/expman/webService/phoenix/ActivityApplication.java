package com.cloudpense.expman.webService.phoenix;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.util.TranslationUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ActivityApplication {
    private static final Logger logger = LoggerFactory.getLogger(ActivityApplication.class);
    @Autowired
    private PhoenixMapper mapper;
    @Autowired
    private TranslationUtil translationUtil;
    private static final String ERROR ="E";
    private  static  final String USER_NAME = "PhoenixAdmin";
    private  static  final String PASS_WORD = "phoenix1928";
    public ForPhoenixResponse activityApplication(JSONObject jsonObject,String username,String password){
        final String KEYNAME = "菲尼克斯activity===";
        logger.info("{}服务请求入参={}", KEYNAME, jsonObject.toJSONString());
        ForPhoenixResponse forPhoenixResponse=new ForPhoenixResponse();
        String resultCode ;
        String resultMessage ;
        if(verify(username,password)) {
            jsonObject = turnsJson(jsonObject);
            FndQuery fndQuery = new FndQuery();
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("Activity");
            logger.info("{}调用存储过程处理请求={}", KEYNAME, JSONObject.toJSONString(fndQuery));
            mapper.savePhoenixInvoice(fndQuery);
            logger.info("{}调用存储过程处理响应={}", KEYNAME, JSONObject.toJSONString(fndQuery));
            if (ERROR.equals(fndQuery.getReturnCode())) {
                resultCode = "E";
                resultMessage = translationUtil.getMessage(resultCode, fndQuery.getReturnMessage(), "zh_CN");
            }else {
                resultCode = "S";
                resultMessage = fndQuery.getReturnMessage();
            }
        }else {
            resultCode ="E";
            resultMessage="验证错误！";
        }
        forPhoenixResponse.RESPONSE_STATUS = resultCode;
        forPhoenixResponse.RESPONSE_MESSAGE =resultMessage;
        logger.info("{}处理结果={}", KEYNAME, JSONObject.toJSONString(forPhoenixResponse));
        return forPhoenixResponse;
    }

    private boolean verify(String username,String password){

        return username.equals(USER_NAME) && password.equals(PASS_WORD);

    }

    private JSONObject turnsJson(JSONObject jsonObjectP){
        JSONObject jsonObjectJ = new JSONObject();
        jsonObjectJ.put("column34",jsonObjectP.getString("IBMPId"));
        jsonObjectJ.put("column14",jsonObjectP.getString("activityCode"));
        jsonObjectJ.put("activity_type",jsonObjectP.getString("activityCategory"));
        jsonObjectJ.put("column35",jsonObjectP.getString("superiorActivityCode"));
        jsonObjectJ.put("column15",jsonObjectP.getString("activityName"));
        jsonObjectJ.put("submit_user",jsonObjectP.getString("proposerCode"));
        jsonObjectJ.put("start_datetime",jsonObjectP.getString("startDate"));
        jsonObjectJ.put("end_datetime",jsonObjectP.getString("endsDate"));
        jsonObjectJ.put("total_amount",jsonObjectP.getString("budget"));
        jsonObjectJ.put("column36",jsonObjectP.getString("status"));

        jsonObjectJ.put("column16",jsonObjectP.getString("location"));
        jsonObjectJ.put("column9",jsonObjectP.getString("goal"));
        JSONArray jsonArray = new JSONArray();
        JSONArray old = jsonObjectP.getJSONArray("lines");
        for (Object o : old) {
            JSONObject obj = (JSONObject) o;
            JSONObject result = new JSONObject();
            result.put("column50",obj.getString("lineNumber"));
            result.put("type_id",obj.getString("expenseType"));
            result.put("receipt_amount",obj.getString("budget"));
            result.put("comments",obj.getString("tips"));
            jsonArray.add(result);
        }
        jsonObjectJ.put("lines", jsonArray);




        return   jsonObjectJ;
    }

    public  static void main(String[] s){
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1 = JSONObject.parseObject("");
        logger.info("jsonObject1==>" + jsonObject1);
        jsonObject1 =new ActivityApplication().turnsJson(jsonObject1);
        logger.info("jsonObject1==>" + jsonObject1);
    }
}
