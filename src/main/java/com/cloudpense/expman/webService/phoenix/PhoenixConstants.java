package com.cloudpense.expman.webService.phoenix;

import java.util.ResourceBundle;

public class PhoenixConstants {

    public static ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    public static final String projectUrl = rb.getString("projectUrl");
    public static final String projectOauthUrl = rb.getString("projectOauthUrl");
//    public static final String basicUrl = "http://192.168.0.76";
//    public static final String basicUrl = "http://218.94.100.195:8081";
    public static final String basicUrl = "https://smartexpense.phoenixcontact.com.cn";

    public static final int phoenixCompanyId = 11052;

    public static final String iBMPUrl = basicUrl + "/cp/iBmpPost";
//    public static final String iBMPUrl = "https://connection.phoenixcontact.com.cn:18016/phoenix/invokeMethod";

    public static final String iBMPUserName = "cloudpense";

    public static final String iBMPKey = "c4ca4238a0b923820dcc509a6f75849b";

//    public static final String iPortalUrl = basicUrl + "/cp/iPortalApprove";

//    public static final String iPortalTravelUrl = basicUrl + "/cp/iPortalPost";

    public static final String iPortalTodoUrl = basicUrl + "/cp/iPortalPush";

    public static final String sapPostUrl = basicUrl + "/cp/sapdocument";

    public static final String sapUserName = "exiportal_r";

    public static final String sapKey = "KJxV2Cqldx";

    public static final String sapHash = "Basic ZXhpcG9ydGFsX3I6S0p4VjJDcWxkeA==";

    public static final String HCMUserUrl = basicUrl + "/cp/user";
    public static final String HCMCostCenterUrl = basicUrl + "/cp/costcenter";
    public static final String HCMOrganUrl = basicUrl + "/cp/organ";
    public static final String HCMUserName = "WS_COST_CTRL";
    public static final String HCMPassword = "HeRo1234";

    public static final String ftpUrl = basicUrl+"/tomcat/phoenix/write1";

    public static final String FTPGetUrl = basicUrl + "/tomcat/phoenix/report";

    public static final String FTPPutUrl = basicUrl + "/tomcat/write";

    public static final String baanRulesPath = "./BAAN/main_data/";

    public static final String supplierPath = "./BI/main_data/";

    public static final String voucherPath = "./BI/voucher/";

    public static final String hrSystemGetAnnualLeaveBatch = basicUrl + "/cp/hrleaveQuery";

    public static final String hrSystemUpdateLeave = basicUrl + "/cp/hrleaveUpdate";

    public static final String hrIdentityKey1 = "api_gateway_auth_app_id";
    public static final String hrIdentityValue1 = "52366059-969d-4162-8010-b97c4a9ec40a";
    public static final String hrIdentityKey2 = "api_gateway_auth_app_password";
    public static final String hrIdentityValue2 = "Phoenix123";
}
