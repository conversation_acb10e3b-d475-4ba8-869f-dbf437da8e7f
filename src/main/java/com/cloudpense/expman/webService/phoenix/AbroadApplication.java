package com.cloudpense.expman.webService.phoenix;

import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.webService.phoenix.entity.AbroadInvoice;

import javax.jws.WebParam;
import javax.jws.WebService;

/**
 * 出国申请
 * <AUTHOR>
 */
@WebService
public interface AbroadApplication {
    /**
     * 提供出国申请的接口
     * @param abroadInvoice
     * @return
     */
    public ForPhoenixResponse abroadApplication(@WebParam(name = "AbroadInvoice") AbroadInvoice abroadInvoice) throws ValidationException;
}
