package com.cloudpense.expman.webService.phoenix.Interceptor;

import org.apache.cxf.binding.soap.interceptor.SoapHeaderInterceptor;
import org.apache.cxf.configuration.security.AuthorizationPolicy;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.message.Message;
import org.slf4j.LoggerFactory;

import java.net.HttpURLConnection;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * 菲尼克斯webservice接口-->密码设置
 */
public class PhoenixAuthInterceptor extends SoapHeaderInterceptor {
    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(PhoenixAuthInterceptor.class);
    private static final String USER_NAME = "PhoenixAdmin";
    private static final String REAL_PASSWORD = "phoenix1928";
    protected Logger log = Logger.getLogger("logger");
    @Override
    public void handleMessage(Message message) throws Fault{
        AuthorizationPolicy policy = message.get(AuthorizationPolicy.class);
        logger.info("Policy==>" + policy);

        if (policy == null) {
            logger.info("UserName and Password are null!");
            sendErrorResponse(message, HttpURLConnection.HTTP_UNAUTHORIZED);
            return;
        }
        logger.info("getUserName==>"+policy.getUserName()+"\n"+
                "getPassword==>"+policy.getPassword());
        if (!USER_NAME.equals(policy.getUserName()) || !REAL_PASSWORD.equals(policy.getPassword())) {
            sendErrorResponse(message, HttpURLConnection.HTTP_FORBIDDEN);
        }

    }

    private void sendErrorResponse(Message message, int responseCode) {
        logger.info("sendErrorMessage...................");
        throw new Fault("error password", log);
    }
}
