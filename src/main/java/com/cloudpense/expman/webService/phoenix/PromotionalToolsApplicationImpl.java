package com.cloudpense.expman.webService.phoenix;


import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.util.TranslationUtil;
import com.cloudpense.expman.webService.phoenix.entity.PromotionalToolsInvoice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jws.WebMethod;
;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public class PromotionalToolsApplicationImpl implements PromotionalToolsApplication {
    private static final Logger logger = LoggerFactory.getLogger(PromotionalToolsApplicationImpl.class);
    @Autowired
    private PhoenixMapper mapper;
    @Autowired
    private TranslationUtil translationUtil;

    private static final String ERROR ="E";
    @WebMethod
    @Override
    public ForPhoenixResponse promotionalToolsApplication( PromotionalToolsInvoice promotionalToolsInvoice) {
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString="";
        try {
            jsonString= objectMapper.writeValueAsString(promotionalToolsInvoice);
        } catch (JsonProcessingException e) {
            logger.error("JsonProcessingException==>", e);
        }
        logger.info("jsonString==>" + jsonString);
        FndQuery fndQuery = new FndQuery();
        fndQuery.setInput(jsonString);
        fndQuery.setType("PromotionalTools");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        mapper.savePhoenixInvoice(fndQuery);
        logger.info("促销工具采购申请==>" + jsonString);
        String resultCode ;
        String resultMessage ;
        ForPhoenixResponse forPhoenixResponse=new ForPhoenixResponse();
        if (ERROR.equals(fndQuery.getReturnCode())) {
            resultCode = ERROR;
            resultMessage =  translationUtil.getMessage(resultCode,fndQuery.getReturnMessage(),"zh_CN");
        } else {
            resultCode = "S";
            resultMessage = fndQuery.getReturnMessage();
        }
        forPhoenixResponse.RESPONSE_STATUS = resultCode;
        forPhoenixResponse.RESPONSE_MESSAGE =resultMessage;
        logger.info("促销==>" + JSONObject.toJSONString(forPhoenixResponse));
        return forPhoenixResponse;
    }


}
