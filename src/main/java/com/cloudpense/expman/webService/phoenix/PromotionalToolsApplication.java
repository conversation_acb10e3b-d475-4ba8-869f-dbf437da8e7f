package com.cloudpense.expman.webService.phoenix;


import com.cloudpense.expman.webService.phoenix.entity.PromotionalToolsInvoice;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public interface PromotionalToolsApplication {
    /**
     * 促销工具采购申请
     * @param promotionalToolsInvoice 申请单据
     * @return
     */
    @WebMethod
    public ForPhoenixResponse promotionalToolsApplication(@WebParam(name = "PromotionalToolsInvoice") PromotionalToolsInvoice promotionalToolsInvoice);

}
