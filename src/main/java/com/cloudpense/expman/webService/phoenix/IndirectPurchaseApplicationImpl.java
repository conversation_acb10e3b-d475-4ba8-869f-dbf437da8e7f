package com.cloudpense.expman.webService.phoenix;


import com.alibaba.fastjson.JSON;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.util.TranslationUtil;
import com.cloudpense.expman.webService.phoenix.entity.IndirectPurchaseInvoice;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.jws.WebMethod;
import javax.jws.WebService;

/**
 * <AUTHOR>
 */
@WebService
public class IndirectPurchaseApplicationImpl implements IndirectPurchaseApplication {
    private static final Logger logger = LoggerFactory.getLogger(IndirectPurchaseApplicationImpl.class);
    @Autowired
    private PhoenixMapper mapper;
    @Autowired
    private TranslationUtil translationUtil;

    private static final String ERROR ="E";
    @Override
    @WebMethod
    public ForPhoenixResponse indirectPurchaseApplication( IndirectPurchaseInvoice indirectPurchaseInvoice) {
        String LOG_KEY = "iportal采购申请单创建===";
        logger.info("{}开始执行", LOG_KEY);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString="";
        ForPhoenixResponse forPhoenixResponse=new ForPhoenixResponse();
        try {
            jsonString= objectMapper.writeValueAsString(indirectPurchaseInvoice);
            logger.info("{}入参={}", LOG_KEY, jsonString);
            FndQuery fndQuery = new FndQuery();
            fndQuery.setInput(jsonString);
            fndQuery.setType("IndirectPurchase");
            CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
            mapper.savePhoenixInvoice(fndQuery);
            logger.info("{}调用procedure处理结果={}", LOG_KEY, JSON.toJSONString(fndQuery));
            String resultCode ;
            String resultMessage ;

            if (ERROR.equals(fndQuery.getReturnCode())) {
                resultCode = "E";
                resultMessage =  translationUtil.getMessage(resultCode,fndQuery.getReturnMessage(),"zh_CN");
            } else {
                resultCode = "S";
                resultMessage = fndQuery.getReturnMessage();
            }
            forPhoenixResponse.RESPONSE_STATUS = resultCode;
            forPhoenixResponse.RESPONSE_MESSAGE = resultMessage;
            logger.info("{}创建结果={}", LOG_KEY, JSON.toJSONString(forPhoenixResponse));
        } catch (JsonProcessingException e) {
            logger.error("{}入参转换异常：", LOG_KEY, e);
            forPhoenixResponse.RESPONSE_STATUS = "E";
            forPhoenixResponse.RESPONSE_MESSAGE = e.getMessage();
        } catch (Exception e) {
            logger.error("{}出现未知异常：", LOG_KEY, e);
            forPhoenixResponse.RESPONSE_STATUS = "E";
            forPhoenixResponse.RESPONSE_MESSAGE = e.getMessage();
        }

        return forPhoenixResponse;
    }
}
