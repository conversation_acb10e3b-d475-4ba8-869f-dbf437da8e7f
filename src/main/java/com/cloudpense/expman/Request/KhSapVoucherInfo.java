package com.cloudpense.expman.Request;

import lombok.Data;

import java.util.Date;

/**
 * Created by wang<PERSON><PERSON><PERSON> on 2019/9/18.
 * 科华凭证头信息
 */
@Data
public class KhSapVoucherInfo {

    //单据id
    private int headerId;
    //单据类型
    private String typeCode;
    //单据号
    private String documentNum;
    //公司代码
    private String companyCode;
    //提交日期
    private Date submitDate;
    //币种
    private String currencyCode;
    //记账码为29、50、39、40的行文本
    private String column10;
    //供应商编码
    private String column3;
    //成本中心编码（分公司代码取前四位）
    private String departmentCode;
    // 分公司编码
    private String branchCode;
    //员工姓名
    private String fullName;
    //员工编码
    private String employeeNumber;
    //科目
    private String accountCode;
    //总支付金额（头）
    private double totalPayAmount;
    private double totalPayCurrencyAmount;
    //本币支付金额（行）
    private double finClaimAmount;
    private double finReceiptAmount;
    //本币支付金额（头）
    private double totalAmount;
    //冲账金额（头）
    private double advanceAmount;

    private String leaveType;

    //税额 （行）
    private double finTaxAmount;
    //借方科目代码
    private String drAccountCode;
    //贷方科目代码
    private String crAccountCode;
    //税方科目代码
    private String taxAccountCode;
    //是否预制凭证 （头）
    private String column1;
    //税转出金额 （行）
    private String column6;
    //非应税转出金额 （行）
    private String column7;
    //供应商代码（头）
    private String supplierId;
    //成本中心（charge_department_column1）
    private String cdColumn1;
    //税码（行）
    private String taxCode;
    //税基额，不含税金额 （行）
    private double finNetAmount;
    //原因代码（column51）
    private String columnJson;
    //供应商代码
    private String supplierCode;
    //付款、预付款类型(头)
    private String headerInternalType;
    //行上internalType (行)
    private String lineInternalType;
    //发票号码（行）
    private String invoiceNum;
    //费用类型 （行）
    private String lineType;
    //供应商名称 （头）
    private String supplierName;
    //摘要 （头）
    private String column49;
    //总账日期、过账日期
    private Date glDate;
    //内部订单号
    private String projectName;
    //submit_user对应员工全名
    private String suFullName;
    //描述(行)
    private String comments;
    //付款方
    private String column13;
    //
    private String receiptCurrency;
}
