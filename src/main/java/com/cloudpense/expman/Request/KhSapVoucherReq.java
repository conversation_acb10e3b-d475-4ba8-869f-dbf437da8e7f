package com.cloudpense.expman.Request;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * Created by wa<PERSON><PERSON><PERSON><PERSON> on 2019/9/18.
 * 科华sap凭证接口请求实体
 */
public class KhSapVoucherReq {

    //charge_department 前四位(分公司代码)
    private String BUKRS;
    //系统日期(过账日期)
    private String BUDAT;
    //系统日期(凭证日期)
    private String BLDAT;
    //凭证类型
    private String BLART;
    //charge_user名字-个人借款-当前年月(凭证抬头文本)
    private String BKTXT;
    //创建人(SAP账户)
    private String USNAM;
    //报销单号(参考凭证号)
    private String XBLNR;
    //借款：借：29，贷：50；冲账、还款：借：40，贷：39、31；(SAP过账码)
    private String BSCHL;
    //员工借还款记E(SAP 特别总账标识)
    private String UMSKZ;
    //会计科目
    private String HKONT;
    //员工供应商编码（用户-Colum3）
    private String LIFNR;
    //SAP记账金额
    private String WRBTR;
    //charge_department（成本中心编码）
    private String KOSTL;
    //原因代码
    private String RSTGR;
    //是否预制凭证
    private String I_STATUS;
    //科目行数
    private Integer BUZEI;
    //行文本
    private String SGTXT;
    //税码
    private String MWSKZ;
    //税基额
    private Double FWBAS;
    //货币码
    private String WAERS;
    //内部订单号
    private String AUFNR;
    private String ZBCZF;
    private String ZCDYF;
    private String ZDJDM;


    @JSONField(name = "AUFNR")
    public String getAUFNR() {
        return AUFNR;
    }

    public void setAUFNR(String AUFNR) {
        this.AUFNR = AUFNR;
    }

    @JSONField(name = "WAERS")
    public String getWAERS() {
        return WAERS;
    }

    public void setWAERS(String WAERS) {
        this.WAERS = WAERS;
    }

    @JSONField(name = "MWSKZ")
    public String getMWSKZ() {
        return MWSKZ;
    }

    public void setMWSKZ(String MWSKZ) {
        this.MWSKZ = MWSKZ;
    }

    @JSONField(name = "FWBAS")
    public Double getFWBAS() {
        return FWBAS;
    }

    public void setFWBAS(Double FWBAS) {
        this.FWBAS = FWBAS;
    }

    @JSONField(name = "SGTXT")
    public String getSGTXT() {
        return SGTXT;
    }

    public void setSGTXT(String SGTXT) {
        this.SGTXT = SGTXT;
    }


    @JSONField(name = "BUZEI")
    public Integer getBUZEI() {
        return BUZEI;
    }

    public void setBUZEI(Integer BUZEI) {
        this.BUZEI = BUZEI;
    }

    @JSONField(name = "I_STATUS")
    public String getI_STATUS() {
        return I_STATUS;
    }

    public void setI_STATUS(String I_STATUS) {
        this.I_STATUS = I_STATUS;
    }

    @JSONField(name = "BUKRS")
    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }
    @JSONField(name = "BLDAT")
    public String getBLDAT() {
        return BLDAT;
    }

    public void setBLDAT(String BLDAT) {
        this.BLDAT = BLDAT;
    }
    @JSONField(name = "BLART")
    public String getBLART() {
        return BLART;
    }

    public void setBLART(String BLART) {
        this.BLART = BLART;
    }
    @JSONField(name = "BKTXT")
    public String getBKTXT() {
        return BKTXT;
    }

    public void setBKTXT(String BKTXT) {
        this.BKTXT = BKTXT;
    }
    @JSONField(name = "USNAM")
    public String getUSNAM() {
        return USNAM;
    }

    public void setUSNAM(String USNAM) {
        this.USNAM = USNAM;
    }
    @JSONField(name = "XBLNR")
    public String getXBLNR() {
        return XBLNR;
    }

    public void setXBLNR(String XBLNR) {
        this.XBLNR = XBLNR;
    }
    @JSONField(name = "BSCHL")
    public String getBSCHL() {
        return BSCHL;
    }

    public void setBSCHL(String BSCHL) {
        this.BSCHL = BSCHL;
    }
    @JSONField(name = "UMSKZ")
    public String getUMSKZ() {
        return UMSKZ;
    }

    public void setUMSKZ(String UMSKZ) {
        this.UMSKZ = UMSKZ;
    }
    @JSONField(name = "HKONT")
    public String getHKONT() {
        return HKONT;
    }

    public void setHKONT(String HKONT) {
        this.HKONT = HKONT;
    }
    @JSONField(name = "LIFNR")
    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }
    @JSONField(name = "WRBTR")
    public String getWRBTR() {
        return WRBTR;
    }

    public void setWRBTR(String WRBTR) {
        this.WRBTR = WRBTR;
    }
    @JSONField(name = "KOSTL")
    public String getKOSTL() {
        return KOSTL;
    }

    public void setKOSTL(String KOSTL) {
        this.KOSTL = KOSTL;
    }
    @JSONField(name = "BUDAT")
    public String getBUDAT() {
        return BUDAT;
    }

    public void setBUDAT(String BUDAT) {
        this.BUDAT = BUDAT;
    }
    @JSONField(name = "RSTGR")
    public String getRSTGR() {
        return RSTGR;
    }

    public void setRSTGR(String RSTGR) {
        this.RSTGR = RSTGR;
    }

    @JSONField(name = "ZBCZF")
    public String getZBCZF() {
        return ZBCZF;
    }

    public void setZBCZF(String ZBCZF) {
        this.ZBCZF = ZBCZF;
    }

    @JSONField(name = "ZCDYF")
    public String getZCDYF() {
        return ZCDYF;
    }

    public void setZCDYF(String ZCDYF) {
        this.ZCDYF = ZCDYF;
    }

    @JSONField(name = "ZDJDM")
    public String getZDJDM() {
        return ZDJDM;
    }

    public void setZDJDM(String ZDJDM) {
        this.ZDJDM = ZDJDM;
    }
}
