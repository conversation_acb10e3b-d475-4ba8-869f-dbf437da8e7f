package com.cloudpense.expman.Request;

/**
 * Created by admin on 2019/9/5.
 * 文思海辉单据推送req
 */
public class WshhClaimPushReq {

    private int header_id;
    //单据号
    private String document_num;
    //单据类型
    private String document_type;
    //描述
    private String description;
    //总金额
    private double total_amount;
    //提交人
    private String submit_user;
    //费用ccc
    private String charge_department;
    //项目PID
    private String project_name;
    //机会号
    private String column11;
    //是否预提
    private String column18;
    //是否包含PID
    private String column20;
    //交易日期
    private String receipt_date;
    //金额
    private double receipt_amount;
    //币种
    private String receipt_currency;
    //汇率
    private double exchange_rate;
    //发票税率
    private int tax_code_id;
    //税额
    private double tax_amount;
    //调整税额
    private double fin_tax_amount;
    //行描述
    private String comments;
    //借会计科目
    private int dr_account_id;
    //贷会计科目
    private int cr_account_id;
    //税会计科目
    private int tax_account_id;
    //单据的审批状态
    private String status;
    //传输日期
    private String z_date_time;
    //调整发票金额
    private double fin_receipt_amount;
    //调整发票税率
    private int fin_tax_code_id;

    public double getFin_receipt_amount() {
        return fin_receipt_amount;
    }

    public void setFin_receipt_amount(double fin_receipt_amount) {
        this.fin_receipt_amount = fin_receipt_amount;
    }

    public int getFin_tax_code_id() {
        return fin_tax_code_id;
    }

    public void setFin_tax_code_id(int fin_tax_code_id) {
        this.fin_tax_code_id = fin_tax_code_id;
    }

    public String getDocument_num() {
        return document_num;
    }

    public void setDocument_num(String document_num) {
        this.document_num = document_num;
    }

    public String getDocument_type() {
        return document_type;
    }

    public void setDocument_type(String document_type) {
        this.document_type = document_type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getTotal_amount() {
        return total_amount;
    }

    public void setTotal_amount(double total_amount) {
        this.total_amount = total_amount;
    }

    public String getSubmit_user() {
        return submit_user;
    }

    public void setSubmit_user(String submit_user) {
        this.submit_user = submit_user;
    }

    public String getCharge_department() {
        return charge_department;
    }

    public void setCharge_department(String charge_department) {
        this.charge_department = charge_department;
    }

    public String getProject_name() {
        return project_name;
    }

    public void setProject_name(String project_name) {
        this.project_name = project_name;
    }

    public String getColumn11() {
        return column11;
    }

    public void setColumn11(String column11) {
        this.column11 = column11;
    }

    public String getColumn18() {
        return column18;
    }

    public void setColumn18(String column18) {
        this.column18 = column18;
    }

    public String getColumn20() {
        return column20;
    }

    public void setColumn20(String column20) {
        this.column20 = column20;
    }

    public String getReceipt_date() {
        return receipt_date;
    }

    public void setReceipt_date(String receipt_date) {
        this.receipt_date = receipt_date;
    }

    public double getReceipt_amount() {
        return receipt_amount;
    }

    public void setReceipt_amount(double receipt_amount) {
        this.receipt_amount = receipt_amount;
    }

    public String getReceipt_currency() {
        return receipt_currency;
    }

    public void setReceipt_currency(String receipt_currency) {
        this.receipt_currency = receipt_currency;
    }

    public double getExchange_rate() {
        return exchange_rate;
    }

    public void setExchange_rate(double exchange_rate) {
        this.exchange_rate = exchange_rate;
    }

    public int getTax_code_id() {
        return tax_code_id;
    }

    public void setTax_code_id(int tax_code_id) {
        this.tax_code_id = tax_code_id;
    }

    public double getTax_amount() {
        return tax_amount;
    }

    public void setTax_amount(double tax_amount) {
        this.tax_amount = tax_amount;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public int getDr_account_id() {
        return dr_account_id;
    }

    public void setDr_account_id(int dr_account_id) {
        this.dr_account_id = dr_account_id;
    }

    public int getCr_account_id() {
        return cr_account_id;
    }

    public void setCr_account_id(int cr_account_id) {
        this.cr_account_id = cr_account_id;
    }

    public int getTax_account_id() {
        return tax_account_id;
    }

    public void setTax_account_id(int tax_account_id) {
        this.tax_account_id = tax_account_id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getZ_date_time() {
        return z_date_time;
    }

    public void setZ_date_time(String z_date_time) {
        this.z_date_time = z_date_time;
    }

    public double getFin_tax_amount() {
        return fin_tax_amount;
    }

    public void setFin_tax_amount(double fin_tax_amount) {
        this.fin_tax_amount = fin_tax_amount;
    }

    public int getHeader_id() {
        return header_id;
    }

    public void setHeader_id(int header_id) {
        this.header_id = header_id;
    }
}
