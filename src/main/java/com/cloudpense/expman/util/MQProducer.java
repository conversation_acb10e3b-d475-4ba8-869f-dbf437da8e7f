package com.cloudpense.expman.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.message.strategy.common.dto.DocumentMQStrategyDTO;
import com.cloudpense.expman.vo.nippon.ExpClaimHeaderWithBLOBsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ResourceBundle;

/**
 * <AUTHOR>
 * @Date 2023/3/29 18:08
 * @Description:
 */
@Component
@Slf4j
public class MQProducer {

    //@Autowired
    RabbitTemplate rabbitTemplate;
    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    private String env = "mq-"+rb.getString("global.env")+"-";
    private String messageHost = rb.getString("message.host");

    private RestTemplate restTemplate = new RestTemplate();

    public void sendMQ(ExpClaimHeaderWithBLOBsDto doc, String eventCode, Long companyId, Long pathId,
                       String oldStatus) {
        DocumentMQStrategyDTO d = new DocumentMQStrategyDTO();
        d.setPathId(pathId);
        d.setDocumentNum(doc.getCode());
        d.setHeaderId(Convert.toLong(doc.getId()));
        d.setStrategyCode(eventCode);
        d.setStrategyId(SnowFlakeUtil.getId());
        d.setStatus(oldStatus);
        send2MessageCenter(d,companyId,doc.getCreatedBy());
    }

    public void send2MessageCenter(DocumentMQStrategyDTO documentMQStrategyDTO, Long companyId,
                                   Long userId){
        log.info("MQProducer#send2MessageCenter#documentMQStrategyDTO:{},companyId:{}",
                JSON.toJSONString(documentMQStrategyDTO),companyId);
        try {
            // rabbitTemplate.convertAndSend("common_strategy_exchange", env + "common_strategy_routing_key", documentMQStrategyDTO);
            postMessage(documentMQStrategyDTO, companyId, userId);
        } catch (Exception e){
            log.error("发送消息异常：", e);
        }
        log.info("rabbitTemplate.convertAndSend end");
    }

    /**
     *
     */
    public void postMessage(DocumentMQStrategyDTO documentMQStrategyDTO, Long companyId,
                            Long userId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("company_id", companyId.toString());
        headers.add("Content-Type", "application/json");
        headers.add("vtoken", "f0a73593-b920-479c-812c-d67b476d8c74");
        JSONObject jsonObj = new JSONObject();
        jsonObj.put("headerId", documentMQStrategyDTO.getHeaderId());
        jsonObj.put("pathId", documentMQStrategyDTO.getPathId());
        jsonObj.put("documentNum", documentMQStrategyDTO.getDocumentNum());
        jsonObj.put("status", documentMQStrategyDTO.getStatus());
        jsonObj.put("strategyCode", documentMQStrategyDTO.getStrategyCode());
        if(StrUtil.isEmpty(documentMQStrategyDTO.getStrategyName())) {
            jsonObj.put("strategyName", "strategySynchronizer");
        } else {
            jsonObj.put("strategyName", documentMQStrategyDTO.getStrategyName());
        }
        jsonObj.put("class", "com.cloudpense.message.strategy.common.dto.DocumentMQStrategyDTO");
        HttpEntity entity = new HttpEntity<>(jsonObj.toJSONString(), headers);
        log.info("发送消息接口请求：{}", JSONUtil.toJsonStr(entity));
        ResponseEntity<String> res = restTemplate.postForEntity(messageHost, entity, String.class);
        log.info("发送消息接口返回：{}", res.getBody());
    }
}
