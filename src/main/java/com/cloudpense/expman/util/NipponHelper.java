package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.vo.nippon.TravelExpenseVo;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;

public class NipponHelper {
    /**
     * 根据summary设置sumAmount
     * @param travelExpenseVo
     * @param currLine
     * @param summary
     */
    public static void setSumAmountBySummary(TravelExpenseVo travelExpenseVo, ExpClaimLine currLine, String summary) {
        double sumAmount = travelExpenseVo.getSumAmount();
        if (!"".equals(summary)) {
            sumAmount += currLine.getNetAmount();
            sumAmount += currLine.getTaxAmount();
        } else {
            sumAmount += currLine.getClaimAmount();
        }
        travelExpenseVo.setSumAmount(sumAmount);
    }

    /**
     * 按照travelType设置相关费用值
     * @param travelExpenseVo
     * @param currLine
     * @param travelType
     */
    public static void setValuesByTravelType(TravelExpenseVo travelExpenseVo, ExpClaimLine currLine, String travelType) {
        double air = travelExpenseVo.getAir();
        double air2 = travelExpenseVo.getAir2();
        double airPort = travelExpenseVo.getAirPort();
        double airOil = travelExpenseVo.getAirOil();
        double airOther = travelExpenseVo.getAirOther();
        double sumAmount = travelExpenseVo.getSumAmount();
        double train = travelExpenseVo.getTrain();
        double car = travelExpenseVo.getCar();
        double zijia = travelExpenseVo.getZijia();
        double zijia2 = travelExpenseVo.getZijia2();
        double zijia3 = travelExpenseVo.getZijia3();
        double services = travelExpenseVo.getServices();
        double other = travelExpenseVo.getOther();
        switch (travelType) {
            case "air":
                air += Double.parseDouble(currLine.getColumn20() == null || "".equals(currLine.getColumn20()) ? "0" : currLine.getColumn20());
                sumAmount += Double.parseDouble(currLine.getColumn20() == null || "".equals(currLine.getColumn20()) ? "0" : currLine.getColumn20());
                airPort += Double.parseDouble(currLine.getColumn41() == null || "".equals(currLine.getColumn41()) ? "0" : currLine.getColumn41());
                sumAmount += Double.parseDouble(currLine.getColumn41() == null || "".equals(currLine.getColumn41()) ? "0" : currLine.getColumn41());
                airOil += Double.parseDouble(currLine.getColumn45() == null || "".equals(currLine.getColumn45()) ? "0" : currLine.getColumn45());
                sumAmount += Double.parseDouble(currLine.getColumn45() == null || "".equals(currLine.getColumn45()) ? "0" : currLine.getColumn45());
                airOther += Double.parseDouble(currLine.getColumn47() == null || "".equals(currLine.getColumn47()) ? "0" : currLine.getColumn47());
                sumAmount += Double.parseDouble(currLine.getColumn47() == null || "".equals(currLine.getColumn47()) ? "0" : currLine.getColumn47());
                travelExpenseVo.setAir(air);
                travelExpenseVo.setAirPort(airPort);
                travelExpenseVo.setAirOil(airOil);
                travelExpenseVo.setAirOther(airOther);
                break;
            case "air2":
                air2 += currLine.getClaimAmount();
                travelExpenseVo.setAir2(air2);
                sumAmount += currLine.getClaimAmount();
                break;
            case "train":
                train += currLine.getClaimAmount();
                travelExpenseVo.setTrain(train);
                sumAmount += currLine.getClaimAmount();
                break;
            case "car":
                car += currLine.getClaimAmount();
                travelExpenseVo.setCar(car);
                sumAmount += currLine.getClaimAmount();
                break;
            case "zijia":
                zijia += currLine.getClaimAmount();
                travelExpenseVo.setZijia(zijia);
                sumAmount += currLine.getClaimAmount();
                break;
            case "zijia2":
                zijia2 += currLine.getClaimAmount();
                travelExpenseVo.setZijia2(zijia2);
                sumAmount += currLine.getClaimAmount();
                break;
            case "zijia3":
                zijia3 += currLine.getClaimAmount();
                travelExpenseVo.setZijia3(zijia3);
                sumAmount += currLine.getClaimAmount();
                break;
            case "services":
                services += currLine.getClaimAmount();
                travelExpenseVo.setServices(services);
                sumAmount += currLine.getClaimAmount();
                break;
            case "other":
                other += currLine.getClaimAmount();
                travelExpenseVo.setOther(other);
                sumAmount += currLine.getClaimAmount();
                break;
        }
        travelExpenseVo.setSumAmount(sumAmount);
    }

    /**
     * 构建推送给费控的报文
     * @param travelExpenseVo
     * @param tempDetails
     * @param j
     * @param type
     * @param detailsList
     * @return
     */
    public static int buildTempObject(TravelExpenseVo travelExpenseVo, JSONObject tempDetails,
                                             int j, String type, JSONArray detailsList ) {
        DecimalFormat df = new DecimalFormat("#.00");
        NumberFormat nf = NumberFormat.getInstance();
        JSONObject tempObject = null;
        if (travelExpenseVo.getAir() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-机票");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAir())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAir())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAirPort() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-其他税费");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirPort())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirPort())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAirOil() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-燃油附加费");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirOil())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirOil())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAirOther() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-其他税费");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirOther())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirOther())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getTrain() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "火车");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getTrain())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getTrain())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getCar() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "实名车船");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getCar())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getCar())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAirService() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-服务费");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirService())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirService())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAirServiceTax() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-服务费税金");
            tempObject.put("CostTypeCode", "N001");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirServiceTax())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAirServiceTax())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getOther() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "差旅其他");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getOther())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getOther())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getZijia() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "短途自驾-油费");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getZijia2() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "短途自驾-公路通行费");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia2())).setScale(2,4)));
            tempObject.put("AccruedExpenses",df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia2())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getZijia3() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "短途自驾-桥、闸");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia3())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getZijia3())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getAir2() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "飞机-国际机票");
            if ("CL18".equals(type)) {
                tempObject.put("CostTypeCode", "CL18");
            } else {
                tempObject.put("CostTypeCode", "N050");
            }
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAir2())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getAir2())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }
        if (travelExpenseVo.getServices() > 0) {
            tempObject = JSON.parseObject(tempDetails.toString());
            tempObject.put("RowID", nf.format(j + 501));
            tempObject.put("YDType", "市内交通费-可抵税");
            tempObject.put("CostTypeCode", "N050");
            tempObject.put("ExpenseMoney", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getServices())).setScale(2,4)));
            tempObject.put("AccruedExpenses", df.format(new BigDecimal(String.valueOf(travelExpenseVo.getServices())).setScale(2,4)));
            j++;
            detailsList.add(tempObject);
        }

        if (tempObject != null) {
            String expenseMoney = (String) tempObject.get("ExpenseMoney");
            String accruedExpenses = (String) tempObject.get("AccruedExpenses");

            expenseMoney = df.format(new BigDecimal(String.valueOf(expenseMoney)).setScale(2,4));
            accruedExpenses = df.format(new BigDecimal(String.valueOf(accruedExpenses)).setScale(2,4));
            tempObject.put("ExpenseMoney", expenseMoney);
            tempObject.put("AccruedExpenses", accruedExpenses);
        }

        return j;
    }
}
