package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.stream.Collectors;

public class FtpUtil {
     public static JSONObject downFile(String url, int port, String username, String password, String remotePath) throws Exception {
        JSONObject jsonObject = new JSONObject();
        FTPClient ftp = new FTPClient();
        try {
            ftp.connect(url, port);
            ftp.login(username, password);
            int reply = ftp.getReplyCode();
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return jsonObject;
            }

            FTPFile[] files = ftp.listFiles(remotePath);
            Collection<String> fileLists = Arrays.stream(files)
                    .map(FTPFile::getName)
                    .collect(Collectors.toList());
            for (String n : fileLists) {
                if (n.endsWith(".CTL")) {
                    jsonObject.put(n.substring(0, n.length() - 4), n.substring(0, n.length() - 4) + ".DAT");
                }
            }

            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
//                ftp.enterLocalPassiveMode();
                ftp.changeWorkingDirectory(remotePath);
                InputStream is = ftp.retrieveFileStream(entry.getValue().toString());
                ftp.getReply();
                String result = null;
                if (null != is) {
                    result = IOUtils.toString(is, "GBK");
                }

                is.close();

                jsonObject.put(entry.getKey(), result.replaceAll("\r\n", ""));
//                ftp.rename(entry.getKey() + ".CTL", entry.getKey() + ".CTL.TMP");
            }


            ftp.logout();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (IOException ioe) {
                }
            }
        }
        return jsonObject;
    }

    public static void main(String[] arg) throws Exception {
        System.out.println(downFile("gphn.cloudpense.com", 21, "SAPUSER", "YHEMERFKRHERO", "/INTERFACE/SAP_OA/MMI003_SAPPR/1000"));
    }

}
