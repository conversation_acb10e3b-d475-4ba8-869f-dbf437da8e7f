package com.cloudpense.expman.util;

/**
 * Created by wa<PERSON><PERSON><PERSON><PERSON> on 2019/9/19.
 * 科华
 */
public enum KeHuaEnum {

    //借款-借
    BORROW_BORROW("29"),
    //借款-贷
    BORROW_CREDIT("50"),
    //还款-借
    REPAY_BORROW("40"),
    //还款-贷
    REPAY_CREDIT("39"),
    //报销-贷
    EXPENSE_CREDIT("31");
    // 成员变量
    private String name;
    // 构造方法
    private KeHuaEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
