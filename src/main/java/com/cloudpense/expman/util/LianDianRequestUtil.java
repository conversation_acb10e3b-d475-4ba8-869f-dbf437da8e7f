package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;

public class LianDianRequestUtil {
    //获取响应时间
    public static String ReturnRequestTime(){
        Date requestTime = new Date();
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH24:mm:ss:SSS");
        String returnRequestTime = df.format(requestTime);
        return returnRequestTime;
    }
    //封装对象
    public static JSONObject ReturnObject(String employeeNumber){
        JSONObject middleInputJson = new JSONObject();
        middleInputJson.put("PERNR", employeeNumber);
        return middleInputJson;
    }
}
