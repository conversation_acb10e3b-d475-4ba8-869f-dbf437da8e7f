package com.cloudpense.expman.util;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Properties;

public class PropertiesUtil {
    private Properties props;

    String filePath;

    public PropertiesUtil(String filePath) {
        try {
            this.filePath = filePath;
            InputStream is = new FileInputStream(filePath);
            props = new Properties();
            props.load(is);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public String getProperty(String key) {
        return props.getProperty(key);
    }

    public void setProperty(String key, String value) {
        try {
            OutputStream os = new FileOutputStream(filePath);
            props.setProperty(key, value);
            props.store(os, null);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void removeProperty(String key) {
        try {
            OutputStream os = new FileOutputStream(filePath);
            props.remove(key);
            props.store(os, null);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void updateProperty(String key, String value) {
        try {
            OutputStream os = new FileOutputStream(filePath);
            props.setProperty(key, value);
            props.store(os, null);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}