package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.exception.FormatException;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.InputStreamRequestEntity;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.RequestEntity;
import org.apache.commons.httpclient.protocol.Protocol;
import org.apache.commons.httpclient.protocol.ProtocolSocketFactory;
import org.apache.http.HttpEntity;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.BasicHttpContext;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

public class HttpUtil {

    static Logger logger = LoggerFactory.getLogger(HttpUtil.class);
    public static JSONObject httpPost(String url, Object data) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(20000).setConnectTimeout(20000).build();
        httpPost.setConfig(requestConfig);
        httpPost.addHeader("Content-Type", "application/json");


        try {
            StringEntity requestEntity = new StringEntity(JSON.toJSONString(data), "utf-8");
            httpPost.setEntity(requestEntity);

            response = httpClient.execute(httpPost, new BasicHttpContext());
            logger.info("request url failed, http code=" + response.getStatusLine().getStatusCode()
                    + ", url=" + url);
            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");

                JSONObject result = JSON.parseObject(resultStr);
                if (result.getInteger("errcode") == 0) {
                    result.remove("errcode");
                    result.remove("errmsg");
                    return result;
                } else {
                    logger.info("HttpUtil == httpPost == url：" + url + ",return value = " + resultStr);
                    String errMsg = result.getString("errmsg");
                    throw new FormatException(errMsg);
                }
            }
        } catch (IOException e) {
            logger.info("request url=" + url + ", error: ", e);
            e.printStackTrace();
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("httpPost == close == error：", e);
            }
        }

        return null;
    }

    public static JSONObject httpGet(String url) throws Exception {
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(20000).setConnectTimeout(20000).build();
        httpGet.setConfig(requestConfig);

        try {
            response = httpClient.execute(httpGet, new BasicHttpContext());

            logger.info("HttpUtil == httpGet == response：" + response + ", url=" + url);
            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");

                JSONObject result = JSON.parseObject(resultStr);
                logger.info("HttpUtil == httpGet == result：" + JSON.toJSONString(result) + ", url=" + url);
                if (result.getInteger("errcode") == 0) {
                    result.remove("errcode");
                    result.remove("errmsg");
                    return result;
                } else {
                    logger.info("HttpUtil == httpGet == url：" + url + ",return value=" + resultStr);
                    int errCode = result.getInteger("errcode");
                    String errMsg = result.getString("errmsg");
                    throw new FormatException(errMsg);
                }
            }
        } catch (IOException e) {
            logger.error("httpGet == error：", e);
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("httpGet == close == error：", e);
            }
        }

        return null;
    }

    public static String doNormalJsonGet(String url) throws Exception {
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(20000).setConnectTimeout(20000).build();
        httpGet.setConfig(requestConfig);

        try {
            response = httpClient.execute(httpGet, new BasicHttpContext());
            logger.info("HttpUtil == doNormalJsonGet == response：" + response + ", url=" + url);

            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");
                return resultStr;

            }
        } catch (IOException e) {
            logger.error("doNormalJsonGet == error：", e);
            e.printStackTrace();
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("doNormalJsonGet == close == error：", e);
            }
        }
        return null;
    }

    public static String sendWebServicePost(String wsdl, String soapRequestData) {
        ProtocolSocketFactory fcty = new MySecureProtocolSocketFactory();
        Protocol.registerProtocol("https", new Protocol("https", fcty, 443));
        String soapResponseData = "";
        PostMethod postMethod = new PostMethod(wsdl);
        // 然后把Soap请求数据添加到PostMethod中
        byte[] b = null;
        InputStream is = null;
        try {
            b = soapRequestData.toString().getBytes("utf-8");
            is = new ByteArrayInputStream(b, 0, b.length);
            RequestEntity re = new InputStreamRequestEntity(is, b.length, "text/xml; charset=UTF-8");
            postMethod.setRequestEntity(re);
            postMethod.addRequestHeader("Authorization","Basic U1ZDX1QmRV9DT05GOkNqbHIyMDE5");
            HttpClient httpClient = new HttpClient();
            int status = httpClient.executeMethod(postMethod);
            if (status == 200) {
                soapResponseData = postMethod.getResponseBodyAsString();
            }
        } catch (Exception e) {
            logger.error("sendWebServicePost == error：", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error("sendWebServicePost == close == error：", e);
                }
            }
        }
        return soapResponseData;
    }

    public static JSONObject hetHttpGet(String url) throws Exception {
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(20000).setConnectTimeout(20000).build();
        httpGet.setConfig(requestConfig);

        try {
            response = httpClient.execute(httpGet, new BasicHttpContext());
            logger.info("HttpUtil == httpGet == response：" + response + ", url=" + url);

            if (response.getStatusLine().getStatusCode() != 200) {
                return null;
            }
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");
                JSONObject result = JSON.parseObject(resultStr);
                return  result;
            }
        } catch (IOException e) {
            logger.error("hetHttpGet == error：", e);
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("httpGet == close == error：", e);
            }
        }

        return null;
    }

    public static String HttpPostString(String url, Map<String, String> headers, String data, String userName, String password) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient;
        if (null != userName && null != password) {
            CredentialsProvider credsProvider = new BasicCredentialsProvider();
            credsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials("sap_fk", "fk123456"));
            httpClient = HttpClients.custom()
                    .setDefaultCredentialsProvider(credsProvider)
                    .build();
        } else {
            httpClient = HttpClients.custom()
                    .build();
        }
        RequestConfig requestConfig = RequestConfig.custom().
                setSocketTimeout(180000).setConnectTimeout(180000).build();
        httpPost.setConfig(requestConfig);

        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                httpPost.addHeader(entry.getKey(), entry.getValue());
            }
        }

        try {
            StringEntity requestEntity = new StringEntity(data, "utf-8");
            httpPost.setEntity(requestEntity);

            response = httpClient.execute(httpPost, new BasicHttpContext());

            logger.info("HttpUtil == HttpPostString == response：" + response + ", url=" + url);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String resultStr = EntityUtils.toString(entity, "utf-8");
                return resultStr;
            }
        } catch (IOException e) {
            logger.error("HttpPostString == error：", e);
            throw new Exception(e.getMessage());
        } finally {
            if (response != null) try {
                response.close();
            } catch (IOException e) {
                logger.error("HttpPostString == close == error：", e);
            }
        }
        return null;
    }
}
