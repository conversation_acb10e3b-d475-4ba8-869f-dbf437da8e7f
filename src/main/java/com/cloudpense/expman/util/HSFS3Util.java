package com.cloudpense.expman.util;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.regions.ServiceAbbreviations;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.cloudpense.expman.util.S3.S3Constants;

import java.io.File;
import java.util.ResourceBundle;

public class HSFS3Util {

    public String upload(String filePath, String fileName) {
        String type = getExtensionName(fileName);

        if (!(filePath.endsWith("/") || filePath.endsWith("\\"))) {
            filePath = filePath + File.separator;
        }
        AWSCredentials credentials = new BasicAWSCredentials(S3Constants.S3_ACCESS_KEY_ID, S3Constants.S3_SECRET_KEY);
        AmazonS3 s3Client = new AmazonS3Client(credentials);
        Region region = Region.getRegion(Regions.CN_NORTH_1);
        s3Client.setRegion(region);
        final String serviceEndpoint = region.getServiceEndpoint(ServiceAbbreviations.S3);
        s3Client.setEndpoint(serviceEndpoint);

        String name = GUIDUtil.generateGUID() + "." + type;

        String result = s3Client.putObject(new PutObjectRequest(S3Constants.HSF_S3_BUCKET_NAME, name, new File(filePath + fileName)).withCannedAcl(CannedAccessControlList.PublicRead)).getContentMd5();

        return name;
    }

    private static String getExtensionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1).toLowerCase();
            }
        }

        return filename;
    }
}
