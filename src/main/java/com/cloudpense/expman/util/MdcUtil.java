package com.cloudpense.expman.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

/**
 * MDC工具
 *
 * <AUTHOR>
 * @Date 2022/5/20
 */
public class MdcUtil {
    private static final Logger log = LoggerFactory.getLogger(MdcUtil.class);
    public static final String DEFAULT_MAP_KEY = "HEADER";
    public static final String DEFAULT_ID_KEY = "uuid";

    public static void setId(String value) {
        setValue(DEFAULT_ID_KEY, value);
    }

    public static String getId() {
        return getValue(DEFAULT_ID_KEY);
    }

    public static void setValue(String valueKey, String value) {
        Assert.notNull(valueKey, "valueKey is null");
        setMapValue(DEFAULT_MAP_KEY, valueKey, value);
    }

    public static String getValue(String valueKey) {
        Assert.notNull(valueKey, "valueKey is null");
        return getMapValue(DEFAULT_MAP_KEY, valueKey);
    }

    public static void setMapValue(String mapKey, String valueKey, String value) {
        Assert.notNull(mapKey, "mapKey is null");
        Assert.notNull(valueKey, "valueKey is null");
        Map<String, String> map = getMap(mapKey);
        map.put(valueKey, value);
        MDC.put(mapKey, map.toString());
    }

    public static String getMapValue(String mapKey, String valueKey) {
        Assert.notNull(mapKey, "mapKey is null");
        return getMap(mapKey).get(valueKey);
    }

    public static Map<String, String> getMap(String key) {
        Map<String, String> map = new HashMap<>();
        try {
            String mapStr = MDC.get(key);
            if(mapStr == null || mapStr.length() == 0) {
                return map;
            }
            String[] values = mapStr.replace("{", "").replace("}", "").split(",");
            if(values.length > 0) {
                for(String value : values) {
                    String[] cols = value.split(":|=");
                    if(cols.length != 2) {
                        continue;
                    }
                    map.put(cols[0].replace("\"", "").trim(), cols[1].replace("\"", "").trim());
                }
            }
        } catch (Exception e) {
            log.error("获取MDC信息异常：{}", e.getMessage(), e);
        }

        return map;
    }

}
