package com.cloudpense.expman.util.Constants;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 */
public class KehuaConstants {
    /*SDK自带常量*/
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TIMEZONE = "GMT+8";
    public static final String CHARSET_UTF8 = "UTF-8";
    public static final String URL = "http://fsm.khbaf.com:8013/api";
    public static final String USERNAME = "adminkh";
    public static final String PASSWORD = "adminkh8888";
//    public static final String URL = "http://dev.jinxiu365.com:8013/api";
//    public static final String USERNAME = "adminkhtest";
//    public static final String PASSWORD = "8888";
    public static final String VERSION = "2";
    public static final String SECRET_KEY = "beacaahkeyty55qapsdck64j5pl8qgtc";
    /*自定义常量*/
    public static final String WORK_ORDER = "workOrderSearch";

    /*sap wsdl地址*/
    public static final String SAP_WSDL ="http://sappoapp.skhb.com:50000/XISOAPAdapter/MessageServlet?senderParty=&senderService=BC_FK&receiverParty=&receiverService=&interface=SI_Voucher_Fk_Out&interfaceNamespace=urn%3Askhb.com%3AErp%3AFk%3AFi";
    /*合同接口地址和路径*/
    public static final String win = "D:\\home\\ubuntu\\project\\kehua\\";
    public static final String linux = "/home/<USER>/projects/kehua/";
    public static final String ATTACHMENT_FILE =pathFormat("attachmentFile");
    public static final String XML_FILE =pathFormat("xmlFile");
    public static boolean kehuaContractRunning = false;

    private static String pathFormat(String path){
        // 获取系统类型
        String os = System.getProperty("os.name");
        // 拼接路径
        if(os.contains("Windows")){
            return win+path+"\\";
        }else {
            return linux+path+"/";
        }
    }

    public static String path = "C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\kehua\\aa.json";
    ;

    public static JSONObject jsonRead(String path){
        File file=new File(path);
        String content ="";
        try {
            content= FileUtils.readFileToString(file,"UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(content);
        JSONObject jsonObject= JSONObject.parseObject(content);
        return  jsonObject;
    }

    public KehuaConstants() {
    }
    public static List<String> findDates(String dBegin, String dEnd) throws ParseException {
        //日期工具类准备
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //设置开始时间
        Calendar calBegin = Calendar.getInstance();
        calBegin.setTime(format.parse(dBegin));

        //设置结束时间
        Calendar calEnd = Calendar.getInstance();
        calEnd.setTime(format.parse(dEnd));

        //装返回的日期集合容器
        List<String> Datelist = new ArrayList<String>();
        Datelist.add(dBegin);
        // 每次循环给calBegin日期加一天，直到calBegin.getTime()时间等于dEnd
        while (format.parse(dEnd).after(calBegin.getTime()))  {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            Datelist.add(format.format(calBegin.getTime()));
        }
        System.out.println(Datelist);
        return Datelist;
    }
}
