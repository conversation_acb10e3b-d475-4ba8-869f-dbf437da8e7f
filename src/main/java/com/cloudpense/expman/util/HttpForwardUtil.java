package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Joiner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.ResourceBundle;

/**
 * 定制接口转发工具
 *
 * <AUTHOR>
 * @Date 2021/11/16
 */
@Component
public class HttpForwardUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpForwardUtil.class);

    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    RestTemplate restTemplate = new RestTemplate();

    public String getForwardUrl(int companyId, String interfaceName) {
        String key = Joiner.on(".").join("redirect.url", interfaceName, companyId);
        return rb.containsKey(key) ? rb.getString(key) : null;
    }

    public JSONObject post(String accessToken, String forwardUrl, String requestJson) {
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("Content-Type", "application/json;charset=UTF-8");
            httpHeaders.add("access_token", accessToken);
            HttpEntity httpEntity = new HttpEntity(requestJson, httpHeaders);
            logger.info("转发请求到定制接口url={}, 请求={}", forwardUrl, requestJson);
            ResponseEntity<String> responseEntity = restTemplate.exchange(forwardUrl, HttpMethod.POST, httpEntity, String.class);
            logger.info("转发请求到定制接口url={}, 响应={}", forwardUrl, JSON.toJSONString(responseEntity));
            if(responseEntity != null && HttpStatus.OK == responseEntity.getStatusCode() && responseEntity.getBody() != null) {
                return JSONObject.parseObject(responseEntity.getBody());
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.info("转发定制接口出现未知异常：{}", e.getMessage(), e);
            return null;
        }

    }

}
