package com.cloudpense.expman.util;

/**
 * Created by <PERSON>pook<PERSON> on 2015/1/28.
 */
public class View {
    public interface SearchSummary {}
    public interface SimpleSummary extends SearchSummary ,SummaryOfBudget {}
    public interface Summary extends SimpleSummary {}
    public interface SummaryOfSave extends Summary,SummaryOfSaveAndLoginInfo {}
    public interface SummaryOfFindAll extends Summary, SummaryOfSave {}
    public interface SummaryOfFindOne extends SummaryOfFindAll, SummaryOfSave {}
    public interface SummaryOfLoginInfo extends SummaryOfSaveAndLoginInfo,SummaryOfBudget {}
    public interface SummaryOfSaveAndLoginInfo extends SimpleSummary{}
    public interface SummaryOfBudget {}

    public interface Hotel1 extends Hotel12,Hotel13,Hotel123 {}
    public interface Hotel2 extends Hotel12,Hotel23,Hotel123 {}
    public interface Hotel3 extends Hotel23,Hotel13,Hotel123 {}
    public interface Hotel12 {}
    public interface Hotel13 {}
    public interface Hotel23 {}
    public interface Hotel123 {}

    public interface History {}
}
