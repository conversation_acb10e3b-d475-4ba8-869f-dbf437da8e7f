package com.cloudpense.expman.util;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

public class SAPMap {

    private static Map<String,Integer> PRI01 = new LinkedHashMap<>();
    private static Map<String,Integer> POI01 = new LinkedHashMap<>();
    private static Map<String,Integer> GRI01 = new LinkedHashMap<>();
    private static Map<String,Integer> FMI09 = new LinkedHashMap<>();
    private static Map<String,Integer> CTI01 = new LinkedHashMap<>();
    private static Map<String,Integer> VDI01 = new LinkedHashMap<>();

    static {
        PRI01.put("Type",16);
        PRI01.put("SAPSN",10);
        PRI01.put("NB",4);
        PRI01.put("SN",10);
        PRI01.put("Factory",4);
        PRI01.put("No",5);
        PRI01.put("Type2",1);
        PRI01.put("Material",35);
        PRI01.put("Item",40);
        PRI01.put("Quantity",14);
        PRI01.put("ApplyUnit",3);
        PRI01.put("Price",12);
        PRI01.put("Currency",5);
        PRI01.put("UI",5);
        PRI01.put("Unit",3);
        PRI01.put("WBS",24);
        PRI01.put("CostCenter",10);
        PRI01.put("RequestDate",8);
        PRI01.put("DeliveryDate",8);
        PRI01.put("DeliverAddress",4);
        PRI01.put("AccountCode",10);
        PRI01.put("Asset_Number",10);
        PRI01.put("AGREEMENT",10);
        PRI01.put("AGMT_ITEM",5);
        PRI01.put("Applicant",50);
        PRI01.put("ApplicantDept",60);
        PRI01.put("Longtext",500);
        PRI01.put("Comments",100);
        PRI01.put("PurchGrp",3);
        PRI01.put("EKPO_MATKL", 9);
        PRI01 = Collections.unmodifiableMap(PRI01);
        POI01.put("BSART",4);
        POI01.put("IHREZ",12);
        POI01.put("EBELN",10);
        POI01.put("LIFNR",10);
        POI01.put("NAME1",35);
        POI01.put("BEDAT",8);
        POI01.put("EKORG",4);
        POI01.put("EKGRP",3);
        POI01.put("WAERS",5);
        POI01.put("BUKRS",4);
        POI01.put("UNSEZ",30);
        POI01.put("FREE1",10);
        POI01.put("FREE2",500);
        POI01.put("FREE3",10);
        POI01.put("FREE4",10);
        POI01.put("FREE5",10);
        POI01.put("IHREZ_ITEM",12);
        POI01.put("MATNR",18);
        POI01.put("TXZ01",40);
        POI01.put("KNTTP",1);
        POI01.put("EBELP",5);
        POI01.put("MENGE",14);
        POI01.put("MEINS",3);
        POI01.put("EINDT",8);
        POI01.put("WERKS",4);
        POI01.put("NETPR",12);
        POI01.put("PEINH",5);
        POI01.put("SAKTO",10);
        POI01.put("KOSTL",10);
        POI01.put("AUFNR",12);
        POI01.put("PS_PSP_PNR",30);
        POI01.put("ANLN1",12);
        POI01.put("BEDNR",10);
        POI01.put("KONNR",10);
        POI01.put("KTPNR",5);
        POI01.put("MWSKZ",2);
        POI01.put("OPER",1);
        POI01.put("UNSEZ02",30);
        POI01.put("FREE102",10);
        POI01.put("FREE202",12);
        POI01.put("FREE302",3);
        POI01.put("FREE402",10);
        POI01.put("FREE502",10);
        POI01.put("EKPO_MATKL", 9);

        POI01 = Collections.unmodifiableMap(POI01);
        GRI01.put("GRNUM",16);
        GRI01.put("ITEM",5);
        GRI01.put("EBELN",10);
        GRI01.put("EBELP",5);
        GRI01.put("BWART",3);
        GRI01.put("MATNR",18);
        GRI01.put("MENGE",14);
        GRI01.put("MEINS",3);
        GRI01.put("WERKS",4);
        GRI01.put("LGORT",4);

        GRI01 = Collections.unmodifiableMap(GRI01);
        FMI09.put("ORDERID",20);
        FMI09.put("ORDERTYPE",3);
        FMI09.put("ORDERDSC",30);
        FMI09.put("BUKRS",4);
        FMI09.put("BLDAT",8);
        FMI09.put("BUDAT",8);
        FMI09.put("BLART",2);
        FMI09.put("WAERS",5);
        FMI09.put("KURSF",12);
        FMI09.put("XBLNR",16);
        FMI09.put("BKTXT",25);
        FMI09.put("BUZEI",3);
        FMI09.put("FLAG",1);
        FMI09.put("BSCHL",2);
        FMI09.put("HKONT",10);
        FMI09.put("UMSKZ",1);
        FMI09.put("DMBTR",15);
        FMI09.put("WRBTR",15);
        FMI09.put("AUFNR",12);
        FMI09.put("KOSTL",10);
        FMI09.put("WBS",30);
        FMI09.put("XNEGP",1);
        FMI09.put("ZUONR",18);
        FMI09.put("SGTXT",50);
        FMI09.put("ZFBDT",8);
        FMI09 = Collections.unmodifiableMap(FMI09);

        CTI01.put("OPERATING_LOGO",1);
        CTI01.put("BSART",4);
        CTI01.put("EKKO_EBELN_OA",10);
        CTI01.put("EKKO_EBELN_SAP",10);
        CTI01.put("EKKO_LIFNR",10);
        CTI01.put("PROCUREMENT",4);
        CTI01.put("PROCUREMENT_SECTION",3);
        CTI01.put("EKKO_KDATB",8);
        CTI01.put("EKKO_KDATE",8);
        CTI01.put("EKPO_NETWR",15);
        CTI01.put("CLAUSE1",3);
        CTI01.put("CLAUSE2",28);
        CTI01.put("EKKO_WAERS",5);
        CTI01.put("COMPANY_PAY_CONDITION",4);
        CTI01.put("EKPO_EBELP",5);
        CTI01.put("EKPO_WERKS",4);
        CTI01.put("EKPO_KNTTP",1);
        CTI01.put("EKPO_MATNR",18);
        CTI01.put("EKPO_TXZ01",40);
        CTI01.put("EKPO_MENGE",14);
        CTI01.put("EKPO_MEINS",3);
        CTI01.put("EKKN_SAKTO",10);
        CTI01.put("EKKN_KOSTL",10);
        CTI01.put("EKKN_ANLN1",12);
        CTI01.put("EKPO_MATKL",9);
        CTI01.put("EKPO_NETPR",12);
        CTI01.put("EKPO_PEINH",5);
        CTI01.put("DEMAND_TRACING",10);
        CTI01.put("MATERIAL_NUM",35);
        CTI01 = Collections.unmodifiableMap(CTI01);

        VDI01.put("COMPANY", 4);
        VDI01.put("LIFNR", 10);
        VDI01.put("ADDR1_DATA_NAME1", 35);
        VDI01.put("SMTP_ADDR", 60);
        VDI01.put("MOB_NUMBER", 30);
        VDI01.put("ADDR3_DATA_DEPARTMENT", 40);
        VDI01.put("LFBK_BANKN", 30);
        VDI01.put("LFBK_BANKL", 15);
        VDI01.put("BNKA_BANKA", 60);
        VDI01.put("IF_DELETE", 1);
        VDI01 = Collections.unmodifiableMap(VDI01);

    }

    public static Map<String,Integer> getMap(String mapName){
        if("PRI01".equals(mapName)){
            return PRI01;
        }else if("POI01".equals(mapName)){
            return POI01;
        }else if("GRI01".equals(mapName)){
            return GRI01;
        }else if("FMI09".equals(mapName)){
            return FMI09;
        }else if("CTI01".equals(mapName)){
            return CTI01;
        }else if("VDI01".equals(mapName)){
            return VDI01;
        }
        return null;
    }
}
