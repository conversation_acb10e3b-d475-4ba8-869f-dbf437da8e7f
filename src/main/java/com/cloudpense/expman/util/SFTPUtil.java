package com.cloudpense.expman.util;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.sap.PRI01;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
/**
 * 类说明 sftp工具类
 */
public class SFTPUtil {
    private transient Logger log = LoggerFactory.getLogger(this.getClass());

    private ChannelSftp sftp;

    private Session session;
    /** SFTP 登录用户名*/
    private String username;
    /** SFTP 登录密码*/
    private String password;
    /** 私钥 */
    private String privateKey;
    /** SFTP 服务器地址IP地址*/
    private String host;
    /** SFTP 端口*/
    private int port;

    public static final String userName = "OAPRDUSER";
    public static final String hostAddress = "***************";
    public static final int sftpPort = 58422;
    public static final String sftpPassword = "PVYKBBP8RF61C";

    public static Map<String, String> address;
    static {
        address = new HashMap();
        address.put("MMI001", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI001_SAPOA/1000");
        address.put("MMI101", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI001_SAPOA/2000");
        address.put("MMI201", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI001_SAPOA/3000");
        address.put("PRI01", "/ftp_vuser_share/INTERFACE/OA_SAP/MMI002_OAPR/1000");
        address.put("PRI02", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI003_SAPPR/1000");
        address.put("POI01", "/ftp_vuser_share/INTERFACE/OA_SAP/MMI004_OAPO/1000");
        address.put("POI02", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI005_SAPPO/1000");
        address.put("GRI01", "/ftp_vuser_share/INTERFACE/OA_SAP/MMI006_OAGR/1000");
        address.put("GRI02", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI007_SAPGR/1000");
        address.put("VDI01", "/ftp_vuser_share/INTERFACE/OA_SAP/MMI008_OASTAFF/1000");
        address.put("SVI01", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI009_SAPVENDOR/1000");
        address.put("CTI01", "/ftp_vuser_share/INTERFACE/OA_SAP/MMI010_OACONTRACT/1000");
        address.put("CTI02", "/ftp_vuser_share/INTERFACE/SAP_OA/MMI011_SAPCONTRACT/1000");
        address.put("FMI01", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI001_FM/1000");
        address.put("FMI02", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI002_CI/1000");
        address.put("FMI03", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI003_ACCOUNT/1000");
        address.put("FMI04", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI004_COST/1000");
        address.put("FMI05", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI005_COSTFM/1000");
        address.put("FMI06", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI006_ACCOUNTCI/1000");
        address.put("FMI07", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI007_FMBUDGET/1000");
        address.put("FMI08", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI008_CHGES/1000");
        address.put("FMI09", "/ftp_vuser_share/INTERFACE/OA_SAP/FMI009_OABX/1000");
        address.put("FMI10", "/ftp_vuser_share/INTERFACE/SAP_OA/FMI010_SAPBX/1000");
    }

    /**
     * 构造基于密码认证的sftp对象
     */
    public SFTPUtil(String username, String password, String host, int port) {
        this.username = username;
        this.password = password;
        this.host = host;
        this.port = port;
    }

    /**
     * 构造基于秘钥认证的sftp对象
     */
    public SFTPUtil(String username, String host, int port, String privateKey) {
        this.username = username;
        this.host = host;
        this.port = port;
        this.privateKey = privateKey;
    }

    public SFTPUtil(){}


    /**
     * 连接sftp服务器
     */
    public void login(){
        try {
            JSch jsch = new JSch();
            if (privateKey != null) {
                jsch.addIdentity(privateKey);// 设置私钥
            }

            session = jsch.getSession(username, host, port);

            if (password != null) {
                session.setPassword(password);
            }
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");

            session.setConfig(config);
            session.connect();

            Channel channel = session.openChannel("sftp");
            channel.connect();

            sftp = (ChannelSftp) channel;
        } catch (JSchException e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭连接 server
     */
    public void logout(){
        if (sftp != null) {
            if (sftp.isConnected()) {
                sftp.disconnect();
            }
        }
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }


    /**
     * 将输入流的数据上传到sftp作为文件。文件完整路径=basePath+directory
     * @param basePath  服务器的基础路径
     * @param directory  上传到该目录
     * @param sftpFileName  sftp端文件名
     * @param input
     */
    public void upload(String basePath,String directory, String sftpFileName, InputStream input) throws SftpException{
        try {
            sftp.cd(basePath);
            sftp.cd(directory);
        } catch (SftpException e) {
            //目录不存在，则创建文件夹
            String [] dirs=directory.split("/");
            String tempPath=basePath;
            for(String dir:dirs){
                if(null== dir || "".equals(dir)) continue;
                tempPath+="/"+dir;
                try{
                    sftp.cd(tempPath);
                }catch(SftpException ex){
                    sftp.mkdir(tempPath);
                    sftp.cd(tempPath);
                }
            }
        }
        sftp.put(input, sftpFileName);  //上传文件
    }

    public void uploadFile(String directory, String sftpFileName, InputStream input) throws SftpException{
        try {
            sftp.cd(directory);
        } catch (SftpException e) {
            System.out.println(e.toString());
        }
        sftp.put(input, sftpFileName);  //上传文件
    }

    /**
     * 下载文件。
     * @param directory 下载目录
     * @param downloadFile 下载的文件
     * @param saveFile 存在本地的路径
     */
    public void download(String directory, String downloadFile, String saveFile) throws SftpException, FileNotFoundException{
        if (directory != null && !"".equals(directory)) {
            sftp.cd(directory);
        }
        File file = new File(saveFile);
        sftp.get(downloadFile, new FileOutputStream(file));
    }

    /**
     * 下载文件
     * @param directory 下载目录
     * @param downloadFile 下载的文件名
     * @return 字节数组
     */
    public byte[] download(String directory, String downloadFile) throws SftpException, IOException{
        if (directory != null && !"".equals(directory)) {
            sftp.cd(directory);
        }
        InputStream is = sftp.get(downloadFile);

        byte[] fileData = IOUtils.toByteArray(is);

        return fileData;
    }

    public String downloadToString(String directory, String downloadFile) throws SftpException, IOException{
        if (directory != null && !"".equals(directory)) {
            sftp.cd(directory);
        }
        InputStream is = sftp.get(downloadFile);

        String result = "";
        if (null != is) {
            result = IOUtils.toString(is, "GBK");
        }
        return result;
    }


    /**
     * 删除文件
     * @param directory 要删除文件所在目录
     * @param deleteFile 要删除的文件
     */
    public void delete(String directory, String deleteFile) throws SftpException{
        sftp.cd(directory);
        sftp.rm(deleteFile);
    }

    public void rename(String oldpath, String newpath) throws SftpException {
        sftp.rename(oldpath, newpath);
    }

    /**
     * 列出目录下的文件
     * @param directory 要列出的目录
     */
    public Vector<ChannelSftp.LsEntry> listFiles(String directory) throws SftpException {
        System.out.println(directory);
        return sftp.ls(directory);
    }

    public static JSONArray getData(String key) throws Exception {
        SFTPUtil sftp = new SFTPUtil(userName, sftpPassword, hostAddress, sftpPort);
        sftp.login();
        Vector<ChannelSftp.LsEntry> files = sftp.listFiles(address.get(key));
        Enumeration<ChannelSftp.LsEntry> elements = files.elements();
        JSONArray jsonArray = new JSONArray();
        while (elements.hasMoreElements()) {
            String ctlFileName = elements.nextElement().getFilename();
            String datFileName;
            if (ctlFileName.endsWith(".CTL")) {
                datFileName = ctlFileName.substring(0, ctlFileName.length() - 4) + ".DAT";
            } else {
                continue;
            }
            String message = sftp.downloadToString(address.get(key), datFileName);
            String[] lists = message.split("\r\n");
            for (String s : lists) {
                if (s.length() == 0) {
                    continue;
                }
                JSONObject json = grokFormat(key, s);
                jsonArray.add(json);
            }
//            sftp.rename(ctlFileName, ctlFileName + ".TMP");
        }
        sftp.logout();
        return jsonArray;
    }

    public static JSONObject grokFormat(String format, String message) throws JSONException {
        String pattern = "%{" + format + "}";
        String json = GrokUtils.toJson(pattern, message);
        JSONObject jsonObject = JSONObject.parseObject(json);
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            jsonObject.put(entry.getKey(), entry.getValue().toString().trim());
        }
        return jsonObject;
    }

    /**
     *  传入一个对象与对应的key 自动填充空格" "
     * @param objList
     * @return
     * @throws Exception
     */
    public InputStream objectToString(List<Object> objList,String mapKey) throws Exception {
        if(null != objList && objList.size()>0){
            Map<String,Integer> map = SAPMap.getMap(mapKey);
            StringBuffer str = new StringBuffer();
            for (Object object : objList){
                String key = object.getClass().getSimpleName();
                if(null!=map&&map.size()>0){

                    for (Map.Entry<String, Integer > entry : map.entrySet()) {
                        String keyAttr = entry.getKey();
                        Integer size = entry.getValue();
                        Field field = object.getClass().getDeclaredField(keyAttr);
                        //设置对象的访问权限，保证对private的属性的访问
                        field.setAccessible(true);
                        // strAttrValue属性值
                        String strAttrValue = (String)field.get(object);
                        if (strAttrValue != null) {
                            strAttrValue = strAttrValue.replaceAll("[\n\r]", " ");
                        }
                        if(null == strAttrValue){
                            for(int i =0;i<size;i++){
                                str.append(" ");
                            }
                        }else if(strAttrValue.length()>size){
                            str.append(strAttrValue.substring(0,size));
                        }else{
                            str.append(strAttrValue);
                            for(int i =strAttrValue.length();i<size;i++){
                                str.append(" ");
                            }
                        }
                    }
                    //添加回车符
                    if(objList.size()>1){
                        str.append("\r\n");
                    }
                }
            }
            return new ByteArrayInputStream(str.toString().getBytes("GBK"));
        }
        return null;
    }

    /**
     *  employee reimbursement
     *
     * @param args
     * @throws Exception
     */

    //测试
    public static void main(String[] args) throws Exception {

        SFTPUtil sftp = new SFTPUtil(userName, sftpPassword, hostAddress, sftpPort);
        sftp.login();

        PRI01 pri01 = new PRI01();
        pri01.setType("数据1");
        pri01.setSAPSN("测试2");
        pri01.setNB("测试3");
        pri01.setFactory("测试4");

        String addressKey = pri01.getClass().getSimpleName();
        List<Object> mmi002List = new ArrayList<>();
        mmi002List.add(pri01);
        mmi002List.add(pri01);
        InputStream input = sftp.objectToString(mmi002List,addressKey);
        Vector<ChannelSftp.LsEntry> vector = sftp.listFiles(address.get(addressKey));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYYMMdd");
        String dateFormat = simpleDateFormat.format(new Date());
        Integer number = 1;
        for(ChannelSftp.LsEntry c : vector){
            String fileName = c.getFilename();
            if(fileName.contains(dateFormat)){
                int firstIndex = fileName.lastIndexOf("_");
                if(firstIndex!=-1){
                    String tempFileName = fileName.substring(firstIndex+1);
                    int lastIndex = tempFileName.indexOf(".");
                    if(lastIndex!=-1){
                        try {
                            int tempNumber =Integer.parseInt(tempFileName.substring(0,lastIndex)) ;
                            if(tempNumber>=number){
                                number = tempNumber+1;
                            }
                        }catch (Exception e){
                            continue;
                        }
                    }
                }

            }
        }
        String fileNumber = number.toString();
        if(fileNumber.length()<5){
            for (int i = fileNumber.length(); i<5;i++){
                fileNumber = "0"+fileNumber;
            }
        }

        String sftpFileName = addressKey+dateFormat+"_"+fileNumber;
        System.out.println(sftpFileName+".DAT");
        sftp.uploadFile(address.get(addressKey), sftpFileName+".DAT",input);

        sftp.uploadFile(address.get(addressKey), sftpFileName+".CTL",new ByteArrayInputStream("".getBytes("GBK")));
        System.out.println("================下载并显示文件内容===================");
        String fileContent = sftp.downloadToString(address.get(addressKey),sftpFileName+".DAT");
        System.out.println("文件内容：");
        System.out.println(fileContent);
        Vector<ChannelSftp.LsEntry> vectors = sftp.listFiles(address.get(addressKey));
        for(ChannelSftp.LsEntry vs : vectors){
            System.out.println(vs.getFilename());
        }




    }
}
