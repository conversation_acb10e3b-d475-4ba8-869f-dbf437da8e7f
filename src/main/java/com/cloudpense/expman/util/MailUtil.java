package com.cloudpense.expman.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

public class MailUtil {
    private static final Logger logger = LoggerFactory.getLogger(MailUtil.class);

    private static String sendUrl = "http://api.sendcloud.net/apiv2/mail/send";
    private static String apiKey = "a2007d6628c6eed08efee0a360bf4980";
    private static String apiUser = "cloudPenseService_customer";
    public static final String SEND_FROM = "<EMAIL>";
    public static final String EMAIL_NAME = "NP Finance Center";



    public static boolean sendMailFromService(String receiver, String title, String body) {
        try {

//            HttpPost httpPost = new HttpPost(sendUrl);
//            CloseableHttpClient httpClient = HttpClients.createDefault();
//            List<NameValuePair> params = new ArrayList<>();
//            params.add(new BasicNameValuePair("apiUser", apiUser));
//            params.add(new BasicNameValuePair("apiKey", apiKey));
//            params.add(new BasicNameValuePair("to", receiver));
//            params.add(new BasicNameValuePair("from", SEND_FROM));
//            params.add(new BasicNameValuePair("subject", title));
//            params.add(new BasicNameValuePair("html", body));
//            params.add(new BasicNameValuePair("cc", notifyDTO.getCc()));
//            params.add(new BasicNameValuePair("bcc", notifyDTO.getBcc()));
//            params.add(new BasicNameValuePair("fromName", EMAIL_NAME + " <" + SEND_FROM + ">"));
//            httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
//            CloseableHttpResponse response = httpClient.execute(httpPost);
//            String string = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
//            httpPost.releaseConnection();
//            httpClient.close();
//            return string;
//
//
//            String username = "<EMAIL>";
//            String password = "CloudShanghai123#";
//            String from = "<EMAIL>";
//
//            Properties props = System.getProperties();
//            // 创建信件服务器
//            props.put("mail.smtp.host", "smtp.exmail.qq.com");
//            props.put("mail.smtp.auth", "true");
//            props.put("mail.transport.protocol", "smtp");
//            // 得到默认的对话对象
//            Authenticator a = new Authenticator() {
//                public PasswordAuthentication getPasswordAuthentication() {
//                    return new PasswordAuthentication(username, password);
//                }
//            };
//            //创建Session实例
//            Session session = Session.getDefaultInstance(props, a);
//            //创建MimeMessage实例对象
//            MimeMessage msg = new MimeMessage(session);
//            //设置发信人
//            //设置自定义发件人昵称
//            String nick = "";
//            try {
//                nick = javax.mail.internet.MimeUtility.encodeText("NP Finance Center");
//            } catch (UnsupportedEncodingException e) {
//                e.printStackTrace();
//            }
//            msg.setFrom(new InternetAddress(nick + " <" + from + ">"));
//            //设置收信人
//            msg.setRecipients(Message.RecipientType.TO, InternetAddress.parse(receiver));
//            //设置发送日期
//            msg.setSentDate(new Date());
//            //设置邮件主题
//            msg.setSubject(title);
//            //设置邮件正文
//            msg.setText(body, "utf-8", "html");
//            logger.info("Transport.send start");
//            Transport.send(msg);
//            return true;


            //20250620 chao.zhou3 采用 sendCloud
            try {
                HttpPost httpPost = new HttpPost(sendUrl);
                CloseableHttpClient httpClient = HttpClients.createDefault();
                List<NameValuePair> params = new ArrayList<>();
                params.add(new BasicNameValuePair("apiUser", apiUser));
                params.add(new BasicNameValuePair("apiKey", apiKey));
                params.add(new BasicNameValuePair("to", receiver));
                params.add(new BasicNameValuePair("from", SEND_FROM));
                params.add(new BasicNameValuePair("subject", title));
                params.add(new BasicNameValuePair("html", body));
//            params.add(new BasicNameValuePair("cc", notifyDTO.getCc()));
//            params.add(new BasicNameValuePair("bcc", notifyDTO.getBcc()));
                params.add(new BasicNameValuePair("fromName", EMAIL_NAME + " <" + SEND_FROM + ">"));
                httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
                CloseableHttpResponse response = httpClient.execute(httpPost);
                String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                httpPost.releaseConnection();
                httpClient.close();
                if(StringUtils.isNotEmpty(result)){
                    JSONObject resultObj = JSONUtil.parseObj(result);
                    if(resultObj.getInt("statusCode")==200){
                        return true;
                    }
                }
                return false;
            } catch (Exception e) {
                logger.info("MailUtil sendMailFromService is error {}",e);
                return false;
            }
        } catch (Exception e) {
        logger.info("MailUtil sendMailFromService is error {}",e);
        return false;
    }
    }
}
