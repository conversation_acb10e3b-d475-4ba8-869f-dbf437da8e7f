package com.cloudpense.expman.util;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.core.util.QuickWriter;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
import com.thoughtworks.xstream.io.xml.PrettyPrintWriter;
import com.thoughtworks.xstream.io.xml.XppDriver;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.io.Writer;

public class XStreamUtil {
 
    /**
     * 扩展xstream，使其支持CDATA块 
     *  整数和浮点数不添加
     * @date 2013-05-19 
     */
    public static XStream xstream2 = new XStream(new XppDriver() {
        public HierarchicalStreamWriter createWriter(Writer out) {
            return new PrettyPrintWriter(out) {
                // 对所有xml节点的转换都增加CDATA标记  
                boolean cdata = true;  
  
                @SuppressWarnings("rawtypes")
                public void startNode(String name, Class clazz) {  
                    if(!name.equals("xml")){
                        char[] arr = name.toCharArray();        
                        if (arr[0] >= 'a' && arr[0] <= 'z') {
                            //arr[0] -= 'a' - 'A';
                            //ASCII码，大写字母和小写字符之间数值上差32
                            arr[0] = (char) ((int) arr[0] - 32);
                        }
                        name = new String(arr);//char数组转字符串
                    }
                    super.startNode(name, clazz);   
                }  
  
                @Override
                public void setValue(String text) {
                    if(text!=null && !"".equals(text)){
                        if(text.matches("[0-9]*(\\.?)[0-9]*") || text.matches("[0-9]*(\\.?)[0-9]*")){//如果是正式或者浮点数
                            cdata = false;
                        }else{
                            cdata = true;
                        }
                    }
                    super.setValue(text);  
                }
                
                protected void writeText(QuickWriter writer, String text) {
                    if (cdata) {  
                        writer.write("<![CDATA[");  
                        writer.write(text);  
                        writer.write("]]>");  
                    } else {  
                        writer.write(text);  
                    }  
                }  
            };  
        }  
    });  
    
 
    /** 
     * 扩展xstream，使其支持CDATA块 
     * 
     * @date 2013-05-19 
     */
    public static XStream xstream = new XStream(new XppDriver() {  
        public HierarchicalStreamWriter createWriter(Writer out) {  
            return new PrettyPrintWriter(out) {  
                // 对所有xml节点的转换都增加CDATA标记  
                boolean cdata = true;  
  
                @SuppressWarnings("rawtypes")
                public void startNode(String name, Class clazz) {  
                    super.startNode(name, clazz);   
                }  
                protected void writeText(QuickWriter writer, String text) {  
                    if (cdata) {  
                        writer.write("<![CDATA[");  
                        writer.write(text);  
                        writer.write("]]>");  
                    } else {  
                        writer.write(text);  
                    }  
                }  
            };  
        }  
    }); 
    
    /**
     * 将XML内容转换成对象
     */
    @SuppressWarnings("unchecked")
    public static <T> T unmarshal(String xml, Class<T> clazz) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        return (T)unmarshaller.unmarshal(new StringReader(xml));
    }
    
    /**
     * 将对象转换成XML
     */
    public static String marshal(Object object, Class<?> clazz) throws JAXBException{
        JAXBContext jaxbContext = JAXBContext.newInstance(clazz);
        Marshaller marshaller = jaxbContext.createMarshaller();
        marshaller.setProperty(Marshaller.JAXB_ENCODING, "UTF-8");
        StringWriter writer = new StringWriter();
        marshaller.marshal(object, writer);
        return writer.toString();
    }
    
    /**
     * 将java对象转换为xml
     * @param <T>
     * @return
     */
    public static <T> String JavaToXml(T t){
        xstream.alias("xml", t.getClass());
        return xstream.toXML(t);
    }
    
}