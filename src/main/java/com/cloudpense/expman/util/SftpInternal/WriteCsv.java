package com.cloudpense.expman.util.SftpInternal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 * @date 9/26
 */
public class WriteCsv {
    /**
     * 这里设置的是缓存的文件名，先把csv文件存放到本地，然后再从本地把文件传送到
     * FTP服务器
     */
    private static final String FILE_TEMP = "temp";

    public static String yfWrite(JSONObject jsonObject, String partLine, String fileName){
        int random = (int)(1+Math.random()*3);
        String url = jsonObject.getString("url")+FILE_TEMP+random+".csv";
        /*输出路径在下一级目录*/
        String outputUrl = jsonObject.getString("url")+"output/"+FILE_TEMP+random+".csv";
        String encoding = jsonObject.getString("encoding");
        String remotePath = "/Out/ExpenseReimbursement/"+fileName;
//        String remotePath = "/Out/Expense-test/"+fileName;
        System.out.println("url: "+url);
        File file = new File(url);
        File file1 = new File(outputUrl);
        String data = sort(jsonObject,partLine);
        try {
            /*具体将jsonArray写入csv*/
            if(data ==null){
                System.out.println("空数据");
                return "empty data"; }
            FileUtils.writeStringToFile(file, data,encoding);
            /*创建空文件*/
            FileUtils.writeStringToFile(file1, "",encoding);
            /*添加BOM头*/
            utfBomTurns(url,outputUrl);
            /*上传*/
            String upload = sftpServices.sftpUpload(outputUrl,remotePath);
            System.out.println(upload);
            return upload;
        } catch (IOException e) {
            e.getStackTrace();
            System.out.println("写temp错误！");
        }
        return "fail3";
    }

    private static String sort(JSONObject jsonObject, String partLine) {
        JSONArray tempContent =null;
        String order =null;
        try {
            tempContent = jsonObject.getJSONArray("content");
            order = jsonObject.getString("order");
        }catch (NullPointerException e){
            System.out.println("获取不到内容！");
            return null;
        }

        if(tempContent ==null|order==null){
            return  null;
        }
        if(tempContent.size()==0|order.length()==0){
            return  null;
        }
        System.out.println();
        StringBuffer sb = new StringBuffer();
        /*表头处理*/
        String[] orderArray = order.split(",");
//        for(int t=0;t<orderArray.length;t++){
//            sb.append(orderArray[t]);
//            if (t<orderArray.length-1){
//                sb.append(partLine);
//            }
//        }
        /*表头结束-->换行*/
//        sb.append('\n');
        for(int i=0;i<tempContent.size();i++){
            JSONObject tempObject_a = (JSONObject) tempContent.get(i);
            for(int j=0; j<orderArray.length;j++){
                /*添加列内容*/
                if(tempObject_a.getString(orderArray[j]) != null){
                    sb.append(tempObject_a.getString(orderArray[j]));
                }
                /*添加分割线*/
                if (j<orderArray.length-1){
                    sb.append(partLine);
                }
            }
            /*json数组中的一个json结束-->换行*/
            sb.append("\r\n");
        }
//        System.out.println("sb::\r\n"+sb);
        return sb.toString();
    }
    private  static  void moveFiles(){
//        ftpServices.ftpMoved();
    }

    public static void  utfBomTurns(String inputPath,String outputPath){
        try {
            FileInputStream fis = new FileInputStream(inputPath);
            InputStreamReader isr = new InputStreamReader(fis, StandardCharsets.UTF_8);
            BufferedReader br = new BufferedReader(isr);
            FileOutputStream fos = new FileOutputStream(outputPath);
            fos.write(new byte[]{(byte)0xEF, (byte)0xBB, (byte)0xBF});
            OutputStreamWriter osw = new OutputStreamWriter(fos, StandardCharsets.UTF_8);
            BufferedWriter bw = new BufferedWriter(osw);
            String line = br.readLine();
            StringBuffer buffer=new StringBuffer("");
            if(line!=null){
               buffer = new StringBuffer(line);
            }
            while(line != null) {
                buffer.append("\r\n");
                bw.write(buffer.toString());
                bw.newLine();
                line = br.readLine();
                if(line!=null){
                   buffer = new StringBuffer(line);
                }
            }
            bw.close();
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    public static void main(String[] args) {
////        write(new AcquireJson().jsonRead("C:\\software_wangwei\\souceCodeFiles\\Phoenix_01\\csv\\test_json_xml.json"),
////                "/","","");
//        for(int i =0;i<100;i++){
//        System.out.println((int)(1+Math.random()*3));}
        utfBomTurns("C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\temp1.csv",
                "C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\output\\temp1.csv");
    }
}
