package com.cloudpense.expman.util.SftpInternal;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ReadCsv {

    private static  final String VOUCHER_PATH = "./BI/voucher/";
    private static  final String MAIN_DATA_PATH = "./BI/main_data/";
    /**
     * 从csv中读取并且转换为二维数组
     * @return list
     */
    public static synchronized List read(String path) {
        BufferedReader reader =null;
        try {
            InputStreamReader isr = new InputStreamReader(new FileInputStream(path), "UTF-8");
            reader = new BufferedReader(isr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (reader ==null){
            return null;
        }
        String line = null;
        String line2 =null;
        try {
            String[] title=null;
            String[] temp_a;
            List items = new ArrayList<String[]>();
            if ((line=reader.readLine())!=null){
                line = line.replace("|","/");
                line = line.replace(";",",");
                title= line.split(",", -1);
                System.out.println("title: "+ Arrays.toString(title));
                temp_a =new String[title.length];
                for (int i=0;i<title.length;i++){
                    temp_a[i] = title[i];
                }
                items.add(temp_a);
            }
            while ((line2=reader.readLine())!=null){
                line2 = line2.replace("|","/");
                line2 = line2.replace(";",",");
                String[] body = line2.split(",", -1);
                System.out.println("body: "+ Arrays.toString(body));
                System.out.println("title.length:"+title.length);
                System.out.println("body.length:"+body.length);
                String[] temp_b=new String[title.length];
                for (int j=0;j<body.length;j++){
                    temp_b[j] = body[j];
                    if(body[j].equals("")){temp_b[j]=null;}
                }
                items.add(temp_b);
            }
            return items;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将list转换为json数组
     * @param list
     */
    public static JSONArray putToJson(List list){

        JSONArray jsonArray = new JSONArray();
        String[] title=(String[])list.get(0);
        for(int i =1;i<list.size();i++){
            JSONObject jsonObject = new JSONObject();
            String[] temp_a=(String[])list.get(i);
            for (int j=0;j<temp_a.length;j++){
                if(temp_a[j]!=null){
                jsonObject.put(title[j],temp_a[j]);
                }
            }
            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

    /**
     * 方法重写，用于遍历文件列表的时候具有延续性
     * @param list
     * @param jsonArray
     * @return
     */
    public static JSONArray putToJson(List list, JSONArray jsonArray, String[] title0){
        String[] title=title0;
        if(title0 == null){
            title=(String[])list.get(0);
        }
       /*这里i改为0，因为第一行不再是表头，第一行就已经是内容了*/
        for(int i =0;i<list.size();i++){
            JSONObject jsonObject = new JSONObject();
            String[] temp_a=(String[])list.get(i);
            for (int j=0;j<temp_a.length;j++){
                if(temp_a[j]!=null){
                    jsonObject.put(title[j],temp_a[j]);
                }
            }

            int flag=0;
            for (int k=0;k<jsonArray.size();k++){
                if(jsonArray.getJSONObject(k).equals(jsonObject)){flag=1;}
            }
            /*去掉空行 && 去重*/
            if(jsonObject.size()!=0 && flag==0){
                jsonArray.add(jsonObject);
            }
        }

        return jsonArray;
    }

    /**
     * 遍历指定目录，读取路径下的所有文件(非文件夹)
     * @param path
     * @return
     */

    public static  List readFileName(String path){
        File file = new File(path);
        File[] fs = file.listFiles();
        List fileList =new ArrayList<String>();
        String pattern = "\\S*?.csv";
        Pattern r = Pattern.compile(pattern);
        if(fs!=null){
            for(File f:fs){
                Matcher m = r.matcher(f+"");
                if((!f.isDirectory())&m.matches()){
                    fileList.add(f);
                }
            }}
        return fileList;
    }
    /**
     * 装配json数组
     * @return
     */
    public static synchronized JSONArray assemble(String path, String[] title0){
        JSONArray jsonArray =new JSONArray();
        /*最外层循环，遍历文件列表*/
        for(int i=0;i<readFileName(path).size();i++){
            String filepath = readFileName(path).get(i)+"";
            jsonArray = putToJson(read(filepath),jsonArray,title0);
        }
        System.out.println(jsonArray);
        JSONObject jsonObject =new JSONObject();
        jsonObject.put("data",jsonArray);
        return  jsonArray;
    }

    public static synchronized JSONArray readFromFtp(String remotePath, String localPath, String[] title0){

        boolean deleteThem = deleteFiles(localPath);
        //下载操作
        sftpServices.sftpDownload(remotePath,localPath);
        //读取操作
        JSONArray output = assemble(localPath,title0);
        //读取完成之后再试图清除一次
        deleteThem = deleteFiles(localPath);

        return output;
    }


    /**
     * 删除缓存文件夹里的所有文件
     * @param filepath
     */
    public static synchronized boolean deleteFiles(String filepath){
        List list = readFileName(filepath);
        for(int i=0;i<list.size();i++) {
            System.out.println("AAA: "+list.get(i));
            File files = new File(""+list.get(i));
            if (files.exists() && files.isFile()) {
                if (files.delete()) {
                    System.out.println("delete single file: " + filepath + "success！");

                } else {
                    System.out.println("delete single file: " + filepath + "failure！");

                }
            } else {
                System.out.println("delete single file：" + filepath + "don't exist！");
            }

        }
        return true;
    }

//    public static boolean deleteFtpFiles(String filePath,String fileName){
//
//        return ftpServices.ftpDeleted(filePath,fileName);
//    }



    public static void main(String[] args){
        String[] s ={"1","2","3"};
//        assemble("C://Users//tx//code//csv/",s);
        String str = "ab❤dedgh❤ijkl";
        String[] aa = str.split("❤");
        System.out.println(Arrays.toString(aa));
    }
}
