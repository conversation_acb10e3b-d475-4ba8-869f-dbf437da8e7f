package com.cloudpense.expman.util.SftpInternal;

import com.jcraft.jsch.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Vector;

/**
 * <AUTHOR>
 */
@Service
public class sftpServices {
    static  boolean sftpDownload(String remotePath,String localPath){
        JSch jsch = new JSch();
        Map<String, Object> map ;
        try {
            map= sftpConnect(jsch);
            ChannelSftp  channelSftp = new ChannelSftp();
            if (map == null) {
                return false;
            }
            channelSftp = (ChannelSftp) map.get("channelSftp");
            Session session = (Session) map.get("Session");
            if (channelSftp != null) {
                channelSftp.connect();
                Vector vector =  channelSftp.ls(remotePath);
                for(Object a:vector){
                    ChannelSftp.LsEntry entry=(ChannelSftp.LsEntry)a;
                    System.out.println(entry.getFilename());
                    channelSftp.get(remotePath+entry.getFilename(),localPath);
                }
            }
            if(channelSftp!=null && channelSftp.isConnected()){
                channelSftp.disconnect();
            }
            if(session!=null && session.isConnected()){
                session.disconnect();
            }
            return true;
        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        }
        return false;
    }


    private static  Map<String, Object> sftpConnect(JSch jSch){
        Map<String, Object> map = new HashMap<String, Object>(2);
        Session session =null;
        try {
            session = jSch.getSession("Cloudpense", "yfsftp.yfsafety.com");
            session.setPassword("Vv91279n");
            Properties config = new Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);
            session.connect();
            ChannelSftp channelSftp = (ChannelSftp)session.openChannel("sftp");
            channelSftp.connect();
            map.put("channelSftp",channelSftp);
            map.put("Session",session);
            return map;
        }
        catch(JSchException e){
            e.printStackTrace();
        }
        return  null;
    }

    static synchronized String sftpUpload(String tempPath,String remotePath){
        System.out.println("tempPath: "+tempPath);
        System.out.println("remotePath: "+remotePath);
        JSch jsch = new JSch();
        Map<String, Object> map ;
        map= sftpConnect(jsch);
        ChannelSftp  channelSftp;
        if (map == null) {
            return "fail1";
        }
        try{
        channelSftp = (ChannelSftp) map.get("channelSftp");
        Session session = (Session) map.get("Session");
        if (channelSftp != null) {
            System.out.println("A01");
//            channelSftp.connect();
            channelSftp.put(tempPath,remotePath);
        }
        if(channelSftp!=null && channelSftp.isConnected()){
            channelSftp.disconnect();
        }
        if(session!=null && session.isConnected()){
            session.disconnect();
        }
        return "success";
        }
        catch (SftpException e){

            e.printStackTrace();
        }
        return "fail2";
    }

    public  static void main(String[] s){
        sftpDownload("","");
    }
}
