package com.cloudpense.expman.util;

import com.cloudpense.expman.exception.ValidationException;
import org.apache.commons.lang3.LocaleUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;

/*
 * Created by Huiyi on 2015/4/14.
 */
@Component
public class TranslationUtil {

    private MessageSource httpErrorMessageSource;

    @Autowired
    public TranslationUtil(MessageSource httpErrorMessageSource) {
        this.httpErrorMessageSource = httpErrorMessageSource;
    }

    public void tryTranslateReturnMessage(String returnCode, String returnMessage, String locale)
            throws ValidationException {
        if (returnCode.equals("E")) {
            String message = httpErrorMessageSource.getMessage(returnMessage, null, returnMessage, LocaleUtils.toLocale(locale));
            throw new ValidationException(message);
        }
    }

    public String getMessage(String returnCode, String returnMessage, String locale) {
        String message = new String();
        if (returnCode.equals("E")) {
            message = httpErrorMessageSource.getMessage(returnMessage, null, returnMessage, LocaleUtils.toLocale(locale));
        }
        return message;
    }

    public void tryTranslateReturnMessage2(String returnCode, String returnMessage, String tmp, String locale)
            throws ValidationException {
        if (returnCode.equals("E")) {
            String message = httpErrorMessageSource.getMessage(returnMessage, null, returnMessage, LocaleUtils.toLocale(locale));
            throw new ValidationException(message + "," + tmp);
        }
    }
}