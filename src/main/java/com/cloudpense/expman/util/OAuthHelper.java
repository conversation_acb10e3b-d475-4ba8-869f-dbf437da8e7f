package com.cloudpense.expman.util;


import java.io.*;

/**
 * Created by danica on 15/10/22.
 */
public class OAuthHelper {

    public static String writeFile(String content, String name) throws Exception{
        File file = new File(name);
        if(!file.exists()){
            file.createNewFile();
        }
        FileOutputStream outputStream = new FileOutputStream(file,false);
        StringBuffer sb = new StringBuffer();
        sb.append(content);
        outputStream.write(sb.toString().getBytes("utf-8"));
        outputStream.close();

        System.out.println("the filename is: " + name);
        System.out.println("write successfully");
        return name;
    }

    public static String readFile(String str) throws Exception{
        BufferedReader br=new BufferedReader(new InputStreamReader(new FileInputStream(str),"UTF-8"));
        String line = "";
        String content = "";
        while ((line = br.readLine()) != null) {
            content += line;
        }
        System.out.println(content);
        br.close();
        File file = new File(str);
        file.delete();
        return content;
    }

    public static String readFileOnly(String str){
        String line;
        String content = "";
        try {
            BufferedReader br=new BufferedReader(new InputStreamReader(new FileInputStream(str),"UTF-8"));
            while ((line = br.readLine()) != null) {
                content += line;
            }
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return content;
    }

    public static void appendFile(String file, String conent) {
        BufferedWriter out = null;
        try {
            out = new BufferedWriter(new OutputStreamWriter(
                    new FileOutputStream(file, true)));
            out.write(conent);
            out.newLine();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
