package com.cloudpense.expman.util;

import java.util.LinkedHashMap;
import java.util.Map;

public class LruMap<K, V> extends LinkedHashMap<K, V> {
    /**
     * 缓存容量
     */
    private int CAPACITY;

    public LruMap(int capacity) {
        super(capacity, 1F, true);
        this.CAPACITY = capacity;
    }

    protected boolean removeEldestEntry(Map.Entry eldest) {
        /** 当size 超过容量时，linkedHashMap会删除最不常用元素 */
        return size() > CAPACITY;
    }
    /**
     *	测试
     */
    public static void main(String[] args) {
        // 缓存容量3
        LruMap cache = new LruMap<Integer, Integer>(3);

        cache.put(1, 1);	// 添加1到链尾
        cache.put(2, 2);	// 添加2到链尾
        cache.put(3, 3);	// 添加3到链尾
        cache.put(4, 4);	// 删除1，添加4到链尾
        cache.put(5, 5);	// 删除2，添加5移链尾

        cache.get(4) ;		// 将3移到链尾
        cache.put(6, 6);

        System.out.println("  cache size : " + cache.size());
        System.out.println("  cache content : " + cache);

        for (Object obj : cache.entrySet()) {
            System.out.println(((Map.Entry) obj).getValue());
        }
    }
}