package com.cloudpense.expman.util;

import java.util.Map;


public class GrokTest2 {
    public static void main(String[] args) {
        String pattern = "%{XINTEST}";
        String message = "SH1001          总经理-政府关系部                                               ";
        String json = GrokUtils.toJson(pattern, message);
        System.out.println(json);
        Map<String,Object> map = GrokUtils.toMap(pattern, message);
        System.out.println(map.toString());
    }

}
