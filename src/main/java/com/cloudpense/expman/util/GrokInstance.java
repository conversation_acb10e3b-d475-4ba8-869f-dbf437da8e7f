package com.cloudpense.expman.util;

import io.thekraken.grok.api.Grok;
import io.thekraken.grok.api.Match;
import io.thekraken.grok.api.exception.GrokException;

public class GrokInstance {

    private static Grok grok;

    private GrokInstance() {

    }

    public static Grok getGrokInstance(String grokPatternPath) {
        if (grok == null) {
            try {
                grok = Grok.create(grokPatternPath);
            } catch (GrokException e) {
                e.printStackTrace();
            }
        }
        return grok;
    }

    public static Match getMatch(String pattern, String message) {
        Match match = null;
        try {
            grok.compile(pattern);
            match = grok.match(message);
            match.captures();
        } catch (GrokException e) {
            e.printStackTrace();
            match = null;
        }
        return match;
    }

}
