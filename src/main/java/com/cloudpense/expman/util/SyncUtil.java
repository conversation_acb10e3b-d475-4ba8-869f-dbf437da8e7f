package com.cloudpense.expman.util;

import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 简单同步锁工具，基于内存map结构，支持超时失效
 *
 * <AUTHOR>
 * @Date 2022/3/7
 */
public class SyncUtil {

    private static Logger logger = LoggerFactory.getLogger(SyncUtil.class);

    private static Map<Object, Long> ctx = Maps.newHashMap();

    /**
     * 加锁
     * @param objKey
     * @param lockSeconds
     * @param sleepSeconds
     * @param timeOut
     * @return
     */
    public synchronized static boolean lock(Object objKey, long lockSeconds, long sleepSeconds, long timeOut) {
        boolean success = false;
        long startTime = System.currentTimeMillis();
        while(true) {
            try {
                Long expireTime = ctx.get(objKey);// 到期时间
                long currTime = System.currentTimeMillis();
                if(expireTime == null || expireTime.longValue() < currTime) {
                    ctx.put(objKey, currTime + lockSeconds * 1000L);
                    logger.info("同步锁加锁成功, 持续时间{}秒, key={}", lockSeconds, objKey);
                    success = true;
                    break;
                } else if(currTime - startTime > timeOut * 1000L) {
                    logger.error("同步锁加锁失败, 超时未获取到锁, key={}", sleepSeconds, objKey);
                    break;
                } else {
                    logger.warn("同步锁加锁失败, 开始休眠{}秒, key={}", sleepSeconds, objKey);
                    Thread.sleep(sleepSeconds * 1000L);
                }
            } catch (Throwable t) {
                logger.error("同步锁加锁失败, key={}, error={}", objKey, t.getMessage(), t);
                break;
            }
        }

        return success;
    }

    /**
     * 解锁
     * @param objKey
     */
    public synchronized static void unlock(Object objKey) {
        Long expire = ctx.get(objKey);
        if(expire != null) {
            ctx.remove(objKey);
            logger.info("同步锁释放成功, key={}", objKey);
        }
    }
}
