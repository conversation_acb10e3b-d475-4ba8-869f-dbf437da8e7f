package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;

public class LianDianResponseUtil {
    private static final Logger logger = LoggerFactory.getLogger(LianDianResponseUtil.class);
    public static JSONObject dataProcessing(String returnRequestTime, String returnStatus, String employeeNumber, Object resultInfo, String instId, String attr1, String attr2, String attr3) throws Exception{
        //获取响应结束时的系统时间
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS");//设置日期格式
        Date responseTime = new Date(new Date().getTime() + 8*60*60*1000);
        String returnResponseTime = df.format(responseTime);
        //放置通用的信息
        JSONObject esbInfo = new JSONObject();
        esbInfo.put("instId", instId);
        esbInfo.put("requestTime", returnRequestTime);
        esbInfo.put("responseTime", returnResponseTime);
        esbInfo.put("attr1", attr1);
        esbInfo.put("attr2", attr2);
        esbInfo.put("attr3", attr3);
        //信息分类处理
        if("S".equals(returnStatus)){
            esbInfo.put("returnStatus", returnStatus);
            esbInfo.put("returnCode", "A0001");
            esbInfo.put("returnMsg", "成功获取员工号为" + employeeNumber + "的待办数量");
        }else if("W".equals(returnStatus)){
            esbInfo.put("returnStatus", returnStatus);
            esbInfo.put("returnCode", "A0002");
            esbInfo.put("returnMsg", "输入JSON解析出错，无法获取员工号为" + employeeNumber + "的待办数量");
        }else if("E".equals(returnStatus)){
            esbInfo.put("returnStatus", returnStatus);
            esbInfo.put("returnCode", "E0001");
            esbInfo.put("returnMsg", "获取员工号为" + employeeNumber + "的待办数量失败");
        }

        JSONObject returnJson = new JSONObject();
        returnJson.put("esbInfo", esbInfo);
        returnJson.put("resultInfo", resultInfo);
        logger.info("returnJson==>" + returnJson);
        return returnJson;
    }
}
