package com.cloudpense.expman.util.S3;

import com.cloudpense.expman.dataSource.CustomerContextHolder;

import java.util.HashMap;

public class S3Constants {

    public static final String S3_ACCESS_KEY_ID = "********************";
    public static final String S3_SECRET_KEY = "u8OnMkKkFd9rXkO/bv69cp4B4ac3H+A1hQ5nBxUl";
    public static final String S3_BUCKET_NAME = "cloudpense";
    public static final String QA_S3_BUCKET_NAME = "cloudpense-qa";
    public static final String DCJ_S3_BUCKET_NAME = "cpdcj";
    public static final String KGF_S3_BUCKET_NAME = "cpkgf";
    public static final String PXC_S3_BUCKET_NAME = "cppxc";
    public static final String NPP_S3_BUCKET_NAME = "cpnpp";
    public static final String HSF_S3_BUCKET_NAME = "cphsf";
    public static final String WHX_S3_BUCKET_NAME = "cpwhx";
    public static final String JRC_S3_BUCKET_NAME = "cpjrc";
    public static final String CFG_S3_BUCKET_NAME = "cpcfg";
    public static final String LGG_S3_BUCKET_NAME = "cplgg";
    public static final String EVC_S3_BUCKET_NAME = "cpevc";
    public static final String SHW_S3_BUCKET_NAME = "cpshw";

    public static final S3Util DATA_DEV = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,S3_BUCKET_NAME);
    public static final S3Util DATA_QA = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,QA_S3_BUCKET_NAME);
    public static final S3Util DATA_KGF = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,KGF_S3_BUCKET_NAME);
    public static final S3Util DATA_DCJ = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,DCJ_S3_BUCKET_NAME);
    public static final S3Util DATA_PXC = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,PXC_S3_BUCKET_NAME);
    public static final S3Util DATA_NPP = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,NPP_S3_BUCKET_NAME);
    public static final S3Util DATA_HSF = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,HSF_S3_BUCKET_NAME);
    public static final S3Util DATA_WHX = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,WHX_S3_BUCKET_NAME);
    public static final S3Util DATA_JRC = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,JRC_S3_BUCKET_NAME);
    public static final S3Util DATA_CFG = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,CFG_S3_BUCKET_NAME);
    public static final S3Util DATA_LGG = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,LGG_S3_BUCKET_NAME);
    public static final S3Util DATA_EVC = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,EVC_S3_BUCKET_NAME);
    public static final S3Util DATA_KHB = new S3Util(S3_ACCESS_KEY_ID,S3_SECRET_KEY,S3_BUCKET_NAME);

    public static final HashMap<String,S3Util> S3_LIST = new HashMap<String,S3Util>(){{
        put(CustomerContextHolder.A,DATA_DEV);
        put(CustomerContextHolder.B,DATA_QA);
        put(CustomerContextHolder.C,DATA_DEV);
        put(CustomerContextHolder.D,DATA_DCJ);
//        put(CustomerContextHolder.KGF,DATA_KGF);
        put(CustomerContextHolder.E,DATA_PXC);
        put(CustomerContextHolder.NPP, DATA_NPP);
        put(CustomerContextHolder.F,DATA_HSF);
        put(CustomerContextHolder.WHX,DATA_WHX);
        put(CustomerContextHolder.CFG,DATA_CFG);
        put(CustomerContextHolder.LGG,DATA_LGG);
        put(CustomerContextHolder.EVC,DATA_EVC);
//        put(CustomerContextHolder.JRC,DATA_JRC);
        put(CustomerContextHolder.CPKHB,DATA_KHB);
        put(CustomerContextHolder.CPPRD03,DATA_DEV);
    }};
}
