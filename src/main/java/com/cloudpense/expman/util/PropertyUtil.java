package com.cloudpense.expman.util;

import java.util.ResourceBundle;

/**
 * <AUTHOR>
 */
public class PropertyUtil {
    private static final ResourceBundle RB = ResourceBundle.getBundle("urisetting");

    public static String getHetConfig(String project,String key){
        String env = RB.getString(project+".env");
        if("qa".equals(env)){
            key = "qa." + project + "." + key;
        }
        if("prod".equals(env)){
            key = "prod." + project + "."+ key;
        }

        return RB.getString(key);
    }

    public static String getEnv(String project) {
        return RB.getString(project+".env");
    }

}
