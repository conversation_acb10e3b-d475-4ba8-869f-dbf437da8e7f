package com.cloudpense.expman.util;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.webService.nippon.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.rmi.RemoteException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

import static com.meiya.MeiyaUtils.HttpPostString;

/**
 * Created by wangjiaxin on 2019/8/22.
 * 立邦调用服务封装
 */
@Component
public class SendUtil {

    RestTemplate restTemplate = new RestTemplate();
    RestTemplate httpsRestTemplate;

    private static Logger logger = LoggerFactory.getLogger(SendUtil.class);

    public SendUtil() {
        logger.info("SendUtil初始化开始...");
        try {
            // 忽略ssl证书
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(65000)
                    .setConnectTimeout(65000).setConnectionRequestTimeout(1000).build();
            SSLContext sslContext = SSLContextBuilder.create().setProtocol(SSLConnectionSocketFactory.SSL)
                    .loadTrustMaterial((x, y) -> true).build();
            HttpClient httpClient = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig)
                    .setSSLContext(sslContext).setSSLHostnameVerifier((x, y) -> true).build();
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
            this.httpsRestTemplate = new RestTemplate(requestFactory);
        } catch (NoSuchAlgorithmException e) {
            logger.error("SendUtil初始化失败，NoSuchAlgorithmException异常：{}", e.getMessage(), e);
        } catch (KeyManagementException e) {
            logger.error("SendUtil初始化失败，KeyManagementException异常：{}", e.getMessage(), e);
        } catch (KeyStoreException e) {
            logger.error("SendUtil初始化失败，KeyStoreException异常：{}", e.getMessage(), e);
        }
        logger.info("SendUtil初始化结束");
    }

    public String postForObject(String method, String url, Object request) {
        logger.info("serviceLogPre" + "方法名称：" + method + ";url:" + url
                + ",请求参数：" + getTop60000(request));
        String response = restTemplate.postForObject(url, request, String.class);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public String getForObject(String method, String url) {
        logger.info("serviceLogPre" + "方法名称：" + method + ";url:" + url);
        String response = restTemplate.getForObject(url, String.class);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public String postForObj(String method, String url, Object request) {
        try {
            if(url.startsWith("https")) {
                return httpsRestTemplate.postForObject(url, request, String.class);
            } else {
                return restTemplate.postForObject(url, request, String.class);
            }
        } catch (Exception e) {
            logger.error("SendUtil处理未知异常：{}", e.getMessage(), e);
        }
        return null;
    }

    public String getForObj(String method, String url) {
        try {
            if(url.startsWith("https")) {
                return httpsRestTemplate.getForObject(url, String.class);
            } else {
                return restTemplate.getForObject(url, String.class);
            }
        } catch (Exception e) {
            logger.error("SendUtil处理未知异常：{}", e.getMessage(), e);
        }
        return null;
    }

    public String httpPostString(String method, String url, Map<String, String> headers, String request) throws Exception {
        logger.info("serviceLogPre" + "方法名称：" + method + ";url:" + url
                + ",请求参数：" + getTop60000(request));
        String response = HttpPostString(url, headers, request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public SAPCompanyStub.ZCompanyCdResultE zCompanyCdReq(String method, SAPCompanyStub stub, SAPCompanyStub.ZCompanyCdReqE request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        SAPCompanyStub.ZCompanyCdResultE response = stub.zCompanyCdReq(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER_RESP sI_FK2ERP_COSTANDPROFITCENTER_T(String method, SAPCostCenterStub stub, SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER_RESP response = stub.sI_FK2ERP_COSTANDPROFITCENTER_T(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public SupplierStub.ZfmGetLifnrUndeletedResponse zfmGetLifnrUndeleted(String method, SupplierStub stub, SupplierStub.ZfmGetLifnrUndeleted request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        SupplierStub.ZfmGetLifnrUndeletedResponse response = stub.zfmGetLifnrUndeleted(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：EtLifnr：" + response.getEtLifnr().toString() + "; "
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public CustomerStub.ZFM_SD0005Response zFM_SD0005(String method, CustomerStub stub, CustomerStub.ZFM_SD0005 request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        CustomerStub.ZFM_SD0005Response response = stub.zFM_SD0005(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public PhoneExpenseLimitStub.ZWebExpenseLimitResE zWebExpenseLimit(String method, PhoneExpenseLimitStub stub, PhoneExpenseLimitStub.ZWebExpenseLimitReq request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        PhoneExpenseLimitStub.ZWebExpenseLimitResE response = stub.zWebExpenseLimit(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER_RESP mI_012_O_WEB_ECC_FRAME_ORDER(String method, FrameOrderStub stub, FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER_RESP response = stub.mI_012_O_WEB_ECC_FRAME_ORDER(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public InterOrderStub.ZIntOrderResultE zIntOrderReq(String method, InterOrderStub stub, InterOrderStub.ZIntOrderReqE request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        InterOrderStub.ZIntOrderResultE response = stub.zIntOrderReq(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public TrainingStub.GETLISTReturn gETLIST(String method, TrainingStub stub, TrainingStub.STRYEARMONTH request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        TrainingStub.GETLISTReturn response = stub.gETLIST(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public OutWorkStub.GETLISTReturn gETLIST(String method, OutWorkStub stub) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method);
        OutWorkStub.GETLISTReturn response = stub.gETLIST();
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public CreditStub.GetEmploeesCreditResponse getEmploeesCredit(String method, CreditStub stub, CreditStub.GetEmploeesCredit request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        CreditStub.GetEmploeesCreditResponse response = stub.getEmploeesCredit(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public RateStub.BAPI_EXCHANGERATE_GETDETAILResponse bAPI_EXCHANGERATE_GETDETAIL(String method, RateStub stub, RateStub.BAPI_EXCHANGERATE_GETDETAIL request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数:" + getTop60000(request));
        RateStub.BAPI_EXCHANGERATE_GETDETAILResponse response = stub.bAPI_EXCHANGERATE_GETDETAIL(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public WbsProjectStub.ZPrjResultE zPrjReq(String method, WbsProjectStub stub, WbsProjectStub.ZPrjReqE request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        WbsProjectStub.ZPrjResultE response = stub.zPrjReq(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public ReimOrderStub.SendTravelOrderResponse sendTravelOrder(String method, ReimOrderStub stub, ReimOrderStub.SendTravelOrder request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        ReimOrderStub.SendTravelOrderResponse response = stub.sendTravelOrder(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public HashMap<String, Object> getResponseString(String method, AbsenceStub stub, AbsenceStub.ZHR_ABSENCE request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        HashMap<String, Object> response = stub.getResponseString(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public BudgetStub.GETBUDGETLISTReturn gETBUDGETLIST(String method, BudgetStub stub, BudgetStub.STRYEAR request) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(request));
        BudgetStub.GETBUDGETLISTReturn response = stub.gETBUDGETLIST(request);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    public NipponWechatStub.DoProcessResponse doProcess(String method, NipponWechatStub stub, NipponWechatStub.DoProcess doProcess, NipponWechatStub.UserValidationSoapHeaderE headerE) throws RemoteException {
        logger.info("serviceLogPre" + "方法名称：" + method
                + ",请求参数：" + getTop60000(doProcess));
        NipponWechatStub.DoProcessResponse response = stub.doProcess(doProcess, headerE);
        logger.info("serviceLogPost" + "方法名称：" + method
                + ",返回参数：" + getTop60000(response));

        return response;
    }

    /**
     * 将对象专程字符串取前60000字符
     *
     * @param obj
     * @return
     */
    private static String getTop60000(Object obj) {
        String str = "";
        try {
            str = JSONObject.toJSONString(obj);
        } catch (Exception e) {
            str = obj.toString();
        } finally {
            if (str.length() > 60000) {
                str = str.substring(0, 60000);
            }
            return str;
        }

    }


}
