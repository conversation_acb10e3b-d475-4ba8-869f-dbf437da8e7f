package com.cloudpense.expman.util;

import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 简单幂等锁工具，基于内存map结构，支持超时失效
 *
 * <AUTHOR>
 * @Date 2022/3/7
 */
public class UniLock {

    private static Logger logger = LoggerFactory.getLogger(UniLock.class);

    private static Map<Object, Long> ctx = Maps.newHashMap();

    public synchronized static boolean lock(Object o, Integer minutes) {
        Long expire = ctx.get(o);
        Long current = System.currentTimeMillis();
        if(expire == null || expire < current) {
            ctx.put(o, current + minutes * 60 * 1000L);
            logger.info("{}幂等锁加锁成功, key={}", LogCtx.getLogKey(), o.toString());
            return true;
        }
        logger.error("{}幂等锁加锁失败, key={}", LogCtx.getLogKey(), o.toString());
        return false;
    }

    public synchronized static void unlock(Object o) {
        Long expire = ctx.get(o);
        if(expire != null) {
            ctx.remove(o);
            logger.info("{}幂等锁释放成功, key={}", LogCtx.getLogKey(), o.toString());
        }
    }
}
