package com.cloudpense.expman.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletInputStream;
import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

/**
 * @param
 * <AUTHOR>
 * @version V1.0    1、request克隆  2、提供增加header里面放置对象的方法
 * @ClassName:
 * @Description:
 * @date 2018/6/26 14:03
 */
public class BodyReaderHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private final byte[] body;

    private static Logger logger = LoggerFactory.getLogger(BodyReaderHttpServletRequestWrapper.class);

    public BodyReaderHttpServletRequestWrapper(HttpServletRequest request) throws IOException {
        super(request);
        body = getBodyString(request).getBytes(Charset.forName("UTF-8"));
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {

        final ByteArrayInputStream bais = new ByteArrayInputStream(body);

        return new ServletInputStream() {

            @Override
            public int read() throws IOException {
                return bais.read();
            }

        };
    }

    /**
     * 获取请求Body
     *
     * @param request
     * @return
     */
    public static String getBodyString(ServletRequest request) {
        StringBuilder sb = new StringBuilder();
        BufferedReader bfReader = null;
        try {
            bfReader = request.getReader();
            char[] buffer = new char[1024];
            int bytesRead = -1;
            while ((bytesRead = bfReader.read(buffer)) != -1) {
                sb.append(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            logger.error("Exception:", e);
        } finally {
            if (bfReader != null) {
                try {
                    bfReader.close();
                } catch (IOException e) {
                    logger.error("Exception:", e);
                }
            }
        }
        return sb.toString();
    }

}