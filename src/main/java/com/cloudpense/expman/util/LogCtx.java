package com.cloudpense.expman.util;

/**
 * 日志关键字容器
 *
 * <AUTHOR>
 * @Date 2021/10/26
 */
public class LogCtx {

    private static final ThreadLocal<String> logKeyHolder = new ThreadLocal<>();

    public static void setLogKey(String logKey) {
        logKeyHolder.set(logKey);
    }

    public static String getLogKey() {
        return logKeyHolder.get();
    }

    public static void clearLogKey() {
        logKeyHolder.remove();
    }

}
