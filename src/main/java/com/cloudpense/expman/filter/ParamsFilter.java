package com.cloudpense.expman.filter;


import com.cloudpense.expman.util.BodyReaderHttpServletRequestWrapper;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @param
 * <AUTHOR>
 * @version V1.0
 * @ClassName:
 * @Description:   通用过滤器   主要是对request 进行克隆  方便后续对请求 InputStream 进行使用
 * @date 2018-05-14 10:30
 */
@Component
@WebFilter(urlPatterns = "/*", filterName = "paramsFilter")
@Order(-1808)
public class ParamsFilter implements Filter {

    private static final Logger logger = LoggerFactory.getLogger(ParamsFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse resp = (HttpServletResponse) response;
        String contentType = req.getContentType();
        if(contentType == null || contentType.contains("application/x-www-form-urlencoded") || ServletFileUpload.isMultipartContent(req)) {
            chain.doFilter(request, response);
        } else {
            BodyReaderHttpServletRequestWrapper requestWrapper = new BodyReaderHttpServletRequestWrapper(req);
            chain.doFilter(requestWrapper, resp);
        }
    }

    @Override
    public void destroy() {
    }


}
