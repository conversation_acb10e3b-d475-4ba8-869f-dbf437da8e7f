package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONObject;

public interface NipponService {

    public void updateCompany();

    public void updateCostCenter();

    public void updateSupplier();

    public void updateCustomer();

    public void updatePhoneLimit();

    public void updateFrameOrder(boolean fullSync);

    public void freshStatus();

    public void updateData(String type);

    public void updateDataDeptleader();

    public void updateInterOrder();

    public void updateTraining(boolean fullsync);

    public void updateOutWork();

    public void updateCredit();

    public void updateRate();

    public void updateWbs(boolean fullSync);

    public String getSAPNo(int companyID);

    public void reimPush(boolean fullsync);

    public void travelPush();

    public void invoicePush();

    public void updateBudget();

    public String toggleTravelPush(String status);

    public String toggleTravelPush(String status, String businessGroup);

    public JSONObject getToken(JSONObject jsonObject) throws Exception;

    public void ntfPush();

    void putDoaTargetEnableFlagN();

    void updateEmployeeWhoLeft();

    String pushClaimWithJson(String json);
}
