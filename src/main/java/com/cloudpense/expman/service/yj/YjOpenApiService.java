package com.cloudpense.expman.service.yj;

import cn.hutool.json.JSONObject;
import org.springframework.http.HttpMethod;

/**
 * 云简openapi服务
 *
 * <AUTHOR>
 * @Date 2022/1/13
 */
public interface YjOpenApiService {
    String token(String host, String clientId, String clientSecret);
    JSONObject api(String host, String clientId, String clientSecret, String path, HttpMethod httpMethod, String req);
}
