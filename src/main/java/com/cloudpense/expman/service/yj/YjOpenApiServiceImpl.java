package com.cloudpense.expman.service.yj;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 云简openapi服务实现
 *
 * <AUTHOR>
 * @Date 2022/1/13
 */
@Service
@Slf4j
public class YjOpenApiServiceImpl implements YjOpenApiService {

    /**缓存token*/
    private Map<String, TokenCache> cacheMap = new HashMap<>();

    RestTemplate restTemplate = new RestTemplate();

    @Override
    public String token(String host, String clientId, String clientSecret) {
        long currentTime = System.currentTimeMillis();
        TokenCache tokenCache = Optional.ofNullable(cacheMap.get(clientId)).orElse(new TokenCache());
        Long  expireTime = Optional.ofNullable(tokenCache.getExpire()).orElse(0L);
        if(expireTime > currentTime) {
            log.info("获取云简token：缓存仍有效，expireTime={}", DateUtil.date(expireTime));
            return tokenCache.getValue();
        }
        String url = host + "/common/unAuth/tokens/get"
                   + "?grant_type="    + "client_credentials"
                   + "&client_id="     + clientId
                   + "&client_secret=" + clientSecret;
        try {
            log.info("获取云简token请求：url={}", url);
            String resStr = restTemplate.getForObject(url, String.class);
            log.info("获取云简token响应：res={}", resStr);
            JSONObject resObj = JSONUtil.parseObj(resStr);
            Integer   resCode = resObj.getInt("resCode");
            if(resCode == null || !resCode.equals(200000)) {
                String  resMsg  = resObj.getStr("resMsg");
                log.error("获取云简token失败：{}", resMsg);
                return null;
            }
            JSONObject data = resObj.getJSONObject("data");
            String  token = data.getStr("access_token");
            Long expireIn = data.getLong("expires_in");
            tokenCache.setValue(token);
            tokenCache.setExpire(currentTime + expireIn * 900);// 90%误差防御
            cacheMap.put(clientId, tokenCache);
        } catch (Exception e) {
            log.error("获取云简token异常：{}", e.getMessage(), e);
            return null;
        }

        return tokenCache.getValue();
    }

    @Override
    public JSONObject api(String host, String clientId, String clientSecret, String path, HttpMethod httpMethod, String req) {
        try {
            String url = host + path;
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            String token = token(host, clientId, clientSecret);
            if(token == null) {
                return null;
            }
            headers.add("access_token", token);
            HttpEntity entity = new HttpEntity<>(req, headers);
            log.info("调用云简openapi请求：url={}，req={}", url, req);
            Object resObj = restTemplate.exchange(url, httpMethod, entity, String.class);
            JSONObject res = JSONUtil.parseObj(((ResponseEntity)resObj).getBody());
            log.info("调用云简openapi响应：url={}，res={}", url, res);
            return res;
        } catch (Exception e) {
            log.error("调用云简openapi异常：{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * token数据
     */
    @Data
    @NoArgsConstructor
    public class TokenCache {
        String value;//token值
        Long  expire;//token失效时间
    }

}
