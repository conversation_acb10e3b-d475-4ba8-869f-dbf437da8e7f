package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.GlBatchTransfer;

import java.util.List;

public interface SAPService {
    public JSONArray SqGetData(String key) throws Exception;

    public void SqPutData(List<Object> objList) throws Exception;

    public ExpClaimHeader getExpClaimData(int headerId) throws Exception;

    public void scanClaimPositionId(int positionId, String type) throws Exception;

    public void receiveSapData(String type) throws Exception;

    public void sendByDocumentNum(String documentNum, String type) throws Exception;

    public void sendByHeaderTypeId(int headerTypeId, String type) throws Exception;

    public void iBMPApprove() throws Exception;

    public void iBMPExpensePost() throws Exception;

    public void iPortalApprove() throws Exception;

    public void iPortalTravelPost() throws Exception;

    public void iPortalPush() throws Exception;

    public void sapPost() throws Exception;

    public void phoenixSync() throws Exception;

    public void phoenixSupplier() throws Exception;

    public void phoenixHrPost() throws Exception;

    public String phoenixHrCheck(JSONObject jsonObject) throws Exception;

    public void iPortalDeleteGenerate() throws Exception;

    public void iPortalDeleteReset(JSONObject jsonObject) throws Exception;

    public String meiyaPostDocument(GlBatchTransfer glBatchTransfer) throws Exception;

    public String heraeusPostDocument(GlBatchTransfer glBatchTransfer) throws Exception;

    public void meiyaVoucherReceive() throws Exception;

    public String cjlrPostDocument(GlBatchTransfer glBatchTransfer) throws Exception;

    void generateDocument(Integer companyId, JSONObject generateObject);

}

