package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public interface KehuaService {
    /**
     * 科华工单排期（定时任务）
     */
    public void orderScheduled(String aTime,String bTime,String flag);

    /**
     * 科华SAP报销单凭证回传
     */
    public String sapVoucherPosted(int documentId, String glDate) throws Exception;

    /**
     * 科华SAP报销单预览
     */
    public JSONArray sapVoucherPreview(int documentId, String glDate) throws Exception;

    /**
     * 科华合同接口
     */
    public void khContract(String date) throws IOException;

}
