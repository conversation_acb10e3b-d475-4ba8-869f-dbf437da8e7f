package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

public interface HSFService {
    public void updateUser(boolean fullSync);

    public void updateCompany(boolean fullSync);

    public void updateDept(boolean fullSync);

    public void updatePosition(boolean fullSync);

//    public void updateExpenseType(boolean fullSync);

    public void updateNCContract(boolean fullSync);

    public void updateBankAccount(boolean fullSync);

//    public void updateCustomList(boolean fullSync);
//
//    public void updateCustomItem(boolean fullSync);

    public void claimPush();

    void claimRepush(List<String> documentNums);

    public JSONObject saveOAContract(String contract);

    public void ntfPush();

    public void updateOAToken();

    public void refreshNtf();

    public String encrypt(String id);

    void disableOutdatedData();

    JSONObject getErrorPushedClaims(String startDate, String endDate);
}
