package com.cloudpense.expman.service;

import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.entity.phoenix.Response;
import org.omg.CORBA.PUBLIC_MEMBER;

/**
 * <AUTHOR>
 */
public interface BaanService {
    /**
     * 凭证回传接口
     */
    public void voucherPassBack();

    /**
     * 去掉fnd_user.full_name的空格
     *
     */
    public void fndUserRepair(int a,int b);

    public void fndDepartmentsShow(int a, int b);

    public void baanRulesUpdate();

    public String postVoucher(GlBatchTransfer glBatchTransfer) throws Exception;

    Response phoenixReimbursement(String json);
}
