package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.CustomUserDetails;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.shangfa.HandleEntity;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ShangFaService {


    void sapUserStatus();

    String shangFaVoucher(JSONObject jsonObject);


    String shangfaBuTie(String stringInput, ExpClaimHeader ech, CustomUserDetails activeUser, String locale) throws Exception;

    HandleEntity shangfaVoucherTest(String documentNum, String userName, StringBuilder allErrorMsgs);
}
