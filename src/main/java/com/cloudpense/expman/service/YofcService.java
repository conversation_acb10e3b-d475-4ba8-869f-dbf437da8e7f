package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.changfei.ClaimCopyInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface YofcService {

    /**
     *武汉长飞cbs付款
     */
    public JSONObject cbsPay(JSONObject jsonObject);

    /**
     *yofc凭证(AP发票老接口，调用存储过程)
     */
    public String yofcSapPosted(JSONObject inputData);

    /**
     *yofc凭证预览
     */
    public JSONArray sapVoucherPreview(int documentId, String date);

    public JSONArray cbsPayStart(JSONObject inputJson, int userId) throws Exception;

    List<Map<String, Object>> getPaymentVouchers(List<Integer> headerIds, int userId, String date, int number);

    String postPaymentVouchers(List<Integer> headerIds, int userId, String date, int number) throws Exception;

    /**
     * cbs对公款结果查询,定时任务
     */
    void cbsPayStatusPublic();

    /**
     * cbs对私付款结果查询,定时任务
     */
    void cbsPayStatusPrivate();

    /**
     * cbs对公/对私付款刷新支付时间,定时任务
     */
    void cbsPayDate();

    /**
     * AP发票新接口
     * @param documentId
     * @param date
     * @param userId
     * @return
     */
    String  yofcFaPiaoPosted(String documentId,String date,String userId);

    String postPaymentVouchersPay003(List<Integer> pay003List, int userId, String date, int number);

    List<ClaimCopyInfo> getCopyClaim();

    ExpClaimHeader copyClaim(String agent_id, String inputJson, String userId,ClaimCopyInfo copyInfo,JSONObject jsonObject);

    void updateGlMessage(Integer headerId, String glStatus, String glMessage);

}
