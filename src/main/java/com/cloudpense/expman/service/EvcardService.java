package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

@Service
public interface EvcardService {

    public JSONObject createAccountingSubject(JSONArray jsonArray, Integer companyId) throws Exception;

    public void postDocuments() throws Exception;

    public void postPrSap() throws Exception;

    public void postPrSrm() throws Exception;

    public void postSinglePrSrm(int headerId, String type) throws Exception;

    public String evcardPushTask();

    public String postGetTokenParameter(String employee_number) throws Exception;
}
