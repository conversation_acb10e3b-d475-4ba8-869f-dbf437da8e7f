package com.cloudpense.expman.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.Kehua.KehuaFtp;
import com.Kehua.KehuaS3;
import com.Kehua.KehuaUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.KhSapVoucherInfo;
import com.cloudpense.expman.Request.KhSapVoucherReq;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.kehua.FndLovValue;
import com.cloudpense.expman.mapper.KehuaMapper;
import com.cloudpense.expman.service.KehuaService;
import com.cloudpense.expman.util.Constants.KehuaConstants;
import com.cloudpense.expman.util.GUIDUtil;
import com.cloudpense.expman.util.KeHuaEnum;
import com.cloudpense.expman.util.LogCtx;
import com.cloudpense.expman.util.SendUtil;
import com.google.common.collect.Lists;
import net.sf.json.JSONSerializer;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.Kehua.KehuaUtils.doPost;


/**
 * <AUTHOR>
 */
@Service
public class KehuaServiceImpl implements KehuaService {
    @Autowired
    private KehuaMapper mapper;

    private static Logger logger = LoggerFactory.getLogger(KehuaServiceImpl.class);

    private static final String ENABLED = "true";
    private static final String SUCCESS = "success";
    private static final String LOG_KEYNAME = "LOG_KEY";
    // 特殊行文本单据类型
    private static final List<String> HEDAER_TYPE_CODES1 = Arrays.asList("T1002","YF002","T1015","T1016");
    private static final List<String> HEDAER_TYPE_CODES2 = Arrays.asList("T1002","T1015","T1000","T1003","T1004","T1018","T018");

    @Autowired
    private SendUtil sendUtil;

    @Override
    public void orderScheduled(String aTime, String bTime, String flag) {
        String LOG_KEY = "科华今修工单==";
        /*获取开始时间和结束时间*/
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        /*为了照顾服务器的UTC时间，这里写成+8h,如果本地测试需要注意;指定的时间不受此影响*/
        String beginTime = df.format(System.currentTimeMillis() + 1000 * 60 * 60 * 8 - 1000 * 60 * 45);
        String endTime = df.format(System.currentTimeMillis() + 1000 * 60 * 60 * 8);
        if (ENABLED.equals(flag)) {
            beginTime = aTime;
            endTime = bTime;
        }
        /*查询数据*/
        logger.info("{}开始执行，入参beginTime={}, endTime={}", beginTime, endTime);
        JSONObject jsonObject0 = new JSONObject();
        jsonObject0.put("methods", "resourcePlanSearch");
        jsonObject0.put("beginTime", beginTime);
        jsonObject0.put("endTime", endTime);

        String response = null;
        JSONArray errorArray = new JSONArray();
        JSONObject responseObject = new JSONObject();
        try {
            response = doPost(jsonObject0, jsonObject0.getString("methods"));
            responseObject = JSONObject.parseObject(response);
        } catch (Exception e) {
            logger.error("{}获取接口数据出现未知异常：{}", LOG_KEY, e.getMessage(), e);
            JSONObject error = new JSONObject();
            error.put("message", e.getMessage());
            error.put("error", "resourcePlanSearch查询：网络异常");
            error.put("beginTime", beginTime);
            error.put("endTime", endTime);
            errorArray.add(error);
        }
        if (response != null && !responseObject.getBoolean(SUCCESS)) {
            JSONObject error = new JSONObject();
            error.put("message", responseObject.getString("message"));
            error.put("error", "resourcePlanSearch查询：网络异常");
            error.put("beginTime", beginTime);
            error.put("endTime", endTime);
            errorArray.add(error);
        }

        if (response != null && responseObject.getBoolean(SUCCESS)) {
            JSONArray dataArray = responseObject.getJSONArray("data");
            int cnt = 0;
            logger.error("{}获取到接口数据{}条", LOG_KEY, dataArray.size());
            for (Object object : dataArray) {
                JSONObject eachJsonObject = (JSONObject) object;
                JSONObject inputJson = new JSONObject();
                String resourcePlanNo = eachJsonObject.getString("resourcePlanNo");
                inputJson.put("resourcePlanNo", resourcePlanNo);
                inputJson.put("planSourceNo", eachJsonObject.getString("planSourceNo"));
                inputJson.put("planBeginTime", eachJsonObject.getString("planBeginTime").replace(" +0000", ""));
                inputJson.put("planEndTime", eachJsonObject.getString("planEndTime").replace(" +0000", ""));
                inputJson.put("resourceDeleted", eachJsonObject.getBoolean("resourceDeleted"));
                inputJson.put("planStatus", eachJsonObject.getInteger("planStatus"));
                JSONObject columnJson = new JSONObject();
                columnJson.put("column63", eachJsonObject.getString("customerName"));
                try {
                    logger.info("{}开始处理", LOG_KEY+resourcePlanNo);
                    //下面查询工人工号和城市名
                    String resourceCode = eachJsonObject.getString("resourceCode");
                    String planSourceNo = eachJsonObject.getString("planSourceNo");
                    /*根据资源号查询userCode*/
                    JSONObject jsonObject1 = new JSONObject();
                    if(jsonObject1.getString(LOG_KEYNAME) == null)
                        jsonObject1.put(LOG_KEYNAME, LOG_KEY + resourcePlanNo);
                    jsonObject1.put("methods", "resourceFindByResourceCodes");
                    List<String> list = new ArrayList<>();
                    list.add(resourceCode);
                    jsonObject1.put("resourceCodeList", list);
                    JSONObject resultJson1 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject1, jsonObject1.getString("methods")));
                    String userCode = JSONArray.parseArray(resultJson1.getString("data")).getJSONObject(0).getString("userCode");
                    /*根据userCodes查询userName*/
                    JSONObject jsonObject2 = new JSONObject();
                    if(jsonObject2.getString(LOG_KEYNAME) == null) {
                        jsonObject2.put(LOG_KEYNAME, LOG_KEY + resourcePlanNo);
                    }
                    jsonObject2.put("methods", "userExGetByUserCode");
                    jsonObject2.put("userCode", userCode);
                    JSONObject resultJson2 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject2, jsonObject2.getString("methods")));
                    String userName = JSONArray.parseArray(resultJson2.getString("data")).getJSONObject(0).getString("userName");
                    /*根据工单号查询工单*/
                    JSONObject jsonObject3 = new JSONObject();
                    if(jsonObject3.getString(LOG_KEYNAME) == null) {
                        jsonObject3.put(LOG_KEYNAME, LOG_KEY + resourcePlanNo);
                    }
                    jsonObject3.put("methods", "workOrderGetByNo");
                    jsonObject3.put("workOrderNo", planSourceNo);
                    JSONObject resultJson3 = JSONObject.parseObject(KehuaUtils.doPost(jsonObject3, jsonObject3.getString("methods")));
                    String city = JSONObject.parseObject(resultJson3.getString("data")).getString("city");
                    /*组装输入的json*/
                    //添加字段 ①现场服务类型、②现场服务内容
                    JSONArray udfValueLines = resultJson3.getJSONObject("data").getJSONArray("udfValueLines");
                    if (udfValueLines != null && udfValueLines.size() != 0) {
                        for (int i = 0; i < udfValueLines.size(); i++) {
                            JSONObject fileObject = udfValueLines.getJSONObject(i);
                            String name = fileObject.getString("fieldDbName");
                            String value = fileObject.getString("fieldValue");
                            if ("WorkOrderUdf552".equals(name)) {
                                columnJson.put("column61", value);
                            }
                            if ("WorkOrderUdf545".equals(name)) {
                                columnJson.put("column62", value);
                            }
                        }
                    }
                    inputJson.put("columnJson", columnJson.toString());
                    inputJson.put("PersonNo", userName.replace("A", ""));
                    inputJson.put("city", city.replace("市", ""));
                    /*执行更新*/
                    if (!(inputJson.getBoolean("resourceDeleted"))) {
                        update(inputJson, "orderScheduled");
                        logger.info("{}完成处理", LOG_KEY+resourcePlanNo);
                    } else {
                        logger.info("{}忽略处理", LOG_KEY+resourcePlanNo);
                    }
                } catch (Exception e) {
                    logger.error("{}处理出现未知异常：{}", LOG_KEY + resourcePlanNo, e.getMessage(), e);
                    JSONObject error = new JSONObject();
                    error.put("message", e.getMessage());
                    error.put("error", "查询中网络异常");
                    error.put("resourcePlanNo", eachJsonObject.getString("resourcePlanNo"));
                    error.put("planSourceNo", eachJsonObject.getString("planSourceNo"));
                    errorArray.add(error);
                }
            }
        }
        /*记录错误日志
         * 在循环之外，一次定时任务发送一次
         * */
        if (errorArray.size() != 0) {
            logger.info("{}记录错误日志：{}", LOG_KEY, errorArray.toJSONString());
            mapper.normalInsertLog(14870, "kehuaOrderScheduled", errorArray.toJSONString());
            JSONObject content = JSONObject.parseObject("{\"bcc_to\":null,\"copy_to\":null,\"template\":null}");
            content.put("mail_to", "<EMAIL>," +
                    "<EMAIL>");
            content.put("message", errorArray.toJSONString());
            content.put("subject", "科华工单排期接口异常提醒");
            mapper.mailSend(14870, content.toJSONString());
            logger.info("{}记录错误日志结束", LOG_KEY);
        }


    }

    @Override
    public String sapVoucherPosted(int documentId, String date) throws Exception {
        JSONObject ret = new JSONObject();
        JSONObject inputJson = new JSONObject();
        inputJson.put("documentId", documentId);
        //查询单据头信息
        KhSapVoucherInfo header = mapper.selectExpClaimHeader(documentId);

        logger.info("sapVoucherPosted方法查询的单据头信息" + JSONObject.toJSONString(header));
        //对公凭证类型
        List<String> dg = Arrays.asList("T024", "T1009", "T1000", "YF002", "T1016", "T1002", "T1015", "T1003", "T1010", "T1012", "T1001", "T1018", "T1004", "T1019", "T018", "T1006");
        //查询封装数据g
        JSONObject dataJson = new JSONObject();
        if (dg.contains(header.getTypeCode())) {
            List<KhSapVoucherInfo> lines = mapper.getClaimLine(header.getHeaderId());
            logger.info("sapVoucherPosted方法查询的单据行信息:" + JSONObject.toJSONString(lines));
            String msg = check(header, lines);
            if (StringUtils.isBlank(msg)) {
                //对公
                dataJson = getDataJson2(header, lines, date);
            } else {
                ret.put("exception_level", 99);
                ret.put("message", header.getDocumentNum() + ": " + msg);
                return ret.toJSONString();
            }
        } else {
            //查询单据行信息贷
            List<KhSapVoucherInfo> lineListCr = mapper.selectExpClaimLineCr(header.getHeaderId());
            logger.info("sapVoucherPosted方法查询单据行信息贷:" + JSONObject.toJSONString(lineListCr));

            //查询单据行信息借
            List<KhSapVoucherInfo> lineListDr = mapper.selectExpClaimLineDr(header.getHeaderId());
            logger.info("sapVoucherPosted方法查询单据行信息借:" + JSONObject.toJSONString(lineListDr));

            //对私
            header.setColumn1("Z");
            dataJson = getDataJson(header, lineListCr, lineListDr, inputJson);
        }

        logger.info("科华对私凭证json获取:" + JSONObject.toJSONString(dataJson));
        String data = JSONObject.toJSONString(dataJson);
        String postData = jsonToXml(dataJson.getJSONObject("data").toJSONString());
        String documentNum = dataJson.getJSONObject("tips").getString("document_num");
        Map<String, String> identity = new HashMap<>();
        identity.put("Authorization", "Basic cGk0Zms6SW5pdDQzMjE=");
        identity.put("Content-Type", "text/xml");
        String result = sendUtil.httpPostString("sapVoucherPosted", KehuaConstants.SAP_WSDL, identity, postData);
//        String result = HttpPostString(KehuaConstants.SAP_WSDL, identity, postData);
        logger.info("科华sap凭证推送返回结果 ======= result：" + result);
        XMLSerializer xmlSerializer2json = new XMLSerializer();
        net.sf.json.JSON resultJson = xmlSerializer2json.read(result);
        JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
        JSONObject responseJson = jsonObject.getJSONObject("SOAP:Body")
                .getJSONObject("ns0:ZFK_FI_GL_010.Response");

        try {
            String resultString = responseJson.getString("MESSAGE");
            String resultCode = responseJson.getString("STA");
            String O_BELNR = responseJson.getString("O_BELNR");
            String sapVoucherCode = "";
            if (O_BELNR.length() > 10) {
                sapVoucherCode = O_BELNR.substring(0, 10);
            }
            if (!"S".equals(resultCode)) {
                ret.put("exception_level", 99);
                ret.put("message", documentNum + ": " + resultString);
            } else {
                ret.put("exception_level", 0);
                ret.put("message", documentNum + ": " + "OK");
                ret.put("voucher_code", sapVoucherCode);
                inputJson.put("voucherNumber", sapVoucherCode);
                String aa = update(inputJson, "apichange");
            }
//            System.out.println(result);
        } catch (Exception e) {
            logger.error("科华凭证接口调用异常，documentNum：" + documentNum + " error：", e);
            ret.put("exception_level", 99);
            ret.put("message", documentNum + "-" + "运行出错: " + e.getMessage());
        }
        return ret.toJSONString();
    }

    /**
     * 单据行数据校验
     *
     * @param header
     * @param lines
     * @return
     */
    private String check(KhSapVoucherInfo header, List<KhSapVoucherInfo> lines) {
        String msg = null;
        //公司代码
        String departmentCode = header.getDepartmentCode();
        if (StringUtils.isBlank(departmentCode)) {
            msg = "单据数据异常，公司代码(charge_department)为空";
            return msg;
        }
        for (KhSapVoucherInfo line : lines) {
            if ("claim_supplier".equals(header.getHeaderInternalType()) && "advance".equals(line.getLineInternalType())) {
                //付款单冲抵行科目可以为空，无需校验
                continue;
            }
            //贷方科目代码
            String crAccountCode = line.getCrAccountCode();
            //借方科目代码
            String drAccountCode = line.getDrAccountCode();
            //税方科目代码
            String taxAccountCode = line.getTaxAccountCode();
            if (StringUtils.isBlank(crAccountCode)) {
                msg = "单据数据异常，贷方科目(cr_account_id)为空";
                return msg;
            }
            if (StringUtils.isBlank(drAccountCode)) {
                msg = "单据数据异常，借方科目(dr_account_id)为空";
                return msg;
            }
            //税额
            Double finTaxAmount = line.getFinTaxAmount();
            //是否有税额
            boolean seFlag = true;
            if (finTaxAmount == null || "".equals(finTaxAmount) || 0 == finTaxAmount) {
                seFlag = false;
            }
            if (seFlag && StringUtils.isBlank(taxAccountCode)) {
                msg = "单据数据异常，税方科目(tax_account_id)为空";
                return msg;
            }
        }
        return msg;
    }

    //科华对公凭证json获取
    private JSONObject getDataJson2(KhSapVoucherInfo header, List<KhSapVoucherInfo> lineList, String glDate) {
        //付款单去除冲抵行
        Iterator<KhSapVoucherInfo> iterator = lineList.iterator();
        while (iterator.hasNext() && "claim_supplier".equals(header.getHeaderInternalType())) {
            KhSapVoucherInfo line = iterator.next();
            if ("advance".equals(line.getLineInternalType())) {
                iterator.remove();
            }
        }

        //冲账金额
        Double advanceAmount = header.getAdvanceAmount();
        //是否需要进行冲账
        boolean czFlag = true;
        if (advanceAmount == null || 0 == advanceAmount || !"claim_supplier".equals(header.getHeaderInternalType())) {
            czFlag = false;
        }

        //科目封装
        JSONArray lineItem = new JSONArray();

        //头column50之后数据
        String columnJson = header.getColumnJson();
        Map<String, Object> columnMap = new HashMap<>();
        if (null != columnJson) {
            columnMap = JSON.parseObject(columnJson, Map.class);
        }

        //所有税方科目代码
        List<String> taxAccountCodes = new ArrayList<>();
        //科目行数
        int num = 1;
        //遍历所有行
        Set<String> lineTypeSet = new HashSet<>();// 费用类型集合
        for (KhSapVoucherInfo line : lineList) {
            lineTypeSet.add(line.getLineType());
            taxAccountCodes.add(line.getTaxAccountCode());
            //税额
            Double finTaxAmount = line.getFinTaxAmount();
            //是否有税额
            boolean seFlag = true;
            if (finTaxAmount == null || "".equals(finTaxAmount) || 0 == finTaxAmount) {
                seFlag = false;
            }
            //税转出金额
            String column6 = line.getColumn6();
            //是否需要进行税转出
            boolean szFlag = true;
            if (column6 == null || "".equals(column6) || Double.valueOf(column6) == 0) {
                szFlag = false;
            }
            //非应税转出金额
            String column7 = line.getColumn7();
            //是否需要进行非应税转出
            boolean fszFlag = true;
            if (column7 == null || "".equals(column7) || Double.valueOf(column7) == 0) {
                fszFlag = false;
            }
            //借方科目
            KhSapVoucherReq jfReq = new KhSapVoucherReq();
            //封装借方数据
            jfReq = getKhSapVoucherJfReq(header, line, seFlag);
            jfReq.setBUZEI(num);
            //借方金额为0时，整行不带出
            if (Double.valueOf(jfReq.getWRBTR()) == 0) {
                continue;
            }
            num++;
            lineItem.add(jfReq);

            //含税额时
            if (seFlag) {
                //税方科目
                KhSapVoucherReq sfReq = new KhSapVoucherReq();
                //封装税方数据
                sfReq = getKhSapVoucherSfReq(header, line, columnMap);
                sfReq.setBUZEI(num);
                num++;
                lineItem.add(sfReq);
            }
            //含税转时新增税转科目
            if (szFlag) {
                KhSapVoucherReq szReq = new KhSapVoucherReq();
                szReq.setBUZEI(num);
                szReq.setBSCHL("50");
                szReq.setHKONT("2221010901");
                szReq.setWRBTR(column6);
//                szReq.setSGTXT("简易征收不得抵扣进项税金");
                szReq.setSGTXT(columnMap.get("column53") + line.getInvoiceNum());
                num++;
                lineItem.add(szReq);
            }
            //含非应税转出时新增非应税转科目
            if (fszFlag) {
                KhSapVoucherReq fszReq = new KhSapVoucherReq();
                fszReq.setBUZEI(num);
                fszReq.setBSCHL("50");
                fszReq.setHKONT("2221010902");
                fszReq.setWRBTR(column7);
//                fszReq.setSGTXT("非应税进项税转出");
                fszReq.setSGTXT(columnMap.get("column53") + line.getInvoiceNum());
                num++;
                lineItem.add(fszReq);
            }
        }
        //计算贷方金额
        double wrbterCredit = header.getTotalPayAmount();
        if ("request_supplier".equals(header.getHeaderInternalType())) {
            wrbterCredit = header.getAdvanceAmount();
        }
        BigDecimal b = new BigDecimal(wrbterCredit);
        wrbterCredit = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        //贷方科目
        KhSapVoucherReq dfReq = new KhSapVoucherReq();
        dfReq.setBUZEI(num);
        dfReq.setBSCHL("50");
        dfReq.setHKONT(lineList.get(0).getCrAccountCode());
        dfReq.setWRBTR(String.valueOf(wrbterCredit));
        //贷方行文本
//        if ("request_supplier".equals(header.getHeaderInternalType())) {
//            //供应商预付款
//            dfReq.setSGTXT("预付款(" + header.getSuFullName() + ")");
//        } else {
            //供应商付款 (费用类型+申请人名+#发票号+供应商名称)
//            if(HEDAER_TYPE_CODES1.contains(header.getTypeCode())) {
//                dfReq.setSGTXT(columnMap.get("column53") + "");
//            } else {
//                dfReq.setSGTXT(lineList.get(0).getLineType() + header.getFullName() + "#" +
//                               lineList.get(0).getInvoiceNum() + header.getSupplierName());
//            }
//        }
        dfReq.setSGTXT(columnMap.get("column53") + "");
        //贷方原因代码
        dfReq.setRSTGR(String.valueOf(columnMap.get("column51")));
        //贷方金额为0时不带出
        if (wrbterCredit != 0) {
            num++;
            lineItem.add(dfReq);
        }

        //冲账科目
        if (czFlag) {
            KhSapVoucherReq czReq = new KhSapVoucherReq();
            czReq.setBUZEI(num);
            czReq.setBSCHL("31");
            czReq.setWRBTR(String.valueOf(advanceAmount));
            czReq.setLIFNR(header.getSupplierCode());
            //冲账行文本
//            if(HEDAER_TYPE_CODES1.contains(header.getTypeCode())) {
//                czReq.setSGTXT(columnMap.get("column53") + "");
//            } else {
//                czReq.setSGTXT(lineList.get(0).getLineType() + header.getFullName() +
//                               lineList.get(0).getInvoiceNum() + header.getSupplierName());
//            }
            czReq.setSGTXT(columnMap.get("column53") + "");
            if(HEDAER_TYPE_CODES2.contains(header.getTypeCode())) {
                List<String> linkDocumentNumList = mapper.queryAdvanceLinkDocumentNumList(header.getHeaderId());
                String linkDocumentNum = "";
                if(CollUtil.isNotEmpty(linkDocumentNumList)) {
                    linkDocumentNum = StrUtil.join("-", linkDocumentNumList);
                }
                czReq.setSGTXT(linkDocumentNum + czReq.getSGTXT());
            }
            if ("T018".equals(header.getTypeCode())) {
                czReq.setHKONT("1221021400");
                czReq.setBSCHL("39");
                czReq.setUMSKZ("L");
            } else {
                czReq.setHKONT("2202020300");
            }
            num++;
            lineItem.add(czReq);
        }

        JSONObject dataJson = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        //头数据
        KhSapVoucherReq headerReq = new KhSapVoucherReq();
        //公司代码
        if (StringUtils.isNotBlank(header.getColumn13())) {
            headerReq.setBUKRS(header.getColumn13());
        }

        String voucherDate = sdf.format(new Date());
        //凭证日期
        headerReq.setBLDAT(voucherDate);
        //过账日期
        headerReq.setBUDAT(voucherDate);

        List<String> KA = Arrays.asList("T1009", "T1000", "YF002", "T1016", "T1003", "T1010", "T1012", "T1001", "T1018", "T1004", "T1019", "T1006");
        List<String> KZ = Arrays.asList("T024", "T1002", "T1015", "T018");
        //凭证类型
        if (KA.contains(header.getTypeCode())) {
            headerReq.setBLART("KA");
        }
        if (KZ.contains(header.getTypeCode())) {
            headerReq.setBLART("KZ");
        }
        //用户名
        headerReq.setUSNAM("LUOTX");
        //货币码
        if (header.getCurrencyCode() == null) {
            headerReq.setWAERS("CNY");
        } else {
            headerReq.setWAERS(header.getCurrencyCode());
        }
        //头文本
//        if (Arrays.asList("T1009", "T1010", "T1012", "T1018", "T1019").contains(header.getTypeCode())) {
//            headerReq.setBKTXT("预付款(" + header.getSuFullName() + ")");
//        } else if (Arrays.asList("T024", "T1000", "T1003", "T1001", "T1004", "T1018").contains(header.getTypeCode())) {
//            headerReq.setBKTXT("付款#" + lineList.get(0).getInvoiceNum());
//        } else if (Arrays.asList("T1015", "T1002", "T1016", "YF002").contains(header.getTypeCode())) {
//            headerReq.setBKTXT(columnMap.get("column53") + "");
//        } else if(Arrays.asList("T1006").contains(header.getTypeCode())) {
//            headerReq.setBKTXT(StrUtil.sub(header.getSuFullName() + header.getColumn49(), 0, 25));
//        }
        headerReq.setBKTXT(StrUtil.join("、", lineTypeSet));
        //是否预制凭证
//        headerReq.setI_STATUS(header.getColumn1());
        //单据号
        headerReq.setXBLNR(header.getDocumentNum());

        // 内部订单号
//        String aufnr = "";
//        if(Arrays.asList("T018", "T1019").contains(header.getTypeCode())) {
//            aufnr = header.getProjectName();
//        }
//        headerReq.setAUFNR(aufnr);

        // 本次支付金额
        headerReq.setZBCZF(String.valueOf(header.getTotalPayCurrencyAmount()));
        // 冲抵预付金额
        headerReq.setZCDYF(String.valueOf(header.getAdvanceAmount()));
        // 单据类型代码
        headerReq.setZDJDM(header.getTypeCode());

        //科目汇总
        JSONArray lineItemNew = new JSONArray();
        int k = 1;
        LinkedHashSet<String> codes = new LinkedHashSet<>();
        for (int i = 0; i < lineItem.size(); i++) {
            String code = lineItem.getJSONObject(i).getString("HKONT");
            codes.add(code);
        }

        for (String code : codes) {
            if (taxAccountCodes.contains(code)) {
                //税科目不进行合并
                JSONObject item = new JSONObject();
                for (int i = 0; i < lineItem.size(); i++) {
                    if (code.equals(lineItem.getJSONObject(i).getString("HKONT"))) {
                        item = lineItem.getJSONObject(i);
                        item.put("BUZEI", k);
//                        item.put("AUFNR", aufnr);
                        lineItemNew.add(item);
                        k++;
                    }
                }
            } else {
                //非税科目进行合并
                JSONObject item = new JSONObject();
                //汇总金额
                String sumAmount = "0";
                for (int i = 0; i < lineItem.size(); i++) {
                    //科目代码
                    String iCode = lineItem.getJSONObject(i).getString("HKONT");
                    //科目金额
                    String iAmount = lineItem.getJSONObject(i).getString("WRBTR");
                    //科目相同将金额汇总
                    if (iCode.equals(code)) {
                        sumAmount = Double.valueOf(sumAmount) + Double.valueOf(iAmount) + "";
                        //处理double类型计算时丢失精度问题
                        BigDecimal amount = new BigDecimal(sumAmount);
                        sumAmount = String.valueOf(amount.setScale(2, BigDecimal.ROUND_HALF_UP));
                        item = lineItem.getJSONObject(i);
                    }
                }
                item.put("WRBTR", sumAmount);
                item.put("BUZEI", k);
//                item.put("AUFNR", aufnr);
                lineItemNew.add(item);
                k++;
            }

        }

        //头封装
        JSONObject headers = new JSONObject();
        headers.put("item", headerReq);
        //行封装
        JSONObject lines = new JSONObject();
        lines.put("item", lineItemNew);
        //数据封装
        JSONObject data = new JSONObject();
        //是否预制凭证
        data.put("I_STATUS", header.getColumn1());
        data.put("header", headers);
        data.put("lines", lines);
        dataJson.put("data", data);
        JSONObject tips = new JSONObject();
        tips.put("document_num", header.getDocumentNum());
        dataJson.put("tips", tips);

        return dataJson;
    }

    //科华对私凭证json获取
    private JSONObject getDataJson(KhSapVoucherInfo header, List<KhSapVoucherInfo> lineListCr, List<KhSapVoucherInfo> lineListDr, JSONObject inputJson) {
        JSONObject dataJson;
        //贷方请求数据
        List<KhSapVoucherReq> creditReqList;
        //借方请求数据
        List<KhSapVoucherReq> borrowReqList;
        //计算贷方金额
        double wrbterCredit = 0;
        for (KhSapVoucherInfo khSapVoucherInfoCurr : lineListCr) {
            wrbterCredit += khSapVoucherInfoCurr.getFinClaimAmount();
        }
        BigDecimal b = new BigDecimal(wrbterCredit);
        wrbterCredit = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
        //行数据
        if ("RT005".equals(header.getTypeCode())) {//借款单
            //贷方
            creditReqList = getKhSapVoucherCreditReq(header, lineListCr, KeHuaEnum.BORROW_CREDIT.getName(), null, wrbterCredit);
            //借方
            borrowReqList = getKhSapVoucherBorrowReq(header, lineListDr, KeHuaEnum.BORROW_BORROW.getName(), "E");
        } else if ("T003".equals(header.getTypeCode())) {//还款单
            //贷方
            creditReqList = getKhSapVoucherCreditReq(header, lineListCr, KeHuaEnum.REPAY_CREDIT.getName(), "E", header.getAdvanceAmount());
            //借方
            borrowReqList = getKhSapVoucherBorrowReq(header, lineListDr, KeHuaEnum.REPAY_BORROW.getName(), null);
        } else {
            if (header.getAdvanceAmount() == 0) {
                dataJson = JSONObject.parseObject(update(inputJson, "sapExpenseAccount"));
            } else {
                dataJson = JSONObject.parseObject(update(inputJson, "sapExpenseAccountNEW"));
            }
            return dataJson;
        }
        dataJson = packageDataJson(header, creditReqList, borrowReqList);
        return dataJson;

    }

    //封装DataJson
    public JSONObject packageDataJson(KhSapVoucherInfo header, List<KhSapVoucherReq> creditReqList, List<KhSapVoucherReq> borrowReqList) {
        JSONObject dataJson = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar now = Calendar.getInstance();
        //头数据
        KhSapVoucherReq headerReq = new KhSapVoucherReq();

        if ("RT005".equals(header.getTypeCode())) {
            headerReq.setBKTXT(header.getFullName() + "-个人借款-" + now.get(Calendar.YEAR) + (now.get(Calendar.MONTH) + 1) + "");
        } else {
            headerReq.setBKTXT(header.getFullName() + "-个人还款-" + now.get(Calendar.YEAR) + (now.get(Calendar.MONTH) + 1) + "");
        }

        headerReq.setBLART("YJ");
        headerReq.setBLDAT(sdf.format(new Date()));
        headerReq.setBUDAT(sdf.format(new Date()));

        if ("RT005".equals(header.getTypeCode())) {
            headerReq.setBUKRS(header.getBranchCode());
        } else {
            headerReq.setBUKRS(header.getColumn13());
        }
        headerReq.setXBLNR(header.getDocumentNum());
//        headerReq.setI_STATUS(header.getColumn1());

        if (Arrays.asList("T017").contains(header.getTypeCode())) {
            headerReq.setAUFNR(header.getProjectName());//
        }

        if(Arrays.asList("RT005").contains(header.getTypeCode())) {
            headerReq.setZDJDM(header.getTypeCode());
            headerReq.setZBCZF(StrUtil.toStringOrNull(header.getTotalPayCurrencyAmount()));
        }

        //头封装
        JSONObject headers = new JSONObject();
        headers.put("item", headerReq);
        //行封装
        JSONArray lineItem = new JSONArray();
        lineItem.addAll(creditReqList);
        lineItem.addAll(borrowReqList);
        JSONObject lines = new JSONObject();
        lines.put("item", lineItem);

        //数据封装
        JSONObject data = new JSONObject();
        //是否预制凭证
        data.put("I_STATUS", header.getColumn1());
        data.put("header", headers);
        data.put("lines", lines);
        dataJson.put("data", data);
        JSONObject tips = new JSONObject();
        tips.put("document_num", header.getDocumentNum());
        dataJson.put("tips", tips);
        return dataJson;
    }

    //对私贷方数据封装
    private List<KhSapVoucherReq> getKhSapVoucherCreditReq(KhSapVoucherInfo header, List<KhSapVoucherInfo> lineListCr, String bschl, String umskz, double wrbterCredit) {
//        String HKONT = getHKONT(header.getLeaveType());
        List<KhSapVoucherReq> creditReqList = new ArrayList<>();
        for (KhSapVoucherInfo line : lineListCr) {
            KhSapVoucherReq creditReq = new KhSapVoucherReq();

            creditReq.setHKONT(line.getAccountCode());
            if (KeHuaEnum.BORROW_CREDIT.getName().equals(bschl)) {
                creditReq.setRSTGR("B04");
            } else {
                creditReq.setUSNAM(header.getEmployeeNumber());
                creditReq.setLIFNR(header.getColumn3());
            }
            creditReq.setBSCHL(bschl);
            creditReq.setUMSKZ(umskz);
            creditReq.setWRBTR(String.valueOf(wrbterCredit));

            if (Arrays.asList("T017").contains(header.getTypeCode())) {
                creditReq.setAUFNR(header.getProjectName());//
            }

            creditReqList.add(creditReq);
        }
        return creditReqList;
//        creditReq.setKOSTL(header.getDepartmentCode());
    }

    //对公借方数据封装
    private KhSapVoucherReq getKhSapVoucherJfReq(KhSapVoucherInfo header, KhSapVoucherInfo line, boolean seFlag) {
        KhSapVoucherReq jfReq = new KhSapVoucherReq();
        //税转金额
        String column6 = line.getColumn6();
        if (StringUtils.isBlank(column6)) {
            column6 = "0";
        }
        //非应税转金额
        String column7 = line.getColumn7();
        if (StringUtils.isBlank(column7)) {
            column7 = "0";
        }
        //科目
        jfReq.setHKONT(line.getDrAccountCode());
        if (seFlag) {
            double jfAmount = line.getFinClaimAmount() - line.getFinTaxAmount() + Double.valueOf(column6) + Double.valueOf(column7);
            BigDecimal b = new BigDecimal(jfAmount);
            jfAmount = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            jfReq.setWRBTR(String.valueOf(jfAmount));
        } else {
            jfReq.setWRBTR(String.valueOf(line.getFinReceiptAmount()));
        }
        //借方记账码
        if (Arrays.asList("T1002", "T1015", "T018").contains(header.getTypeCode())) {
            jfReq.setBSCHL("40");
        } else if ("T1019".equals(header.getTypeCode())) {
            jfReq.setBSCHL("29");
            jfReq.setUMSKZ("L");
        }  else if ("T1006".equals(header.getTypeCode())) {
            jfReq.setBSCHL("39");
            if(StrUtil.equals(jfReq.getHKONT(), "**********")) {
                jfReq.setUMSKZ("J");
            } else if(StrUtil.equals(jfReq.getHKONT(), "**********")) {
                jfReq.setUMSKZ("T");
            }
        }  else {
            jfReq.setBSCHL("21");
        }
        //供应商
        jfReq.setLIFNR(header.getSupplierCode());
        //成本中心
        if (!"**********".equals(line.getDrAccountCode())) {
            //借方行如果是**********-应付职工薪酬-短期薪酬-职工福利费 科目，则此行不传成本中心
            jfReq.setKOSTL(header.getCdColumn1());
        }
        //借方行文本
        //头column50之后数据
        String columnJson = header.getColumnJson();
        Map<String, Object> columnMap = new HashMap<>();
        if (null != columnJson) {
            columnMap = JSON.parseObject(columnJson, Map.class);
        }
//        if ("request_supplier".equals(header.getHeaderInternalType())) {
//            //供应商预付款
//            jfReq.setSGTXT("预付款(" + header.getSuFullName() + ")");
//        } else {
            //供应商付款 (费用类型+申请人名+#发票号+供应商名称)
//            if(HEDAER_TYPE_CODES1.contains(header.getTypeCode())) {
//                jfReq.setSGTXT(columnMap.get("column53") + "");
//            } else {
//                jfReq.setSGTXT(line.getLineType() + header.getSuFullName() + "#" +
//                               line.getInvoiceNum() + header.getSupplierName());
//            }
//        }
        jfReq.setSGTXT(columnMap.get("column53") + "");
        //内部订单号
        if (Arrays.asList("T1015", "T018", "T019").contains(header.getTypeCode())) {
            jfReq.setAUFNR(header.getProjectName());
        }
        // 币种
        jfReq.setWAERS(line.getReceiptCurrency());

        return jfReq;
    }

    //对公税方数据封装
    private KhSapVoucherReq getKhSapVoucherSfReq(KhSapVoucherInfo header, KhSapVoucherInfo line, Map<String, Object> columnMap) {
        KhSapVoucherReq sfReq = new KhSapVoucherReq();
        //记账码
        sfReq.setBSCHL("40");
        //科目代码
        sfReq.setHKONT(line.getTaxAccountCode());
        //税额
        sfReq.setWRBTR(String.valueOf(line.getFinTaxAmount()));
        //税码
        sfReq.setMWSKZ(line.getTaxCode());
        //税基额
//        sfReq.setFWBAS(line.getFinNetAmount());
//        sfReq.setLIFNR(header.getSupplierCode());
//        sfReq.setKOSTL(header.getCdColumn1());
        //税方行文本
        sfReq.setSGTXT(columnMap.get("column53") + line.getInvoiceNum());
        return sfReq;
    }

    //对私借方数据封装
    private List<KhSapVoucherReq> getKhSapVoucherBorrowReq(KhSapVoucherInfo header, List<KhSapVoucherInfo> lineList, String bschl, String umskz) {
        List<KhSapVoucherReq> borrowReqList = new ArrayList<KhSapVoucherReq>();
        String HKONT = getHKONT(header.getLeaveType());
        for (KhSapVoucherInfo line : lineList) {
            KhSapVoucherReq borrowReq = new KhSapVoucherReq();
            borrowReq.setBSCHL(bschl);
            borrowReq.setUMSKZ(umskz);
            borrowReq.setHKONT(line.getAccountCode());
            if (KeHuaEnum.REPAY_BORROW.getName().equals(bschl)) {
                borrowReq.setRSTGR("B04");
            } else {
                borrowReq.setUSNAM(header.getEmployeeNumber());
                borrowReq.setLIFNR(header.getColumn3());
            }
            borrowReq.setWRBTR(String.valueOf(line.getFinClaimAmount()));
//            borrowReq.setKOSTL(header.getDepartmentCode());

            if (Arrays.asList("T017").contains(header.getTypeCode())) {
                borrowReq.setAUFNR(header.getProjectName());//
            }

            borrowReqList.add(borrowReq);
        }
        return borrowReqList;
    }

    private String getHKONT(String leaveType) {
        String HKONT = "";
        if (leaveType == null) {
            HKONT = "";
        } else if (leaveType.length() <= 10) {
            HKONT = leaveType;
        } else if (leaveType.length() > 10) {
            HKONT = leaveType.substring(0, 10);
        }
        return HKONT;
    }

    @Override
    public JSONArray sapVoucherPreview(int documentId, String glDate) {
        JSONArray dataJsonArray;
        //查询单据头信息
        KhSapVoucherInfo header = mapper.selectExpClaimHeader(documentId);
        //查询单据行信息
        List<KhSapVoucherInfo> lines = mapper.getClaimLine(header.getHeaderId());

        //付款单去除冲抵行
        Iterator<KhSapVoucherInfo> iterator = lines.iterator();
        while (iterator.hasNext() && "claim_supplier".equals(header.getHeaderInternalType())) {
            KhSapVoucherInfo line = iterator.next();
            if ("advance".equals(line.getLineInternalType())) {
                iterator.remove();
            }
        }
        //对公凭证类型
        List<String> dg = Arrays.asList("T024", "T1009", "T1000", "YF002", "T1016", "T1002", "T1015", "T1003", "T1010", "T1012", "T1001", "T1018", "T1004", "T1019", "T018", "T1006");

        if (dg.contains(header.getTypeCode())) {
            //校验单据数据
            String msg = check(header, lines);
            if (StringUtils.isNotBlank(msg)) {
                JSONArray array = new JSONArray();
                array.add(msg);
                JSONArray msgArray = new JSONArray();
                msgArray.add(array);
                return msgArray;
            }
            //对公凭证预览
            JSONObject dataJson = getDataJson2(header, lines, glDate);
            Map headerMap = JSONObject.parseObject(dataJson.getJSONObject("data").getJSONObject("header").getJSONObject("item").toJSONString());
            List<Map> lineList = JSONObject.parseArray(dataJson.getJSONObject("data").getJSONObject("lines").getJSONArray("item").toJSONString(), Map.class);
            JSONArray jsonArray = new JSONArray();
            //头
            jsonArray.add(JSONArray.parseArray("[\"类型\", \"单据编号\", \"公司代码\", \"财务年度\", \"提交日期\", \"过账日期\", \"币种\", \"摘要\"]"));
            JSONArray headerJson = new JSONArray();
            headerJson.add(lines.get(0).getLineType());
            headerJson.add(headerMap.get("XBLNR"));
            headerJson.add(headerMap.get("BUKRS"));
            headerJson.add(headerMap.get("BLDAT").toString().substring(0, 4));
            headerJson.add(headerMap.get("BLDAT"));
            headerJson.add(headerMap.get("BUDAT"));
            headerJson.add(headerMap.get("WAERS"));
            headerJson.add(headerMap.get("BKTXT"));
            jsonArray.add(headerJson);
            //行
            jsonArray.add(JSONArray.parseArray("[\"行项目号\", \"记账码\", \"记账科目\", \"科目描述\", \"成本中心\", \"记账金额\", \"描述\"]"));
            for (Map lineMap : lineList) {
                JSONArray lineJson = new JSONArray();
                lineJson.add(lineMap.get("BUZEI"));
                lineJson.add(lineMap.get("BSCHL"));
                lineJson.add(lineMap.get("HKONT"));
                String accountName = mapper.getAccountName(String.valueOf(lineMap.get("HKONT")));
                lineJson.add(accountName);
                lineJson.add(lineMap.get("KOSTL"));
                lineJson.add(lineMap.get("WRBTR"));
                lineJson.add(lineMap.get("SGTXT"));
                jsonArray.add(lineJson);
            }
            dataJsonArray = jsonArray;
            logger.info("对公凭证接口预览：" + dataJsonArray);
        } else {
//            //对私凭证预览
//            JSONObject inputJson = new JSONObject();
//            inputJson.put("documentId", documentId);
//            dataJsonArray = JSONArray.parseArray(update(inputJson, "excl"));
            //对私凭证预览
            //查询单据行信息贷
            List<KhSapVoucherInfo> lineListCr = mapper.selectExpClaimLineCr(header.getHeaderId());
            logger.info("sapVoucherPosted查询单据行信息贷:" + JSONObject.toJSONString(lineListCr));

            //查询单据行信息借
            List<KhSapVoucherInfo> lineListDr = mapper.selectExpClaimLineDr(header.getHeaderId());
            logger.info("sapVoucherPosted查询单据行信息借:" + JSONObject.toJSONString(lineListDr));
            JSONObject inputJson = new JSONObject();
            inputJson.put("documentId", documentId);

            JSONObject dataJson = getDataJson(header, lineListCr, lineListDr, inputJson);
            Map headerMap = JSONObject.parseObject(dataJson.getJSONObject("data").getJSONObject("header").getJSONObject("item").toJSONString());
            List<Map> lineList = JSONObject.parseArray(dataJson.getJSONObject("data").getJSONObject("lines").getJSONArray("item").toJSONString(), Map.class);
            JSONArray jsonArray = new JSONArray();
            //头
            jsonArray.add(JSONArray.parseArray("[\"类型\", \"单据编号\", \"公司代码\", \"财务年度\", \"提交日期\", \"过账日期\", \"币种\", \"摘要\"]"));
            JSONArray headerJson = new JSONArray();
            headerJson.add(lines.get(0).getLineType());
            headerJson.add(headerMap.get("XBLNR"));
            headerJson.add(headerMap.get("BUKRS"));
            headerJson.add(headerMap.get("BLDAT").toString().substring(0, 4));
            headerJson.add(headerMap.get("BLDAT"));
            headerJson.add(headerMap.get("BUDAT"));
            headerJson.add("CNY");
            headerJson.add(headerMap.get("BKTXT"));
            jsonArray.add(headerJson);
            //行
            jsonArray.add(JSONArray.parseArray("[\"行项目号\", \"记账码\", \"记账科目\", \"科目描述\", \"成本中心\", \"员工供应商编码\", \"员工姓名\", \"记账金额\", \"描述\"]"));
            for (Map lineMap : lineList) {
                JSONArray lineJson = new JSONArray();
                lineJson.add(lineMap.get("BUZEI"));
                lineJson.add(lineMap.get("BSCHL"));
                lineJson.add(lineMap.get("HKONT"));
                String accountName = mapper.getAccountName(String.valueOf(lineMap.get("HKONT")));
                lineJson.add(accountName);
                lineJson.add(lineMap.get("KOSTL"));
                lineJson.add(lineMap.get("LIFNR"));
                lineJson.add(lineMap.get("EMPLOYEE_NAME"));
                lineJson.add(lineMap.get("WRBTR"));
                lineJson.add(lineMap.get("SGTXT"));
                jsonArray.add(lineJson);
            }
            dataJsonArray = jsonArray;
            logger.info("对私凭证接口预览：" + dataJsonArray);
        }
        return dataJsonArray;
    }

    @Override
    public void khContract(String date) throws IOException {
        LogCtx.setLogKey("科华合同"+date);
        // 本地目录
        String localPath = KehuaConstants.XML_FILE;
        // 远程目录
        String remotePath = "/oa-contract/" + date + "/";
        logger.info("{}下载文件，本地目录={}，远程目录={}", LogCtx.getLogKey(), localPath, remotePath);
        // todo 服务端有连接限制，此处统一获取连接资源
        Map<Integer, FTPClient> ftpClientMap = KehuaFtp.getFtpClientMap(5);
        try {
            // 获取执行历史
            List<Object> hisList;
            Integer lovId = mapper.queryLovIdByName("ContractLog");
            FndLovValue fndLovValue = mapper.queryLovValueByCode(lovId, date);
            if(fndLovValue != null && !StringUtils.isEmpty(fndLovValue.getColumn10())) {
                // 使用new ArrayList包装，防止出现UnsupportedOperationException
                hisList = new ArrayList<>(Arrays.asList(JSON.parseArray(fndLovValue.getColumn10()).toArray()));
            } else {
                hisList = Lists.newArrayList();
            }
            logger.info("{}获取执行历史结果={}", LogCtx.getLogKey(), hisList);
            // 异步下载文件，返回xml文件
            List fileList = KehuaFtp.asycftpDownLoadFiles(hisList, localPath, remotePath, ftpClientMap);
            if (null == fileList || fileList.isEmpty()) {
                logger.info("{}本次无待处理xml文件，结束处理", LogCtx.getLogKey());
                return;
            }
            // 遍历获取到的服务器本地文件地址，并读取 xml 文件并且转为 json，并获取文件相对路径
            List<Object> list = new ArrayList<>();
            for (Object o : fileList) {
                String file = o.toString();
                String xmlName = file.substring(file.lastIndexOf(File.separator)+1);
                try {
                    logger.info("{}读取xml文件内容，{}开始处理", LogCtx.getLogKey(), file);
                    // 读取服务器地址上的xml文件中的内容，转换成为字节流,如果文件内容不是空的就行处理
                    String xml = readXml(file);
                    if (xml.length() > 0) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("xmlName", xmlName);
                        map.put("xml", xml);
                        map.put("remotePath", remotePath);
                        map.put("xmlPath", remotePath + file.replace(KehuaConstants.XML_FILE, ""));
                        list.add(map);
                    }
                } catch (Exception e) {
                    logger.error("{}读取xml文件内容，{}出现未知异常：{}", LogCtx.getLogKey(), file, e.getMessage(), e);
                }
                logger.info("{}读取xml文件内容，{}结束处理", LogCtx.getLogKey(), file);
            }
            logger.info("{}本次成功读取xml文件数={}", LogCtx.getLogKey(), list.size());
            if(list.isEmpty()) {
                return;
            }
            // 遍历创建订单，下载订单附件
            logger.info("{}下载xml文件附件及创建单据，开始处理", LogCtx.getLogKey());
            for (Object o : list) {
                HashMap map = (HashMap) o;
                String xmlName = (String) map.get("xmlName");
                try {
                    khCreateExp(map, ftpClientMap);
                    hisList.add(xmlName);
                } catch (Exception e) {
                    logger.error("{}下载xml文件附件及创建单据，{}出现未知异常：{}", LogCtx.getLogKey(), xmlName, e.getMessage(), e);
                }
            }
            logger.info("{}下载xml文件附件及创建单据，结束处理", LogCtx.getLogKey());
            // 登记执行历史
            logger.info("{}登记执行历史开始，入参={}", LogCtx.getLogKey(), hisList);
            if(fndLovValue == null) {
                mapper.insertLovValue(lovId, date, JSON.toJSONString(hisList));
                mapper.insertLovValueTl(lovId, date, "ContractLog", "", "zh_CN");
            } else {
                mapper.updateLovColumnById(fndLovValue.getValueId(), JSON.toJSONString(hisList));
            }
            logger.info("{}登记执行历史结束", LogCtx.getLogKey());
        } catch (Exception e) {
            logger.error("{}出现未知异常：{}", LogCtx.getLogKey(), e.getMessage(), e);
            throw e;
        } finally {
            // 释放资源
            KehuaFtp.closeFtpClientMap(ftpClientMap);
        }
    }

    private void khCreateExp(HashMap map, Map<Integer, FTPClient> ftpClientMap) {
        String xml = (String) map.get("xml");
        String remotePath = (String) map.get("remotePath");
        String xmlPath = (String) map.get("xmlPath");
        String xmlName = (String) map.get("xmlName");
        // 根据xml文件中描述的附件名从FTP上下载附件到本地缓存
        logger.info("{}文件{}解析内容，处理开始", LogCtx.getLogKey(), xmlName);
        boolean attachmentFlag = true; //如果从xml中读取附件信息异常，后面就不再进行附件的下载操作
        JSONArray fileArray = new JSONArray();//判断是否有附件fileArray对应的是当前xml需要下载的附件的名称集合
        JSONObject contract = null;
        try {
            // 将xml文件转换成需要的 json
            JSONObject jsonObject = xmlToJson(xml);
            contract = jsonObject.getJSONObject("contract");
            Object con = contract.getJSONObject("attachment").get("file");
            if (con instanceof JSONArray) {
                fileArray = (JSONArray) con;
            } else if (con instanceof String) {
                fileArray.add((String) con);
            } else {
                attachmentFlag = false;
            }
        } catch (Exception e) {
            logger.error("{}文件{}解析内容，出现未知异常：{}", LogCtx.getLogKey(), xmlName, e.getMessage(), e);
            attachmentFlag = false;
        }
        if (!attachmentFlag || contract == null) {
            logger.error("{}文件{}不满足下载条件，忽略处理", LogCtx.getLogKey(), xmlName);
            return;
        }
        logger.info("{}文件{}解析内容，处理结束", LogCtx.getLogKey(), xmlName);
        JSONArray attachments;
        //附件的ftp下载和上传到s3抽离出去
        logger.info("{}文件{}下载附件，处理开始", LogCtx.getLogKey(), xmlName);
        try {
            // 判断目前的 xml 对应的单据是否有附件，并做下载处理
            attachments = attachmentsDownload(fileArray, remotePath, xmlPath, ftpClientMap);
            // 需要判断需要下载的附件和下载到的附件的数量是否是一致的，如果不是一致就不能够创建当前 xml 对应的单据信息
            if (attachments == null || attachments.size() != fileArray.size()) {
                logger.error("{}文件{}下载附件，下载结果数量不一致：{}={}", LogCtx.getLogKey(), xmlName, attachments.size(), fileArray.size());
                return;
            }
        } catch (Exception e) {
            logger.error("{}文件{}下载附件，出现未知异常：{}", LogCtx.getLogKey(), xmlName, e.getMessage(), e);
            return;
        }
        logger.info("{}文件{}下载附件，处理结束", LogCtx.getLogKey(), xmlName);
        logger.info("{}文件{}处理审批流参数，处理开始", LogCtx.getLogKey(), xmlName);
        /*处理审批流为字符串数组*/
        JSONArray jsonArray1 = new JSONArray();
        try {
            // 创建需要入库对应的存储过程参数
            contract.remove("attachment");
            contract.put("attachments", attachments);
            contract.put("xmlPath", xmlPath);
            Object approvalObject = contract.getJSONObject("requestlog")
                    .get("approval");
            if (approvalObject instanceof JSONArray) {
                JSONArray array = (JSONArray) approvalObject;
                String approvalStr = "";
                for (int i = 0; i < array.size(); i++) {
                    approvalStr += array.getString(i) + "\n";
                }
                approvalStr = cutStringByUTF8(approvalStr, 255);
                jsonArray1.add(approvalStr);
            } else if (approvalObject instanceof String) {
                jsonArray1.add(approvalObject);
            }
        } catch (Exception e) {
            logger.error("{}文件{}处理审批流参数，出现未知异常：{}", LogCtx.getLogKey(), xmlName, e.getMessage(), e);
        }
        logger.info("{}文件{}处理审批流参数，处理结束", LogCtx.getLogKey(), xmlName);
        logger.info("{}文件{}创建订单，处理开始", LogCtx.getLogKey(), xmlName);
        //由于执行时间较长，要做好异常处理
        try {
            contract.remove("requestlog");
            contract.put("approval", jsonArray1);
            update(contract, "contract");
        } catch (Exception e) {
            logger.error("{}文件{}创建订单，出现未知异常：{}", LogCtx.getLogKey(), xmlName, e.getMessage(), e);
        }
        logger.info("{}文件{}创建订单，处理结束", LogCtx.getLogKey(), xmlName);
    }

    /**
     * 把附件的下载和S3上传抽离出来，并获取附件名称和附件所对应的地址
     *
     * @param fileArray  需要下载的附件名称集合
     * @param remotePath ftp 服务器远程路径名称
     * @param xmlPath    需要下载附件对应的当前的 xml 文件
     * @return
     */
    private JSONArray attachmentsDownload(JSONArray fileArray, String remotePath, String xmlPath, Map<Integer, FTPClient> ftpClientMap) {
        // 拼接本地服务器上的文件地址
        String attachmentFile = KehuaConstants.ATTACHMENT_FILE;
        JSONArray attachments = new JSONArray();
        /*首次循环，下载*/
        int size = ftpClientMap.size();// 同时起多少个线程下载
        // 遍历远程文件地址，并做下载处理
        int attachmentDownLoadFiles = 0;
        List<String> attachmentFileSuccessDownList = new ArrayList<>();
        List<String> attachmentFileErrorDownList = new ArrayList<>();
        List<Object> list = Lists.newArrayList();
        for (int i = 0; i < fileArray.size(); i++) {
            list.add(fileArray.get(i));
            if ((i + 1) % size == 0 || i == fileArray.size() - 1) {
                CountDownLatch countDownLatch = new CountDownLatch(list.size());
                for (int j = 0; j < list.size(); j++) {
                    Object o = list.get(j);
                    String file = (String) o;
                    try {
                        // 下载文件 attachmentFile：本地服务器文件夹地址  remotePath: 远程 ftp 服务器地址 file：xml中附件名称集合
                        boolean flag = KehuaFtp.asycftpDownLoad(attachmentFile, remotePath, file, countDownLatch, ftpClientMap.get(j));
                        // 判断附件是否下载成功
                        if (flag) {
                            attachmentDownLoadFiles++;
                            attachmentFileSuccessDownList.add(file);
                            logger.info("ftp 本地路径:{}, 远程路径:{}, 远程文件名称:{}, 下载的结果是: {}, 成功", attachmentFile, remotePath, file, flag);
                        } else {
                            attachmentFileErrorDownList.add(file);
                            logger.info("ftp 本地路径:{}, 远程路径:{}, 远程文件名称:{}, 下载的结果是: {}, 失败", attachmentFile, remotePath, file, flag);
                        }
                    } catch (IOException e) {
                        logger.error("科华合同, download {} error: ", file, e);
                    }
                }
                /*等待子线程执行完毕*/
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    logger.error("科华合同，附件下载错误: ", e);
                }
                list = new ArrayList<>();
            }
        }

        // 判断当前需要下载附件文件集合全部数量下载成功
        if (attachmentDownLoadFiles != fileArray.size()) {
            logger.error("ftp kh 科华 xml 对应的附件下载数量信息不正确，xml 文件地址是：{}, 需要下载的附件是:{}, 下载到的文件的是：{}", xmlPath, attachmentFileSuccessDownList, attachmentFileErrorDownList);
        }
        /*日志结构体*/
        JSONArray errorArray = new JSONArray();
        /*再次循环，判断并且放入数组中*/
        for (Object o : fileArray) {
            JSONObject attachment = new JSONObject();
            JSONObject errorJson = new JSONObject();
            String file = (String) o;
            // 从当前的本地服务器路径下获取文件，查看是否存在，并上传到S3上
            File file1 = new File(attachmentFile + file);
            // 判断本地服务器上的文件是否下载成功
            logger.info("科华合同附件名称:{} exists:{}", file1, file1.exists());
            if (file1.exists()) {
                String name = "kehua/" + GUIDUtil.generateGUID() + "." + KehuaS3.getExtentionName(file);
                // 判断本地文件上传的S3服务器的结果
                boolean uploadResult = new KehuaS3().upload(attachmentFile + file, name);
                // 上传成功
                if (uploadResult) {
                    attachment.put("attachment_url", name);
                    attachment.put("file_name", file);
                    attachments.add(attachment);
                } else {
                    // 如果附件上传到 s3 服务器上
                    logger.info("async upload file kehua  文件名称是:{}, 上传S3服务器的结果是:{}， 对应的 xml 文件名称是:{}", attachmentFile + file, uploadResult, xmlPath);
                }
            } else {
                // 如果本地下载失败
                errorJson.put("file_name", file);
                errorJson.put("remote_path", remotePath);
                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String date = df.format(new Date());
                errorJson.put("date", date);
                errorArray.add(errorJson);
            }
        }
        /*如果有缺失的文件，放入日志记录*/
        if (errorArray.size() > 0) {
            logger.error("{}本次S3出错的文件集合={}", LogCtx.getLogKey(), errorArray.toJSONString());
        }
        return attachments;
    }

    /**
     * 删除当前目录下的所有的xml文件
     *
     * @param path
     * @return
     */
    public static List readFileName(String path, String pattern) {
        // 获取系统路径
        File file = new File(path);
        // 获取当前文件下的子目录文件
        File[] fs = file.listFiles();
        List fileList = new ArrayList<String>();
        // 创建正则表达式
//        String pattern = "\\S*?.(xml|XML)";
        // 将正则表达式转换成正则表达式对象
        Pattern r = Pattern.compile(pattern);
        // 如果是多级目录
        if (fs != null) {
            for (File f : fs) {
                Matcher m = r.matcher(f + "");
                if ((!f.isDirectory()) & m.matches()) {
                    fileList.add(f);
                }
            }
        }
        return fileList;
    }

    /**
     * 调用科华的存储过程创建单据
     *
     * @param inputJson 单据请求信息
     * @param type      类型
     * @return 执行的结果
     */
    public String update(JSONObject inputJson, String type) {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setInput(inputJson.toJSONString());
        fndQuery.setCompanyId(14870);
        fndQuery.setType(type);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        String result = mapper.kehuaDataUpdate(fndQuery);
        logger.info("科华单据存储过程入参是：{}，调用类型是:{}，执行的结果是：{}", JSON.toJSONString(fndQuery), type, result);
        return result;
    }

    private String jsonToXml(String s) {
        XMLSerializer xmlSerializer = new XMLSerializer();
        String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:sap-com:document:sap:rfc:functions\">"
                + "<soapenv:Header/><soapenv:Body><urn:ZFK_FI_GL_010>"
                + xmlSerializer.write(JSONSerializer.toJSON(s))
                .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                .replaceAll("<item class=\"array\">", "")
                .replaceAll("</item></lines>", "</lines>")
                .replaceAll("<e class=\"object\">", "<item>")
                .replaceAll("</e>", "</item>")
                .replaceAll(" type=\"string\"", "")
                .replaceAll(" type=\"number\"", "")
                .replaceAll("<header class=\"object\">", "<T_HEAD>")
                .replaceAll("</header>", "</T_HEAD>")
                .replaceAll("<lines class=\"object\">", "<T_ITEM>")
                .replaceAll("</lines>", "</T_ITEM>")
                .replaceAll("<item class=\"object\">", "<item>")
                .replaceAll("<o>", "")
                .replaceAll("</o>", "")
                + "<RETURN><item><TYPE></TYPE><ID></ID><NUMBER></NUMBER>" +
                "<MESSAGE></MESSAGE><LOG_NO></LOG_NO><LOG_MSG_NO></LOG_MSG_NO>" +
                "<MESSAGE_V1></MESSAGE_V1><MESSAGE_V2></MESSAGE_V2>" +
                "<MESSAGE_V3></MESSAGE_V3><MESSAGE_V4></MESSAGE_V4>" +
                "<PARAMETER></PARAMETER><ROW></ROW>" +
                "<FIELD></FIELD><SYSTEM></SYSTEM></item></RETURN>"
                + "</urn:ZFK_FI_GL_010></soapenv:Body></soapenv:Envelope>";

        return data;
    }

    private String readXml(String path) throws IOException {
//        path = "C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\159.xml";
        BufferedReader reader = null;
        InputStreamReader isr = null;
        StringBuilder builder = new StringBuilder();
        String line = null;
        try {
            // 根据文件流，获取相对应的本地服务器上的文件
            isr = new InputStreamReader(new FileInputStream(path), StandardCharsets.UTF_8);
            reader = new BufferedReader(isr);
        } catch (FileNotFoundException e) {
            logger.error("科华合同接口:单据XML文件读取异常", e);
        }
        if (reader != null) {
            while ((line = reader.readLine()) != null) {
                builder.append(line);
            }

        }
        return builder.toString();
    }

    // 将xml格式的文件转换成JSONObject
    private JSONObject xmlToJson(String xml) {
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(xml);
        return JSONObject.parseObject(xmlJSONObj.toString());
    }

    /**
     * 使用UTF-8编码表进行截取字符串，一个汉字对应三个负数，一个英文字符对应一个正数
     *
     * @param str
     * @param len
     * @return
     * @throws IOException
     */
    private static String cutStringByUTF8(String str, int len) throws IOException {

        byte[] buf = str.getBytes("utf-8");
        if (buf.length < len - 1) {
            return str;
        }
        int count = 0;
        for (int x = len - 1; x >= 0; x--) {
            if (buf[x] < 0) {
                count++;
            } else {
                break;
            }
        }
        if (count % 3 == 0) {
            return new String(buf, 0, len, "utf-8");
        } else if (count % 3 == 1) {
            return new String(buf, 0, len - 1, "utf-8");
        } else {
            return new String(buf, 0, len - 2, "utf-8");
        }
    }

    public static void main(String[] s) throws IOException {
        KehuaFtp.deleteFiles(KehuaConstants.XML_FILE);
    }
}
