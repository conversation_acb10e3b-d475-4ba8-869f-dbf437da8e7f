package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.service.CheryJaguarLandRoverToDoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class CheryCheryJaguarLandRoverToDoServiceImpl implements CheryJaguarLandRoverToDoService {
    private static final Logger logger = LoggerFactory.getLogger(CheryCheryJaguarLandRoverToDoServiceImpl.class);
    @Override
    public JSONObject generateRtn1(Object data, Object ErrorDetail, String Message, String Detail, int Code) throws Exception {
        JSONObject rtn = new JSONObject();
        rtn.put("Code", Code);
        if (null != data) {
            rtn.put("Data", data);
        }
        if (Code == 200)
        {
            rtn.put("ErrorDetail", null);
        } else if(Code == 1001){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Message", "输入JSON解析出错");
            jsonObject.put("Detail", "输入JSON解析出错");
            rtn.put("ErrorDetail", jsonObject);
        }
        else {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("Message", Message);
            jsonObject.put("Detail", Detail);
            rtn.put("ErrorDetail", jsonObject);
        }
        rtn.put("TaskType", "1");
        logger.info("rtn==>" + rtn);
        return rtn;
    }
}
