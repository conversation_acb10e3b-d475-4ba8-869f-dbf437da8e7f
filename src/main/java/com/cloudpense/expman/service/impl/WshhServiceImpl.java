package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.WshhClaimPushReq;
import com.cloudpense.expman.Request.WshhSapReturnReq;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.mapper.WshhMapper;
import com.cloudpense.expman.service.WshhService;
import com.cloudpense.expman.util.SendUtil;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * 文思海辉service实现
 */
@Service
public class WshhServiceImpl implements WshhService {

    @Autowired
    private WshhMapper wshhMapper;

    @Autowired
    private SendUtil sendUtil;

    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    private static final Logger logger = LoggerFactory.getLogger(WshhServiceImpl.class);

    /**
     * 出差报销单推送
     */
    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void claimPush() {
        try{
            String url = rb.getString("wshh.claimPush");
            String requestBody, result;
            HttpEntity<String> request;
            HttpHeaders headers;
            headers = new HttpHeaders();
            String json = getClaimListJson();
            requestBody = "{\"certificates\":" + json + "}";
            //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            //RestTemplate带参传的时候要用HttpEntity<?>对象传递
            request = new HttpEntity<>(requestBody, headers);
            //推送单据
            result = sendUtil.postForObject("claimPush",url,request);
        } catch (Exception e) {
            logger.error("claim_push_error文思海辉出差报销单推送出现错误", e);
        }
    }

    /**
     * 推送单据到sap
     * @return
     * @param pushDate
     * @param pushDateTo
     */
    @Override
    public String sapPush(Date pushDate, Date pushDateTo) throws IOException {
        JSONObject json = new JSONObject();
        json.put("code", 0);
        json.put("msg", "成功");
        JSONArray jsonArray = new JSONArray();
        //查询头信息
        List<Map<String, Object>> headerMapList = wshhMapper.getExpClaimHeader(pushDate, pushDateTo);
        logger.info("sapPush------" + json.toJSONString());
        if(headerMapList == null || headerMapList.size() <= 0){
            json.put("msg", "没有数据");
            return json.toJSONString();
        }
        for(Map<String, Object> map : headerMapList){
            //封装数据
            jsonArray.add(getSapPushJson(map));
        }
        json.put("data",jsonArray);
        logger.info("sapPush------" + json.toJSONString());
        return json.toJSONString();
    }

    /**
     * SAP传输结果
     * @param wshhSapReturnReq
     * @return
     */
    @Override
    public String sapReturn(WshhSapReturnReq wshhSapReturnReq) throws IOException {
        JSONObject json = new JSONObject();
        json.put("code", 0);
        json.put("msg", "成功");
        //解密
        BASE64Decoder decoder = new BASE64Decoder();
        String headerId = new String(decoder.decodeBuffer(wshhSapReturnReq.getHeaderId()), "UTF-8");
        logger.info("sapReturn------解密" + "headerId:" + headerId);
        //查询单据号
        ExpClaimHeader expClaimHeader= wshhMapper.selectDocument(Integer.parseInt(headerId));
        logger.info("sapReturn------查询单据号" + JSONObject.toJSONString(expClaimHeader));
        wshhSapReturnReq.setHeaderId(headerId + "");
        wshhSapReturnReq.setDocumentNum(expClaimHeader.getDocumentNum());
        //插入单据信息
        wshhMapper.insertExpClaimInfo(wshhSapReturnReq);
        return json.toJSONString();
    }

    /**
     * 封装推送单据到sap数据
     * @param headerMap
     * @return
     */
    private JSONObject getSapPushJson(Map<String, Object> headerMap) throws IOException {
        JSONObject json = new JSONObject();
        int headerId = (int) headerMap.get("headerId");
        int lineId = 1;
        //查询行信息
        List<Map<String, Object>> lineMapList = wshhMapper.getExpClaimLine(headerId);
        BigDecimal totalFinClaimAmount = new BigDecimal(0);
        for (Map<String, Object> lineMap : lineMapList){
            lineMap.put("lineId", lineId);
            totalFinClaimAmount = totalFinClaimAmount.add((BigDecimal)lineMap.get("finClaimAmount"));
            lineId++;
        }
        BASE64Encoder encoder = new BASE64Encoder();
        String headerIdStr = headerId + "";
        byte[] headerIdByte = headerIdStr.getBytes("UTF-8");
        headerIdStr = encoder.encode(headerIdByte);
        headerMap.put("headerId", headerIdStr);
        headerMap.put("totalFinClaimAmount", totalFinClaimAmount);
        json.put("header", headerMap);
        json.put("lineList", lineMapList);
        return json;
    }

    /**
     * 查询单据信息
     * @return
     */
    private String getClaimListJson(){
        String json = "";
        //查询单据头
        List<WshhClaimPushReq> expClaimHeaderList = wshhMapper.selectExpClaimHeader();
        List<WshhClaimPushReq> wshhClaimPushReqList = new ArrayList<WshhClaimPushReq>();
        if(expClaimHeaderList != null && expClaimHeaderList.size() > 0){
            for(WshhClaimPushReq expClaimHeader : expClaimHeaderList){
                //查询单据行
                List<ExpClaimLine> expClaimLineList = wshhMapper.selectExpClaimLine(expClaimHeader.getHeader_id());
                if(expClaimLineList != null && expClaimLineList.size() > 0){
                    for(ExpClaimLine expClaimLine : expClaimLineList){
                        //将数据封装
                        WshhClaimPushReq wshhClaimPushReq = getWshhClaimPushReq(expClaimHeader, expClaimLine);
                        wshhClaimPushReqList.add(wshhClaimPushReq);
                    }
                }
            }
        }
        //将封装的数据转为json
        if(wshhClaimPushReqList != null && wshhClaimPushReqList.size() > 0){
            json = JSONArray.fromObject(wshhClaimPushReqList).toString();
        }
        return json;
    }

    /**
     * 封装请求数据
     * @param wshhClaimPushReqCurr
     * @param expClaimLine
     * @return
     */
    private WshhClaimPushReq getWshhClaimPushReq(WshhClaimPushReq wshhClaimPushReqCurr,ExpClaimLine expClaimLine){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        WshhClaimPushReq wshhClaimPushReq = new WshhClaimPushReq();
        wshhClaimPushReq.setDocument_num(wshhClaimPushReqCurr.getDocument_num());
        wshhClaimPushReq.setDocument_type("出差报销");
        wshhClaimPushReq.setDescription(wshhClaimPushReqCurr.getDescription());
        wshhClaimPushReq.setTotal_amount(wshhClaimPushReqCurr.getTotal_amount());
        wshhClaimPushReq.setSubmit_user(wshhClaimPushReqCurr.getSubmit_user());
        wshhClaimPushReq.setCharge_department(wshhClaimPushReqCurr.getCharge_department());
        wshhClaimPushReq.setProject_name(wshhClaimPushReqCurr.getProject_name());
        wshhClaimPushReq.setColumn11(wshhClaimPushReqCurr.getColumn11());
        wshhClaimPushReq.setColumn18(wshhClaimPushReqCurr.getColumn18());
        wshhClaimPushReq.setColumn20(wshhClaimPushReqCurr.getColumn20());
        if(expClaimLine.getReceiptDate() != null){
            wshhClaimPushReq.setReceipt_date(sdf.format(expClaimLine.getReceiptDate()));
        }
        wshhClaimPushReq.setReceipt_amount(expClaimLine.getReceiptAmount());
        wshhClaimPushReq.setReceipt_currency(expClaimLine.getReceiptCurrency());
        wshhClaimPushReq.setExchange_rate(expClaimLine.getExchangeRate());
        wshhClaimPushReq.setTax_code_id(expClaimLine.getTaxCodeId());
        wshhClaimPushReq.setTax_amount(expClaimLine.getTaxAmount());
        wshhClaimPushReq.setFin_tax_amount(expClaimLine.getFinTaxAmount());
        wshhClaimPushReq.setComments(expClaimLine.getComments());
        wshhClaimPushReq.setDr_account_id(expClaimLine.getDrAccountId());
        wshhClaimPushReq.setCr_account_id(expClaimLine.getCrAccountId());
        wshhClaimPushReq.setTax_account_id(expClaimLine.getTaxAccountId());
        wshhClaimPushReq.setStatus(wshhClaimPushReqCurr.getStatus());
        wshhClaimPushReq.setZ_date_time(sdf.format(new Date()));
        wshhClaimPushReq.setFin_receipt_amount(expClaimLine.getFinReceiptAmount());
        wshhClaimPushReq.setFin_tax_code_id(expClaimLine.getFinTaxCodeId());
        return wshhClaimPushReq;
    }
}
