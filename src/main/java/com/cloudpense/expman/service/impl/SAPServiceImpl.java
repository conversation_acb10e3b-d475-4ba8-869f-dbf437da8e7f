package com.cloudpense.expman.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.stream.CollectorUtil;
import cn.hutool.core.util.StrUtil;
import com.cloudpense.expman.controller.SAPController;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.*;
import com.cloudpense.expman.entity.sap.*;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.*;
import com.cloudpense.expman.service.SAPService;
import com.cloudpense.expman.util.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.SftpException;
import com.landray.kmss.km.review.webservice.IKmReviewWebserviceService;
import com.landray.kmss.km.review.webservice.KmReviewParamterForm;
import com.landray.kmss.phoenix.lbpm.webservice.ApproveObject;
import com.landray.kmss.phoenix.lbpm.webservice.IPhoenixLbpmWebservice;
import com.landray.kmss.sys.notify.webservice.ISysNotifyTodoWebService;
import com.landray.kmss.sys.notify.webservice.NotifyTodoAppResult;
import com.landray.kmss.sys.notify.webservice.NotifyTodoRemoveContext;
import com.landray.kmss.sys.notify.webservice.NotifyTodoSendContext;
import com.phoenix.sap.rfc.costcenter.ZHRTCCKOSTL;
import com.phoenix.sap.rfc.costcenter.ZhrCcSyncKostl;
import com.phoenix.sap.rfc.department.ZHRTCCORGAN;
import com.phoenix.sap.rfc.department.ZhrCcSyncOrgan;
import com.phoenix.sap.rfc.user.ZHRTCCPERSON;
import com.phoenix.sap.rfc.user.ZhrCcSyncPerson;
import net.sf.json.JSONSerializer;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.HttpClientBuilder;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Encoder;

import javax.xml.ws.Holder;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.cloudpense.expman.util.SFTPUtil.*;
import static com.cloudpense.expman.webService.phoenix.PhoenixConstants.*;
import static com.cloudpense.expman.webService.phoenix.PhoenixHttpHelper.PhoenixHttpPostIBMP;
import static com.cloudpense.expman.webService.phoenix.PhoenixHttpHelper.PhoenixHttpPostString;
import static com.meiya.MeiyaUtils.*;

//import com.cloudpense.expman.entity.finance.TestRFC;

/**
 * Created by THINK on 2015/8/24.
 */
@Service
public class SAPServiceImpl implements SAPService {

    private static final Logger logger = LoggerFactory.getLogger(SAPServiceImpl.class);

    private PhoenixMapper phoenixMapper;
    private SapMapper sapMapper;
    private MeiyaMapper meiyaMapper;
    private HeraeusMapper heraeusMapper;
    ExecutorService executorService = Executors.newFixedThreadPool(10);
    RestTemplate restTemplate;

    @Autowired
    public SAPServiceImpl(final PhoenixMapper phoenixMapper,
                          SapMapper sapMapper,
                          MeiyaMapper meiyaMapper,
                          HeraeusMapper heraeusMapper) {
        this.phoenixMapper = phoenixMapper;
        this.sapMapper = sapMapper;
        this.meiyaMapper = meiyaMapper;
        this.heraeusMapper = heraeusMapper;
    }

    {
        logger.info("SAPServiceImpl初始化开始...");
        try {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(300000)
                    .setConnectTimeout(300000).setConnectionRequestTimeout(300000).build();
            HttpClient httpClient = HttpClientBuilder.create().setDefaultRequestConfig(requestConfig).build();
            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
            this.restTemplate = new RestTemplate(requestFactory);
        } catch (Exception e) {
            logger.error("SAPServiceImpl初始化失败：{}", e.getMessage(), e);
        }
        logger.info("SAPServiceImpl初始化结束");
    }


    @Autowired
    private ExpClaimHeaderMapper expClaimHeaderMapper;

    private static  ResourceBundle RB = ResourceBundle.getBundle("third");
    private static  final String  CJRSAPURL = RB.getString("cjr-sap-url");

    @Override
    public JSONArray SqGetData(String key) throws Exception {
        if (null == key) {
            key = "PRI02";
        }
        SFTPUtil sftp = new SFTPUtil(userName, sftpPassword, hostAddress, sftpPort);
        sftp.login();
        Vector<ChannelSftp.LsEntry> files = sftp.listFiles(address.get(key));
        Enumeration<ChannelSftp.LsEntry> elements = files.elements();
        JSONArray jsonArray = new JSONArray();
        while (elements.hasMoreElements()) {
            String ctlFileName = elements.nextElement().getFilename();
            String datFileName;
            if (ctlFileName.endsWith(".CTL")) {
                datFileName = ctlFileName.substring(0, ctlFileName.length() - 4) + ".DAT";
            } else {
                continue;
            }
            String message = sftp.downloadToString(address.get(key), datFileName);
            try {
                JSONObject log = new JSONObject();
                log.put("datFileName", datFileName);
                log.put("message", message);
                sapMapper.normalInsertLog(0, "sqsap", log.toJSONString());
            } catch (Exception e) {
                logger.error("SqGetData=====日志写入出错", e);
            }
            String[] lists = message.split("\r\n");
            for (String s : lists) {
                if (s.length() == 0) {
                    continue;
                }
                JSONObject json = grokFormat(key, s);
                jsonArray.add(json);
            }
            sftp.rename(ctlFileName, ctlFileName + ".TMP");
        }
        sftp.logout();
        return jsonArray;
    }

    @Override
    public void SqPutData(List<Object> objList) throws Exception {
        SFTPUtil sftp = new SFTPUtil(userName, sftpPassword, hostAddress, sftpPort);
        sftp.login();
        String key = "";
        if (null != objList && objList.size() > 0) {
            Object object = objList.get(0);
            key = object.getClass().getSimpleName();
        }
        InputStream input = sftp.objectToString(objList, key);
        Vector<ChannelSftp.LsEntry> vector = sftp.listFiles(address.get(key));
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYYMMdd");
        String dateFormat = simpleDateFormat.format(new Date());
        Integer number = 1;
        for (ChannelSftp.LsEntry c : vector) {
            String fileName = c.getFilename();
            if (fileName.contains(dateFormat)) {
                int firstIndex = fileName.lastIndexOf("_");
                if (firstIndex != -1) {
                    String tempFileName = fileName.substring(firstIndex + 1);
                    int lastIndex = tempFileName.indexOf(".");
                    if (lastIndex != -1) {
                        try {
                            int tempNumber = Integer.parseInt(tempFileName.substring(0, lastIndex));
                            if (tempNumber >= number) {
                                number = tempNumber + 1;
                            }
                        } catch (Exception e) {
                            continue;
                        }
                    }
                }

            }
        }
        String fileNumber = number.toString();
        if (fileNumber.length() < 5) {
            for (int i = fileNumber.length(); i < 5; i++) {
                fileNumber = "0" + fileNumber;
            }
        }

        String sftpFileName = key + dateFormat + "_" + fileNumber;
        try {
            sftp.uploadFile(address.get(key), sftpFileName + ".DAT", input);
            sftp.uploadFile(address.get(key), sftpFileName + ".CTL", new ByteArrayInputStream("".getBytes("GBK")));
        } catch (SftpException e) {
            logger.error("SqPutData=======ftp更新异常", e);
            JSONObject error = new JSONObject();
            error.put("error_data", input);
            error.put("message", e.getMessage());
            sapMapper.normalInsertLog(11903, "SFTP-ERROR", error.toJSONString());
            throw new ValidationException(e.getMessage());
        }
    }

    public String dateFormat(Date datetime, float timeZoneOffset) {
        if (datetime == null) {
            return "19700101";
        }
        if (timeZoneOffset > 13 || timeZoneOffset < -12) {
            timeZoneOffset = 0;
        }

        int newTime = (int) (timeZoneOffset * 60 * 60 * 1000);
        TimeZone timeZone;
        String[] ids = TimeZone.getAvailableIDs(newTime);
        if (ids.length == 0) {
            timeZone = TimeZone.getDefault();
        } else {
            timeZone = new SimpleTimeZone(newTime, ids[0]);
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        format.setTimeZone(timeZone);
        String d = format.format(datetime);
        return d;
    }

    @Override
    public ExpClaimHeader getExpClaimData(int headerId) throws Exception {
        ExpClaimHeader expClaimHeader = sapMapper.getExpClaimHeader(headerId);
        expClaimHeader.setClaimLines(sapMapper.findAllByHeaderIdWithFinanceInfo(headerId, "zh_CN"));
        return expClaimHeader;
    }

    public void pushPR(ExpClaimHeader expClaimHeader) throws Exception {
        Collection<ExpClaimLine> expClaimLines = expClaimHeader.getClaimLines();
        List<Object> mmi002s = new ArrayList<>();
        for (ExpClaimLine expClaimLine : expClaimLines) {
            try {
                FndDepart fndDepart = sapMapper.getDepartById(expClaimHeader.getChargeDepartment(), "zh_CN");
                PRI01 pri01 = new PRI01();
                pri01.setType("PRtoSAP");
                pri01.setNB(expClaimHeader.getColumn33());
                String documentNum = expClaimHeader.getDocumentNum();
                pri01.setSN(transDocumentNum(documentNum));
                FndDepart branchDepart = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                pri01.setFactory(branchDepart.getDepartmentCode());
                pri01.setNo(expClaimLine.getLineId() % 100000 + "");
                pri01.setType2(expClaimLine.getColumn2());
                pri01.setItem(expClaimLine.getComments());
                pri01.setQuantity(expClaimLine.getQuantity() + "");
                pri01.setApplyUnit("EA");
                pri01.setPrice(expClaimLine.getPrice() + "");
                pri01.setCurrency(expClaimLine.getReceiptCurrency());
                pri01.setCostCenter(fndDepart.getDepartmentCode());
                pri01.setRequestDate(dateFormat(expClaimHeader.getSubmitDate(), 8));
                pri01.setPurchGrp("200");
                pri01.setEKPO_MATKL(expClaimLine.getExpType().getTypeCode());
                if (expClaimLine.getColumn31() != null && !expClaimLine.getColumn31().equals("")) {
                    try {
                        long paymentDateTime = Long.parseLong(expClaimLine.getColumn31());
                        Date column31_date = new Date(paymentDateTime);
                        pri01.setDeliveryDate(dateFormat(column31_date, 8));
                    } catch (Exception e) {
                        logger.error("pushPR=======类型转换异常", e);
                    }
                }
                if (expClaimLine.getColumn11() != null && !expClaimLine.getColumn11().equals("")) {
                    try {
                        long paymentDateTime = Long.parseLong(expClaimLine.getColumn11());
                        Date column11_date = new Date(paymentDateTime);
                        pri01.setDeliveryDate(dateFormat(column11_date, 8));
                    } catch (Exception e) {
                        logger.error("pushPR=======类型转换异常", e);
                    }
                }
                String equipmentId = "";
                if (expClaimLine.getColumn2() != null && expClaimLine.getColumn2().equals("A")) {
                    equipmentId = "**********";
                }
                pri01.setAsset_Number(equipmentId);
                GeneralLedgerAccount glAccount = sapMapper.getGlAccountById(expClaimLine.getDrAccountId(), "zh_CN");
                if (null != glAccount) {
                    pri01.setAccountCode(glAccount.getAccountCode());
                }
                if (expClaimLine.getLinkLineId() > 0) {
                    ExpClaimHeader ech = getExpClaimData(expClaimLine.getLinkHeaderId());
                    ExpClaimLine ecl = sapMapper.getExpClaimLine(expClaimLine.getLinkLineId(), "zh_CN");
                    pri01.setAGREEMENT(ech.getColumn32());
                    pri01.setAGMT_ITEM(expClaimLine.getLinkLineId() % 100000 + "");
                }
                mmi002s.add(pri01);
            } catch (Exception e) {
                logger.error("pushPR=======行字段转换异常", e);
                JSONObject error = new JSONObject();
                error.put("line_id", expClaimLine.getLineId());
                error.put("message", e.getMessage());
                sapMapper.normalInsertLog(11903, "PR-SAP-ERROR", error.toJSONString());
            }
        }
        SqPutData(mmi002s);
    }

    public void pushPO(ExpClaimHeader expClaimHeader) throws Exception {
        Collection<ExpClaimLine> expClaimLines = expClaimHeader.getClaimLines();
        List<Object> mmi004s = new ArrayList<>();
        for (ExpClaimLine expClaimLine : expClaimLines) {
            try {
                POI01 poi01 = new POI01();
                poi01.setBSART("SM1");
                poi01.setIHREZ(transDocumentNum(expClaimHeader.getDocumentNum()));
                FndSupplier supplier = sapMapper.getSupplierById(expClaimHeader.getSupplierId(), "zh_CN");
                poi01.setLIFNR(supplier.getSupplierCode());
                poi01.setNAME1(supplier.getSupplierName());
                poi01.setBEDAT(dateFormat(expClaimHeader.getSubmitDate(), 8));
                poi01.setEKORG("2000");
                poi01.setEKGRP("200");
                poi01.setWAERS(expClaimHeader.getCurrencyCode());
                poi01.setFREE2(expClaimHeader.getColumn43());
                poi01.setFREE3(expClaimHeader.getColumn38());
                FndDepart branch = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                poi01.setBUKRS(branch.getDepartmentCode());
                poi01.setIHREZ_ITEM(transDocumentNum(expClaimHeader.getDocumentNum()));
                poi01.setEBELP(expClaimLine.getLineId() % 100000 + "");
                poi01.setTXZ01(expClaimLine.getComments());
                poi01.setKNTTP(expClaimLine.getColumn2());
                poi01.setMENGE(expClaimLine.getQuantity() + "");
                poi01.setMEINS("EA");
                poi01.setOPER("I");
                String taxCode = sapMapper.getTaxCodeFromDesc("shuilv", expClaimLine.getColumn13(), 11903, "zh_CN'");
                poi01.setMWSKZ(taxCode);
                if (expClaimLine.getColumn11() != null && !expClaimLine.getColumn11().equals("")) {
                    try {
                        long paymentDateTime = Long.parseLong(expClaimLine.getColumn11());
                        Date column11_date = new Date(paymentDateTime);
                        poi01.setEINDT(dateFormat(column11_date, 8));
                    } catch (Exception e) {
                        logger.error("pushPO=======类型转换异常", e);
                    }
                }
                FndDepart branchDepart = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                poi01.setWERKS(branchDepart.getDepartmentCode());
                poi01.setNETPR(expClaimLine.getPrice() + "");
                poi01.setPEINH("1");
                GeneralLedgerAccount glAccount = sapMapper.getGlAccountById(expClaimLine.getDrAccountId(), "zh_CN");
                if (null != glAccount) {
                    poi01.setSAKTO(glAccount.getAccountCode());
                }
                FndDepart chargeDepartment = sapMapper.getDepartById(expClaimHeader.getChargeDepartment(), "zh_CN");
                if (null != chargeDepartment) {
                    poi01.setKOSTL(chargeDepartment.getDepartmentCode());
                }

                String equipmentId = "";
                if (expClaimLine.getColumn2() != null && expClaimLine.getColumn2().equals("A")) {
                    equipmentId = "**********";
                }
                poi01.setANLN1(equipmentId);
                if (expClaimLine.getLinkLineId() > 0) {
                    ExpClaimHeader ech = getExpClaimData(expClaimLine.getLinkHeaderId());
                    poi01.setKONNR(ech.getColumn44());
                    ExpClaimLine ecl = sapMapper.getExpClaimLine(expClaimLine.getLinkLineId(), "zh_CN");
                    poi01.setKTPNR(ecl.getLineId() % 100000 + "");
                }
                poi01.setEKPO_MATKL(expClaimLine.getExpType().getTypeCode());
                mmi004s.add(poi01);
            } catch (Exception e) {
                logger.error("pushPO=======行字段转换异常", e);
                JSONObject error = new JSONObject();
                error.put("line_id", expClaimLine.getLineId());
                error.put("message", e.getMessage());
                sapMapper.normalInsertLog(11903, "PO-SAP-ERROR", error.toJSONString());
            }
        }
        SqPutData(mmi004s);
    }

    public void pushGR(ExpClaimHeader expClaimHeader) throws Exception {
        Collection<ExpClaimLine> expClaimLines = expClaimHeader.getClaimLines();

        List<Object> mmi006s = new ArrayList<>();
        for (ExpClaimLine expClaimLine : expClaimLines) {
            try {
                ExpClaimHeader preClaimHeader = getExpClaimData(expClaimLine.getLinkHeaderId());
                GRI01 gri01 = new GRI01();
                gri01.setGRNUM(transDocumentNum(expClaimHeader.getDocumentNum()));
                gri01.setITEM(expClaimLine.getLineId() % 100000 + "");
                gri01.setEBELN(preClaimHeader.getColumn47());
                ExpClaimLine preClaimLine = sapMapper.getExpClaimLine(expClaimLine.getLinkLineId(), "zh_CN");
                gri01.setEBELP(expClaimLine.getLinkLineId() % 100000 + "");
                gri01.setBWART("101");
                gri01.setMENGE(expClaimLine.getQuantity() + "");
                FndDepart branchDepart = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                gri01.setWERKS(branchDepart.getDepartmentCode());
                mmi006s.add(gri01);
            } catch (Exception e) {
                logger.error("pushGR=======行字段转换异常", e);
                JSONObject error = new JSONObject();
                error.put("line_id", expClaimLine.getLineId());
                error.put("message", e.getMessage());
                sapMapper.normalInsertLog(11903, "RE-SAP-ERROR", error.toJSONString());
            }
        }
        SqPutData(mmi006s);
    }

    public void pushCT(ExpClaimHeader expClaimHeader) throws Exception {
        Collection<ExpClaimLine> expClaimLines = expClaimHeader.getClaimLines();
        List<Object> cti01s = new ArrayList<>();
        for (ExpClaimLine expClaimLine : expClaimLines) {
            try {
                CTI01 cti01 = new CTI01();
                cti01.setOPERATING_LOGO("I");
                cti01.setBSART("ZWK"); //todo
                cti01.setEKKO_EBELN_OA(transDocumentNum(expClaimHeader.getDocumentNum()));
                FndSupplier fndSupplier = sapMapper.getSupplierById(expClaimHeader.getSupplierId(), "zh_CN");
                cti01.setEKKO_LIFNR(fndSupplier.getSupplierCode());
                cti01.setPROCUREMENT("2000");
                cti01.setPROCUREMENT_SECTION("200");
                cti01.setEKKO_KDATB(dateFormat(expClaimHeader.getStartDatetime(), 8));
                cti01.setEKKO_KDATE(dateFormat(expClaimHeader.getEndDatetime(), 8));
//                cti01.setEKPO_NETWR(new BigDecimal(expClaimHeader.getTotalAmount()) + "");
                cti01.setEKPO_NETWR("");
                cti01.setEKKO_WAERS(expClaimLine.getClaimCurrency());
                cti01.setCOMPANY_PAY_CONDITION(expClaimHeader.getColumn38());
                cti01.setEKPO_EBELP(expClaimLine.getLineId() % 100000 + "");
                FndDepart branchDepart = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                cti01.setEKPO_WERKS(branchDepart.getDepartmentCode());
                cti01.setEKPO_KNTTP("U");
//                cti01.setEKPO_KNTTP(expClaimLine.getColumn2());
                cti01.setEKPO_TXZ01(expClaimLine.getComments());
                cti01.setEKPO_MENGE("");
//                cti01.setEKPO_MENGE(expClaimLine.getQuantity() + "");
                cti01.setEKPO_MEINS("EA");
//                GeneralLedgerAccount glAccount = sapMapper.getGlAccountById(expClaimLine.getDrAccountId(), "zh_CN");
                cti01.setEKKN_SAKTO("");
//                cti01.setEKKN_SAKTO(glAccount.getAccountCode());
//                FndDepart fndDepart = sapMapper.getDepartById(expClaimHeader.getChargeDepartment(), "zh_CN");
                cti01.setEKKN_KOSTL("");
//                cti01.setEKKN_KOSTL(fndDepart.getDepartmentCode());
//                String equipmentId = "";
//                if (expClaimLine.getColumn2() != null && expClaimLine.getColumn2().equals("A")) {
//                    equipmentId = "**********";
//                }
                cti01.setEKKN_ANLN1("");
//                cti01.setEKKN_ANLN1(equipmentId);
//                        String matkl = "001";
//                        if (expClaimLine.getColumn2().equals("K")) {
//                            matkl = "002";
//                        }
                cti01.setEKPO_MATKL(expClaimLine.getExpType().getTypeCode());
                cti01.setEKPO_NETPR(expClaimLine.getPrice() + "");
                cti01.setEKPO_PEINH("1");
                cti01s.add(cti01);
            } catch (Exception e) {
                logger.error("pushCT=======行字段转换异常", e);
                JSONObject error = new JSONObject();
                error.put("line_id", expClaimLine.getLineId());
                error.put("message", e.getMessage());
                sapMapper.normalInsertLog(11903, "KJ-SAP-ERROR", error.toJSONString());
            }
        }
        SqPutData(cti01s);
    }

    @Override
    public void scanClaimPositionId(int positionId, String type) throws Exception {
        Collection<FndWorkflowPath> fndWorkflowPaths = new ArrayList<>();
        if (positionId > 0) {
            fndWorkflowPaths = sapMapper.getWorkflowPathByPositionId(11903, positionId, "approving");
        }
        if ("PRI01".equals(type)) {
            for (FndWorkflowPath fndWorkflowPath : fndWorkflowPaths) {
                ExpClaimHeader expClaimHeader = getExpClaimData(fndWorkflowPath.getSourceId());
                pushPR(expClaimHeader);
                sapMapper.updateWorkflowStatus(fndWorkflowPath.getPathId(), "commenting");
            }
        }
        if ("POI01".equals(type)) {
            for (FndWorkflowPath fndWorkflowPath : fndWorkflowPaths) {
                ExpClaimHeader expClaimHeader = getExpClaimData(fndWorkflowPath.getSourceId());
                pushPO(expClaimHeader);
                sapMapper.updateWorkflowStatus(fndWorkflowPath.getPathId(), "commenting");
            }
        }

        if ("GRI01".equals(type)) {
            for (FndWorkflowPath fndWorkflowPath : fndWorkflowPaths) {
                ExpClaimHeader expClaimHeader = getExpClaimData(fndWorkflowPath.getSourceId());
                pushGR(expClaimHeader);
                sapMapper.updateWorkflowStatus(fndWorkflowPath.getPathId(), "commenting");
            }
        }

        if ("CTI01".equals(type)) {
            for (FndWorkflowPath fndWorkflowPath : fndWorkflowPaths) {
                ExpClaimHeader expClaimHeader = getExpClaimData(fndWorkflowPath.getSourceId());
                pushCT(expClaimHeader);
                sapMapper.updateWorkflowStatus(fndWorkflowPath.getPathId(), "commenting");
            }
        }

        if ("FMI09".equals(type)) {
//            Collection<ExpClaimHeader> expClaimHeaders = sapMapper.getTobeGeneratedExp(11903);
            for (FndWorkflowPath fndWorkflowPath : fndWorkflowPaths) {
                ExpClaimHeader expClaimHeader = getExpClaimData(fndWorkflowPath.getSourceId());
                Collection<ExpClaimLine> expClaimLines = expClaimHeader.getClaimLines();
                List<Object> fmi009s = new ArrayList<>();
                int count = 0;
                try {
                    for (ExpClaimLine expClaimLine : expClaimLines) {
                        if (expClaimLine.getDrAccountId() > 0) {
                            GeneralLedgerAccount glAccount = sapMapper.getGlAccountById(expClaimLine.getDrAccountId(), "zh_CN");
                            FMI09 fmi09 = new FMI09();
                            fmi09.setORDERID(transDocumentNum(expClaimHeader.getDocumentNum()));
//                            fmi09.setORDERTYPE(expClaimHeader.getDescription());
                            fmi09.setORDERDSC(expClaimLine.getComments());
                            FndDepart branch = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                            fmi09.setBUKRS(branch.getDepartmentCode());
                            fmi09.setBLDAT(dateFormat(new Date(), 8));
                            fmi09.setBUDAT(dateFormat(expClaimHeader.getLastUpdateDate(), 8));
                            fmi09.setBLART("ZF");
                            fmi09.setWAERS(expClaimLine.getClaimCurrency());
                            fmi09.setBKTXT(transDocumentNum(expClaimHeader.getDocumentNum()));
                            count += 1;
                            fmi09.setBUZEI(String.format("%03d", count));
                            fmi09.setBSCHL("40");
                            fmi09.setHKONT(glAccount.getAccountCode());
                            fmi09.setDMBTR(new BigDecimal(expClaimLine.getFinNetAmount()) + "");
                            fmi09.setWRBTR(new BigDecimal(expClaimLine.getFinNetAmount()) + "");
                            FndDepart chargeDepartment = sapMapper.getDepartById(expClaimHeader.getChargeDepartment(), "zh_CN");
                            fmi09.setKOSTL(chargeDepartment.getDepartmentCode());
                            fmi09.setSGTXT(expClaimLine.getExpType().getType());
                            fmi09.setXBLNR(expClaimLine.getInvoiceNum());
                            fmi009s.add(fmi09);
                        }
                        if (expClaimLine.getTaxAccountId() > 0) {
                            GeneralLedgerAccount glAccount = sapMapper.getGlAccountById(expClaimLine.getTaxAccountId(), "zh_CN");
                            FMI09 fmi09 = new FMI09();
                            fmi09.setORDERID(transDocumentNum(expClaimHeader.getDocumentNum()));
//                            fmi09.setORDERTYPE(expClaimHeader.getDescription());
                            fmi09.setORDERDSC(expClaimLine.getComments());
                            FndDepart branch = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                            fmi09.setBUKRS(branch.getDepartmentCode());
                            fmi09.setBLDAT(dateFormat(new Date(), 8));
                            fmi09.setBUDAT(dateFormat(expClaimHeader.getLastUpdateDate(), 8));
                            fmi09.setBLART("ZF");
                            fmi09.setWAERS(expClaimLine.getClaimCurrency());
                            fmi09.setBKTXT(transDocumentNum(expClaimHeader.getDocumentNum()));
                            count += 1;
                            fmi09.setBUZEI(String.format("%03d", count));
                            fmi09.setBSCHL("40"); //todo
                            fmi09.setHKONT(glAccount.getAccountCode());
                            fmi09.setDMBTR(new BigDecimal(expClaimLine.getFinTaxAmount()) + "");
                            fmi09.setWRBTR(new BigDecimal(expClaimLine.getFinTaxAmount()) + "");
                            fmi09.setSGTXT(expClaimLine.getExpType().getType());
                            fmi09.setXBLNR(expClaimLine.getInvoiceNum());
//                            fmi09.setZUONR("********");
                            fmi009s.add(fmi09);
                        }
                    }
                    FMI09 fmi09 = new FMI09();
                    fmi09.setORDERID(transDocumentNum(expClaimHeader.getDocumentNum()));
//                    fmi09.setORDERTYPE(expClaimHeader.getDescription());
                    fmi09.setORDERDSC(transDocumentNum(expClaimHeader.getDocumentNum()));
                    FndDepart branch = sapMapper.getDepartById(expClaimHeader.getBranchId(), "zh_CN");
                    fmi09.setBUKRS(branch.getDepartmentCode());
                    fmi09.setBLDAT(dateFormat(new Date(), 8));
                    fmi09.setBUDAT(dateFormat(expClaimHeader.getLastUpdateDate(), 8));
                    fmi09.setBLART("ZF");
                    fmi09.setWAERS(expClaimHeader.getCurrencyCode());
                    fmi09.setBKTXT(transDocumentNum(expClaimHeader.getDocumentNum()));
                    count += 1;
                    fmi09.setBUZEI(String.format("%03d", count));
                    fmi09.setFLAG("X");
                    fmi09.setBSCHL("31"); //todo
                    SystemUser user = sapMapper.getUserById(expClaimHeader.getSubmitUser(), "zh_CN");
                    fmi09.setHKONT(user.getEmployeeNumber());
                    fmi09.setDMBTR(new BigDecimal(expClaimHeader.getTotalPayAmount()) + "");//todo
                    fmi09.setWRBTR(new BigDecimal(expClaimHeader.getTotalPayAmount()) + "");
                    fmi09.setSGTXT(transDocumentNum(expClaimHeader.getDocumentNum()));
                    fmi009s.add(fmi09);
                    SqPutData(fmi009s);
                    sapMapper.updateWorkflowStatus(fndWorkflowPath.getPathId(), "commenting");
                } catch (Exception e) {
                    logger.error("scanClaimPositionId===FMI09====行字段转换异常", e);
                    JSONObject error = new JSONObject();
                    error.put("line_id", expClaimHeader.getHeaderId());
                    error.put("message", e.getMessage());
                    sapMapper.normalInsertLog(11903, "EXP-SAP-ERROR", error.toJSONString());
                }

            }
        }

        if ("VDI01".equals(type)) {
            Collection<SystemUser> users = sapMapper.getAllUsersByCompany(11903, "zh_CN");
            List<Object> vdi01s = new ArrayList<>();
            for (SystemUser user : users) {
                try {
                    FndUserAccount primaryUserAccount = null;
                    Collection<FndUserAccount> userAccounts = sapMapper.findUserAccounts(user.getUserId(), 11903);
                    for (FndUserAccount userAccount : userAccounts) {
                        primaryUserAccount = userAccount;
                        if (null != userAccount.getPrimaryFlag() && userAccount.getPrimaryFlag().equals("Y")) {
                            primaryUserAccount = userAccount;
                            break;
                        }
                    }
                    FndDepart fndDepart = sapMapper.getDepartById(user.getDepartmentId(), "zh_CN");
                    FndDepart branchDepart = sapMapper.getDepartById(fndDepart.getBranchCompanyId(), "zh_CN");
                    VDI01 vdi01 = new VDI01();
                    vdi01.setCOMPANY(branchDepart.getDepartmentCode());
                    vdi01.setLIFNR(user.getEmployeeNumber());
                    vdi01.setADDR1_DATA_NAME1(user.getFullName());
                    vdi01.setSMTP_ADDR(user.getEmailAddress());
                    vdi01.setMOB_NUMBER(user.getMobile());
                    vdi01.setADDR3_DATA_DEPARTMENT(fndDepart.getDepartmentName());
                    if (null != primaryUserAccount) {
                        vdi01.setLFBK_BANKN(primaryUserAccount.getAccountNumber());
                        vdi01.setLFBK_BANKL(primaryUserAccount.getBankCode());
                        vdi01.setBNKA_BANKA(primaryUserAccount.getBankName());
                    }
                    if (!"Y".equals(user.getEnabled())) {
                        vdi01.setIF_DELETE("D");
                    }
                    if (null != user.getEmployeeNumber() && !"".equals(user.getEmployeeNumber())
                            && null != primaryUserAccount && null != primaryUserAccount.getBankCode()
                            && !"".equals(primaryUserAccount.getBankCode())) {
                        vdi01s.add(vdi01);
                    }
                } catch (Exception e) {
                    logger.error("scanClaimPositionId===VDI01====行字段转换异常", e);
                    JSONObject error = new JSONObject();
                    error.put("user", user);
                    error.put("message", e.getMessage());
                    sapMapper.normalInsertLog(11903, "User-SAP-ERROR", error.toJSONString());
                }
            }
            try {
                SqPutData(vdi01s);
            } catch (Exception e) {
                logger.error("scanClaimPositionId===VDI01====员工主数据写入ftp失败", e);
                JSONObject error = new JSONObject();
                error.put("desc", "员工主数据写入ftp失败");
                error.put("message", e.getMessage());
                sapMapper.normalInsertLog(11903, "User-SAP-ERROR", error.toJSONString());
            }

        }

    }

    public String transDocumentNum(String documentNum) {
        return documentNum.substring(0, 3) + documentNum.substring(documentNum.length() - 7, documentNum.length());
    }

    public String returnDocumentNum(String subDocumentNum) {
        return subDocumentNum.substring(0, 3) + "000" + subDocumentNum.substring(subDocumentNum.length() - 7, subDocumentNum.length());
    }

    @Override
    public void receiveSapData(String type) throws Exception {

        if ("CTI02".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===CTI02====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SAP-KJ-ERROR", error.toJSONString());
            }
            for (Object object : result) {
                try {
                    JSONObject jsonObject = (JSONObject) object;
                    String prsn = jsonObject.getString("ekko_ebeln_oa");
                    String documentNum = returnDocumentNum(prsn);
                    jsonObject.put("ekko_ebeln_oa", documentNum);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(11903);
                    fndQuery.setInput(jsonObject.toJSONString());
                    fndQuery.setType(type);
                    sapMapper.updateSapData(fndQuery);
                } catch (Exception e) {
                    logger.error("receiveSapData===CTI02====数据库执行异常", e);
                    JSONObject jsonObject = (JSONObject) object;
                    sapMapper.normalInsertLog(11903, "SAP-KJ-ERROR", jsonObject.toJSONString());
                }

            }
        }

        if ("PRI02".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===PRI02====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SAP-PR-ERROR", error.toJSONString());
            }
            for (Object object : result) {
                try {
                    JSONObject jsonObject = (JSONObject) object;
                    String prsn = jsonObject.getString("prsn");
                    String documentNum = returnDocumentNum(prsn);
                    jsonObject.put("prsn", documentNum);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(11903);
                    fndQuery.setInput(jsonObject.toJSONString());
                    fndQuery.setType(type);
                    sapMapper.updateSapData(fndQuery);
                } catch (Exception e) {
                    logger.error("receiveSapData===PRI02====数据库执行异常", e);
                    JSONObject jsonObject = (JSONObject) object;
                    sapMapper.normalInsertLog(11903, "SAP-PR-ERROR", jsonObject.toJSONString());
                }

            }
        }

        if ("POI02".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===POI02====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SAP-PO-ERROR", error.toJSONString());
            }
            for (Object object : result) {
                try {
                    JSONObject jsonObject = (JSONObject) object;
                    String prsn = jsonObject.getString("ihrez");
                    String documentNum = returnDocumentNum(prsn);
                    jsonObject.put("ihrez", documentNum);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(11903);
                    fndQuery.setInput(jsonObject.toJSONString());
                    fndQuery.setType(type);
                    sapMapper.updateSapData(fndQuery);
                } catch (Exception e) {
                    logger.error("receiveSapData===POI02====数据库执行异常", e);
                    JSONObject jsonObject = (JSONObject) object;
                    sapMapper.normalInsertLog(11903, "SAP-PO-ERROR", jsonObject.toJSONString());
                }

            }
        }

        if ("GRI02".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===GRI02====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SAP-RE-ERROR", error.toJSONString());
            }
            for (Object object : result) {
                try {
                    JSONObject jsonObject = (JSONObject) object;
                    String prsn = jsonObject.getString("grnum");
                    String documentNum = returnDocumentNum(prsn);
                    jsonObject.put("grnum", documentNum);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(11903);
                    fndQuery.setInput(jsonObject.toJSONString());
                    fndQuery.setType(type);
                    sapMapper.updateSapData(fndQuery);
                } catch (Exception e) {
                    logger.error("receiveSapData===GRI02====数据库执行异常", e);
                    JSONObject jsonObject = (JSONObject) object;
                    sapMapper.normalInsertLog(11903, "SAP-RE-ERROR", jsonObject.toJSONString());
                }

            }
        }

        if ("FMI10".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI10====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SAP-EXP-ERROR", error.toJSONString());
            }
            for (Object object : result) {
                try {
                    JSONObject jsonObject = (JSONObject) object;
                    String prsn = jsonObject.getString("orderid");
                    String documentNum = returnDocumentNum(prsn);
                    jsonObject.put("orderid", documentNum);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(11903);
                    fndQuery.setInput(jsonObject.toJSONString());
                    fndQuery.setType(type);
                    sapMapper.updateSapData(fndQuery);

//                    JSONObject input = new JSONObject();
//                    input.
//                    GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
//                    glBatchTransfer.setCompanyId(11903);
//                    glBatchTransfer.setUserId(124638);
//                    glBatchTransfer.setType("api");
//                    glBatchTransfer.setInput(expReport.getInput());
//                    sapMapper.glBatchCreate2(glBatchTransfer);
                } catch (Exception e) {
                    logger.error("receiveSapData===FMI10====数据库执行异常", e);
                    JSONObject jsonObject = (JSONObject) object;
                    sapMapper.normalInsertLog(11903, "SAP-EXP-ERROR", jsonObject.toJSONString());
                }
            }
        }

        if ("FMI01".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                JSONArray fundData = SqGetData(type);
                JSONArray costCenterData = SqGetData("FMI04");
                JSONArray mappingData = SqGetData("FMI05");
                for (Object rel : fundData) {
                    JSONObject target = (JSONObject) rel;
                    String fundCode = target.getString("fictr");
                    if (null == fundCode) {
                        continue;
                    }
                    for (Object mapping : mappingData) {
                        JSONObject mappingObj = (JSONObject) mapping;
                        String costCode = null;
                        if (fundCode.equals(mappingObj.getString("target"))) {
                            costCode = mappingObj.getString("kostl");
                            if (null == costCode) {
                                continue;
                            }
                            for (Object cost : costCenterData) {
                                JSONObject costObj = (JSONObject) cost;
                                if (costCode.equals(costObj.getString("kostl"))) {
                                    target.put("branch_fictr", costObj.getString("bukrs"));
                                    break;
                                }
                            }
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("receiveSapData===FMI01====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "FMI01-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI01=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI01====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI01-ERROR", result.toJSONString());
            }
        }

        if ("FMI04".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI04====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "FMI04-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI04=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI04====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI04-ERROR", result.toJSONString());
            }
        }

        if ("FMI02".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI02====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "FMI02-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI02=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI02====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI02-ERROR", result.toJSONString());
            }
        }

        if ("FMI03".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI03====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "FMI03-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI03=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI03====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI03-ERROR", result.toJSONString());
            }
        }

        if ("FMI06".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI06====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "FMI06-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI06=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI06====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI06-ERROR", result.toJSONString());
            }
        }

        if ("SVI01".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===SVI01====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SVI01-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====SVI01=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===SVI01====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "SVI01-ERROR", result.toJSONString());
            }
        }

        if ("FMI07".equals(type)) {
            JSONArray result = new JSONArray();
            try {
                result = SqGetData(type);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI07====ftp获取异常", e);
                JSONObject error = new JSONObject();
                error.put("type", type);
                error.put("message", e.getMessage());
                error.put("error", "ftp获取异常");
                sapMapper.normalInsertLog(11903, "SVI01-ERROR", error.toJSONString());
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(11903);
                fndQuery.setInput(result.toJSONString());
                fndQuery.setType(type);
                logger.info("receiveSapData====FMI07=======Input数据：" + result);
                sapMapper.updateSapData(fndQuery);
            } catch (Exception e) {
                logger.error("receiveSapData===FMI07====数据库执行异常", e);
                sapMapper.normalInsertLog(11903, "FMI07-ERROR", result.toJSONString());
            }
        }
    }

    @Override
    public void sendByDocumentNum(String documentNum, String type) throws Exception {
        ExpClaimHeader expClaim = sapMapper.getDocumentByDocumentNum(documentNum, 11903);
        ExpClaimHeader expClaimHeader = getExpClaimData(expClaim.getHeaderId());
        if ("PRI01".equals(type)) {
            pushPR(expClaimHeader);
        }
        if ("POI01".equals(type)) {
            pushPO(expClaimHeader);
        }
        if ("GRI01".equals(type)) {
            pushGR(expClaimHeader);
        }
        if ("CTI01".equals(type)) {
            pushCT(expClaimHeader);
        }
    }

    @Override
    public void sendByHeaderTypeId(int headerTypeId, String type) throws Exception {
        Collection<ExpClaimHeader> expClaimHeaders = sapMapper.getDocumentByHeaderType(headerTypeId, 11903);
        for (ExpClaimHeader expClaim : expClaimHeaders) {
            ExpClaimHeader expClaimHeader = getExpClaimData(expClaim.getHeaderId());
            if ("PRI01".equals(type)) {
                pushPR(expClaimHeader);
            }
            if ("POI01".equals(type)) {
                pushPO(expClaimHeader);
            }
            if ("GRI01".equals(type)) {
                pushGR(expClaimHeader);
            }
            if ("CTI01".equals(type)) {
                pushCT(expClaimHeader);
            }
        }
    }

    public void heraeusDocumentPosting() throws Exception {

//        JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
//        factory.setServiceClass(DocumentPostingSYOB.class);
//        factory.setAddress("http://q-soap.heraeus.com/XISOAPAdapter/MessageServlet?senderParty=Cloudpense&senderService=CloudpenseAPI&receiverParty=&receiverService=&interface=DocumentPosting_SY_OB&interfaceNamespace=http://heraeus.com/pi/MG/CP/DocumentPosting/10");
//        factory.setUsername("I_CLOUDPENSE");
//        factory.setPassword("ioCskB8!");
//        DocumentPostingSYOB client = (DocumentPostingSYOB) factory.create();
//        DocumentPostingGroup.DocumentPosting documentPosting = new DocumentPostingGroup.DocumentPosting();
//        documentPosting.setCompanyCode("2000");
//        DocumentPostingGroup documentPostingGroup = new DocumentPostingGroup();
////        documentPostingGroup.documentPosting = documentPosting;
//        DocumentPostingResponse documentPostingResponse = client.documentPostingSYOB(documentPostingGroup);
//        System.out.println(documentPostingResponse);

//        DocumentPostingSYOBService documentPostingSYOBService = new DocumentPostingSYOBService();
//        DocumentPostingSYOB documentPostingSYOB = documentPostingSYOBService.getHTTPPort();
//        DocumentPostingResponse documentPostingResponse = documentPostingSYOB.documentPostingSYOB(new DocumentPostingGroup());
//        System.out.println(documentPostingResponse);
    }

    @Override
    public void iBMPApprove() throws Exception {
        String LOG_KEY = "phoenix iBMPApprove===";
        FndQuery fndQuery = new FndQuery();
        fndQuery.setType("iBMPPost");
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setInput(new JSONObject().toJSONString());
        Collection<String> result = phoenixMapper.phoenixPr(fndQuery);
        if(result == null || result.isEmpty()) {
            logger.info("{}无待处理数据", LOG_KEY);
            return;
        }
        logger.info("{}获取待处理供{}条", LOG_KEY, result.size());
        Map<String, String> identity = new HashMap<>();
        identity.put("user", iBMPUserName);
        identity.put("password", iBMPKey);
        int index = 0;
        for (String s : result) {
            index++;
            logger.info("{}开始处理第{}条数据={}", LOG_KEY, index, s);
            JSONObject obj = JSONObject.parseObject(s);
            JSONObject postData = new JSONObject();
            postData.put("method", "CampaignFeedBack");
            postData.put("entity", obj.toJSONString());
            int headerId = obj.getInteger("headerId");

            JSONArray updateResult = new JSONArray();
            JSONObject updateObj = new JSONObject();
            updateObj.put("headerId", headerId);

            FndQuery updateResultInput = new FndQuery();
            updateResultInput.setType("documentPostUpdate");
            updateResultInput.setCompanyId(phoenixCompanyId);
            try {
                logger.info("{}第{}条数据调用外部接口url={}, 请求={}", LOG_KEY, index, iBMPUrl, postData);
                String postResult = PhoenixHttpPostIBMP(iBMPUrl, identity, postData);
                logger.info("{}第{}条数据调用外部接口url={}, 响应={}", LOG_KEY, index, iBMPUrl, postResult);
                JSONObject objResult = JSONObject.parseObject(postResult);
                if (objResult.getInteger("resultCode") == 0) {
                    //成功
                    updateResultInput.setType("documentPostUpdate");
                } else {
                    updateResultInput.setType("documentPostError");
                    updateObj.put("message", objResult.getString("resultMsg"));
                    sapMapper.normalInsertLog(phoenixCompanyId, "phoenixIBMPError", objResult.toJSONString());
                }
            } catch (Exception e) {
                logger.info("{}第{}条数据出现未知异常：", LOG_KEY, index, e);
                updateResultInput.setType("documentPostError");
                updateObj.put("message", e.getMessage());
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixdocumentPostError", updateObj.toJSONString());
            } finally {
                updateResult.add(updateObj);
                updateResultInput.setInput(updateResult.toJSONString());
                phoenixMapper.phoenixPr(updateResultInput);
            }
        }
    }

    @Override
    public void iBMPExpensePost() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setType("iBMPExpensePost");
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setInput(new JSONObject().toJSONString());
        logger.info("iBMPExpensePost=====fndQuery：" + fndQuery);
        Collection<String> result = phoenixMapper.phoenixPr(fndQuery);
        logger.info("iBMPExpensePost=====result：" + Arrays.toString(result.toArray()));
        Map<String, String> identity = new HashMap<>();
        identity.put("user", iBMPUserName);
        identity.put("password", iBMPKey);
        for (String s : result) {
            JSONObject obj = JSONObject.parseObject(s);
            obj.put("Details", JSONObject.parseArray(obj.getString("Detail")));
            obj.put("Detail", null);
            JSONArray data = new JSONArray();
            data.add(obj);
            JSONObject postData = new JSONObject();
            postData.put("method", "CostFeedBack");
            postData.put("entity", data.toJSONString());
            int headerId = obj.getInteger("headerId");

            JSONArray updateResult = new JSONArray();
            JSONObject updateObj = new JSONObject();
            updateObj.put("headerId", headerId);

            FndQuery updateResultInput = new FndQuery();
            updateResultInput.setType("documentPostUpdate");
            updateResultInput.setCompanyId(phoenixCompanyId);
            logger.info("iBMPExpensePost=====postData：" + postData);
            try {
                String postResult = PhoenixHttpPostIBMP(iBMPUrl, identity, postData);
                JSONObject objResult = JSONObject.parseObject(postResult);
                if (objResult.getInteger("resultCode") == 0) {
                    //成功
                    updateResultInput.setType("documentPostUpdate");
                } else {
                    updateResultInput.setType("documentPostError");
                    updateObj.put("message", objResult.getString("resultMsg"));
                    sapMapper.normalInsertLog(phoenixCompanyId, "phoenixIBMPError", objResult.toJSONString());
                }
            } catch (Exception e) {
                updateResultInput.setType("documentPostError");
                updateObj.put("message", e.getMessage());
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixdocumentPostError", updateObj.toJSONString());
            } finally {
                updateResult.add(updateObj);
                updateResultInput.setInput(updateResult.toJSONString());
                phoenixMapper.phoenixPr(updateResultInput);
            }
        }
    }

    @Override
    public void iPortalApprove() throws Exception {
        String LOG_KEY = "phoenix iPortalApprove===";
        FndQuery fndQuery = new FndQuery();
        fndQuery.setType("iPortalPost");
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setInput(new JSONObject().toJSONString());
        Collection<String> result = phoenixMapper.phoenixPr(fndQuery);
        if(result == null || result.isEmpty()) {
            logger.info("{}无待处理数据", LOG_KEY);
            return;
        }
        logger.info("{}获取待处理供{}条", LOG_KEY, result.size());
        int index = 0;
        for (String s : result) {
            index++;
            logger.info("{}开始处理第{}条数据={}", LOG_KEY, index, s);
            JSONObject obj = JSONObject.parseObject(s);
            ApproveObject approveObject = new ApproveObject();
            approveObject.setAuditNote(obj.getString("auditNote"));
            approveObject.setFdNumber(obj.getString("fdNumber"));
            approveObject.setLoginName(obj.getString("loginName"));
            approveObject.setOprFlag(obj.getString("oprFlag"));
            int headerId = obj.getInteger("headerId");

            JSONArray updateResult = new JSONArray();
            JSONObject updateObj = new JSONObject();
            updateObj.put("headerId", headerId);

            FndQuery updateResultInput = new FndQuery();
            updateResultInput.setType("documentPostUpdate");
            updateResultInput.setCompanyId(phoenixCompanyId);
            try {
                String iPortalUrl = rb.getString("phoenix.iPortalUrl");
                logger.info("{}第{}条数据调用外部接口url={}, 请求={}", LOG_KEY, index, iPortalUrl, JSONObject.toJSONString(approveObject));
                JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
                factory.setServiceClass(IPhoenixLbpmWebservice.class);
                factory.setAddress(iPortalUrl);
                IPhoenixLbpmWebservice client = (IPhoenixLbpmWebservice) factory.create();
                String approveResponse = client.approve(approveObject);
                logger.info("{}第{}条数据调用外部接口url={}, 响应={}", LOG_KEY, index, iPortalUrl, approveResponse);
                JSONObject objResult = JSONObject.parseObject(approveResponse);
                if (objResult.getInteger("resultCode") == 1) {
                    //成功
                    updateResultInput.setType("documentPostUpdate");
                } else {
                    updateResultInput.setType("documentPostError");
                    updateObj.put("message", objResult.getString("resultMsg"));
                    sapMapper.normalInsertLog(phoenixCompanyId, "iPortalApproveError", objResult.toJSONString());
                }
            } catch (Exception e) {
                logger.info("{}第{}条数据出现未知异常：", LOG_KEY, index, e);
                updateResultInput.setType("documentPostError");
                updateObj.put("message", e.getMessage());
                sapMapper.normalInsertLog(phoenixCompanyId, "iPortalApproveError", updateObj.toJSONString());
            } finally {
                updateResult.add(updateObj);
                updateResultInput.setInput(updateResult.toJSONString());
                phoenixMapper.phoenixPr(updateResultInput);
            }
        }
    }

    @Override
    public void iPortalTravelPost() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setType("iPortalTravelPost");
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setInput(new JSONObject().toJSONString());
        Collection<String> result = phoenixMapper.phoenixPr(fndQuery);
        for (String s : result) {
            JSONObject obj = JSONObject.parseObject(s);
            KmReviewParamterForm kmReviewParamterForm = new KmReviewParamterForm();
            kmReviewParamterForm.setDocSubject(obj.getString("docSubject"));
            kmReviewParamterForm.setFdTemplateId(obj.getString("fdTemplateId"));
            kmReviewParamterForm.setDocStatus(obj.getString("docStatus"));
            kmReviewParamterForm.setDocCreator(obj.getString("docCreator"));
            kmReviewParamterForm.setFormValues(obj.getString("formValues"));
            kmReviewParamterForm.setFlowParam("{}");

            int headerId = obj.getInteger("headerId");

            JSONArray updateResult = new JSONArray();
            JSONObject updateObj = new JSONObject();
            updateObj.put("headerId", headerId);

            FndQuery updateResultInput = new FndQuery();
            updateResultInput.setType("travelPostSuccessUpdate");
            updateResultInput.setCompanyId(phoenixCompanyId);
            logger.info("iPortalTravelPost=========kmReviewParamterForm：" + JSONObject.toJSONString(kmReviewParamterForm));
            try {
                String iPortalTravelUrl = rb.getString("phoenix.iPortalTravelUrl");
                JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
                factory.setServiceClass(IKmReviewWebserviceService.class);
                factory.setAddress(iPortalTravelUrl);
                IKmReviewWebserviceService client = (IKmReviewWebserviceService) factory.create();
                String approveResponse = client.addReview(kmReviewParamterForm);
                logger.info("iPortalTravelPost========approveResponse：" + approveResponse);
                if (null != approveResponse) {
                    updateResultInput.setType("travelPostSuccessUpdate");
                } else {
                    updateResultInput.setType("travelPostErrorUpdate");
                    updateObj.put("message", "iportal出差申请传输返回为空");
                    sapMapper.normalInsertLog(phoenixCompanyId, "iPortalApproveError", updateObj.toJSONString());
                }

            } catch (Exception e) {
                logger.error("iPortalTravelPost=======error：", e);
                updateResultInput.setType("travelPostErrorUpdate");
                updateObj.put("message", e.getMessage());
                sapMapper.normalInsertLog(phoenixCompanyId, "iPortalApproveError", updateObj.toJSONString());
            } finally {
                updateResult.add(updateObj);
                updateResultInput.setInput(updateResult.toJSONString());
                phoenixMapper.phoenixPr(updateResultInput);
            }
        }
    }

    @Override
    public void iPortalPush() throws Exception {
        PhoenixPush query = new PhoenixPush();
        query.setCompanyId(phoenixCompanyId);
        query.setStatus(0);
        Collection<PhoenixPush> pushTasks = phoenixMapper.getPushTask(query);
        for (PhoenixPush phoenixPush : pushTasks) {
            if (phoenixPush.getType().equals("phoenix_approve")) {
                try {
                    NotifyTodoSendContext notifyTodoSendContext = new NotifyTodoSendContext();
                    notifyTodoSendContext.setAppName("SP");
                    notifyTodoSendContext.setModelName("cloudpense");
                    JSONObject jsonObject = JSONObject.parseObject(phoenixPush.getContent());
                    int headerId = jsonObject.getInteger("header_id");
                    ExpClaimHeader expClaimHeader = sapMapper.getExpClaimHeader(headerId);
                    if (!"submitted".equals(expClaimHeader.getStatus())) {
                        phoenixPush.setStatus(3);
                        phoenixMapper.updatePushStatus(phoenixPush);
                        continue;
                    }
                    notifyTodoSendContext.setModelId(jsonObject.getString("path_id"));
                    notifyTodoSendContext.setSubject(jsonObject.getString("subject"));
                    notifyTodoSendContext.setLink(jsonObject.getString("link"));
                    notifyTodoSendContext.setType(jsonObject.getInteger("type"));
                    notifyTodoSendContext.setCreateTime(jsonObject.getString("create_time"));
                    notifyTodoSendContext.setTargets(jsonObject.getString("targets"));
//                    notifyTodoSendContext.setExtendContent(jsonObject.getString("content"));
                    JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
                    factory.setServiceClass(ISysNotifyTodoWebService.class);
                    factory.setAddress(iPortalTodoUrl);
                    ISysNotifyTodoWebService client = (ISysNotifyTodoWebService) factory.create();
                    NotifyTodoAppResult notifyTodoAppResult = client.sendTodo(notifyTodoSendContext);
                    logger.info("iPortalPush====notifyTodoAppResult：" + JSONObject.toJSONString(notifyTodoAppResult));
                    phoenixPush.setStatus(2);
                    phoenixMapper.updatePushStatus(phoenixPush);
                } catch (Exception e) {
                    logger.error("iPortalPush=======error：", e);
                    phoenixPush.setStatus(3);
                    phoenixMapper.updatePushStatus(phoenixPush);
                }
            }
            if (phoenixPush.getType().equals("phoenix_approve_delete")) {
                try {
                    NotifyTodoRemoveContext notifyTodoRemoveContext = new NotifyTodoRemoveContext();
                    notifyTodoRemoveContext.setAppName("SP");
                    notifyTodoRemoveContext.setModelName("cloudpense");
                    JSONObject jsonObject = JSONObject.parseObject(phoenixPush.getContent());
                    notifyTodoRemoveContext.setModelId(jsonObject.getString("path_id"));
                    notifyTodoRemoveContext.setOptType(jsonObject.getInteger("type"));
                    notifyTodoRemoveContext.setTargets(jsonObject.getString("targets"));
                    JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
                    factory.setServiceClass(ISysNotifyTodoWebService.class);
                    factory.setAddress(iPortalTodoUrl);
                    ISysNotifyTodoWebService client = (ISysNotifyTodoWebService) factory.create();
                    client.deleteTodo(notifyTodoRemoveContext);
                    phoenixPush.setStatus(2);
                    phoenixMapper.updatePushStatus(phoenixPush);
                } catch (Exception e) {
                    logger.error("iPortalPush=======error：", e);
                    phoenixPush.setStatus(3);
                    phoenixMapper.updatePushStatus(phoenixPush);
                }
            }
        }

    }

    @Override
    public void sapPost() throws Exception {
//        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
//        glBatchTransfer.setCompanyId(phoenixCompanyId);
//        glBatchTransfer.setType("api");
//        glBatchTransfer.setLanguage("zh_CN");
//        glBatchTransfer.setInput("{\"document_id\":[450],\"date\":\"2018-11-01\"}");
//        String data = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
//        System.out.println(data);
//
//        Map<String, String> identity = new HashMap<>();
//        identity.put("Authorization", sapHash);
//
//        String str = PhoenixHttpPostString(sapPostUrl, identity, data);
//        System.out.println(str);
    }

    @Override
    public void phoenixSync() throws Exception {
        String lastDate = "2017-01-01";
        try {
            lastDate = OAuthHelper.readFileOnly("lastFetchPhoenixDate");
        } catch (Exception e) {
            lastDate = "2017-01-01";
            OAuthHelper.writeFile(lastDate, "lastFetchPhoenixDate");
            logger.error("phoenixSync=======error", e);
        }
        logger.info("aaaaaaaaaaaaaaaaaaaaaaa");
        logger.info("HCMUserUrl:"+HCMUserUrl);
        logger.info("HCMUserName:"+HCMUserName);
        logger.info("HCMPassword:"+HCMPassword);
        logger.info("phoenixSync=====lastDate：" + lastDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        GregorianCalendar calendar = new GregorianCalendar(TimeZone.getTimeZone("UTC"));
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR, 8);
        Date moreDate = calendar.getTime();
        String nowDate = sdf.format(moreDate);
        String[] companyCodes = phoenixMapper.syncCompanyCode(phoenixCompanyId);
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(ZhrCcSyncOrgan.class);
            factory.setAddress(HCMOrganUrl);
            factory.setUsername(HCMUserName);
            factory.setPassword(HCMPassword);
            Holder<ZHRTCCORGAN> etOUTORG = new Holder<>();
            Holder<String> evMESSG = new Holder<>();
            Holder<String> evMSGTY = new Holder<>();
            ZhrCcSyncOrgan client = (ZhrCcSyncOrgan) factory.create();
//            lastDate = "2020-08-05";
            logger.info("phoenixSync=====lastDate：" + lastDate);
            logger.info("phoenixSync=====nowDate：" + nowDate);
            client.zhrCCSYNCORGAN(lastDate, nowDate, etOUTORG, evMESSG, evMSGTY);
            logger.info("phoenixSync====== 菲尼克斯第一次部门同步 ==Item：" + JSON.toJSONString(etOUTORG.value.getItem()));
            if ("S".equals(evMSGTY.value)) {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setInput(JSON.toJSONString(etOUTORG.value.getItem()));
                fndQuery.setCompanyId(phoenixCompanyId);
                fndQuery.setType("department");
                phoenixMapper.phoenixPr(fndQuery);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("error", JSON.toJSONString(etOUTORG.value.getItem()));
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixDepartmentSync", JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            logger.error("phoenixSync=== 菲尼克斯第一次部门同步 ====error", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "phoenixDepartmentSync", JSONObject.toJSONString(jsonObject));
        }
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(ZhrCcSyncPerson.class);
            factory.setAddress(HCMUserUrl);
            factory.setUsername(HCMUserName);
            factory.setPassword(HCMPassword);
            Holder<ZHRTCCPERSON> etOUTPERSON = new Holder<>();
            Holder<String> evMESSG = new Holder<>();
            Holder<String> evMSGTY = new Holder<>();
            ZhrCcSyncPerson client = (ZhrCcSyncPerson) factory.create();
            client.zhrCCSYNCPERSON(lastDate, "", nowDate, etOUTPERSON, evMESSG, evMSGTY);
            logger.info("phoenixSync====== 菲尼克斯人员同步 ==Item：" + JSON.toJSONString(etOUTPERSON.value.getItem()));
            if ("S".equals(evMSGTY.value)) {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setInput(JSON.toJSONString(etOUTPERSON.value.getItem()));
                fndQuery.setCompanyId(phoenixCompanyId);
                fndQuery.setType("employee");
                phoenixMapper.phoenixPr(fndQuery);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("error", JSON.toJSONString(etOUTPERSON.value.getItem()));
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixUserSync", JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            logger.error("phoenixSync==== 菲尼克斯人员同步 ===error", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "phoenixUserSync", JSONObject.toJSONString(jsonObject));
        }
        for (String companyCode : companyCodes) {
            try {
                JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
                factory.setServiceClass(ZhrCcSyncKostl.class);
                factory.setAddress(HCMCostCenterUrl);
                factory.setUsername(HCMUserName);
                factory.setPassword(HCMPassword);
                Holder<ZHRTCCKOSTL> etOUTCOSTCENTER = new Holder<>();
                Holder<String> evMESSG = new Holder<>();
                Holder<String> evMSGTY = new Holder<>();
                ZhrCcSyncKostl client = (ZhrCcSyncKostl) factory.create();
                client.zhrCCSYNCKOSTL(lastDate, companyCode, nowDate, etOUTCOSTCENTER, evMESSG, evMSGTY);
                logger.info("phoenixSync====== 菲尼克斯成本中心同步 ==Item：" + JSON.toJSONString(etOUTCOSTCENTER.value.getItem()));

                if ("S".equals(evMSGTY.value)) {
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setInput(JSON.toJSONString(etOUTCOSTCENTER.value.getItem()));
                    fndQuery.setCompanyId(phoenixCompanyId);
                    fndQuery.setType("costcenter");
                    phoenixMapper.phoenixPr(fndQuery);
                } else {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("error", JSON.toJSONString(etOUTCOSTCENTER.value.getItem()));
                    sapMapper.normalInsertLog(phoenixCompanyId, "phoenixCostCenterSync", JSONObject.toJSONString(jsonObject));
                }
            } catch (Exception e) {
                logger.error("phoenixSync=== 菲尼克斯成本中心同步 ====error", e);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("error", e.getMessage());
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixCostCenterSync", JSONObject.toJSONString(jsonObject));
            }
        }
        try {
            JaxWsProxyFactoryBean factory = new JaxWsProxyFactoryBean();
            factory.setServiceClass(ZhrCcSyncOrgan.class);
            factory.setAddress(HCMOrganUrl);
            factory.setUsername(HCMUserName);
            factory.setPassword(HCMPassword);
            Holder<ZHRTCCORGAN> etOUTORG = new Holder<>();
            Holder<String> evMESSG = new Holder<>();
            Holder<String> evMSGTY = new Holder<>();
            ZhrCcSyncOrgan client = (ZhrCcSyncOrgan) factory.create();
            logger.info("phoenixSync=====lastDate：" + lastDate);
            logger.info("phoenixSync=====nowDate：" + nowDate);
            client.zhrCCSYNCORGAN(lastDate, nowDate, etOUTORG, evMESSG, evMSGTY);
            logger.info("phoenixSync====== 菲尼克斯第二次部门同步 ==Item：" + JSON.toJSONString(etOUTORG.value.getItem()));
            if ("S".equals(evMSGTY.value)) {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setInput(JSON.toJSONString(etOUTORG.value.getItem()));
                fndQuery.setCompanyId(phoenixCompanyId);
                fndQuery.setType("department");
                phoenixMapper.phoenixPr(fndQuery);
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("error", JSON.toJSONString(etOUTORG.value.getItem()));
                sapMapper.normalInsertLog(phoenixCompanyId, "phoenixDepartmentSync", JSONObject.toJSONString(jsonObject));
            }
        } catch (Exception e) {
            logger.error("phoenixSync=== 菲尼克斯第二次部门同步 ====error", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "phoenixDepartmentSync", JSONObject.toJSONString(jsonObject));
        }
        phoenixMapper.calculateDepartment(phoenixCompanyId);
        OAuthHelper.writeFile(nowDate, "lastFetchPhoenixDate");
    }

    @Override
    public void phoenixSupplier() {
        LogHolder.setLogKey("phoenixSupplier==");
        logger.info("{}开始执行", LogHolder.getLogKey());
        JSONObject data = new JSONObject();
        Map<String, String> identity = new HashMap<>();
        identity.put("user-name", "phoenix");
        identity.put("pass-word", "phoenix1028");
        data.put("path", supplierPath);
        data.put("partLine", ";");
        data.put("title", "column1,column2,column3,column4,column5,column6," +
                 "column7,column8,column9,column10,column11,column12");
        JSONArray jsonArray;
        try {
            logger.info("{}调用外部接口请求={}", LogHolder.getLogKey(), data.toJSONString());
            String result = PhoenixHttpPostIBMP(FTPGetUrl, identity, data);
            jsonArray = JSONArray.parseArray(result);
            if(jsonArray == null || jsonArray.size() == 0) {
                logger.info("{}本次无待处理数据", LogHolder.getLogKey());
                LogHolder.clearLogKey();
                return;
            }
            logger.info("{}本次待处理数据{}条", LogHolder.getLogKey(), jsonArray.size());
        } catch (Exception e) {
            logger.error("{}调用外部接口异常={}", LogHolder.getLogKey(), e.getMessage(), e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "phoenixSupplierSync", JSONObject.toJSONString(jsonObject));
            LogHolder.clearLogKey();
            return;
        }
        // 分配更新数据
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setType("supplier");
        JSONArray array = new JSONArray();
        int batch = 0;
        for(int i = 0; i < jsonArray.size(); i++) {
            try {
                JSONObject json = jsonArray.getJSONObject(i);
                if(json == null || StringUtils.isEmpty(json.getString("column1")) ||
                   StringUtils.isEmpty(json.getString("column2"))) {
                    logger.error("{}忽略异常数据：{}", LogHolder.getLogKey(), json);
                    continue;
                }
                array.add(json);
                if(array.size() == 100 || i == jsonArray.size() - 1) {
                    batch++;
                    logger.info("{}调用proc更新批次={}，请求={}", LogHolder.getLogKey(), batch, array.toString());
                    fndQuery.setInput(array.toString());
                    phoenixMapper.phoenixPr(fndQuery);
                    logger.info("{}调用proc更新批次={}，响应={}", LogHolder.getLogKey(), batch, fndQuery);
                    array.clear();
                    logger.info("{}休眠5秒");
                    Thread.sleep(5000);
                }
            } catch (Exception e) {
                logger.error("{}调用proc更新批次={}，异常={}", LogHolder.getLogKey(), batch, e.getMessage(), e);
            }
        }
        LogHolder.clearLogKey();
        logger.info("{}结束执行", LogHolder.getLogKey());
    }

    @Override
    public void phoenixHrPost() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setType("hrAnnualLeavePostDelete");
        fndQuery.setInput("{}");
        Collection<String> result = phoenixMapper.phoenixPr(fndQuery);
        for (String s : result) {
            try {
                JSONObject obj = JSONObject.parseObject(s);

                FndQuery updateResultInput = new FndQuery();
                updateResultInput.setType("hrPostSuccessUpdate");
                updateResultInput.setCompanyId(phoenixCompanyId);

                JSONObject hrPostData = new JSONObject();
                hrPostData.put("type", "2");
                hrPostData.put("applyNo", obj.getString("unid"));

                Map<String, String> identity = new HashMap<>();
                identity.put(hrIdentityKey1, hrIdentityValue1);
                identity.put(hrIdentityKey2, hrIdentityValue2);
                String str = PhoenixHttpPostIBMP(hrSystemUpdateLeave, identity, hrPostData);

                JSONArray updateResult = new JSONArray();
                JSONObject updateObj = new JSONObject();
                updateObj.put("headerId", obj.getString("headerId"));
                updateResult.add(updateObj);

                try {
                    JSONObject strObj = JSONObject.parseObject(str);
                    if (!strObj.getString("resultCode").equals("1")) {
                        updateResultInput.setType("hrPostErrorUpdate");
                        updateObj.put("message", strObj.getString("returnMsg"));
                    }
                } catch (Exception e) {
                    logger.error("phoenixHrPost=======error", e);
                }

                updateResultInput.setInput(updateResult.toJSONString());
                phoenixMapper.phoenixPr(updateResultInput);
            } catch (Exception e) {
                logger.error("phoenixHrPost=======error", e);
            }
        }
    }

    @Override
    public String phoenixHrCheck(JSONObject claimInput) throws Exception {
        JSONObject result = new JSONObject();

        if (claimInput.getInteger("headerTypeId") != 84768 && claimInput.getInteger("headerTypeId") != 84769) {
            result.put("exception_level", 1);
            result.put("message", "OK");
            return result.toJSONString();
        }

        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setType("hrAnnualLeavePost");
        fndQuery.setInput(claimInput.toJSONString());
        Collection<String> results = phoenixMapper.phoenixPr(fndQuery);
        for (String s : results) {
            try {
                JSONArray attendance = new JSONArray();
                JSONObject obj = JSONObject.parseObject(s);

                String travelStartDate = obj.getString("startDate");
                String travelStartTime = obj.getString("startTime");
                String travelEndDate = obj.getString("endDate");
                String travelEndTime = obj.getString("endTime");
                String leaveStartDate = obj.getString("outBeginDate");
                String leaveStartTime = obj.getString("outBeginTime");
                String leaveEndDate = obj.getString("outReturnDate");
                String leaveEndTime = obj.getString("outReturnTime");

                JSONObject hrPostData = new JSONObject();
                hrPostData.put("type", "1");
                hrPostData.put("applyNo", obj.getString("unid"));
                hrPostData.put("loginName", obj.getString("loginName"));

                JSONArray updateResult = new JSONArray();
                JSONObject updateObj = new JSONObject();
                updateObj.put("headerId", obj.getString("headerId"));
                updateResult.add(updateObj);
                if (travelStartDate == null || travelEndDate == null) {
                    continue;
                }
                PhoenixDate travelStart = new PhoenixDate();
                travelStart.setDate(LocalDate.parse(travelStartDate));
                travelStart.setTime(travelStartTime);
                PhoenixDate travelEnd = new PhoenixDate();
                travelEnd.setDate(LocalDate.parse(travelEndDate));
                travelEnd.setTime(travelEndTime);

                hrPostData.put("beginTime", travelStart.getFormattedDate());
                hrPostData.put("endTime", travelEnd.getFormattedDate());
                if (leaveStartDate == null || leaveEndDate == null) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("type", obj.getString("type"));
                    jsonObject.put("unid", obj.getString("unid") + "-1");
                    jsonObject.put("outType", "出差");
                    jsonObject.put("loginName", obj.getString("loginName"));
                    jsonObject.put("username", obj.getString("username"));
                    jsonObject.put("outBeginTime", travelStart.getFormattedDate());
                    jsonObject.put("outReturnTime", travelEnd.getFormattedDate());
                    jsonObject.put("outDays", travelEnd.minus(travelStart));
                    logger.info("phoenixHrCheck=====考勤数据jsonObject：" + jsonObject);
                    attendance.add(jsonObject);
                    hrPostData.put("attendance", attendance);
                    Map<String, String> identity = new HashMap<>();
                    identity.put(hrIdentityKey1, hrIdentityValue1);
                    identity.put(hrIdentityKey2, hrIdentityValue2);
                    String str = PhoenixHttpPostIBMP(hrSystemUpdateLeave, identity, hrPostData);
                    logger.info("hrCheck==>" + str);
                    try {
                        JSONObject strObj = JSONObject.parseObject(str);
                        if (!strObj.getString("resultCode").equals("1")) {
                            result.put("exception_level", 99);
                            result.put("message", strObj.getString("returnMsg"));
                            return result.toJSONString();
                        } else {
                            result.put("exception_level", 1);
                            result.put("message", "OK");
                            return result.toJSONString();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        result.put("exception_level", 99);
                        result.put("message", "考勤接口响应异常");
                        return result.toJSONString();
                    }
                }
                PhoenixDate leaveStart = new PhoenixDate();
                leaveStart.setDate(LocalDate.parse(leaveStartDate));
                leaveStart.setTime(leaveStartTime);
                PhoenixDate leaveEnd = new PhoenixDate();
                leaveEnd.setDate(LocalDate.parse(leaveEndDate));
                leaveEnd.setTime(leaveEndTime);
                double daysDiff1 = leaveStart.minus(travelStart);
                double daysDiff2 = leaveEnd.minus(leaveStart);
                double daysDiff3 = travelEnd.minus(leaveEnd);
                logger.info("phoenixHrCheck======daysDiff1：" + daysDiff1);
                logger.info("phoenixHrCheck======daysDiff2：" + daysDiff2);
                logger.info("phoenixHrCheck======daysDiff3：" + daysDiff3);
                if (daysDiff1 > 0) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("type", obj.getString("type"));
                    jsonObject.put("unid", obj.getString("unid") + "-1");
                    jsonObject.put("outType", "出差");
                    jsonObject.put("loginName", obj.getString("loginName"));
                    jsonObject.put("username", obj.getString("username"));
                    jsonObject.put("outBeginTime", travelStart.getFormattedDate());
                    jsonObject.put("outReturnTime", leaveStart.getFormattedDate());
                    jsonObject.put("outDays", daysDiff1);
                    logger.info("phoenixHrCheck======jsonObject：" + jsonObject);
                    attendance.add(jsonObject);

                }
                if (daysDiff2 > 0) {
                    double residualVacation = 0;
                    double annualVacation = 0;
                    Map<String, String> identity = new HashMap<>();
                    identity.put(hrIdentityKey1, hrIdentityValue1);
                    identity.put(hrIdentityKey2, hrIdentityValue2);
                    String queryVacation = PhoenixHttpPostString(hrSystemGetAnnualLeaveBatch, identity, "[" + obj.getString("loginName") + "]");
                    try {
                        JSONObject resultObj = JSONObject.parseObject(queryVacation);
                        if ("1".equals(resultObj.getString("resultCode"))) {
                            JSONObject leaveData = resultObj.getJSONArray("data").getJSONObject(0);
                            residualVacation = leaveData.getDoubleValue("residualVacation");
                            annualVacation = leaveData.getDoubleValue("annualVacation");
                        }
                    } catch (Exception e) {
                        logger.error("phoenixHrCheck=======error", e);
                    }
                    double daysDiff = daysDiff2;
                    double finalAnnual = 0;
                    double finalResidual = 0;
                    if (annualVacation > daysDiff) {
                        finalAnnual = daysDiff;
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("type", obj.getString("type"));
                        jsonObject.put("unid", obj.getString("unid") + "-2");
                        jsonObject.put("outType", "年假");
                        jsonObject.put("loginName", obj.getString("loginName"));
                        jsonObject.put("username", obj.getString("username"));
                        jsonObject.put("outBeginTime", leaveStart.getFormattedDate());
                        jsonObject.put("outReturnTime", leaveEnd.getFormattedDate());
                        jsonObject.put("outDays", finalAnnual);
                        logger.info("phoenixHrCheck======jsonObject：" + jsonObject);
                        attendance.add(jsonObject);
                    } else {
                        finalAnnual = annualVacation;
                        finalResidual = daysDiff - annualVacation;
                        PhoenixDate middle = leaveStart.plusDays(finalAnnual);

                        if (finalAnnual > 0) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("type", obj.getString("type"));
                            jsonObject.put("unid", obj.getString("unid") + "-2");
                            jsonObject.put("outType", "年假");
                            jsonObject.put("loginName", obj.getString("loginName"));
                            jsonObject.put("username", obj.getString("username"));
                            jsonObject.put("outBeginTime", leaveStart.getFormattedDate());
                            jsonObject.put("outReturnTime", middle.getFormattedDate());
                            jsonObject.put("outDays", finalAnnual);
                            logger.info("phoenixHrCheck======jsonObject：" + jsonObject);
                            attendance.add(jsonObject);
                        }
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("type", obj.getString("type"));
                        jsonObject.put("unid", obj.getString("unid") + "-3");
                        jsonObject.put("loginName", obj.getString("loginName"));
                        jsonObject.put("username", obj.getString("username"));
                        jsonObject.put("outType", "调休假");
                        jsonObject.put("outBeginTime", middle.getFormattedDate());
                        jsonObject.put("outReturnTime", leaveEnd.getFormattedDate());
                        jsonObject.put("outDays", finalResidual);
                        logger.info("phoenixHrCheck======jsonObject：" + jsonObject);
                        attendance.add(jsonObject);

                    }
                }
                if (daysDiff3 > 0) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("type", obj.getString("type"));
                    jsonObject.put("unid", obj.getString("unid") + "-4");
                    jsonObject.put("outType", "出差");
                    jsonObject.put("loginName", obj.getString("loginName"));
                    jsonObject.put("username", obj.getString("username"));
                    jsonObject.put("outBeginTime", leaveEnd.getFormattedDate());
                    jsonObject.put("outReturnTime", travelEnd.getFormattedDate());
                    jsonObject.put("outDays", daysDiff3);
                    logger.info("phoenixHrCheck======jsonObject：" + jsonObject);
                    attendance.add(jsonObject);
                }
                hrPostData.put("attendance", attendance);
                logger.info("phoenixHrCheck======hrPostData：" + hrPostData);
                Map<String, String> identity = new HashMap<>();
                identity.put(hrIdentityKey1, hrIdentityValue1);
                identity.put(hrIdentityKey2, hrIdentityValue2);
                String str = PhoenixHttpPostIBMP(hrSystemUpdateLeave, identity, hrPostData);
                logger.info("hrCheck==>" + str);
                try {
                    JSONObject strObj = JSONObject.parseObject(str);
                    if (!strObj.getString("resultCode").equals("1")) {
                        result.put("exception_level", 99);
                        result.put("message", strObj.getString("returnMsg"));
                        return result.toJSONString();
                    } else {
                        result.put("exception_level", 1);
                        result.put("message", "OK");
                        return result.toJSONString();
                    }
                } catch (Exception e) {
                    logger.error("phoenixHrCheck=======考勤接口响应异常", e);
                    result.put("exception_level", 99);
                    result.put("message", "考勤接口响应异常");
                    return result.toJSONString();
                }
            } catch (Exception e) {
                logger.error("phoenixHrCheck=======error", e);
            }
        }

        return result.toJSONString();

//        int userId = jsonObject.getInteger("submit_user");
//        int leaveDay = jsonObject.getInteger("leave_day");
//        SystemUser systemUser = sapMapper.getUserById(userId, "zh_CN");
//        String employeeNumber = systemUser.getEmployeeNumber();
//        String queryVacation = PhoenixHttpPostString(hrSystemGetAnnualLeaveBatch, null, "[" + employeeNumber + "]");
//        double residualVacation = 0;
//        double annualVacation = 0;
//        double total = 0;
//        double usedDaysInSystem = (double) phoenixMapper.getUsedLeaveDay(userId);
//        JSONObject resultObj = JSONObject.parseObject(queryVacation);
//        if ("1".equals(resultObj.getString("resultCode"))) {
//            JSONObject leaveData = resultObj.getJSONArray("data").getJSONObject(0);
//            residualVacation = leaveData.getDoubleValue("residualVacation");
//            annualVacation = leaveData.getDoubleValue("annualVacation");
//            total = residualVacation + annualVacation;
//            if (total < ((double) leaveDay + usedDaysInSystem)) {
//                result.put("exception_level", 99);
//                result.put("message", "可用假期不足，无法提交");
//            } else {
//                result.put("exception_level", 1);
//                result.put("message", "OK");
//            }
//        }

    }

    @Override
    public void iPortalDeleteGenerate() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setType("iPortalOADelete");
        fndQuery.setInput("{}");
        phoenixMapper.phoenixPr(fndQuery);
    }

    @Override
    public void iPortalDeleteReset(JSONObject jsonObject) throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(phoenixCompanyId);
        fndQuery.setType("iPortalOADeleteReset");
        fndQuery.setInput(jsonObject.toJSONString());
        phoenixMapper.phoenixPr(fndQuery);
    }

    @Override
    public String meiyaPostDocument(GlBatchTransfer glBatchTransfer) throws Exception {
        String KEYWORD = "美亚凭证推送===";
        JSONObject ret = new JSONObject();
        Integer documentId = null;
        try {
            // {"batchId":0,"companyId":14967,"input":"{\"ledger_id\":33213,\"document_id\":[1648052],\"date\":\"2021-08-05\"}","journalNum":0,"language":"zh_CN","userId":163220}
            JSONObject input = JSON.parseObject(glBatchTransfer.getInput());
            JSONArray documentIds = input.getJSONArray("document_id");
            documentId = (Integer)documentIds.get(0);
            logger.info("{}单据{}开始处理", KEYWORD, documentId);
            glBatchTransfer.setType("api");
            List<String> strings = meiyaMapper.glBatchCreateMeiya(glBatchTransfer);
            logger.info("{}单据{}获取待处理数据api结果={}", KEYWORD, documentId, JSON.toJSONString(glBatchTransfer));
            for (String s : strings) {
                XMLSerializer xmlSerializer = new XMLSerializer();
                xmlSerializer.setObjectName("bill");
                xmlSerializer.setElementName("entry");
                String data = xmlSerializer.write(JSONSerializer.toJSON(s));
                data = data.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                        .replaceAll("<lines class=\"array\">", "")
                        .replaceAll("</lines>", "");
                String postData = dbCreateVoucherRequest(data);
                // 先登录
                Map<String, String> identity = new HashMap<>();
                identity.put("SOAPAction", "");
                //identity.put("User-Agent", "Axis/1.4");
                //identity.put("Content-Type", "text/xml; charset=utf-8");
                //identity.put("Accept", "application/soap+xml, application/dime, multipart/related, text/*");
                //identity.put("Cache-Control", "no-cache");
                //identity.put("Pragma", "no-cache");
                HttpPostString(loginurl, identity, logOut);
                HttpPostString(loginurl, identity, logIn);
                // 调用外部接口
                logger.info("{}单据{}调用外部接口{}请求={}", KEYWORD, documentId, hosturl, postData);
                String result = HttpPostString(hosturl, identity, postData);
                logger.info("{}单据{}调用外部接口{}响应xml={}", KEYWORD, documentId, hosturl, result);
                XMLSerializer xmlSerializer2json = new XMLSerializer();
                net.sf.json.JSON resultJson = xmlSerializer2json.read(result);
                JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
                logger.info("{}单据{}调用外部接口{}响应json={}", KEYWORD, documentId, hosturl, resultJson);
                String resultString = jsonObject.getJSONObject("soapenv:Body").getJSONObject("ns1:createVoucherResponse")
                        .getJSONObject("createVoucherReturn").getString("#text");
                String resultCode = resultString.split("-")[0];
                logger.info("{}单据{}调用外部接口{}响应result={}", KEYWORD, documentId, hosturl, resultCode);
                if (!"00".equals(resultCode)) {
                    ret.put("exception_level", 99);
                    ret.put("message", resultString);
                    String errorMessage = resultString.length() > 255 ?
                            resultString.substring(0, 255) : resultString;// 保证不超长
                    meiyaMapper.updateGlStatusByDocumentId(documentId, "error", errorMessage);
                } else {
                    ret.put("exception_level", 1);
                    ret.put("message", "OK");
                    glBatchTransfer.setType("apichange");
                    List<String> sa = meiyaMapper.glBatchCreateMeiya(glBatchTransfer);
                    logger.info("{}单据{}获取待处理数据apichange结果={}", KEYWORD, documentId, JSON.toJSONString(glBatchTransfer));
                }
            }
            logger.info("{}单据{}结束处理", KEYWORD, documentId);
        } catch (Exception e) {
            logger.error("{}单据{}出现未知异常: {}", KEYWORD, documentId, e.getMessage(), e);
            String glMessage = "系统异常：" + e.getMessage();
            if(documentId != null) {
                if(glMessage.length() > 255)
                    glMessage = glMessage.substring(0, 255);// 保证不超长
                meiyaMapper.updateGlStatusByDocumentId(documentId, "error", glMessage);
            }
            ret.put("exception_level", 99);
            ret.put("message", glMessage);
        }

        return ret.toJSONString();
    }

    @Override
    public void meiyaVoucherReceive() throws Exception {
        LocalDateTime localDateTime = LocalDateTime.now();
        DateTimeFormatter fomatter1 = DateTimeFormatter
                .ofPattern("yyyy-MM-dd");
        String postData = dbQueryRequest("select a.FNUMBER, a.FDESCRIPTION from t_gl_voucher a where to_char(a.FBIZDATE,'yyyy-MM-dd')>='" + localDateTime.minusDays(2).format(fomatter1) + "' order by a.FBIZDATE");
        Map<String, String> identity = new HashMap<>();
        identity.put("SOAPAction", "");
        HttpPostString(loginurl, identity, logOut);
        HttpPostString(loginurl, identity, logIn);

        String result = HttpPostString(hosturl, identity, postData);
        String xml = dbQueryResponse(StringEscapeUtils.unescapeHtml(result)).replaceAll("\r", "").replaceAll("\n","");
        XMLSerializer xmlSerializer = new XMLSerializer();
        net.sf.json.JSON json = xmlSerializer.read(xml);
        System.out.println(json.toString());
        try {
            JSONArray jsonArray = JSONArray.parseArray(json.toString());
            GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
            glBatchTransfer.setType("apiupdate");
            for (Object o : jsonArray) {
                glBatchTransfer.setInput(JSON.toJSONString(o));
                meiyaMapper.glBatchCreateMeiya(glBatchTransfer);
            }
        } catch (JSONException e) {
            JSONObject jsonObject = JSONObject.parseObject(json.toString());
            GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
            glBatchTransfer.setType("apiupdate");
            glBatchTransfer.setInput(jsonObject.getString("bill"));
            meiyaMapper.glBatchCreateMeiya(glBatchTransfer);
        }
    }

    @Override
    public String heraeusPostDocument(GlBatchTransfer glBatchTransfer) throws Exception {
        String KEYWORD = "贺利氏凭证推送===";
        JSONObject ret = new JSONObject();
        ret.put("exception_level", 0);
        ret.put("message", "OK");

        if (22180 == glBatchTransfer.getCompanyId()) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
        } else if(17239 == glBatchTransfer.getCompanyId() || 3954 == glBatchTransfer.getCompanyId()) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        } else {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        }

        glBatchTransfer.setType("api");
        if (glBatchTransfer.getCompanyId() == 17239 || glBatchTransfer.getCompanyId() == 22180) {
            //#55964 检查税会计科目
            if (glBatchTransfer.getInput() != null) {
                String json = glBatchTransfer.getInput();
                JSONObject jsonObject = (JSONObject) JSONObject.parse(json);
                Integer documentId = (Integer) jsonObject.getJSONArray("document_id").get(0);
                ArrayList<ExpClaimLine> expClaimLines = heraeusMapper.queryClaimLineTax(documentId);
                logger.info("检查税会计科目 expClaimLines is{}", JSON.toJSONString(expClaimLines));
                if(expClaimLines!=null && expClaimLines.size()>0){
                    for (ExpClaimLine expClaimLine:expClaimLines) {
                        if(expClaimLine!=null){
                            if((expClaimLine.getFinTaxAmount()>0 && (Double.isNaN(expClaimLine.getTaxAccountId())||expClaimLine.getTaxAccountId()==0))||
                                    (expClaimLine.getTaxAccountId()>0 && (Double.isNaN(expClaimLine.getFinTaxAmount())||expClaimLine.getFinTaxAmount()==0))){
                                logger.error("{} 税额为空 documentId:{}", KEYWORD, documentId);
                                ret.put("exception_level", 99);
                                ret.put("message", "请检查税会计科目");
                                return ret.toJSONString();
                            }
                        }
                    }
                }
            }
        }

        List<String> strings;
        if (glBatchTransfer.getCompanyId() == 17239) {// 贺立氏
            strings = heraeusMapper.glBatchCreateHeraeus1(glBatchTransfer);
        } else if (glBatchTransfer.getCompanyId() == 3954) {// 贺立氏电测
            List<String> list = heraeusMapper.glBatchCreateHeraeus(glBatchTransfer);
            strings = Lists.newArrayList();
            for (String s : list) {// 统一名称
                JSONObject source = JSONObject.parseObject(s);
                JSONObject target = new JSONObject();
                target.put("ExpenseReportNumber", source.get("document_num"));
                target.put("DocumentType", source.get("document_type"));
                target.put("CompanyCode", source.get("company_code"));
                target.put("FiscalYear", source.get("fiscal_year"));
                target.put("DocumentDate", source.get("document_date"));
                target.put("PostingDate", source.get("posting_date"));
                target.put("Period", source.get("period"));
                target.put("DocumentCurrency", source.get("document_currency"));
                target.put("DocumentHeadText", source.get("comments"));

                JSONArray lines = source.getJSONArray("lines");
                JSONArray newLines = new JSONArray();
                if (lines != null) {
                    for (int i = 0; i < lines.size(); i++) {
                        JSONObject line = lines.getJSONObject(i);
                        JSONObject newLine = new JSONObject();
                        newLine.put("PK", line.get("pk"));
                        newLine.put("Account", line.get("account_code"));
                        newLine.put("Tx", line.get("tx"));
                        newLine.put("CostCenterCode", line.get("costCenterCode"));
                        newLine.put("Order", line.get("order"));
                        newLine.put("Amount", line.get("amount"));
                        newLine.put("Assignment", line.get("assignment"));
                        newLine.put("Text", line.get("comments"));

                        newLines.add(newLine);
                    }
                }
                target.put("lines", newLines);
                strings.add(target.toJSONString());
            }
        } else if (glBatchTransfer.getCompanyId() == 22180) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
            strings = heraeusMapper.glBatchCreateHeraeus2(glBatchTransfer);
        } else {
            logger.error("{}不支持的companyId={}", KEYWORD, glBatchTransfer.getCompanyId());
            ret.put("exception_level", 99);
            ret.put("message", "不支持的companyId");
            return ret.toJSONString();
        }
        if (CollectionUtils.isEmpty(strings)) {
            logger.error("{}待推送数据信息不存在", KEYWORD);
            ret.put("exception_level", 99);
            ret.put("message", "待推送数据信息不存在");
            return ret.toJSONString();
        }
        logger.info("{}获取到待推送数据list={}", KEYWORD, JSON.toJSONString(strings));
        String json = glBatchTransfer.getInput();
        JSONObject response = (JSONObject) JSONObject.parse(json);
        JSONArray documentIdArray = response.getJSONArray("document_id");
        for (Object o : documentIdArray) {
            Integer documentId = (Integer) o;
            heraeusMapper.updateGlStatusAndGlMessage("generating", "暂时状态", documentId);
        }
        executorService.execute(() -> assembleData(glBatchTransfer, strings, KEYWORD));
        logger.info("{}return:{}", KEYWORD, ret.toJSONString());
        return ret.toJSONString();
    }

    void assembleData(GlBatchTransfer glBatchTransfer, List<String> strings, String KEYWORD) {
        logger.info("{}进入了assembleData:{}", KEYWORD, JSON.toJSONString(glBatchTransfer));
        for (String s : strings) {
            if (22180 == glBatchTransfer.getCompanyId()) {
                CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
            } else if(17239 == glBatchTransfer.getCompanyId() || 3954 == glBatchTransfer.getCompanyId()) {
                CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
            } else {
                CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
            }
            JSONObject ori = JSONObject.parseObject(s);
            try {
                JSONArray oriArray = ori.getJSONArray("lines");
                JSONArray resArray = new JSONArray();
                int itemNumber = 1;
                for (Object o : oriArray) {
                    JSONObject oria = (JSONObject) o;
                    oria.put("ItemNumber", itemNumber);
                    itemNumber++;
                    resArray.add(oria);
                }
                JSONObject detail = new JSONObject();
                detail.put("Detail", resArray);
                ori.put("lines", detail);
                if (glBatchTransfer.getInput() != null) {
                    String json = glBatchTransfer.getInput();
                    JSONObject jsonObject = (JSONObject) JSONObject.parse(json);
                    Integer documentId = (Integer) jsonObject.getJSONArray("document_id").get(0);
                    JSONArray attachmentList = new JSONArray();

                    // header-attachment
                    List<Map<String, String>> headerFileNames = heraeusMapper.getHeaderAttachments(documentId);
                    if (!CollectionUtils.isEmpty(headerFileNames)) {
                        List<String> headerAttachmentUrl = headerFileNames.stream()
                                .map(headerAttachment -> headerAttachment.get("attachment_url"))
                                .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(headerAttachmentUrl)) {
                            JSONObject attachment = new JSONObject();
                            attachment.put("Filename", headerFileNames.get(0).get("document_num") + "报销附件");
                            attachment.put("Filecontent", headerAttachmentUrl.stream().collect(Collectors.joining(";")));
                            attachmentList.add(attachment);
                        }
                    }

                    // line-attachment
                    int i = 1;
                    List<Map<String, Object>> lineAttachAndReceipt = new ArrayList<>();
                    List<Map<String, Object>> lineFileNames = heraeusMapper.getLineAttachments(documentId);
                    // line-receipt
                    List<Map<String, Object>> allLineReceiptUrl = heraeusMapper.getLineReceipts(documentId);
                    List<Map<String, Object>> newLineReceiptUrl = new ArrayList<>();
                    for (Map<String, Object> map : allLineReceiptUrl) {
                        if (Objects.isNull(map.get("attachments_url")) || map.get("attachments_url") == "") continue;
                        List<String> attachmentsUrl = Arrays.asList(((String) map.get("attachments_url"))
                                .replace("[", "").replace("]", "")
                                .replace("\"", "")
                                .split(","));
                        if (CollectionUtils.isEmpty(attachmentsUrl)) {
                            continue;
                        }
                        for (String attachmentUrl : attachmentsUrl) {
                            Map<String, Object> temp = new HashMap<>();
                            temp.put("header_id", ((Integer) map.get("header_id")).toString());
                            temp.put("document_num", map.get("document_num"));
                            temp.put("type", map.get("type"));
                            temp.put("line_id", ((Integer) map.get("line_id")).toString());
                            temp.put("attachment_url", attachmentUrl);
                            temp.put("file_name", attachmentUrl);
                            newLineReceiptUrl.add(temp);
                        }
                    }
                    Optional.ofNullable(lineFileNames).ifPresent(lineAttachAndReceipt::addAll);
                    Optional.ofNullable(newLineReceiptUrl).ifPresent(lineAttachAndReceipt::addAll);

                    Map<String, List<Map<String, Object>>> linesInfo = lineAttachAndReceipt.stream()
                            .sorted(Comparator.comparing(item -> Integer.parseInt(item.get("line_id").toString())))
                            .collect(Collectors.groupingBy(mapLine -> String.valueOf(mapLine.get("line_id"))))
                            .entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(
                                    Collectors.toMap(
                                            Map.Entry::getKey,
                                            Map.Entry::getValue,
                                            (oldVal, newVal) -> oldVal,
                                            LinkedHashMap::new
                                    )
                            );

                    for (Map.Entry<String, List<Map<String, Object>>> lineInfo : linesInfo.entrySet()) {
                        List<Map<String, Object>> value = lineInfo.getValue();
                        List<String> lineAttachmentUrl = value.stream()
                                .map(linesInfoT -> (String) linesInfoT.get("attachment_url"))
                                .collect(Collectors.toList());
                        List<String> lineFilenameUrl = value.stream()
                                .map(linesInfoT -> (String) linesInfoT.get("file_name"))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isEmpty(lineAttachmentUrl)) {
                            continue;
                        }
                        JSONObject attachment = new JSONObject();
                        attachment.put("Filename", value.get(0).get("document_num") + "-" + i + "-" + value.get(0).get("type"));
                        attachment.put("Filecontent", lineAttachmentUrl.stream().collect(Collectors.joining(";")));
                        attachment.put("source_file_name", lineFilenameUrl.stream().collect(Collectors.joining(";")));
                        attachmentList.add(attachment);
                        i++;
                    }

                    JSONObject attachment = new JSONObject();
                    attachment.put("Attachment", attachmentList);
                    ori.put("AttachmentGroup", attachment);
                }

                if (glBatchTransfer.getCompanyId() == 22180) {
                    JSONArray detail1 = ori.getJSONObject("lines").getJSONArray("Detail");
                    for (int i = 0; i < detail1.size(); i++) {
                        JSONObject detailTemp = detail1.getJSONObject(i);
                        Object amount = detailTemp.get("Amount");
                        // 台湾金额传整数
                        if (amount instanceof BigDecimal) {
                            BigDecimal amountDecimal = detailTemp.getBigDecimal("Amount");
                            detailTemp.put("Amount", amountDecimal.setScale(0, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                }

                ResourceBundle rb = ResourceBundle.getBundle("urisetting");
                String helishiCustomerUrl = rb.getString("helishiCustomerUrl");
                try {
                    logger.info("{}调用heraeus-customer凭证服务请求：{}", KEYWORD, ori.toJSONString());
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("Content-Type", ContentType.APPLICATION_JSON.toString());
                    headers.add("access_token", hlsToken(glBatchTransfer.getCompanyId()));
                    headers.add("company_id", glBatchTransfer.getCompanyId() + "");
                    HttpEntity<String> httpEntity = new HttpEntity<>(ori.toJSONString(), headers);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(helishiCustomerUrl, httpEntity, String.class);
                    JSONObject responseJson = JSON.parseObject(responseEntity.getBody());
                    logger.info("{}调用heraeus-customer凭证服务响应：{}", KEYWORD, responseJson);
                    String resultCode = responseJson.getString("RESPONSE_STATUS");
                    if (!"Y".equals(resultCode)) {
                        String json = glBatchTransfer.getInput();
                        JSONObject response = (JSONObject) JSONObject.parse(json);
                        String responseMessage = responseJson.getString("RESPONSE_MESSAGE");
                        JSONArray documentIdArray = response.getJSONArray("document_id");
                        for (Object o : documentIdArray) {
                            Integer documentId = (Integer) o;
                            heraeusMapper.updateGlStatusAndGlMessage("pending", StrUtil.sub(responseMessage, 0, 255), documentId);
                        }
                    } else {
                        glBatchTransfer.setType("apichange");
                        List<String> sa = null;
                        if (glBatchTransfer.getCompanyId() == 17239) {// 贺立氏
                            sa = heraeusMapper.glBatchCreateHeraeus1(glBatchTransfer);
                        } else if (glBatchTransfer.getCompanyId() == 3954) {// 贺立氏电测
                            sa = heraeusMapper.glBatchCreateHeraeus(glBatchTransfer);
                        } else if (glBatchTransfer.getCompanyId() == 22180) {
                            sa = heraeusMapper.glBatchCreateHeraeus2(glBatchTransfer);
                        }
                    }
                } catch (Exception e) {
                    logger.error("{}解析heraeus返回结果出现未知异常: {}", KEYWORD, e);
                    if(e.getMessage().contains("SocketTimeoutException")){
                        continue;
                    }
                    String json = glBatchTransfer.getInput();
                    JSONObject response = (JSONObject) JSONObject.parse(json);
                    JSONArray documentIdArray = response.getJSONArray("document_id");
                    for (Object o : documentIdArray) {
                        Integer documentId = (Integer) o;
                        heraeusMapper.updateGlStatusAndGlMessage("pending", StrUtil.sub("运行出错:" + e.getMessage(),0 ,255), documentId);
                    }
                }
            } catch (Exception e) {
                logger.error("{}解析行数据出现未知异常: {}", KEYWORD, e);
                String json = glBatchTransfer.getInput();
                JSONObject response = (JSONObject) JSONObject.parse(json);
                JSONArray documentIdArray = response.getJSONArray("document_id");
                for (Object o : documentIdArray) {
                    Integer documentId = (Integer) o;
                    heraeusMapper.updateGlStatusAndGlMessage("pending", StrUtil.sub("运行出错:" + e.getMessage(), 0, 255), documentId);
                }
            }
        }
    }

    private String hlsToken(Integer companyId) {
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String token;
        if (companyId == 17239) {
            token = rb.getString("helishi.shanghai.tokenUrl");
        } else if (companyId == 22180) {
            token = rb.getString("helishi.tw.tokenUrl");
        } else {
            token = rb.getString("helishi.diance.tokenUrl");
        }
        String resStr = restTemplate.getForObject(token, String.class);
        JSONObject resJson = JSONObject.parseObject(resStr);
        return resJson.getJSONObject("data").getString("access_token");
    }

    @Transactional
    public Integer updatebyDocumentIdAndCompanyId(Integer documentId) {
        Integer integer = 0;
        try {
            integer = expClaimHeaderMapper.updatebyDocumentIdAndCompanyId(documentId);
        } catch (Exception e) {
            logger.error("updatebyDocumentIdAndCompanyId=======数据库执行异常", e);
        }
        return integer;
    }

    @Override
    public String cjlrPostDocument(GlBatchTransfer glBatchTransfer) throws Exception {

        String input = glBatchTransfer.getInput();
        JSONObject param = JSONObject.parseObject(input);
        String generalLedgerDate = param.getString("date");
        JSONArray documentIdArray = param.getJSONArray("document_id");
        List<Integer> documentIdList = new ArrayList<>();
        for (int i = 0; i < documentIdArray.size(); i++) {
            documentIdList.add(documentIdArray.getInteger(i));
        }
        JSONArray resultArray = new JSONArray();
        for (int i = 0; i < documentIdList.size(); i++) {
            JSONObject resultJson = new JSONObject();
            List<String> strings = sapMapper.getProofInfoByCJLR(documentIdList.get(i));
            List<JSONObject> paramList = new ArrayList<>();
            for (String string : strings) {
                JSONObject jsonObject = JSONObject.parseObject(string);
                String previous_type_code = jsonObject.getString("previous_type_code");
                if ("RT001".equals(previous_type_code) || "RT006".equals(previous_type_code)) {
                    jsonObject.put("true_previous_document_type", "Travel plan");
                } else if ("P002".equals(previous_type_code)) {
                    jsonObject.put("true_previous_document_type", "NPP Direct PR 2");
                } else if ("P001".equals(previous_type_code)) {
                    jsonObject.put("true_previous_document_type", "Training");
                } else if ("P004".equals(previous_type_code)) {
                    jsonObject.put("true_previous_document_type", "GE");
                } else {
                    jsonObject.put("true_previous_document_type", "NA");
                }
                String type_code = jsonObject.getString("type_code");
                if ("T001".equals(type_code)) {
                    jsonObject.put("true_previous_document_number", jsonObject.getString("previous_document_number"));
                } else if ("T007".equals(type_code) && "P002".equals(previous_type_code)) {
                    if (jsonObject.getString("previous_header_column10").length() != 0) {
                        jsonObject.put("true_previous_document_number", jsonObject.getString("previous_header_column10"));
                    } else {
                        jsonObject.put("true_previous_document_number", jsonObject.getString("previous_header_column28"));
                    }
                } else if ("T007".equals(type_code) && !"P002".equals(previous_type_code)) {
                    jsonObject.put("true_previous_document_number", jsonObject.getString("previous_header_column28"));
                } else if ("T120".equals(type_code) || "T125".equals(type_code) || "T126".equals(type_code) || "T127".equals(type_code) || "T128".equals(type_code)) {
                    if (jsonObject.getString("previous_header_column10").length() != 0) {
                        jsonObject.put("true_previous_document_number", jsonObject.getString("previous_header_column10"));
                    } else {
                        jsonObject.put("true_previous_document_number", jsonObject.getString("previous_header_column28"));
                    }
                }
                logger.info("generateDataForCJLR==>" + jsonObject);
                paramList.add(jsonObject);
                logger.info("generateParamListDataForCJLR==>" + paramList);
            }

            Integer integer = updatebyDocumentIdAndCompanyId(documentIdList.get(i));
            if (0 == integer) {
                resultJson.put("exception_level", 99);
                resultJson.put("message", "当前数据已生成凭证或正在生成中");
                resultArray.add(resultJson);
                break;
            }
            String result = syncProofInfo(generalLedgerDate, paramList);
            JSONObject response = JSONObject.parseObject(result);
            logger.info("resultCjlr==>" + result);
            try {
                JSONObject item = response.getJSONObject("soapenv:Envelope").getJSONObject("soapenv:Body").getJSONObject("NS1:ZFM_FI_TE_POST_RFCResponse").getJSONObject("ET_RETURN").getJSONObject("item");
                String type = item.getString("TYPE");
                if (!"S".equalsIgnoreCase(type)) {
                    Object itemObj = item.getJSONObject("LOGS").get("item");
                    JSONArray itemArray = new JSONArray();
                    if (itemObj instanceof JSONObject) {
                        itemArray.add(itemObj);
                    } else if (itemObj instanceof JSONArray) {
                        itemArray = (JSONArray) itemObj;
                    }
                    StringBuffer message = new StringBuffer();
                    for (int j = 0; j < itemArray.size(); j++) {
                        JSONObject itemJson = itemArray.getJSONObject(j);
                        message.append(itemJson.getString("MESSAGE") + ";");
                    }
                    resultJson.put("exception_level", 99);
                    resultJson.put("message", "运行出错:" + item.getString("ZTEDOC") + ":" + message.toString());
                } else {
                    resultJson.put("exception_level", 0);
                    resultJson.put("message", "OK");
                    JSONObject glInput = new JSONObject();
                    glInput.put("document_id", documentIdList.get(i));
                    glInput.put("FDESCRIPTION", item.getString("ZTEDOC"));
                    glInput.put("FNUMBER", item.getString("BELNR"));
                    glBatchTransfer.setInput(glInput.toJSONString());
                    glBatchTransfer.setType("apiupdate");
                    sapMapper.glBatchCreateCJLR(glBatchTransfer);
                }
                resultArray.add(resultJson);
            } catch (Exception e) {
                logger.error("cjlrPostDocument=======响应数据解析异常", e);
                resultJson.put("exception_level", 99);
                resultJson.put("message", "响应数据解析异常" + e.getMessage() + ":" + result);
                resultArray.add(resultJson);
            }
        }
        return resultArray.toString();
    }

    private String syncProofInfo(String generalLedgerDate, List<JSONObject> lines) {
        String url = CJRSAPURL;
        JSONObject jsonObject = lines.get(0);
        String date = jsonObject.getString("document_date");
        if (date != null && !"".equals(date)) {
            date = date.substring(0, 10);
        } else {
            date = "";
        }

        StringBuffer param = new StringBuffer();
        String employeeNumber = jsonObject.getString("employee_number");
        if (employeeNumber != null) {
            while (employeeNumber.length() < 10) {
                employeeNumber = "0" + employeeNumber;
            }
        }
        param.append("" +
                "<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:urn=\"urn:sap-com:document:sap:rfc:functions\">\n" +
                "   <soap:Header/>" +
                "   <soap:Body>" +
                "      <urn:ZFM_FI_TE_POST_RFC>" +
                "         <IT_TEDATA>" +
                "            <item>" +
                "               <ZTEDOC>" + (jsonObject.getString("document_num") == null ? "" : jsonObject.getString("document_num")) + "</ZTEDOC>" +
                "               <BUKRS>" + (jsonObject.getString("company_code") == null ? "" : jsonObject.getString("company_code")) + "</BUKRS>" +
                "               <DEPAR>" + (jsonObject.getString("department_code") == null ? "" : jsonObject.getString("department_code")) + "</DEPAR>" +
                "               <ZTECC>" + (jsonObject.getString("cost_center_code") == null ? "" : jsonObject.getString("cost_center_code")) + "</ZTECC>" +
                "               <PERNR_S>" + employeeNumber + "</PERNR_S>" +
                "               <NAME_S>" + (jsonObject.getString("employee_full_name") == null ? "" : jsonObject.getString("employee_full_name")) + "</NAME_S>" +
                "               <BUDAT>" + generalLedgerDate + "</BUDAT>" +
                "               <BLDAT>" + date + "</BLDAT>" +
                "               <WAERS>" + (jsonObject.getString("document_currency") == null ? "" : jsonObject.getString("document_currency")) + "</WAERS>" +
                "               <ZTETYPE>" + (jsonObject.getString("document_type") == null ? "" : jsonObject.getString("document_type")) + "</ZTETYPE>" +
                "               <LINES>" +
                "");
        logger.info("generateParamDataOneForCJLR==>" + param);
        for (JSONObject line : lines) {

            double crAmount = line.getDoubleValue("cr_amount");
            double drAmount = line.getDoubleValue("dr_amount");
            double taxAmount = line.getDoubleValue("tax_amount");
            String cr = line.getString("cr_account") == null ? "" : line.getString("cr_account");
            String dr = line.getString("dr_account") == null ? "" : line.getString("dr_account");
            String tax = line.getString("tax_account") == null ? "" : line.getString("tax_account");
            if (dr != null && !"".equals(dr)) {
                String item = createItem("DR", dr, drAmount, 0.00, drAmount, "40", line);
                param.append(item);
            }
            if (taxAmount != 0.00) {
                if (tax != null && !"".equals(tax)) {
                    String item = createItem("TAX", tax, taxAmount, 0.00, taxAmount, "40", line);
                    param.append(item);
                }
            }
            if (cr != null && !"".equals(cr)) {
                String item = createItem("CR", cr, 0.00, crAmount, crAmount, "31", line);
                param.append(item);
            }
        }
        logger.info("generateParamDataTwoForCJLR==>" + param);
        param.append("" +
                "               </LINES>" +
                "               <ZPDTP>" + (jsonObject.getString("true_previous_document_type") == null ? "" : jsonObject.getString("true_previous_document_type")) + "</ZPDTP>" +
                "               <ZPDCN>" + (jsonObject.getString("true_previous_document_number") == null ? "" : jsonObject.getString("true_previous_document_number")) + "</ZPDCN>" +
                "               <HBACK1></HBACK1>" +
                "               <HBACK2></HBACK2>" +
                "               <HBACK3></HBACK3>" +
                "               <HBACK4></HBACK4>" +
                "            </item>" +
                "         </IT_TEDATA>" +
                "      </urn:ZFM_FI_TE_POST_RFC>" +
                "   </soap:Body>" +
                "</soap:Envelope>" +
                "");
        logger.info("sap's param==>" + param);
        String xmlResult = HttpUtil.sendWebServicePost(url, param.toString());
        logger.info("xmlResult==>" + xmlResult);
        if (null == xmlResult || "".equals(xmlResult)) {
            return "";
        }
        return XML.toJSONObject(xmlResult).toString();
    }

    private String createItem(String type, String typeCode, double drAmount, double crAmount, double amount, String postKey, JSONObject jsonObject) {
        String item = "<item>" +
                "  <ZTEDCT>" + type + "</ZTEDCT>" +
                "  <HKONT>" + typeCode + "</HKONT>" +
                "  <KOSTL>" + (jsonObject.getString("cost_center_code") == null ? "" : jsonObject.getString("cost_center_code")) + "</KOSTL>" +
                "  <LIFNR>" + (jsonObject.getString("charge_user_number") == null ? "" : jsonObject.getString("charge_user_number")) + "</LIFNR>" +
                "  <ZLFNAM>" + (jsonObject.getString("charge_user_name") == null ? "" : jsonObject.getString("charge_user_name")) + "</ZLFNAM>" +
                "  <ZJFJE>" + drAmount + "</ZJFJE>" +
                "  <ZDFJE>" + crAmount + "</ZDFJE>" +
                "  <WRBTR>" + amount + "</WRBTR>" +
                "  <MWSKZ></MWSKZ>" +
                "  <SDATE>" + (jsonObject.getString("start_datetime") == null ? "" : (jsonObject.getString("start_datetime")).substring(0, 10)) + "</SDATE>" +
                "  <EDATE>" + (jsonObject.getString("end_datetime") == null ? "" : (jsonObject.getString("end_datetime")).substring(0, 10)) + "</EDATE>" +
                "  <POSID>" + (jsonObject.getString("project_name") == null ? "" : jsonObject.getString("project_name")) + "</POSID>" +
                "  <AUFNR>" + (jsonObject.getString("column29") == null ? "" : jsonObject.getString("column29")) + "</AUFNR>" +
                "  <ZUONR>" + (jsonObject.getString("column30") == null ? "" : jsonObject.getString("column30")) + "</ZUONR>" +
                "  <ZBMJC>" + (jsonObject.getString("column3") == null ? "" : jsonObject.getString("column3")) + "</ZBMJC>" +
                "  <BSCHL>" + postKey + "</BSCHL>" +
                "  <RSTGR></RSTGR>" +
                "  <NETBW></NETBW>" +
                "  <LBACK1>" + (jsonObject.getString("fee_type") == null ? "" : jsonObject.getString("fee_type")) + "</LBACK1>" +
                "  <LBACK2></LBACK2>" +
                "  <LBACK3></LBACK3>" +
                "  <LBACK4></LBACK4>" +
                "  <LBACK5></LBACK5>" +
                "</item>";
        item = item.replace("&", "&amp;");
        return item;
    }

    @Override
    public void generateDocument(Integer companyId, JSONObject generateObject) {
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(companyId);


        //获取文件路径
        String documentNum = "";
        if (companyId == 17239) {
            try {
                documentNum = generateObject.getString("ExpenseReportNumber");
                ResourceBundle rb = ResourceBundle.getBundle("urisetting");
                String tomcatPath = rb.getString("helishi.tomcatPath");
                File file = new File(tomcatPath + File.separator + "gldate.json");
                if (file.exists()) {
                    JSONObject gldates = JSON.parseObject(FileUtils.readFileToString(file, "UTF-8"));
                    String gldate = gldates.getString(documentNum);
                    generateObject.put("glDate", gldate);
                    generateObject.put("date", gldate);
                    logger.info("单据:{},,日期:{}", documentNum,gldate);
                }

//                if (FileUtil.exist("gldate.json")) {
//                    String s = FileUtil.readString("gldate.json", "UTF-8");
//                    JSONObject gldates = JSON.parseObject(s);
//                    String gldate = gldates.getString(documentNum);
//                    generateObject.put("glDate", gldate);
//                    generateObject.put("date", gldate);
//                    logger.info("单据:{},,日期:{}", documentNum,gldate);
//                }


            } catch (Exception e) {
                logger.error("单据:{},出现异常:{}", documentNum, e.getMessage(), e);
            }
        }




        glBatchTransfer.setInput(generateObject.toJSONString());
        glBatchTransfer.setLanguage("zh_CN");
        glBatchTransfer.setType("apiupdate");



        List<String> strings = null;
        if(glBatchTransfer.getCompanyId() == 17239) {// 贺立氏
            strings = heraeusMapper.glBatchCreateHeraeus1(glBatchTransfer);
        } else if(glBatchTransfer.getCompanyId() == 3954) {// 贺立氏电测
            strings = heraeusMapper.glBatchCreateHeraeus(glBatchTransfer);
        }  else if (glBatchTransfer.getCompanyId() == 22180) {// 贺立氏台湾
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
            strings = heraeusMapper.glBatchCreateHeraeus2(glBatchTransfer);
        }
    }

}

