package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.entity.phoenix.Response;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.mapper.SapMapper;
import com.cloudpense.expman.service.BaanService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.cloudpense.expman.webService.phoenix.PhoenixConstants.*;
import static com.cloudpense.expman.webService.phoenix.PhoenixHttpHelper.PhoenixHttpPostIBMP;
import static com.cloudpense.expman.webService.phoenix.PhoenixHttpHelper.PhoenixHttpPostString;

/**
 * <AUTHOR>
 */
@Service
public class BaanServiceImpl implements BaanService {
    private PhoenixMapper phoenixMapper;
    private SapMapper sapMapper;

    @Autowired
    public BaanServiceImpl(final PhoenixMapper phoenixMapper,
                           SapMapper sapMapper){
        this.phoenixMapper = phoenixMapper;
        this.sapMapper = sapMapper;
    }

    private static Logger logger = LoggerFactory.getLogger(BaanServiceImpl.class);
    @Override
    public void voucherPassBack() {
        JSONObject data = new JSONObject();
        Map<String, String> identity = new HashMap<>();
        identity.put("user-name","phoenix");
        identity.put("pass-word","phoenix1028");
        data.put("path", voucherPath);
        data.put("title", "VoucherNumber,VoucherDate,ExpenseReportNumber,PaymentNumber,e,f,g");
        data.put("partLine",",");

        try {
            String result = PhoenixHttpPostIBMP(FTPGetUrl, identity, data);
            logger.info("菲尼克斯凭证回传 == result：" + result);
            JSONArray jsonArray = JSONArray.parseArray(result);
            if(jsonArray!=null){
            for (Object jsonObject:jsonArray){
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(phoenixCompanyId);
                fndQuery.setType("voucherPassBack");
                logger.info(jsonObject.toString());
                fndQuery.setInput(jsonObject.toString());
                phoenixMapper.phoenixPr(fndQuery);
            }
            }
        } catch (Exception e) {
            logger.error("菲尼克斯凭证回传 == error：", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "phoenixVoucherSync", JSONObject.toJSONString(jsonObject));
        }

    }

    @Override
    public void baanRulesUpdate() {
        JSONObject data = new JSONObject();
        Map<String, String> identity = new HashMap<>();
        identity.put("user-name","phoenix");
        identity.put("pass-word","phoenix1028");
        data.put("path", baanRulesPath);
        data.put("title", "category,account,A1,A2,D1,D2,D3,D4,D5");
        data.put("partLine",";");
        try {
            String result = PhoenixHttpPostIBMP(FTPGetUrl, identity, data);
            JSONArray jsonArray = JSONArray.parseArray(result);
            if(jsonArray!=null){
                logger.info("jsonArray.length(): " + jsonArray.size());
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(phoenixCompanyId);
                fndQuery.setType("BaanRulesUpdate");
//                logger.info("jsonArray.toString(): "+jsonArray.toString());
                fndQuery.setInput(jsonArray.toString());
                phoenixMapper.phoenixPr(fndQuery);
            }
        } catch (Exception e) {
            logger.error("baanRulesUpdate ==== error：", e);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("error", e.getMessage());
            sapMapper.normalInsertLog(phoenixCompanyId, "BaanRulesUpdateSync", JSONObject.toJSONString(jsonObject));
        }


    }


    @Override
    public void fndUserRepair(int a,int b) {
        JSONObject data = new JSONObject();
        for(int i=a;i<b;i++){
            data.put("user_id", i);
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(phoenixCompanyId);
            fndQuery.setType("fndUserRepair");
            logger.info("fndUserRepair == data：" + data.toString());
            fndQuery.setInput(data.toString());
            phoenixMapper.phoenixPr(fndQuery);
        }

    }

    @Override
    public void fndDepartmentsShow(int a,int b) {
        JSONObject data = new JSONObject();
        for(int i=a;i<b;i++){
            data.put("department_id", i);
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(phoenixCompanyId);
            fndQuery.setType("fndDepartmentShow");
            logger.info("fndDepartmentsShow == data：" + data.toString());
            fndQuery.setInput(data.toString());
            phoenixMapper.phoenixPr(fndQuery);
        }

    }

    @Override
    public String postVoucher(GlBatchTransfer glBatchTransfer) throws Exception {
        JSONObject inputObject = JSONObject.parseObject(glBatchTransfer.getInput());
        JSONObject ret = new JSONObject();
        boolean hasError = false;

        synchronized (this){
            List<Integer> list = inputObject.getJSONArray("document_id").toJavaList(Integer.class);
            List<Integer> pendingDocumentIds = phoenixMapper.getPendingDocumentIds(list);
            inputObject.put("document_id",pendingDocumentIds);
        }

        glBatchTransfer.setInput(JSON.toJSONString(inputObject));
        logger.info("postVoucher gl_status 过滤后glBatchTransfer:{}",JSON.toJSONString(glBatchTransfer));
        if (inputObject.getInteger("ledger_id") == 1) {
            glBatchTransfer.setType("split");
            List<String> strings = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
            for (String str : strings) {
                glBatchTransfer.setType("api");
                inputObject.put("document_id", JSONObject.parseArray(str));
                glBatchTransfer.setInput(inputObject.toJSONString());
                List<String> xmls = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                logger.info("菲尼克斯postVoucher ==== xmls：" + xmls);
                for (String xml : xmls) {
                    Map<String, String> identity = new HashMap<>();
                    identity.put("Authorization", sapHash);
                    identity.put("Content-Type", "application/xml");
                    logger.info("菲尼克斯postVoucher ==== xml：" + xml);
                    String strs = PhoenixHttpPostString(sapPostUrl, identity, xml);
                    logger.info("菲尼克斯postVoucher ==== result：" + strs);
                    if (strs != null) {
                        glBatchTransfer.setType("apichange");
                        List<String> success = null;
                        do {
                            try {
                                success = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                            } catch (Exception e) {
                                logger.error("菲尼克斯postVoucher ==1== error：", e);
                                Thread.sleep(500);
                            }
                        } while (null == success);
                    } else {
                        glBatchTransfer.setType("apierror");
                        try {
                            inputObject.put("error_message", "ERROR");
                            glBatchTransfer.setInput(JSONObject.toJSONString(inputObject));
                            hasError = true;
                        } catch (Exception e) {
                            logger.error("菲尼克斯postVoucher ==2== error：", e);
                        }
                        List<String> errors = null;
                        do {
                            try {
                                errors = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                            } catch (Exception e) {
                                logger.error("菲尼克斯postVoucher ==3== error：", e);
                                Thread.sleep(500);
                            }
                        } while (null == errors);
                    }
                }
            }

        } else {
            glBatchTransfer.setType("spilt2");
            List<String> strings = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
            logger.info("strings: " + strings);
//                    glBatchTransfer.setType("baan");
            int sigma = 0;
            for (String str:strings){
                glBatchTransfer.setType("baan");
                sigma++;
                logger.info("菲尼克斯postVoucher spilt2 == str：" + str);
                inputObject.put("document_id",JSONObject.parseArray(str));
                glBatchTransfer.setInput(inputObject.toJSONString());
                logger.info("菲尼克斯postVoucher ==== spilt2 glBatchTransfer：" + glBatchTransfer);
                List<String> baans = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
                String fileName = df.format(new Date());
//                        logger.info(baans.size());
                logger.info("菲尼克斯postVoucher ==== spilt2 baans：" + Arrays.toString(baans.toArray()));
                JSONObject temp_baans = JSONObject.parseObject(baans.get(0));
                temp_baans.put("fileName",fileName+sigma);
                String message = temp_baans.getString("message");
                if(message == null) {

                    Map<String, String> identity_s = new HashMap<>();
                    identity_s.put("user-name", "phoenix");
                    identity_s.put("pass-word", "phoenix1028");
                    String iUrl = ftpUrl;
                    String postData = temp_baans.toJSONString();
                    logger.info("phoenixvoucher====postData：" + postData);
                    String str_s = PhoenixHttpPostString(iUrl, identity_s, temp_baans.toJSONString());
                    logger.info("phoenixvoucher====str_s：" + str_s);
                    if (str_s != null) {
                        glBatchTransfer.setType("apichangeForBaan");
                        List<String> success = null;
                        do {
                            try {
                                success = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                            } catch (Exception e) {
                                logger.error("菲尼克斯postVoucher ==4== error：", e);
                                Thread.sleep(500);
                            }
                        } while (null == success);
                    } else {
                        glBatchTransfer.setType("apierror");
                        try {
                            inputObject.put("error_message", "ERROR");
                            glBatchTransfer.setInput(JSONObject.toJSONString(inputObject));
                        } catch (Exception e) {
                            logger.error("菲尼克斯postVoucher ==5== error：", e);
                        }
                        List<String> errors = null;
                        do {
                            try {
                                errors = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                            } catch (Exception e) {
                                logger.error("菲尼克斯postVoucher ==6== error：", e);
                                Thread.sleep(500);
                            }
                        } while (null == errors);
                    }
                }else {
                    glBatchTransfer.setType("apierror");
                    try {
                        inputObject.put("error_message", message);
                        glBatchTransfer.setInput(JSONObject.toJSONString(inputObject));
                    } catch (Exception e) {
                        logger.error("菲尼克斯postVoucher ==7== error：", e);
                    }
                    List<String> errors = null;
                    do {
                        try {
                            errors = phoenixMapper.glBatchCreatePhoenix(glBatchTransfer);
                        } catch (Exception e) {
                            logger.error("菲尼克斯postVoucher ==8== error：", e);
                            Thread.sleep(500);
                        }
                    } while (null == errors);
                }
            }
        }
        if (hasError) {
            ret.put("exception_level", 99);
            ret.put("message", "部分更新出错，请刷新查看");
        } else {
            ret.put("exception_level", 0);
            ret.put("message", "OK");
        }
        logger.info("phoenixVoucher: " + ret.toJSONString());
        return ret.toJSONString();
    }

    @Override
    public Response phoenixReimbursement(String json) {

        JSONObject jsonObject = JSONObject.parseObject(json);
        Object headerId = null;
        Boolean flag = true;
        try{
            logger.info("reimbursement====="+jsonObject.get("header_id")+"延迟计算开始");
            String headerTypeCode =  phoenixMapper.getTypeCode(jsonObject.getString("header_type_id"));
            BigDecimal totalReceiptAmount = new BigDecimal("0.00");
            String status = jsonObject.getString("status");
            String ignoreWarning = jsonObject.getString("ignore_warning");
            if (!"Y".equals(ignoreWarning)){
                if (("C10".equals(headerTypeCode) || "C20".equals(headerTypeCode)) && "submitted".equals(status)){
                    //Long submitDate = jsonObject.getLong("submit_date");
                    Long submitDate = System.currentTimeMillis();
                    Long endDate = jsonObject.getLong("end_datetime");
                    headerId = jsonObject.get("header_id");
                    JSONArray lineArray = jsonObject.getJSONArray("claim_line");
                    if(null !=lineArray && lineArray.size()>0 && submitDate != null && endDate != null && endDate > 0 && submitDate > 0){
                        String prob = null;
                        if(submitDate > endDate){
                            long days =  (submitDate - endDate)/(1000 * 60 * 60 * 24);
                            if (days > 62 && days <= 120){
                                prob = "0.75";
                                flag = false;

                            }else if (days > 120){
                                prob = "0.5";

                                flag = false;
                            }
                        }
                        if (null != prob){
                            //BigDecimal oldTotalAmount  = null != jsonObject.get("total_amount") ? new BigDecimal(jsonObject.get("total_amount").toString()) : new BigDecimal("0.00");
                            BigDecimal oldTotalAmount = new BigDecimal("0.00");
                            for (Object lineObj : lineArray) {
                                Map line =  (Map)lineObj;
                                String payMethodId = null != line.get("pay_method_id") ?line.get("pay_method_id").toString():"";
                                if ("31741".equals(payMethodId)){
                                    continue;
                                }
                                BigDecimal receiptAmount  = null != line.get("original_amount") ? new BigDecimal(line.get("original_amount").toString()) : new BigDecimal("0.00");
                                BigDecimal newReceiptAmount = receiptAmount.multiply(new BigDecimal(prob)).setScale(2,BigDecimal.ROUND_HALF_UP);
                                line.put("receipt_amount",newReceiptAmount);
                                totalReceiptAmount = totalReceiptAmount.add(newReceiptAmount);
                                oldTotalAmount = oldTotalAmount.add(receiptAmount);
                            }

                            if ("0.5".equals(prob) || "0.75".equals(prob)) {
                                jsonObject.put("column10", "提交日期超过行程结束日期/发票日期60/120天，可报销金额按75%/50%折算");
                            }

                            jsonObject.put("total_amount",totalReceiptAmount);
                        }
                    }

                }else if("C30".equals(headerTypeCode) && "submitted".equals(status)) {
                    //Long submitDate = jsonObject.getLong("submit_date");
                    Long submitDate = System.currentTimeMillis();
                    JSONArray lineArray = jsonObject.getJSONArray("claim_line");
                    if(null !=lineArray && lineArray.size()>0 && submitDate != null && submitDate > 0){
                        for (Object lineObj : lineArray) {
                            Map line =  (Map)lineObj;
                            String payMethodId = null != line.get("pay_method_id") ?line.get("pay_method_id").toString():"";
                            if ("31741".equals(payMethodId)){
                                continue;
                            }

                            String prob = "1";
                            if (null != line.get("receipt_date")){
                                Long  receiptDate = Long.valueOf(line.get("receipt_date").toString());
                                if(submitDate > receiptDate){
                                    long days =  (submitDate - receiptDate)/(1000 * 60 * 60 * 24);
                                    if (days > 120){
                                        prob = "0.5";
                                        flag = false;
                                    }else if (days > 62 && days <= 120){
                                        prob = "0.75";
                                        flag = false;
                                    }
                                }
                            }

                           // BigDecimal receiptAmount  = null != line.get("receipt_amount") ? new BigDecimal(line.get("receipt_amount").toString()) : new BigDecimal("0.00");
                            BigDecimal receiptAmount  = null != line.get("original_amount") ? new BigDecimal(line.get("original_amount").toString()) : new BigDecimal("0.00");
                            BigDecimal newReceiptAmount = receiptAmount.multiply(new BigDecimal(prob)).setScale(2,BigDecimal.ROUND_HALF_UP);
                            line.put("receipt_amount",newReceiptAmount);
                            totalReceiptAmount = totalReceiptAmount.add(newReceiptAmount);
                            if ("0.5".equals(prob) || "0.75".equals(prob)) {
                                line.put("column27", "提交日期超过行程结束日期/发票日期60/120天，可报销金额按75%/50%折算");
                            }
                        }
                        jsonObject.put("total_amount",totalReceiptAmount);
                    }

                }
            }

        }catch (Exception e){
            logger.error("菲尼克斯phoenixReimbursement ==== error：", e);
            logger.error("reimbursement====="+headerId+"解析参数报错"+"入参=="+json);
        }



        Map<String, Object> map = new HashMap<>();
        map.put("exp_claim_header",jsonObject);

        List<Map<String, Object>> list = new ArrayList<>();
        Map<String, Object> exceptionsMap = new HashMap<>();
        exceptionsMap.put("exception_level",0);
        logger.info("reimbursement====="+jsonObject.get("header_id")+"延迟计算结束");
        if (!flag){
            logger.info("reimbursement====="+headerId+"该单是延迟报销单"+"入参===="+json);
        }
//        if (flag){
//            exceptionsMap.put("exception_level",0);
//        }else {
//            exceptionsMap.put("exception_level",1);
//            exceptionsMap.put("exception_message","该单是延迟报销单");
//            logger.inf("reimbursement====="+headerId+"该单是延迟报销单"+"入参===="+json);
//        }
        list.add(exceptionsMap);
        map.put("exceptions",list);
        Response response = new Response();
        response.setResCode(200000);
        response.setResMsg("success");
        response.setData(map);
        return response;
    }
}
