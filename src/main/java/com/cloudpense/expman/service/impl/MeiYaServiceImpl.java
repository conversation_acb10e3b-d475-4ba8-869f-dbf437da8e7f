package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.BindUserInfo;
import com.cloudpense.expman.entity.DingTalk;
import com.cloudpense.expman.entity.FndUser;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.MeiyaMapper;
import com.cloudpense.expman.mapper.OauthMapper;
import com.cloudpense.expman.service.MeiYaService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.meiya.MeiyaUtils.HttpPostString;
import static com.cloudpense.expman.controller.WanhuaController.Encrypt;
import static com.cloudpense.expman.webService.phoenix.PhoenixConstants.projectUrl;
import static com.cloudpense.expman.webService.phoenix.PhoenixConstants.projectOauthUrl;
@Service
public class MeiYaServiceImpl implements MeiYaService {
    static Logger logger = LoggerFactory.getLogger(MeiYaServiceImpl.class);
    private OauthMapper oauthMapper;
    private MeiyaMapper meiyaMapper;
    //  专属美亚集团配置
    private final static String APP_KEY = "dingdd96fd84b289e49135c2f4657eb6378f";
    private final static String APP_SECRET = "0NPeatHD1uuHKwHA7FX5w2La7biLjJDJHAdCSMXQlE_izZXnNIIL0OIG-3zIPrZI";
    private final static int AGENT_ID = *********;
    private final static int COMPANY_ID = 14967;
    private final static String PLATFORM = "meiya_dingtalk";
    private final static String MEIYA_ID = "c13de52307ef9962";
    private final static String MEIYA_KEY = "K893ab4cA02554cA";
    private static final String OAUTH_URL = projectOauthUrl+"/sso/login";
    private final static String Cloudpense_Logo = projectUrl+"/product/template/images/logo.png";
    private final static String MEIYA_DINGTALK_TASK = projectUrl+"/redirect/meiya-dingtalk.html";

    @Autowired
    public MeiYaServiceImpl(OauthMapper oauthMapper,
                            MeiyaMapper meiyaMapper){
        this.oauthMapper = oauthMapper;
        this.meiyaMapper = meiyaMapper;
    }

    @Override
    public void syncMeiYaUserInfoByDingDing() throws Exception {
        String accessToken = DingTalk.getToken(APP_KEY,APP_SECRET);
        JSONObject result = DingTalk.getDepartmentList(accessToken,"zh_CN");
        System.out.println("result=>"+result);
        JSONArray departmentList = result.getJSONArray("department");
        JSONArray userList = new JSONArray();
        for (int i = 0; i < departmentList.size(); i++) {
            JSONObject department = departmentList.getJSONObject(i);
            String deptId = department.getString("id");
            long offset = 0; // 与size参数同时设置时才生效，此参数代表偏移量,偏移量从0开始
            int size = 100;   // 与offset参数同时设置时才生效，此参数代表分页大小，最大100
            //entry_asc：代表按照进入部门的时间升序，entry_desc：代表按照进入部门的时间降序，
            //custom：代表用户定义(未定义时按照拼音)排序,modify_asc：代表按照部门信息修改时间升序，
            // modify_desc：代表按照部门信息修改时间降序，
            String order = "entry_desc";

            boolean hasMore = false;
            do {
                JSONObject userInfoList = DingTalk.userListByPage(accessToken,deptId,offset,size,order);
                if(null != userInfoList){
                    userList.addAll(userInfoList.getJSONArray("userlist"));
                }
                offset+=1;
                hasMore = userInfoList.getBoolean("hasMore");
            }while (hasMore);
        }
        System.out.println("userList==>"+userList);
        if(userList != null && userList.size()>0){
            Collection<FndUser> fndUserList = oauthMapper.findUserByMobile(COMPANY_ID);
            Map<String,FndUser> fndUserMap = new HashMap<>(fndUserList.size());
            for (FndUser fndUser : fndUserList) {
                String mobile = fndUser.getMobile();
                if(null != mobile && !"".equals(mobile)){
                    fndUserMap.put(mobile,fndUser);
                }
            }
            Collection<BindUserInfo> bindUserInfos = oauthMapper.findFndUserBindingByCompanyId(COMPANY_ID);
            Map<String, BindUserInfo> bindUserInfoMap = new HashMap<>();
            for (BindUserInfo bindUserInfo : bindUserInfos) {
                if(PLATFORM.equals(bindUserInfo.getPlatform())){
                    bindUserInfoMap.put(bindUserInfo.getMobile(),bindUserInfo);
                }
            }
            for (int i = 0; i < userList.size(); i++) {
                JSONObject userInfo = userList.getJSONObject(i);
                String dingUserMobile = userInfo.getString("mobile");
                if(fndUserMap.containsKey(dingUserMobile) && !bindUserInfoMap.containsKey(dingUserMobile)){
                    //当通过手机号匹配验证之后进行user binding
                    FndUser user = fndUserMap.get(dingUserMobile);
                    try {
                        oauthMapper.addFndUserBinding(user.getUserId(),user.getCompanyId(),PLATFORM,
                                userInfo.getString("userid"),userInfo.getString("mobile"));
                    }catch (Exception e){

                    }

                }
            }
        }
    }

    @Override
    public JSONObject meiyaDingTalkAuth(JSONObject json) throws Exception{
        String code = json.getString("code");
        //根据code+token获取userId
        String dingAccessToken = DingTalk.getToken(APP_KEY,APP_SECRET);
        JSONObject result = DingTalk.getUserIdByCode(dingAccessToken,code);
        String userId = result.getString("userid");
        JSONObject userInfoDetail = DingTalk.getUserInfoById(dingAccessToken,userId);
        String mobile = userInfoDetail.getString("mobile");
//        String maiyaServerName = oauthMapper.findServerNameByCorpId(MEIYA_ID);
//        if (maiyaServerName == null){
//            throw new ValidationException("公司绑定异常，未查询到corp"+MEIYA_ID+"对应sso_login信息");
//        }
//        logger.info("MEIYA_ID-=>sso_login-maiyaServerName==="+maiyaServerName);
        CustomerContextHolder.clearCustomerType();
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        BindUserInfo bind = meiyaMapper.findBindByMobile(PLATFORM,mobile);
        if(null == bind){
            throw new ValidationException("未找到该用户");
        }
        logger.info("mobile==="+ JSON.toJSONString(bind));
        CustomerContextHolder.clearCustomerType();
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        String userName = meiyaMapper.findUserNameById(bind.getUserId());
        logger.info(bind.getUserId()+"==="+ userName);
        JSONObject ret = new JSONObject();
        String token = "";
        try {
            String encrypted = Encrypt(userName + "," + System.currentTimeMillis(), MEIYA_KEY);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("corp", MEIYA_ID);
            jsonObject.put("code", encrypted);
            logger.info("request OAUTH_URL param=>"+jsonObject);
            String results = HttpPostString(OAUTH_URL, null, jsonObject.toJSONString());
            ret = JSONObject.parseObject(results);
            System.out.println(ret);
            if (ret.getString("token") != null) {
                token = ret.getString("token");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new ValidationException(e.getMessage());
        }
        if ("".equals(token)) {
            throw new ValidationException("登录失败");
        }
        return ret;
    }

    @Override
    public void pushDingTalkTasks() throws Exception{
        Collection<String> tasks = meiyaMapper.getDingTalkPushTasks(COMPANY_ID);
        for (String s : tasks) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            int bpdid = jsonObject.getIntValue("bpdid");
            int headerId = jsonObject.getIntValue("headerid");
            String headerType = jsonObject.getString("headertype");
            String url = MEIYA_DINGTALK_TASK+"?state="+bpdid+"&headerid="+headerId;
            String urlEncode = "dingtalk://dingtalkclient/page/link?url="+URLEncoder.encode(url,"utf-8")+"&pc_slide=true";
            int userId = jsonObject.getIntValue("userid");
            int positionId = jsonObject.getIntValue("positionid");
            Date createDate = jsonObject.getDate("createtime");
            String documentNum = jsonObject.getString("documentnum");
            String amount = jsonObject.getString("amount");
            String fullName = jsonObject.getString("fullname");
            String description = jsonObject.getString("description");
            JSONObject msg = new JSONObject();
            msg.put("msgtype","link");
            JSONObject link = new JSONObject();
            link.put("messageUrl",urlEncode);
            link.put("picUrl",Cloudpense_Logo);
            link.put("title",headerType);
            StringBuffer text = new StringBuffer();
            if(createDate != null){
                String createTime = formatTimeEight(createDate,"yyyy-MM-dd HH:mm:ss");
                text.append("提交时间："+createTime+"\r");
            }
            if(documentNum != null){
                text.append("单号："+documentNum+"\r");
            }
            if(amount != null){
                text.append("金额："+amount+"\r");
            }
            if(fullName != null){
                text.append("创建人："+fullName+"\r");
            }
            if(description != null){
                text.append("描述："+description+"\r");
            }
            link.put("text",text.toString());
            msg.put("link",link);
            String token = DingTalk.getToken(APP_KEY,APP_SECRET);
            if(userId != 0){
                BindUserInfo bindUserInfo = meiyaMapper.getBindUserInfoByUserId(COMPANY_ID,PLATFORM,userId);
                if(bindUserInfo == null){
                    continue;
                }
                try {
                    logger.info("push params=>"+msg);
                    JSONObject result = DingTalk.asyncSendMsg(token,AGENT_ID,bindUserInfo.getBindId(),msg);
                    logger.info("push resule=>"+result);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }else if(positionId != 0){
                Collection<Integer> userIds = meiyaMapper.getUserIdsByPositionId(COMPANY_ID,positionId);
                for (Integer id : userIds) {
                    BindUserInfo bindUserInfo = meiyaMapper.getBindUserInfoByUserId(COMPANY_ID,PLATFORM,id);
                    if(bindUserInfo == null){
                        continue;
                    }
                    try {
                        JSONObject result = DingTalk.asyncSendMsg(token,AGENT_ID,bindUserInfo.getBindId(),msg);
                        logger.info("push resule=>"+result);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private String formatTimeEight(Date dateTime, String format) throws Exception {
        Calendar cal = Calendar.getInstance();
        cal.setTime(dateTime);
        cal.add(Calendar.HOUR_OF_DAY, +8);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        return simpleDateFormat.format(cal.getTime());
    }

}
