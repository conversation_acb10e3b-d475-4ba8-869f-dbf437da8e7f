package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.OpenApiMapper;
import com.cloudpense.expman.service.AsynService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.util.S3.S3Util;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@EnableAsync
public class AsynServiceImpl implements AsynService {
    private static final Logger logger = LoggerFactory.getLogger(AsynServiceImpl.class);

    public static final String patt = "(?<=\\/)[A-z0-9\\-.]*(?=\\?)";
    public static final String patt2 = "(?:[^/][\\d\\w\\.]+)+$";

    // 单据附件下载临时目录
    public static final String savePath = "/home/<USER>/projects/document/attachmentFile";

    @Autowired
    private OpenApiService openApiService;
    @Autowired
    private OpenApiMapper openApiMapper;

    private RestTemplate restTemplate = new RestTemplate();

    @Override
    @Async
    public void documentUpdateAsyc(Integer companyId, String json, String server) {
        CustomerContextHolder.setCustomerType(server);
        logger.info("开始异步执行单据更新，入参：companyId={}, json={}, server={}", companyId, json, server);
        try {
            // 解析请求数据
            JSONObject jsonObject = null;// 单据数据
            String noticeUrl = null;// 通知地址
            String errorMessage = "";
            int errorCode = 0;
            try {
                JSONObject data = JSONObject.parseObject(json);
                String documentData = data.getString("document");
                jsonObject = JSONObject.parseObject(documentData);
                noticeUrl = data.getString("url");
            } catch (Exception e) {
                logger.error("输入JSON解析出错", e);
                documentUpdateNotice(null, getNoticeData(jsonObject), "输入JSON解析出错", 1001, noticeUrl);
            }
            if (null == jsonObject) {
                documentUpdateNotice(null, getNoticeData(jsonObject), "当前传入的数据结构是空", 1003, noticeUrl);
            }
            JSONArray claim_line = jsonObject.getJSONArray("claim_line");
            if (null != claim_line) {
                for (Object obj : claim_line) {
                    JSONObject line = (JSONObject) obj;
                    if (null != line.getJSONArray("attachments")) {
                        JSONArray attaches = new JSONArray();
                        JSONArray attachments = line.getJSONArray("attachments");
                        for (Object o : attachments) {
                            try {
                                attaches.add(getSingleFile(o.toString(), savePath));
                            } catch (Exception e) {
                                logger.error("{}附件处理失败：{}", o.toString(), e.getMessage(), e);
                                errorMessage = errorMessage + "|" + o.toString() + "附件处理失败: " + e.getMessage();
                                errorCode = 1005;
                            }
                        }
                        if (attaches.size() > 0) {
                            line.put("attachments", attaches);
                        }
                    }

                }
            }
            JSONArray attachments;
            try {
                JSONArray attaches = new JSONArray();
                attachments = jsonObject.getJSONArray("attachment_urls");
                if (null != attachments) {
                    for (Object o : attachments) {
                        try {
                            attaches.add(getSingleFile(o.toString(), savePath));
                        } catch (Exception e) {
                            logger.error("{}附件处理失败：{}", o.toString(), e.getMessage(), e);
                            errorMessage = errorMessage + "|" + o.toString() + "附件处理失败: " + e.getMessage();
                            errorCode = 1005;
                        }
                    }
                    if (attaches.size() > 0) {
                        jsonObject.put("attachments", attaches);
                    }
                }
            } catch (Exception e) {
                logger.error("没有附件", e);
            }
            try {
                FndQuery fndQuery = new FndQuery();
                fndQuery.setCompanyId(companyId);
                fndQuery.setInput(jsonObject.toJSONString());
                fndQuery.setType("document_update");
                openApiMapper.commonUpdate(fndQuery);
                if (fndQuery.getReturnCode().equals("S")) {
                    errorMessage = "ok";
                    documentUpdateNotice(null, getNoticeData(jsonObject), errorMessage, errorCode, noticeUrl);
                } else {
                    documentUpdateNotice(null, getNoticeData(jsonObject), errorMessage + "|" + fndQuery.getReturnMessage(), 1002, noticeUrl);
                }
            } catch (Exception e) {
                logger.error("数据库执行异常", e);
                documentUpdateNotice(null, getNoticeData(jsonObject), "数据库执行异常", 1003, noticeUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("系统异常：{}", e.getMessage());
            JSONObject rtn = new JSONObject();
            rtn.put("errcode", 9999);
            rtn.put("errmsg", "System error");
            return;
        }

    }

    private JSONObject getNoticeData(JSONObject jsonObject) {
        JSONObject data = new JSONObject();
        data.put("column32", jsonObject.get("column32"));
        return data;
    }

    public JSONObject getSingleFile(String url, String savePath) throws Exception {
        JSONObject jsonObject = new JSONObject();
        String fileName = System.currentTimeMillis() + ".unknown";
        Pattern urlName = Pattern.compile(patt);
        Matcher m = urlName.matcher(url);
        if (m.find()) {
            fileName = m.group(0);
        } else {
            Pattern urlName2 = Pattern.compile(patt2);
            Matcher m2 = urlName2.matcher(url);
            if (m2.find()) {
                fileName = m2.group(0);
            }
        }
        String[] arr = fileName.split("\\.");
        String newFileName = UUID.randomUUID() + "." + arr[arr.length - 1];
        downLoadFromUrl(url, newFileName, savePath);
        logger.info("文件{}开始上传s3服务器", newFileName);
        S3Util.uploadToS3(savePath, newFileName);
        logger.info("文件{}结束上传s3服务器", newFileName);
        delSingleFile(savePath, newFileName);
        jsonObject.put("attachment_url", newFileName);
        jsonObject.put("file_name", fileName);
        return jsonObject;
    }

    /**
     * 删除单个文件
     * @param savePath
     * @param fileName
     */
    private void delSingleFile(String savePath, String fileName) {
        try {
            File file = new File(savePath + File.separator + fileName);
            if(file.isFile() && file.exists()) {
                file.delete();
                logger.info("删除单个文件{}成功", fileName);
            } else {
                logger.info("文件{}不存在", fileName);
            }
        } catch (Exception e) {
            logger.error("删除单个文件{}异常：{}", fileName, e.getMessage(), e);
        }
    }

    public static void downLoadFromUrl(String urlStr, String fileName, String savePath) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(10 * 1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);

        //文件保存位置
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
        }
        File file = new File(saveDir + File.separator + fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if (fos != null) {
            fos.close();
        }
        if (inputStream != null) {
            inputStream.close();
        }

        logger.info("info:" + url + " download success");
    }

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    // 异步通知处理
    private void documentUpdateNotice(String type, Object data, String errMsg, int errCode, String noticeUrl) {
        logger.info("开始执行异步通知，入参：type={}, data={}, errMsg={}, errCode={}, noticeUrl={}",
                type, data, errMsg, errCode, noticeUrl);
        JSONObject req = new JSONObject();
        req.put("errcode", errCode);
        req.put("errmsg", errMsg);
        if(data != null)
            req.put("data", data);
        int send = 0;
        while (send < 3) {
            send++;
            try {
                logger.info("==执行第{}次异步通知请求信息: {}", send, req);
                JSONObject rsp = restTemplate.postForObject(noticeUrl, req, JSONObject.class, Maps.newHashMap());
                logger.info("==执行第{}次异步通知响应信息: {}", send, rsp);
                if(rsp.getBoolean("success"))
                    break;
                Thread.sleep(2000);
            } catch (Exception e) {
                logger.info("==执行第{}次异步通知出现异常:", send, e);
                e.printStackTrace();
            }
        }
        logger.info("结束执行异步通知");
    }

}
