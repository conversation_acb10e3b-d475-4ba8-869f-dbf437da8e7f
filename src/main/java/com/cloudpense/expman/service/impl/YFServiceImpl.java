package com.cloudpense.expman.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.YfMapper;
import com.cloudpense.expman.service.YFService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.util.SftpInternal.ReadCsv;
import com.cloudpense.expman.util.SftpInternal.WriteCsv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 */
@Service
public class YFServiceImpl implements YFService {
    @Autowired
    private YfMapper mapper;
    @Override
    public void yfPaperGenerating() {
        FndQuery fndQuery1 = new FndQuery();
        fndQuery1.setType("yfPretreatment");
        fndQuery1.setCompanyId(CommonConstants.YANFENG_ID);
        fndQuery1.setInput("{}");
        String output = mapper.yfPaperGenerating(fndQuery1);
        System.out.println(output);
        JSONArray array = JSONArray.parseArray(output);
        for(Object headerId:array){
            System.out.println(headerId);
            FndQuery fndQuery =new FndQuery();
            JSONObject object = new JSONObject();
            object.put("header_id",headerId);
            fndQuery.setInput(object.toJSONString());
            fndQuery.setType("yfExpensePaper");
            fndQuery.setCompanyId(CommonConstants.YANFENG_ID);
            String outputs = mapper.yfPaperGenerating(fndQuery);
            JSONObject outputJson = JSONObject.parseObject(outputs);
            JSONObject inputJson =new JSONObject();
            inputJson.put("content",JSONArray.parse(outputJson.getString("content")));
            inputJson.put("encoding","utf-8");
            inputJson.put("fileName",outputJson.getString("documentNum")+".csv");
            inputJson.put("partLine",",");
            inputJson.put("order","bRow,cRow,dRow,eRow,fRow,gRow,hRow,iRow,jRow,kRow,lRow,mRow,nRow," +
                    "oRow,pRow,qRow,rRow");
            inputJson.put("url","/home/<USER>/csvdata/send-prd/");
//            inputJson.put("url","C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Back\\csv\\");
            /*连接sftp发送请求*/
            System.out.println(inputJson.toJSONString());
            String yfWriteResult = WriteCsv.yfWrite(inputJson,inputJson.getString("partLine"),
                    inputJson.getString("fileName"));
            /*写成功的话更新状态*/
            FndQuery fndQuery2 = new FndQuery();
            JSONObject object1 = new JSONObject();
            object1.put("header_id",headerId);
            System.out.println("yfWriteResult: "+yfWriteResult);
            if ("success".equals(yfWriteResult)){
                System.out.println("kl01");
                //成功1
                object1.put("code",1);
                object1.put("message","");
            }else {
                //失败0
                object1.put("code",0);
                object1.put("message",yfWriteResult);
            }
            fndQuery2.setType("yfApiUpdate");
            fndQuery2.setCompanyId(CommonConstants.YANFENG_ID);
            fndQuery2.setInput(object1.toJSONString());
            System.out.println(object1.toJSONString());
            mapper.yfPaperGenerating(fndQuery2);

        }
    }

    @Override
    public void personMainData() {
        String[] aa = ("employee_number,full_name,email_address,department_name,gid,level_code," +
                "position_name,supervisor_gid,wechat_number").split(",");
        JSONArray jsonArray = ReadCsv.readFromFtp(CommonConstants.MAIN_DATA, CommonConstants.UNIX_CSV_PATH, aa);
        for(Object object:jsonArray){
            JSONObject jsonObject = (JSONObject)object;
            System.out.println(jsonObject);
            FndQuery fndQuery =new FndQuery();
            fndQuery.setCompanyId(CommonConstants.YANFENG_ID);
            fndQuery.setType("yfMainDataUpdate");
            fndQuery.setInput(jsonObject.toJSONString());
            mapper.yfPaperGenerating(fndQuery);
        }

    }

    public static void main(String[] s){
//        new YFServiceImpl().personMainData();
//        new YFServiceImpl().yfPaperGenerating();
        JSONObject inputJson=JSONObject.parseObject("{\"content\":[{\"bRow\":\"1-单据头\",\"cRow\":\"出差类型\",\"dRow\":\"申请单号\",\"eRow\":\"报销单号\",\"fRow\":\"是否租车\",\"gRow\":\"是否自驾\",\"hRow\":\"出差说明\",\"iRow\":\"提交日期\",\"jRow\":\"发起人工号\",\"kRow\":\"发起人姓名\",\"lRow\":\"出差人工号\",\"mRow\":\"出差人姓名\",\"nRow\":\"费用出处\",\"oRow\":\"成本中心\",\"pRow\":\"项目\",\"qRow\":\"子项\",\"rRow\":\"费用分摊科目\"},{\"bRow\":\"2-出差行程行\",\"cRow\":\"出发日期\",\"dRow\":\"到达日期\",\"eRow\":\"交通工具\",\"fRow\":\"舱位等级\",\"gRow\":\"出发国\",\"hRow\":\"出发城市\",\"iRow\":\"目的国\",\"jRow\":\"目的城市\",\"kRow\":\"\",\"lRow\":\"\"},{\"bRow\":\"3-费用明细\",\"cRow\":\"费用类型\",\"dRow\":\"支付方\",\"eRow\":\"舱位等级\",\"fRow\":\"币种\",\"gRow\":\"不含税金额\",\"hRow\":\"税金\",\"iRow\":\"汇率\",\"jRow\":\"人民币金额\",\"kRow\":\"是否超标\",\"lRow\":\"备注\"},{\"bRow\":\"4-审批流明细\",\"cRow\":\"审批节点描述\",\"dRow\":\"审批日期\",\"eRow\":\"责任人\",\"fRow\":\"责任人GID\",\"gRow\":\"处理人\",\"hRow\":\"处理人GID\",\"iRow\":\"操作\",\"jRow\":\"备注\",\"kRow\":\"\",\"lRow\":\"\"},{\"bRow\":\"\",\"cRow\":\"\",\"dRow\":\"\",\"eRow\":\"\",\"fRow\":\"\",\"gRow\":\"\",\"hRow\":\"\",\"iRow\":\"\",\"jRow\":\"\",\"kRow\":\"\",\"lRow\":\"\"},{\"bRow\":\"单据头\",\"cRow\":\"境外出差\",\"dRow\":\"REQ0000000021\",\"eRow\":\"EXP0000000025\",\"fRow\":\"否\",\"gRow\":\"否\",\"hRow\":\"境外出差\",\"iRow\":\"20181227\",\"kRow\":\"云简支持1\",\"lRow\":\"测试工号-韩燕妮\",\"mRow\":\"韩燕妮\",\"nRow\":\"成本中心\",\"oRow\":\"2115\",\"rRow\":514109},{\"bRow\":\"出差行程行\",\"cRow\":\"20181201\",\"dRow\":\"20181202\",\"eRow\":\"飞机\",\"fRow\":\"经济舱\",\"gRow\":\"中国\",\"hRow\":\"上海\",\"iRow\":\"美国\",\"jRow\":\"纽约\"},{\"bRow\":\"费用明细行\",\"cRow\":\"飞机\",\"dRow\":\"公司\",\"eRow\":\"经济舱\",\"fRow\":\"CNY\",\"gRow\":60000.00,\"hRow\":0.00,\"iRow\":1.0000000000,\"jRow\":60000.00,\"kRow\":\"未超标\",\"lRow\":\"上海->广州\"},{\"bRow\":\"审批流明细\",\"cRow\":\"发起出差报销\",\"dRow\":\"2018-12-27 10:43:22\",\"eRow\":\"韩燕妮\",\"fRow\":\"测试gid-韩燕妮\",\"gRow\":\"云简支持1\",\"iRow\":\"提交\"},{\"bRow\":\"审批流明细\",\"cRow\":\"外事审批\",\"dRow\":\"2018-12-27 10:45:38\",\"eRow\":\"外事审核\",\"gRow\":\"外事审核\",\"iRow\":\"审批通过\",\"jRow\":\"同意\"},{\"bRow\":\"审批流明细\",\"cRow\":\"财务审批\",\"dRow\":\"2018-12-27 10:50:04\",\"eRow\":\"财务审核\",\"fRow\":\"测试gid-财务审核\",\"gRow\":\"财务审核\",\"hRow\":\"测试gid-财务审核\",\"iRow\":\"审批通过\",\"jRow\":\"同意\"},{\"bRow\":\"审批流明细\",\"cRow\":\"指定用户审批\",\"dRow\":\"2018-12-27 10:51:39\",\"eRow\":\"金董春\",\"fRow\":\"测试gid-金董春\",\"gRow\":\"金董春\",\"hRow\":\"测试gid-金董春\",\"iRow\":\"审批通过\",\"jRow\":\"同意\"},{\"bRow\":\"审批流明细\",\"cRow\":\"总经理审批\",\"dRow\":\"2018-12-27 10:54:35\",\"eRow\":\"云简支持\",\"gRow\":\"云简支持\",\"iRow\":\"审批通过\",\"jRow\":\"同意\"}],\"encoding\":\"utf-8\",\"fileName\":\"EXP0000000025.csv\",\"order\":\"bRow,cRow,dRow,eRow,fRow,gRow,hRow,iRow,jRow,kRow,lRow,mRow,nRow,oRow,pRow,qRow,rRow\",\"partLine\":\",\",\"url\":\"/home/<USER>/csvdata/send/\"}\n");
//        System.out.println(inputJson.getString("content"));
        inputJson.put("url","C:\\software_wangwei\\souceCodeFiles\\Cloudpense_Project\\csv\\");
        String yfWriteResult = WriteCsv.yfWrite(inputJson,inputJson.getString("partLine"),
                inputJson.getString("fileName"));
    }



}
