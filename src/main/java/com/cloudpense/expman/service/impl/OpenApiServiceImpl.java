package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.regions.ServiceAbbreviations;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.cloudpense.expman.Request.TokenVo;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.OauthMapper;
import com.cloudpense.expman.mapper.OpenApiMapper;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.service.YjApiService;
import com.cloudpense.expman.util.JWTUtil;
import com.cloudpense.expman.util.S3.S3Constants;
import com.cloudpense.expman.util.S3.S3Util;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class OpenApiServiceImpl implements OpenApiService {

    private final OauthMapper oauthMapper;
    private final OpenApiMapper openApiMapper;
    private final YjApiService yjApiService;

    @Autowired
    public OpenApiServiceImpl(final OauthMapper oauthMapper,final OpenApiMapper openApiMapper,final YjApiService yjApiService) {
        this.oauthMapper = oauthMapper;
        this.openApiMapper = openApiMapper;
        this.yjApiService = yjApiService;
    }

    private static ResourceBundle RB = ResourceBundle.getBundle("token");
    private static String secret = RB.getString("token_Secret");

    private static ConcurrentHashMap<String, String> cache = new ConcurrentHashMap<>();

    @Override
    public int authCompany(String token) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        //解析token
        String key = JWTUtil.getUsername(token);
        //判断是否在有效期内
        boolean flag = JWTUtil.verify(token, key, secret);
        if (!flag) {
            throw new ValidationException("Invalid access_token");
        }

        String companyId = cache.get(key);
        if (StringUtils.isBlank(companyId)) {
            TokenVo tokenVo = oauthMapper.getTokenVoByClientId(key);
            if (tokenVo == null) {
                throw new ValidationException("Invalid access_token");
            }
            companyId = tokenVo.getCompanyId().toString();
            cache.put(key, companyId);
        }

        String server;
        if (companyId == null || Integer.valueOf(companyId) == 0) {
            throw new ValidationException("Invalid access_token");
        } else {
            server = oauthMapper.fndServer(Integer.valueOf(companyId));
            if (server != null) {
                CustomerContextHolder.setCustomerType(server);
            }
        }
        return Integer.valueOf(companyId);
    }

    @Override
    public JSONObject generateRtn(String type, Object data, String errMsg, int errCode) throws Exception {
        JSONObject rtn = new JSONObject();
        rtn.put("errcode", errCode);
        rtn.put("errmsg", "ok");
        if (null != errMsg) {
            rtn.put("errmsg", errMsg);
        }
        if (null != type && null != data) {
            rtn.put(type, data);
        }
        return rtn;
    }

    @Override
    public void changeVoucherStatus(JSONArray claimVos, String status, String glMessage) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        for (int i = 0; i < claimVos.size(); i++) {
            String journeyNum = "";
            JSONObject updatesClaim = claimVos.getJSONObject(i);
            if(updatesClaim.get("voucher_number") != null && updatesClaim.getString("voucher_number").length() > 0 && !updatesClaim.getString("voucher_number").startsWith("E")){
                status = "generated";
            }else {
                status = "error";
            }
            journeyNum = updatesClaim.get("voucher_number")==null?"":updatesClaim.getString("voucher_number");
            yjApiService.api("/common/voucher/back", org.springframework.http.HttpMethod.POST, changeGlStatus(updatesClaim.getString("document_num"), status, glMessage, journeyNum));
        }
    }

    private String changeGlStatus(String documentNum, String status, String glMessage, String currentTimeMillis) {
        JSONObject req = new JSONObject();
        req.put("bizId", UUID.randomUUID().toString());
        req.put("timestamp", System.currentTimeMillis());
        JSONObject data = new JSONObject();
        req.put("data", data);
        data.put("unique", documentNum);
        data.put("gl_status", status);
        data.put("gl_message", glMessage);
        data.put("journal_num", currentTimeMillis);
        return req.toJSONString();
    }

    @Override
    public HashMap<String, String> createAllAttachmentsURL(JSONArray requirement, int companyId) throws Exception {
        HashMap<String,String> stringHashMap = new HashMap<>();
        for(Object o:requirement){
            HashMap<String,String> map = newS3URL(getExtentionName(o.toString()),companyId);
            stringHashMap.put(o.toString(),map.get("url"));
        }
        return stringHashMap;

    }


    public  HashMap<String,String> newS3URL(String attachmentType,int companyId) throws Exception {

        String use_id = S3Constants.S3_ACCESS_KEY_ID;
        String use_secret = S3Constants.S3_SECRET_KEY;
        String use_name = S3Constants.S3_BUCKET_NAME;
        String server = CustomerContextHolder.getCustomerType();
        if (!server.equals(CustomerContextHolder.A)){
            use_id = S3Constants.S3_LIST.get(server).getKeyID();
            use_secret = S3Constants.S3_LIST.get(server).getKey();
            use_name = S3Constants.S3_LIST.get(server).getName();
        }

        AWSCredentials credentials = new BasicAWSCredentials(use_id, use_secret);
        AmazonS3 s3Client = new AmazonS3Client(credentials);
        Region region = Region.getRegion(Regions.CN_NORTH_1);
        s3Client.setRegion(region);
        final String serviceEndpoint = region.getServiceEndpoint(ServiceAbbreviations.S3);
        s3Client.setEndpoint(serviceEndpoint);

        GregorianCalendar calendar = new GregorianCalendar(TimeZone.getTimeZone("UTC"));
        calendar.setTime(new Date());
        calendar.add(Calendar.HOUR, 2);
        Date expirationDate = calendar.getTime();

        String name = UUID.randomUUID() + "." + attachmentType;

        try {
            openApiMapper.cacheExpAttachmentName(companyId,
                    1, "document", name);
        } catch (Exception e) {
            e.printStackTrace();
        }
        HashMap<String,String> map = new HashMap<>();
        String url = s3Client.generatePresignedUrl(use_name,name,expirationDate, HttpMethod.PUT).toString();
        map.put("url",url);
        map.put("name",name);
        return map;
    }


    public static  String getExtentionName(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < (filename.length() - 1))) {
                return filename.substring(dot + 1).toLowerCase();
            }
        }

        return filename;
    }

    @Override
    public void asynchronousBudget(JSONObject input){
        BudgetThread t1 = new BudgetThread();
        t1.setInput(input);
        t1.start();
    }

    public class BudgetThread extends Thread {
        private JSONObject input;
        @Override
        public void run(){
            int companyId = input.getInteger("companyId");
            String server = oauthMapper.fndServer(companyId);
            if (server != null) {
                CustomerContextHolder.setCustomerType(server);
            }
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(input.getString("input"));
            fndQuery.setType("cost_center_budget_2");
            openApiMapper.commonUpdate(fndQuery);
            System.out.println("子线程执行完毕");
        }

        public void setInput(JSONObject input){
            this.input=input;
        }

    }


    public static void main(String[] s){
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);


        String get = "114fae48-8466-472d-9612-8271aa289900.jpg";
        try {
            System.out.println(S3Util.getS3URL(get));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
