package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.controller.EvcardController;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.EvcardMapper;
import com.cloudpense.expman.mapper.SapMapper;
import com.cloudpense.expman.service.EvcardService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.util.MD5Util;
import net.sf.json.JSONSerializer;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiya.MeiyaUtils.HttpPostString;

/**
 * <AUTHOR>
 */
@Service
public class EvcardServiceImpl implements EvcardService {
    private final OpenApiService openApiService;
    private final EvcardMapper evcardMapper;
    private final SapMapper sapMapper;
    public static final int evcardCompanyId = 16904;
    public static final int evcardReceivePosition = 6843;
    public static final String ssoAppKey = "evcard-cp";
    public static final String ssoAppSecret = "KDEbyusgSyOTf6bi8Dr06g";
    public static final String evcardId = "c13de52307ef9978";
    public static final String evcardOAId = "c13de52307ef9979";
    public static final String evcardOAMPId = "c13de52307ef9981";
    public static final String evcardKey = "KE35G4jr8GcaPi9F";
    public static final String evcardKeyGetToken = "2019htkey";
    /*常量*/
    private static final String ADD = "add";
    private static final String DELETE = "delete";
    /**
     * 泛微OA系统的主要地址
     */
    private static final String OA_URL ="http://oa.gcsrental.com/services/";
    private static final String TASK = "OfsTodoDataWebService";
    /*日志*/
    private static final Logger logger = LoggerFactory.getLogger(EvcardServiceImpl.class);

    @Autowired
    public EvcardServiceImpl(OpenApiService openApiService,
                            EvcardMapper evcardMapper,
                            SapMapper sapMapper){
        this.openApiService = openApiService;
        this.evcardMapper = evcardMapper;
        this.sapMapper = sapMapper;
    }

    @Override
    public JSONObject createAccountingSubject(JSONArray jsonArray, Integer companyId) throws Exception {
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("createAccountingSubject");
            evcardMapper.evcardDataUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, "ok", 0);
            } else {
                return openApiService.generateRtn("errorDetails",
                         JSONObject.parseArray(fndQuery.getReturnMessage()),
                        "请检查输入数据",
                        1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常==>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    private List<String> getPostList(String type) {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(evcardCompanyId);
        fndQuery.setType(type);
        List<String> types = evcardMapper.evcardDataUpdate(fndQuery);
        logger.info("调用proc获取type={}数据结果={}", type, JSON.toJSONString(fndQuery));
        return types;
    }

    private String concatPost(String type, String data) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:int=\"http://www.evcard.com/int\">\n" +
                "    <soapenv:Header />\n" +
                "    <soapenv:Body>\n" +
                "        <int:MT_XML>\n" +
                "            <ZSYSID>FKXT</ZSYSID>\n" +
                "            <ZINTNO>" + type +
                "</ZINTNO>\n" +
                "            <ZXMLDT>" + data +
                "</ZXMLDT>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD1>" + UUID.randomUUID().toString().substring(0,32) + "</ZYLFD1>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD2>?</ZYLFD2>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD3>?</ZYLFD3>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD4>?</ZYLFD4>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD5>?</ZYLFD5>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD6>?</ZYLFD6>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD7>?</ZYLFD7>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD8>?</ZYLFD8>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD9>?</ZYLFD9>\n" +
                "            <!--Optional:-->\n" +
                "            <ZYLFD10>?</ZYLFD10>\n" +
                "        </int:MT_XML>\n" +
                "    </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }

    @Override
    public void postDocuments() throws Exception {
        String KEYNAME = "evcard凭证推送sap===";
        logger.info("{}开始处理", KEYNAME);
        List<String> postList = getPostList("getList");
        logger.info("{}获取到待处理数据List={}条", KEYNAME, postList.size());
//        postList = Arrays.asList("278344,INT_FICO080_001");
        for (String con : postList) {
            try {
                String[] arr = con.split(",");
                int headerId = Integer.parseInt(arr[0]);
                String type = arr[1];
                postSingleDocuments(headerId, type);
                sapDocumentFeedBack(headerId, type);
            } catch (Exception e) {
                logger.error("{}getList出现未知异常：", KEYNAME, e);
            }
        }
        List<String> postList1 = getPostList("getFinanceList");
        logger.info("{}获取到待处理数据FinanceList={}条", KEYNAME, postList1.size());
        for (String con : postList1) {
            try {
                String[] arr = con.split(",");
                int headerId = Integer.parseInt(arr[0]);
                String type = arr[1];
                postSingleDocuments(headerId, type);
                sapDocumentFeedBack(headerId, type);
            } catch (Exception e) {
                logger.error("{}getFinanceList出现未知异常：", KEYNAME, e);
            }
        }
    }

    @Override
    public void postPrSap() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(evcardCompanyId);
        fndQuery.setType("getPrListSap");
        List<String> postList = evcardMapper.evcardDataUpdate(fndQuery);
        for (String con : postList) {
            int outterHeaderId = 0;
            try {
                String[] arr = con.split(",");
                int headerId = Integer.parseInt(arr[0]);
                outterHeaderId = headerId;
                String type = arr[1];
                postSingleDocuments(headerId, type);
                prFeedBack(headerId, "updatePostPrSap");
            } catch (Exception e) {
                logger.error("postPrSap报错==>", e);
                prFeedBack(outterHeaderId, "updatePostPrSapError");
            }
        }
    }

    @Override
    public void postPrSrm() throws Exception {
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(evcardCompanyId);
        fndQuery.setType("getPrListSrm");
        List<String> postList = evcardMapper.evcardDataUpdate(fndQuery);
        for (String con : postList) {
            int outterHeaderId = 0;
            try {
                String[] arr = con.split(",");
                int headerId = Integer.parseInt(arr[0]);
                outterHeaderId = headerId;
                String type = arr[1];
                postSinglePrSrm(headerId, type);
                prFeedBack(headerId, "updatePostPrSrm");
            } catch (Exception e) {
                logger.error("postPrSrm报错==>", e);
                prFeedBack(outterHeaderId, "updatePostPrSrmError");
            }
        }
    }

    public String wrapPrSrm(String data) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:sch=\"http://www.aurora-framework.org/schema\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <sch:REQUEST>\n" +
                "         <sch:HEADER>\n" +
                "            <sch:BUSINESS_GROUP>BG00000101</sch:BUSINESS_GROUP>\n" +
                "            <sch:SYSTEM_CODE>BG00000101_SAP</sch:SYSTEM_CODE>\n" +
                "            <sch:REQUEST_ID>1000</sch:REQUEST_ID>\n" +
                "            <sch:IF_CATE_CODE>PUR_PO</sch:IF_CATE_CODE>\n" +
                "            <sch:IF_CODE>PUR_REQ_IMP</sch:IF_CODE>\n" +
                "            <sch:USER_NAME>ADMIN</sch:USER_NAME>\n" +
                "            <sch:PASSWORD>0FE1408BD012FBA3DC38BC12E8E6FF64</sch:PASSWORD>\n" +
                "            <sch:BATCH_NUM>1</sch:BATCH_NUM>\n" +
                "            <sch:TOTAL_SEG_COUNT>1</sch:TOTAL_SEG_COUNT>\n" +
                "            <sch:SEG_NUM>1</sch:SEG_NUM>\n" +
                "         </sch:HEADER>\n" +
                "         <sch:CONTEXT>\n" +
                "            <sch:EITF_PUR_REQ_HEADERS>" + data +
                "</sch:EITF_PUR_REQ_HEADERS>\n" +
                "         </sch:CONTEXT>\n" +
                "      </sch:REQUEST>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }

    @Override
    public void postSinglePrSrm(int headerId, String type) throws Exception {
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String evcardSrmUrl = rb.getString("evcardSrm");
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(evcardCompanyId);
        fndQuery.setType(type);
        JSONObject content = new JSONObject();
        content.put("header_id", headerId);
        fndQuery.setInput(content.toJSONString());
        List<String> results = evcardMapper.evcardDataUpdate(fndQuery);
        for (String result : results) {

            JSONObject resultjsonObject = JSONObject.parseObject(result);
            List<String> headerAttachments = evcardMapper.evcardGetHeaderAttachments(evcardCompanyId, headerId);
            resultjsonObject.put("sch:HEADER_ATTACHMENTS", "<![CDATA[" + headerAttachments + "]]>");
            JSONArray claimLines = resultjsonObject.getJSONArray("lines");
            for (Object o : claimLines) {
                JSONObject line = (JSONObject) o;
                List<String> lineAttachments = evcardMapper.evcardGetLinesAttachments(evcardCompanyId, headerId, line.getString("sch:LINE_NUM"));
                line.put("sch:LINE_ATTACHMENTS", "<![CDATA[" + lineAttachments + "]]>");
            }
            logger.info("send srm json result==>"+resultjsonObject);
            XMLSerializer xmlSerializer = new XMLSerializer();
            xmlSerializer.setObjectName("sch:RECORD");
            xmlSerializer.setElementName("sch:RECORD");
            String data = xmlSerializer.write(JSONSerializer.toJSON(resultjsonObject));
            data = data.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                    .replaceAll("<lines class=\"array\">", "<sch:EITF_PUR_REQ_LINES class=\"array\">")
                    .replaceAll("</lines>", "</sch:EITF_PUR_REQ_LINES>")
                    .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                    .replaceAll(" class=\"object\"", "")
                    .replaceAll(" class=\"array\"", "")
                    .replaceAll(" null=\"true\"", "")
                    .replaceAll(" type=\"string\"", "")
                    .replaceAll(" type=\"number\"", "");

            String wrapPrSrmData = wrapPrSrm(data);
            logger.info("wrap PrSrm data==>"+ wrapPrSrmData);
            Map<String, String> identity = new HashMap<>();
            identity.put("soapAction", "execute");
            String postResult = HttpPostString(evcardSrmUrl, identity, wrapPrSrmData);
            logger.info("post result==>"+postResult);

        }
    }

    public void postSingleDocuments(int headerId, String type) throws Exception {
        String KEYNAME = "evcard推送sap==type=" + type + "===";
        logger.info("{}单据headerId={}开始处理", KEYNAME, headerId);
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String evcardSapUrl = rb.getString("evcardSap");
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(evcardCompanyId);
        fndQuery.setType(type);
        JSONObject content = new JSONObject();
        content.put("header_id", headerId);
        fndQuery.setInput(content.toJSONString());
        List<String> results = evcardMapper.evcardDataUpdate(fndQuery);
        logger.info("{}单据headerId={}results为{}", KEYNAME, headerId, JSON.toJSONString(results));
        List<String> accountList = evcardMapper.getLovValue(evcardCompanyId,"Accounts Payable");
        List<String> drTaxAccountList = evcardMapper.getLovValue(evcardCompanyId,"drTaxAccounts");
        List<String> filterSuppliersList = evcardMapper.getLovValue(evcardCompanyId,"filterSuppliers");
        XMLSerializer xmlSerializer = new XMLSerializer();
        xmlSerializer.setObjectName("RowInfo");
        String body = " <![CDATA[<Body><TableInfo>";
        if (results.size() < 1) {
            return;
        }
        Map<String,List<JSONObject>> glAccountLineMap = new HashMap<>();
        Map<String,List<JSONObject>> supplierLineMap = new HashMap<>();
        List<JSONObject> lines = null;
        List<JSONObject> supLines = null;
        Iterator<String> it=results.iterator();
        while (it.hasNext()){
            JSONObject lineInfo = JSONObject.parseObject(it.next());
            if((lineInfo.get("LIFNR") == null || !filterSuppliersList.contains(lineInfo.getString("LIFNR")))) {
                if (null != lineInfo.get("HKONT") && accountList.contains(lineInfo.getString("HKONT"))) {
                    if (null != glAccountLineMap.get(lineInfo.getString("HKONT")+(Objects.isNull(lineInfo.get("TaxCode"))?"":lineInfo.getString("TaxCode")))) {
                        lines = glAccountLineMap.get(lineInfo.getString("HKONT")+(Objects.isNull(lineInfo.get("TaxCode"))?"":lineInfo.getString("TaxCode")));
                        lines.add(lineInfo);
                    } else {
                        lines = new ArrayList<>();
                        lines.add(lineInfo);
                        glAccountLineMap.put(lineInfo.getString("HKONT")+(Objects.isNull(lineInfo.get("TaxCode"))?"":lineInfo.getString("TaxCode")), lines);
                    }
                    it.remove();
                } else {
                    if (lineInfo.get("dr_cr") != null && "cr".equals(lineInfo.get("dr_cr"))) {
                        if (null != glAccountLineMap.get(lineInfo.getString("LIFNR"))) {
                            lines = glAccountLineMap.get(lineInfo.getString("LIFNR"));
                            lines.add(lineInfo);
                        } else {
                            lines = new ArrayList<>();
                            lines.add(lineInfo);
                            glAccountLineMap.put(lineInfo.getString("LIFNR"), lines);
                        }
                        it.remove();
                    }
                }
            }
        }
        logger.info("{}单据headerId={}glAccountLineMap{}", KEYNAME, headerId, glAccountLineMap);
        for (Map.Entry<String, List<JSONObject>> entry : glAccountLineMap.entrySet()) {
            List<JSONObject> lineList = entry.getValue();
            List<BigDecimal> dMBTRs = lineList.stream().map(line -> null == line.get("DMBTR") ? new BigDecimal("0") : new BigDecimal(line.getString("DMBTR"))).collect(Collectors.toList());
            BigDecimal dMBTRsTotal = dMBTRs.stream().reduce(new BigDecimal("0"), BigDecimal::add);
            List<JSONObject> objectList = lineList.stream().filter(newline -> null != newline.get("receiptAmount") && new BigDecimal(String.valueOf(newline.get("receiptAmount"))).compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
            JSONObject line = CollectionUtils.isEmpty(objectList)?lineList.get(0):objectList.get(0);
            line.put("DMBTR",String.valueOf(dMBTRsTotal));
            String lineString = line.toJSONString();
            results.add(lineString);
        }
        for (String result : results) {
            JSONObject before = JSONObject.parseObject(result);
            //借贷方行各自相同LIFNR的行只取其中一个
//            if(before.get("LIFNR") != null && before.get("dr_cr") != null && "cr".equals(before.get("dr_cr")) && null != supplierLineMap.get(before.getString("dr_cr")+before.getString("LIFNR"))){
//                continue;
//            }else {
//                supLines = new ArrayList<>();
//                supLines.add(before);
//                supplierLineMap.put(before.getString("dr_cr")+before.getString("LIFNR"),supLines);
//            }
            if(before.get("dr_cr") != null && ("cr".equals(before.get("dr_cr")) || "dr".equals(before.get("dr_cr"))) && (before.get("receiptAmount") == null || new BigDecimal(String.valueOf(before.get("receiptAmount"))).compareTo(BigDecimal.ZERO)==0)){
                continue;
            }
            if(before.get("dr_cr") != null && ("dr_tax".equals(before.get("dr_cr")) || ("dr".equals(before.get("dr_cr"))  && null != before.get("HKONT") && !drTaxAccountList.contains(before.getString("HKONT"))))){
                before.put("LIFNR",null);
            }
            if(before.get("dr_cr") != null && "dr".equals(before.get("dr_cr")) && before.get("HKONT") != null && "**********".equals(before.get("HKONT")) && before.get("ZTYPE") != null && "003".equals(before.get("ZTYPE"))){
                before.put("XNEGP","X");
            }
            if (null != before.getString("type_id")) {
                type = before.getString("type_id");
                before.remove("type_id");
            }
            String data = xmlSerializer.write(JSONSerializer.toJSON(before.toJSONString()));
            data = data.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                    .replaceAll(" class=\"object\"", "")
                    .replaceAll(" null=\"true\"", "")
                    .replaceAll(" type=\"string\"", "")
                    .replaceAll(" type=\"number\"", "");
            body += data;
        }
        body += "</TableInfo></Body>]]>";
        String postData = concatPost(type, body);
        logger.info("{}单据headerId={}推送请求={}", KEYNAME, headerId, postData);
        sapMapper.normalInsertLog(evcardCompanyId, "evcardMessage", JSONObject.toJSONString(new JSONObject().put("data",postData)));
        Map<String, String> identity = new HashMap<>();
        identity.put("Authorization", "Basic cG9fcDAxOldlbGNvbWUy"); //po_p01:Welcome2
        identity.put("Content-Type", "text/xml;charset=UTF-8");
        String result = HttpPostString(evcardSapUrl, identity, postData);
        logger.info("{}单据headerId={}推送结果={}", KEYNAME, headerId, result);
    }

    public void sapDocumentFeedBack(int headerId, String type) throws Exception {
        JSONObject content = new JSONObject();
        content.put("header_id", headerId);
        FndQuery fndQuery1 = new FndQuery();
        fndQuery1.setType("updatePosted");
        fndQuery1.setInput(content.toJSONString());
        fndQuery1.setCompanyId(evcardCompanyId);
        evcardMapper.evcardDataUpdate(fndQuery1);
    }

    public void prFeedBack(int headerId, String channel) throws Exception {
        JSONObject content = new JSONObject();
        content.put("header_id", headerId);
        FndQuery fndQuery1 = new FndQuery();
        fndQuery1.setType(channel);
        fndQuery1.setInput(content.toJSONString());
        fndQuery1.setCompanyId(evcardCompanyId);
        evcardMapper.evcardDataUpdate(fndQuery1);
    }

    private String addRequestInfo(JSONObject jsonObject) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\"webservices.ofs.weaver.com.cn\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <web:receiveTodoRequestByJson><web:in0>"+
                jsonObject.toJSONString()+
                " </web:in0></web:receiveTodoRequestByJson>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }

    private String deleteRequestInfo(JSONObject jsonObject) {
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:web=\"webservices.ofs.weaver.com.cn\">\n" +
                "<soapenv:Header/>\n" +
                "<soapenv:Body>\n" +
                "<web:deleteRequestInfoByJson>\n" +
                "<web:in0>\n"+
                jsonObject.toJSONString()+
                "</web:in0>\n" +
                "</web:deleteRequestInfoByJson>\n" +
                "</soapenv:Body>\n" +
                "</soapenv:Envelope>";

    }

    public String repeater1(JSONObject jsonObject) throws Exception {
        String type = jsonObject.getString("type");
        String result = "";
        String postData="";
        String data = "";
//        jsonObject.remove("type");
        if (ADD.equals(type)){
            postData = addRequestInfo(jsonObject);
        }else if(DELETE.equals(type)){
            postData = deleteRequestInfo(jsonObject);
        }
        logger.info("postData==>" + postData);
        String postUrl = OA_URL+TASK;
        result = HttpPostString(postUrl, null, postData);
        if (result!=null){
            data =  StringEscapeUtils.unescapeHtml3(result);
        }
        logger.info("待办请求结果==>" + data);
        return data;

    }

    @Override
    public String evcardPushTask() {
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String basePushUrl = rb.getString("basePush");
        String cloudpenseevcardUrl = rb.getString("cloudpenseevcard");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        List<String> tasks =evcardMapper.getOaPushTask(evcardCompanyId);
        logger.info("tasks.size()==>" + tasks.size());
        StringBuilder result= new StringBuilder();
        if(tasks.size()>0){
            for (String s : tasks) {
                JSONObject dataJson = JSONObject.parseObject(s);
                logger.info("post数据==>" + dataJson);
                try {
                    result.append(repeater1(dataJson)).append("\n").append("\n");
                } catch (Exception e) {
                    logger.error("result.append报错==>", e);
                }
                if (!"add".equals(dataJson.getString("type")))
                {
                    continue;
                }
                HttpClient httpClient = new HttpClient();
                PostMethod postMethod = null;
                try {
                    logger.info("message start==>");
                    String key = "9c20ca45-0aa8-411c-a488-8b6a552e3708";//emobile后台的推送秘钥

                    String messagetypeid = "1"; //在mobile后台注册的消息类型id(登录mobile6.5后台，消息类型设置)
                    String badge = "1";  //消息数量+1
                    String pcurl = dataJson.getString("pcurl");
                    String pcurl1 = pcurl.replace("amp;","");
                    String url = cloudpenseevcardUrl + pcurl1;
                    String reviceIds = dataJson.getString("column3");
                    String message = dataJson.getString("requestname");


                    //设置访问编码
                    httpClient.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
                    //让服务器知道访问源为浏览器
                    httpClient.getParams().setParameter(HttpMethodParams.USER_AGENT, "Mozilla/5.0 (Windows NT 6.1; rv:8.0.1) Gecko/20100101 Firefox/8.0.1");
                    postMethod = new PostMethod(basePushUrl);
                    Map<String, String> para = new HashMap<String, String>();
                    para.put("messagetypeid", messagetypeid);
                    para.put("module", "-2");      //标示属于自定义消息
                    para.put("url", url);
                    String paraJson = net.sf.json.JSONObject.fromObject(para).toString();
                    if (message.length() > 100) message = message.substring(0, 100) + "...";
                    StringBuffer sendMsg = new StringBuffer();
                    sendMsg.append(reviceIds);
                    sendMsg.append(message);
                    sendMsg.append(badge);
                    sendMsg.append(paraJson);
                    sendMsg.append(key);
                    logger.info("sendMsg==>" + sendMsg);
                    String hash = DigestUtils.md5Hex(sendMsg.toString().getBytes("UTF-8"));
                    ArrayList<NameValuePair> datalist = new ArrayList<NameValuePair>();
                    datalist.add(new NameValuePair("userid", reviceIds));
                    datalist.add(new NameValuePair("msg", message));
                    datalist.add(new NameValuePair("badge", badge));
                    datalist.add(new NameValuePair("para", paraJson));
                    datalist.add(new NameValuePair("hash", hash));
                    NameValuePair[] data = datalist.toArray(new NameValuePair[0]);
                    postMethod.setRequestBody(data);
                    httpClient.executeMethod(postMethod);
                } catch (Exception e) {
                    logger.error("evcardPushTask报错==>", e);
                } finally {
                    if (postMethod != null) {
                        postMethod.releaseConnection();
                    }
                }
            }
        }
        logger.info("result==>" + result);
        return result.toString();
    }

    @Override
    public String postGetTokenParameter(String employee_number) throws Exception{
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");

        String getTokenUrl = rb.getString("getTokenurl");
        Long times = System.currentTimeMillis();
        String signOriginalStr = employee_number + times + evcardKeyGetToken;
        String sign = MD5Util.MD5Encode(signOriginalStr).toLowerCase();
        String url = getTokenUrl + employee_number + "&times=" + times + "&sign=" + sign;
        logger.info("oa url==>" + url);
        RestTemplate restTemplate = new RestTemplate();
        String result = restTemplate.getForObject(url, String.class);
        logger.info("oa result==>" + result);
        return result;
    }


    public static void main(String[] args) throws Exception {
//        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
//        String basePushUrl = rb.getString("basePush");
//        String cloudpenseevcardUrl = rb.getString("cloudpenseevcard");
        JSONObject dataJson = JSONObject.parseObject
                ("{\n" + "   \"text\": \"OA跳转\",\n" + "   \"url\": \"https://gphn.cloudpense.com/iExpenseSqftp/getTokenJump?requestid=64055\"\n" + "  }");
        //        String key = "f76a3cac-319f-4cf7-afef-d87113ad9969";//emobile后台的推送秘钥
//        String messagetypeid = "1"; //在mobile后台注册的消息类型id(登录mobile6.5后台，消息类型设置)
//        String badge = "1";  //消息数量+1
//
//        String pcurl = dataJson.getString("pcurl");
//        String pcurl1 = pcurl.replace("amp;","");
//        String url = cloudpenseevcardUrl + pcurl1;
//        String reviceIds = "zhangjing";
//        String message = dataJson.getString("workflowname");
//
//
//        HttpClient httpClient = new HttpClient();
//        //设置访问编码
//        httpClient.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
//        //让服务器知道访问源为浏览器
//        httpClient.getParams().setParameter(HttpMethodParams.USER_AGENT, "Mozilla/5.0 (Windows NT 6.1; rv:8.0.1) Gecko/20100101 Firefox/8.0.1");
//        PostMethod postMethod = new PostMethod(basePushUrl);
//        Map<String, String> para = new HashMap<String, String>();
//        para.put("messagetypeid", messagetypeid);
//        para.put("module", "-2");      //标示属于自定义消息
//        para.put("url", url);
//        String paraJson = net.sf.json.JSONObject.fromObject(para).toString();
//        if (message.length() > 100) message = message.substring(0, 100) + "...";
//        StringBuffer sendMsg = new StringBuffer();
//        sendMsg.append(reviceIds);
//        sendMsg.append(message);
//        sendMsg.append(badge);
//        sendMsg.append(paraJson);
//        sendMsg.append(key);
//        String hash = DigestUtils.md5Hex(sendMsg.toString().getBytes("UTF-8"));
//        ArrayList<NameValuePair> datalist = new ArrayList<NameValuePair>();
//        datalist.add(new NameValuePair("userid", reviceIds));
//        datalist.add(new NameValuePair("msg", message));
//        datalist.add(new NameValuePair("badge", badge));
//        datalist.add(new NameValuePair("para", paraJson));
//        datalist.add(new NameValuePair("hash", hash));
//        NameValuePair[] data = datalist.toArray(new NameValuePair[0]);
//        postMethod.setRequestBody(data);
        logger.info("dataJson==>" + dataJson );
//        httpClient.executeMethod(postMethod);
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String getTokenUrl = rb.getString("getTokenurl");
        Long times = System.currentTimeMillis();
        String originalStr = "G000203" + times + evcardKeyGetToken;
        logger.info("originalStr==>" + originalStr );
        String sign = MD5Util.MD5Encode(originalStr).toLowerCase();
        String url = getTokenUrl + "G000203" + "&times=" + times + "&sign=" + sign;
        logger.info("url==>" + url );
        RestTemplate restTemplate = new RestTemplate();
        String result = restTemplate.getForObject(url, String.class);
        JSONObject jsonObject1 = JSONObject.parseObject(result);
        logger.info("jsonObject1==>" + jsonObject1 );
        JSONObject result1 = new JSONObject();
        result1.put("a", 23);
        logger.info("JSONSerializer.toJSON(result1)==>" + JSONSerializer.toJSON(result1));
    }
}
