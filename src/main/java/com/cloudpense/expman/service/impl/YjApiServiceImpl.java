package com.cloudpense.expman.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cloudpense.expman.config.YjResCode;
import com.cloudpense.expman.service.YjApiService;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ResourceBundle;

/**
 * 云简openapi服务实现
 *
 * <AUTHOR>
 * @Date 2022/1/13
 */
@Service
public class YjApiServiceImpl implements YjApiService {

    /**缓存token*/
    private TokenCache  tokenCache = new TokenCache();
    private static ResourceBundle RB = ResourceBundle.getBundle("token");
    private static String evcardClientId = RB.getString("evcardClientId");
    private static String evcardClientSecret = RB.getString("evcardClientSecret");
    private static String grantType = RB.getString("grantType");
    private static String domainUrl = RB.getString("domainUrl");
    private static final Logger logger = LoggerFactory.getLogger(YjApiServiceImpl.class);

    @Override
    public String token() {
        long currentTime = System.currentTimeMillis();
        RestTemplate restTemplate = new RestTemplate();
        if(tokenCache.getExpire() != null && tokenCache.getExpire() > currentTime) {
            logger.info("当前token仍在有效期内，tokenCache={}", tokenCache);
            return tokenCache.getValue();
        }
        logger.info("token not exists or expired，start get token");
        String url = domainUrl + "/common/unAuth/tokens/get?"
                   + "grant_type=" + grantType
                   + "&client_id=" + evcardClientId
                   + "&client_secret=" + evcardClientSecret;
        Integer resCode;
        String resMsg;
        JSONObject resData;
        try {
            Object resObj = restTemplate.getForObject(url, String.class);
            JSONObject resJson = JSONUtil.parseObj((String)resObj);
            resCode = resJson.getInt("resCode");
            resMsg = resJson.getStr("resMsg");
            resData = resJson.getJSONObject("data");
        } catch (Exception e) {
            logger.error("接口异常：{}", e.getMessage(), e);
            throw new RuntimeException("接口异常：" + e.getMessage());
        }
        if(!YjResCode.SUCCESS.getCode().equals(resCode)) {
            logger.error("接口返回失败：{}", resMsg);
            throw new RuntimeException("接口返回失败：" + resMsg);
        }
        tokenCache.setValue(resData.getStr("access_token"));
        // 折扣90%的时间进行防御两端服务器时间不同步
        tokenCache.setExpire(currentTime + resData.getLong("expires_in") * 900);

        return tokenCache.getValue();
    }

    @Override
    public Object api(String path, HttpMethod httpMethod, String req) {
        String url = domainUrl + path;
        RestTemplate restTemplate = new RestTemplate();
        Integer resCode;
        String resMsg;
        Object resData;
        try {
            HttpHeaders httpHeader = new HttpHeaders();
            httpHeader.add("Content-Type", ContentType.APPLICATION_JSON.toString());
            httpHeader.add("access_token", token());
            HttpEntity httpEntity = new HttpEntity<>(req, httpHeader);
            logger.info("接口url：{}，请求参数为{}",url, com.alibaba.fastjson.JSONObject.toJSONString(httpEntity));
            Object resObj =  restTemplate.exchange(url, httpMethod, httpEntity, String.class);
            logger.info("接口url：{}，请求结果为{}",url, com.alibaba.fastjson.JSONObject.toJSONString(resObj));
            JSONObject resJson = JSONUtil.parseObj(((ResponseEntity)resObj).getBody());
            if(path.startsWith("/cproxy")) {
                resCode = resJson.getInt("code");
            } else {
                resCode = resJson.getInt("resCode");
            }
            if(!YjResCode.SUCCESS.getCode().equals(resCode)) {
                logger.error("调用云简api服务端返回失败：url={}, body={}", url, resJson);
                return null;
            }
            resData = resJson.getObj("data");
            logger.info("调用云简api结果: url={}, data={}", url, resData);
        } catch (Exception e) {
            logger.error("接口异常：{}",  e.getMessage(), e);
            throw new RuntimeException("接口异常：" + e.getMessage());
        }
        return resData;
    }



    /**
     * token数据
     */
    class TokenCache {
        String value;//token值
        Long expire;//token失效时间

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public Long getExpire() {
            return expire;
        }

        public void setExpire(Long expire) {
            this.expire = expire;
        }
    }

}
