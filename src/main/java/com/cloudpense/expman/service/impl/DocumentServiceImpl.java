package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.mapper.DocumentMapper;
import com.cloudpense.expman.service.DocumentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * 修改单据service实现
 */
@Service
public class DocumentServiceImpl implements DocumentService {

    @Autowired
    private DocumentMapper documentMapper;

    /**
     * 修改单据
     */
    @Override
    public void updateDocument(String json){
        String objData = JSONObject.parseObject(json).getString("document");
        JSONObject jsonObject = JSONObject.parseObject(objData);

        //对方系统的column10对应我方系统的column28
        String column28 = (String) jsonObject.get("column10");
        //查询column28值匹配的单据
        ExpClaimHeader expClaimHeaderCurr = new ExpClaimHeader();
        expClaimHeaderCurr.setColumn28(column28);
        List<Integer> list = documentMapper.selectDocument(expClaimHeaderCurr);
        if(list != null && list.size() > 0){
            for(int x = 0; x < list.size(); x++){
                //修改查询出的单据状态
                documentMapper.updateDocument(list.get(x),14968,171917,"closed");
            }

        }

    }
}
