package com.cloudpense.expman.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.FndCompany;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.FndUser;
import com.cloudpense.expman.entity.FndUserAccount;
import com.cloudpense.expman.entity.MqQueue;
import com.cloudpense.expman.entity.YofcPayElement;
import com.cloudpense.expman.entity.YofcPaymentVoucher;
import com.cloudpense.expman.entity.changfei.*;
import com.cloudpense.expman.mapper.MqMapper;
import com.cloudpense.expman.mapper.YofcMapper;
import com.cloudpense.expman.service.YofcService;
import com.cloudpense.expman.service.yj.YjOpenApiService;
import com.cloudpense.expman.util.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.cloudpense.expman.util.YofcUtils.getUTF8StringFromGBKString;

/**
 * <AUTHOR>
 */
@Service
public class YofcServiceImpl implements YofcService {

    private static final Integer companyId = 16895;

    private static  ResourceBundle RB = ResourceBundle.getBundle("changfeiurl");
    private static  final String  CHANGFEITOKENURL = RB.getString("changfei_token");
    private static  final String  APFAPIAOURL = RB.getString("ap_fapiao");
    private static  final String  APFUKUANURL = RB.getString("ap_fukuan");
    private static  final String  CBSURL = RB.getString("cbs");
    private static  final String  CBSKEY = RB.getString("cbs_key");
    private static  final String  VOUCHERNEWURL = RB.getString("voucher_url_new");
    //对私支付单据类型
    private static  final String  PRIVATEPAYTYPE = "PAY001";
    //对公支付单据类型
    private static  final String  PUBLICPAYTYPE = "PAY003";

    //支付方式，公司已付
    private static final int PAYMETHODID0 = 48849;

    //支付方式，需支付
    private static final int PAYMETHODID1 = 48848;

    //支付方式，备用金还款
    private static final int PAYMETHODID2 = 48850;

    // 不包含发票的内部类型列表
    private static final List<String> INTERNALTYPElIST = Arrays.asList("advance");

    // 预算单据类型
    private static final List<String> PREPAYMENT_TYPE_LIST = Arrays.asList("RQ01","RQ03","T018","RQ08");

    // 头行摘要统一的单据类型
    private static final List<String> UNION_DESC_TYPE_LIST = Arrays.asList("T017", "T018");

    // 支持超网代发branch
    private static final List<String> OVER_BRANCH_LIST = Arrays.asList("130","105","326","306","446","668");

    // 走交行代发branch
    private static final List<String> BANKCOMM_BRANCH_LIST = Arrays.asList("346","506");
    private static final List<String> BANKCOMM_BRANCH_BANKNAME_LIST = Arrays.asList("交通银行", "交通银行股份有限公司", "交通银行湖北省分行");

    // 支付结果错误时status=3的OPTSTU集合
    private static final List<String> OPT_STU_ERROR_LIST = Arrays.asList("AE","AF","AQ","BE","BF","BR","CD","CF","CL","GC","OD");
    // 支付结果成功时status=3的OPTSTU集合
    private static final List<String> OPT_STU_SUCCESS_LIST = Arrays.asList("DB","DP","GD","GF");

    List<String> branchCodes = new ArrayList<>();// PG项目限定子公司列表


    private YofcMapper yofcMapper;

    @Autowired
    private MqMapper mqMapper;
    @Autowired
    private YjOpenApiService yjOpenApiService;

    private static Logger logger = LoggerFactory.getLogger(YofcServiceImpl.class);

    @Autowired
    public YofcServiceImpl(YofcMapper yofcMapper) {
        this.yofcMapper = yofcMapper;
    }

    public YofcServiceImpl() {
    }

    @Override
    public JSONObject cbsPay(JSONObject jsonObject) {
        JSONObject callResult = JSONObject.parseObject(update(jsonObject, "cbsPay"));
        String payingData = callResult.getString("xmlStr");
        String documentNum = callResult.getString("documentNum");
        FndUserAccount fndBranchAccount = yofcMapper.getBranchAccountNumberById(jsonObject.getInteger("account_id"));
        JSONObject responseJsonObject = postAndHandleResult("", payingData);
        JSONObject errorJsonObject = responseJsonObject.getJSONObject("CBSERPPGK");
        JSONArray apDocumentIds = new JSONArray();
        apDocumentIds.add(jsonObject.getInteger("document_id"));
        return processResult(errorJsonObject, payingData, documentNum, apDocumentIds, fndBranchAccount.getBankAddress(), false, jsonObject.getString("refnbr"));
    }

    private JSONObject postAndHandleResult(String LOG_KEY, String payingData) {
        String result = postToServer(payingData);
        if (result == null) {
            return null;
        }
        JSONObject responseJsonObject;
        try {
            //将结果由GBK转为UTF-8
            String utf8String = getUTF8StringFromGBKString(result);
            String xmlData = utf8String.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", "")
                    .replace("<PGK>", "").replace("</PGK>", "")
                    .replace("<DATA>", "").replace("</DATA>", "\n")
                    .replace("]]>", "")
                    .replace("<![CDATA[<?xml version=\"1.0\" encoding =\"GBK\"?>", "");
            //将结果转为JSON格式
            org.json.JSONObject xmlJSONObj = XML.toJSONObject(xmlData);
            responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
        } catch (Exception e) {
            logger.error("{}解析返回数据data={}, 异常：{}", LOG_KEY, result, e.getMessage(), e);
            responseJsonObject = new JSONObject();
        }
        try {
            //每次查询都要记录到log表中
            JSONObject content = new JSONObject();
            content.put("paying_data", payingData);
            content.put("result_data", responseJsonObject);
            yofcMapper.normalInsertLog(16895, "yofc_bank_payment", content.toJSONString());
        } catch (Exception e) {
            logger.error("{}登记log信息异常：{}", LOG_KEY, e.getMessage(), e);
        }
        return responseJsonObject;
    }

    private JSONObject processResult(JSONObject jsonObject, String payingData, String documentNum, JSONArray apDocumentIds, String bankAccountCode, Boolean isBatch, String refnbr) {
        JSONObject ret = new JSONObject();
        ret.put("success", false);
        if (null == jsonObject) {
            ret.put("message", "接口报错，未获取到有效信息");
            ret.put("documentNum", documentNum);
            return ret;
        }
        JSONObject errorJsonObject = jsonObject.getJSONObject("APSALSAVZ");
        if(errorJsonObject == null) {
            JSONObject infoJsonObject = jsonObject.getJSONObject("INFO");
            if(infoJsonObject != null) {
                ret.put("message", infoJsonObject.getString("ERRMSG"));
            } else {
                ret.put("message", "cbs返回数据结构异常，无法解析错误信息");
            }
            ret.put("documentNum", documentNum);
            return ret;
        }

        String busNbr = errorJsonObject.getString("BUSNBR");

        if (null != busNbr && !StringUtils.isEmpty(busNbr)) {
            if (isBatch) {
                ret.put("success", cbsBatchExist(payingData));
            } else {
                ret.put("success", cbsExist(payingData));
            }
            for (Object o : apDocumentIds) {
                int apDocumentId = (int) o;
                Integer linkHeaderId = yofcMapper.getLinkBydocumentId(apDocumentId);
                yofcMapper.yofcUpdateColumn50New(busNbr, bankAccountCode, linkHeaderId);// 前序单据
                yofcMapper.yofcUpdatePayColumn50(busNbr, refnbr, bankAccountCode, apDocumentId);// 当前单据
            }
        } else {
            ret.put("message", errorJsonObject.getString("ERRMSG"));
            ret.put("documentNum", documentNum);
        }

        return ret;
    }

    @Override
    public String yofcSapPosted(JSONObject inputData) {
        LogCtx.setLogKey("长飞对私财务凭证==");
        logger.info("长飞对私财务凭证开始处理，入参={}", LogCtx.getLogKey(), inputData.toJSONString());
        String date = inputData.getString("date");
        JSONArray documentId = JSONArray.parseArray(inputData.getString("documentId"));
        String seriousNum = inputData.getString("seriousNum");
        String userId = inputData.getString("userId");
        JSONObject inputJson = new JSONObject();
        JSONArray dataJsonArrayC = new JSONArray();
        StringBuilder documentNum = new StringBuilder();
        String documentNumLog = null;
        Map<String, Object> batchNameMap = new HashMap<>();
        for (Object o : documentId) {
            logger.info("{}单据{}开始处理", LogCtx.getLogKey(), o);
            // 检查是否不需支付行
            Map claimMap = yofcMapper.getTypeByDocumentId(Integer.valueOf(o.toString()));
            String typeCode = claimMap.get("typeCode").toString();
            Integer headerId = (Integer)claimMap.get("headerId");
            String code = claimMap.get("documentNum").toString();
            String glStatus = (String)claimMap.get("glStatus");
            logger.info("{}单据{}开始处理", LogCtx.getLogKey(), code);
            // 是否重复处理
            if(!Arrays.asList("pending","error").contains(glStatus)) {
                logger.info("单据{}，对私凭证状态{}，忽略处理", code, glStatus);
                JSONObject ret = new JSONObject();
                ret.put("status", "success");
                ret.put("exception_level", 0);
                return ret.toJSONString();
            }
            // 检查数据
            List<FaPiaoLineInfo> LineInfos =  yofcMapper.yofcPostLineData(headerId.toString());
            List<String> invalidCostCenter = new ArrayList<>();
            for (FaPiaoLineInfo lineInfo : LineInfos) {
                if (!StrUtil.equals(lineInfo.getInternalType(), "advance") && lineInfo.getCostCenter() != null && !StrUtil.equals(lineInfo.getCostCenterEnabledFlag(), "Y")) {
                    invalidCostCenter.add(lineInfo.getCostCenter());
                }
            }
            if(!invalidCostCenter.isEmpty()) {
                logger.error("单据{}已失效行成本中心：{}", code, invalidCostCenter);
                updateGlMessage(Integer.valueOf(o.toString()), "error", "已失效行成本中心" + JSONUtil.toJsonStr(invalidCostCenter));
                continue;
            }
            //
            yofcMapper.updateGlStatus(Integer.valueOf(headerId), "generating");
            //
            Integer supplierId = (Integer)claimMap.get("supplierId");
            Integer branchId = (Integer)claimMap.get("branchId");
            // branch信息
            BranchInfo branchInfo = yofcMapper.getBranchById(branchId);
            //
            if(Arrays.asList("T001", "T002", "T003").contains(typeCode)) {
                Integer payMethod = yofcMapper.payMethodExists(headerId);
                if(payMethod == null) {
                    logger.warn("{}单据{}付款方式全为48849（不需支付）, 忽略", LogCtx.getLogKey(), code);
                    yofcMapper.updateGlStatus(headerId, "generated");
                    continue;
                }
            }
            //
            inputJson.put("documentId", o);
            inputJson.put("date", date);
            inputJson.put("serialNumber", seriousNum);
            inputJson.put("lastUser", userId);
            //
            String outputJson = update(inputJson, "yofc_voucher");
            outputJson = translate(branchInfo.getCurrencyCode(), outputJson);
            JSONObject getData = JSONObject.parseObject(outputJson);
            documentNum.append(getData.getString("documentNum"));
            JSONArray dataJsonArray = getData.getJSONArray("array");
            logger.info("{}单据{}科目数据封装结果={}", LogCtx.getLogKey(), code, dataJsonArray.toJSONString());
            // 补充科目处理
            JSONArray extArray = new JSONArray();
            boolean extFlag = true;
            for(int i = 0; i < dataJsonArray.size(); i++) {
                JSONObject dataJson = dataJsonArray.getJSONObject(i);
                String drCr = dataJson.getString("dr_cr");
                if(StringUtils.isEmpty(drCr)) {
                    continue;
                }
                // 是否PG项目
                FaPiaoLineInfo lineInfo = null;
                String[] columns = drCr.split("!!!");
                if(columns.length > 1 && NumberUtil.isNumber(columns[1])) {
                    String lineId = columns[1];
                    lineInfo = yofcMapper.yofcQueryLineByLineId(lineId);
                }
                if(!isPG(lineInfo)) {
                    continue;
                }
                dataJson.put("DOC_TYPE", typeCode);
                // 补充科目号
                if("dr".equals(columns[0])) {
                    String accountCodeDr = getPriPgAccountCode("dr", branchInfo, lineInfo);
                    if(accountCodeDr == null) {
                        logger.error("单据{}补充科目借方行数据校验失败={}", code, dataJson);
                        extFlag = false;
                        break;
                    }
                    dataJson.put("dr_cr", "");
                    JSONObject extJsonDr = JSONObject.parseObject(dataJson.toJSONString());
                    extJsonDr.put("account_code", accountCodeDr);
                    extArray.add(extJsonDr);
                    String accountCodeCr = getPriPgAccountCode("cr", branchInfo, lineInfo);
                    if(accountCodeCr == null) {
                        logger.error("单据{}补充科目贷方行数据校验失败={}", code, dataJson);
                        extFlag = false;
                        break;
                    }
                    JSONObject extJsonCr = JSONObject.parseObject(dataJson.toJSONString());
                    extJsonCr.put("account_code", accountCodeCr);
                    extJsonCr.put("line_amount", "-" + extJsonCr.getString("line_amount"));
                    extArray.add(extJsonCr);
                }
                dataJson.put("dr_cr", "");
            }
            logger.info("{}单据{}补充科目封装结果 extFlag={}, extArray={}", LogCtx.getLogKey(), code, extFlag, extArray.toJSONString());
            if(!extFlag) {
                continue;
            }
            dataJsonArray.addAll(extArray);
            //
            dataJsonArrayC.addAll(dataJsonArray);
            // 单据类型=保证金RQ08，增加补充字段
            if("RQ08".equals(typeCode)) {
                String columnJson = yofcMapper.yofcgetColumn54(headerId.toString());
                String column84 = StringUtils.isNotEmpty(columnJson) ?
                       JSON.parseObject(columnJson).getString("column84") : "";
                Map supplierMap = yofcMapper.yofcGetSupplier(supplierId);
                for(int i = 0; i < dataJsonArrayC.size(); i++) {
                    JSONObject dataJson = dataJsonArrayC.getJSONObject(i);
                    // 单据类型
                    dataJson.put("Bill_Type", typeCode);
                    // 记账员工号码
                    dataJson.put("Emp_Num", column84);
                    // 供应商名称
                    if(supplierMap != null) {
                        dataJson.put("Vendor_Name", supplierMap.get("supplierName"));
                    }
                }
            }
            //
            batchNameMap.put(o.toString(),((Map)dataJsonArray.get(0)).get("batch_name"));
            documentNumLog = getData.getString("documentNum");
        }
        boolean flag = false;
        // 调用外部接口
        String result = post2YofcPrivate(dataJsonArrayC, documentNum.toString());
        LogCtx.clearLogKey();
        return afterPost(result, documentId, true, batchNameMap, date);
    }

    /**
     * 调用发票老接口
     * @param data
     * @param documentNum
     * @return
     */
    private String post2YofcPrivate(JSONArray data, String documentNum) {
        //获取长飞token
        Map<String, String> tokenMap = getTokenMap(null);
        String result = "";
        //请求
        try {
            if(tokenMap!=null){
                for (Object o : data) {
                    ((Map)o).put("TOKEN",tokenMap.get("TOKEN"));
                }
                String postData = JSON.toJSONString(data,SerializerFeature.WriteMapNullValue);
                logger.info("{}请求长飞发票老接口，单据={}，入参={}", LogCtx.getLogKey(), documentNum, postData);
                result = voucherPostArray(data);
                logger.info("{}请求长飞发票老接口，单据={}，返参={}", LogCtx.getLogKey(), documentNum, result);
            }else {
                logger.info("{}获取长飞token失败，单据={}", LogCtx.getLogKey(), documentNum);
            }
        } catch (Exception e) {
            logger.error("{}请求长飞发票老接口异常，单据={}，error={}", LogCtx.getLogKey(), documentNum, e.getMessage(), e);
        }

        return result;
    }


    private String afterPost(String result, JSONArray documentId, boolean needUpdateVoucher, Map<String, Object> batchNameMap, String glDate) {
        JSONObject ret = new JSONObject();
        boolean flag = false;
        //判断这一条发过去了吗
        if (result != null && result.length() >= 1) {
            JSONObject resultJson = JSONObject.parseObject(result);
            String status = resultJson.getString("status");
            if ("SUCCESS".equals(status)) {
                flag = true;
            }
        }
        //如果都成功发送过去,对凭证的状态进行更新
        if (flag) {
            if (needUpdateVoucher) {
                if (null != batchNameMap){
                    for (String id : batchNameMap.keySet()) {
//                        JSONObject data = new JSONObject();
//                        data.put("documentId", id);
//                        data.put("voucherNumber", batchNameMap.get(id));
//                        update(data, "apichange");
                        String voucherNum = batchNameMap.get(id).toString();
                        ClaimHeader claimHeader = yofcMapper.yofcGetClaimHeaderByDocumentId(id);
                        voucherBack(claimHeader.getDocumentNum(), voucherNum, "generated", "",
                                DateUtil.parse(glDate, "yyyy-MM-dd").getTime(), null);
                    }
                }else {
                    for (Object o : documentId) {
                        JSONObject data = new JSONObject();
                        data.put("documentId", o);
                        update(data, "apichange");
                    }
                }
            }

            ret.put("status", "success");
            ret.put("exception_level", 0);
            return ret.toJSONString();
        } else {
            ret.put("status", "failure");
            ret.put("exception_level", 99);
            ret.put("message", "接口调用错误");
            for (String id : batchNameMap.keySet()) {
                updateGlMessage(Integer.valueOf(id), "error", result);
            }
            return ret.toJSONString();
        }
    }

    @Override
    public JSONArray sapVoucherPreview(int documentId, String date) {
        JSONObject inputJson = new JSONObject();
        inputJson.put("documentId", documentId);
        inputJson.put("date", date);
        String result = update(inputJson, "excl");
        JSONArray dataJsonArray = JSONArray.parseArray(result);
        logger.info("dataJsonArray: " + dataJsonArray);
        return dataJsonArray;
    }


    private String postToServer(String str) {
        // 报文头
        String head = "<?xml version='1.0' encoding='GBK'?><PGK><DATA><![CDATA[";
        // 密钥:与设置的一样
        //String key = KEY;
        String key = CBSKEY;
        // 校验码
        String checkCode = YofcUtils.GetCheckSumWithCRC32(key, str);
        logger.info("checkCode" + checkCode);
        // 报文尾
        String end = "]]></DATA><CHECKCODE>" + checkCode + "</CHECKCODE></PGK>";
        //整理数据
        String postData = head + str + end;
        logger.info("postToServer == 长飞 == postData：" + postData);
        String postResult = "";
        try {
            postResult = YofcUtils.httpPostString(CBSURL, null, postData,
                    "GBK", "GBK");
//            postResult = YofcUtils.httpPostString(POST_URL, null, postData,
//                    "GBK", "GBK");
        } catch (Exception e) {
            logger.error("postToServer == 长飞 == cbs post error: ", e);
        }
        logger.info("postToServer == 长飞 == postResult：" + postResult);

        return postResult;
    }

    public String update(JSONObject inputJson, String type) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        logger.info("长飞调用存储过程入参type={},inputJson={}", type, inputJson);
        FndQuery fndQuery = new FndQuery();
        fndQuery.setInput(inputJson.toJSONString());
        fndQuery.setCompanyId(16895);
        fndQuery.setType(type);
        // 更新凭证号
        if(inputJson.get("voucherNumber") != null) {
            yofcMapper.updateJournalNum(inputJson.getInteger("documentId"),
                    inputJson.get("voucherNumber").toString());
        }
        String outputJson = yofcMapper.yofcDataUpdate(fndQuery);
        logger.info("长飞调用存储过程出参type={},outputJson={}", type, outputJson);
        return outputJson;
    }

    private boolean cbsExist(String payingData) {
        logger.info("cbsExist ============== APPAYSAVZ SYCOMRETZ CBS支付");
        String searchXml =
                "<?xml version=\"1.0\" encoding =\"GBK\"?>" +
                        "<CBSERPPGK>" +
                        "<INFO><FUNNAM>ERPAYBUS</FUNNAM></INFO>" +
                        "<ERPAYBUSX>" +
                        "<CLTACC>" + getXmlKey("CLTACC", payingData) + "</CLTACC>" +
                        "<REVACC>" + getXmlKey("REVACC", payingData) + "</REVACC>" +
                        "<TRSAMT>" + getXmlKey("TRSAMT", payingData) + "</TRSAMT>" +
                        "<REFNBR>" + getXmlKey("REFNBR", payingData) + "</REFNBR>" +
                        "</ERPAYBUSX>" +
                        "</CBSERPPGK>";
        logger.info("cbsExist searchXml: " + searchXml);
        String result = postToServer(searchXml);
        //将结果由GBK转为UTF-8
        String utf8String = getUTF8StringFromGBKString(result);
        String xmlData = utf8String.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", "")
                .replace("<PGK>", "").replace("</PGK>", "")
                .replace("<DATA>", "").replace("</DATA>", "\n")
                .replace("]]>", "")
                .replace("<![CDATA[<?xml version=\"1.0\" encoding =\"GBK\"?>", "");
        //将结果转为JSON格式
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(xmlData);
        JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
        logger.info("responseJsonObject_2: " + responseJsonObject.toJSONString());
        JSONObject errorJsonObject = responseJsonObject.getJSONObject("CBSERPPGK");
        //APPAYSAVZ标识的返回code
        String apErrCode = errorJsonObject.getJSONObject("APPAYSAVZ").getString("ERRCOD");
        //SYCOMRETZ标识的返回code
        String syErrCode = errorJsonObject.getJSONObject("SYCOMRETZ").getString("ERRCOD");
        boolean ap = "0000000".equals(apErrCode);
        boolean sy = "0000000".equals(syErrCode);
        return ap && sy;
    }

    private boolean checkCbsExist(String payingData) {
        logger.info("checkCbsExist ============== ERPAYBUSZ CBS支付");
        String searchXml =
                "<?xml version=\"1.0\" encoding =\"GBK\"?>" +
                        "<CBSERPPGK>" +
                        "<INFO><FUNNAM>ERPAYBUS</FUNNAM></INFO>" +
                        "<ERPAYBUSX>" +
                        "<CLTACC>" + getXmlKey("CLTACC", payingData) + "</CLTACC>" +
                        "<REVACC>" + getXmlKey("REVACC", payingData) + "</REVACC>" +
                        "<TRSAMT>" + getXmlKey("TRSAMT", payingData) + "</TRSAMT>" +
                        "<REFNBR>" + getXmlKey("REFNBR", payingData) + "</REFNBR>" +
                        "</ERPAYBUSX>" +
                        "</CBSERPPGK>";
        logger.info("checkCbsExist searchXml: " + searchXml);

        // 报文头
        String head = "<?xml version='1.0' encoding='GBK'?><PGK><DATA><![CDATA[";
        // 密钥:与设置的一样
        String key = CBSKEY;
        // 校验码
        String checkCode = YofcUtils.GetCheckSumWithCRC32(key, searchXml);
        logger.info("checkCode: " + checkCode);
        // 报文尾
        String end = "]]></DATA><CHECKCODE>" + checkCode + "</CHECKCODE></PGK>";
        //整理数据
        String postData = head + searchXml + end;
        logger.info("postData: " + postData);
        String postResult = "";
        try {
            postResult = YofcUtils.httpPostString(CBSURL, null, postData,
                    "GBK", "GBK");
        } catch (Exception e) {
            logger.error("checkCbsExist === error：", e);
        }
        System.err.println(postResult);

        //将结果由GBK转为UTF-8
        String utf8String = getUTF8StringFromGBKString(postResult);
        String xmlData = utf8String.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", "")
                .replace("<PGK>", "").replace("</PGK>", "")
                .replace("<DATA>", "").replace("</DATA>", "\n")
                .replace("]]>", "")
                .replace("<![CDATA[<?xml version=\"1.0\" encoding =\"GBK\"?>", "");
        //将结果转为JSON格式
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(xmlData);
        JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
        System.out.println("responseJsonObject_2: " + responseJsonObject.toJSONString());
        JSONObject errorJsonObject = responseJsonObject.getJSONObject("CBSERPPGK")
                .getJSONObject("ERPAYBUSZ");
        String errorCode = errorJsonObject.getString("ERRCOD");
        return "0000000".equals(errorCode);
    }

    private boolean cbsBatchExist(String payingData) {
        logger.info("cbsBatchExist ============== ERAGNBUSZ CBS支付");
        String searchXml =
                "<?xml version=\"1.0\" encoding =\"GBK\"?>" +
                        "<CBSERPPGK>" +
                        "<INFO><FUNNAM>ERAGNBUS</FUNNAM></INFO>" +
                        "<ERAGNBUSX>" +
                        "<CLTACC>" + getXmlKey("CLTACC", payingData) + "</CLTACC>" +
                        "<OPRTYP>" + getXmlKey("OPRTYP", payingData) + "</OPRTYP>" +
                        "<TRSAMT>" + getXmlKey("TRSAMT", payingData) + "</TRSAMT>" +
                        "<REFNBR>" + getXmlKey("REFNBR", payingData) + "</REFNBR>" +
                        "</ERAGNBUSX>" +
                        "</CBSERPPGK>";
        System.out.println("searchXml:");
        System.out.println(searchXml);
        String result = postToServer(searchXml);
        //将结果由GBK转为UTF-8
        String utf8String = getUTF8StringFromGBKString(result);
        String xmlData = utf8String.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", "")
                .replace("<PGK>", "").replace("</PGK>", "")
                .replace("<DATA>", "").replace("</DATA>", "\n")
                .replace("]]>", "")
                .replace("<![CDATA[<?xml version=\"1.0\" encoding =\"GBK\"?>", "");
        //将结果转为JSON格式
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(xmlData);
        JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
        System.out.println("responseJsonObject_2: " + responseJsonObject.toJSONString());
        JSONObject errorJsonObject = responseJsonObject.getJSONObject("CBSERPPGK")
                .getJSONObject("ERAGNBUSZ");
        String errorCode = errorJsonObject.getString("ERRCOD");
        return "0000000".equals(errorCode);
    }

    /**
     * 根据CBS业务流水号和单据号查询付款状态
     */
    public String cbsStatus(JSONArray array) {
        //数据拼接
        String head = "<?xml version=\"1.0\" encoding =\"GBK\"?>" +
                "<CBSERPPGK>" +
                "<INFO>" +
                "<FUNNAM>ERPAYSTA</FUNNAM>" +
                "</INFO>";
        StringBuilder body = new StringBuilder();
        String tail = "</CBSERPPGK>";
        for (Object o : array) {
            JSONObject object = (JSONObject) o;
            String BUSNBR = object.getString("BUSNBR");
            String REFNBR = object.getString("REFNBR");
            String single = "<ERPAYSTAX>" +
                    "<REFNBR>" + REFNBR + "</REFNBR>" +
                    "<BUSNBR>" + BUSNBR + "</BUSNBR>" +
                    "</ERPAYSTAX>";
            body.append(single);
        }

        String postXml = head + body.toString() + tail;
        System.out.println("cbsStatus: " + postXml);
        //发送请求
        String result = postToServer(postXml);
        //将结果由GBK转为UTF-8
        JSONObject responseJsonObject = getResponseResult(result, "utf8String : ");
        System.out.println("responseJsonObject" + responseJsonObject.toJSONString());
        Object jsonResult = responseJsonObject.getJSONObject("CBSERPPGK").get("ERPAYSTAZ");
        if (jsonResult instanceof JSONObject) {
            JSONObject jsonObject = (JSONObject) jsonResult;
            JSONArray jsonArray = new JSONArray();
            jsonArray.add(jsonObject);
            return jsonArray.toJSONString();
        } else {
            JSONArray jsonArray = (JSONArray) jsonResult;
            return jsonArray.toJSONString();
        }
    }

    public String cbsBatchStatus(JSONArray array) {
        //数据拼接
        String head = "<?xml version=\"1.0\" encoding =\"GBK\"?>" +
                "<CBSERPPGK>" +
                "<INFO>" +
                "<FUNNAM>ERAGNSTA</FUNNAM>" +
                "</INFO>";
        StringBuilder body = new StringBuilder();
        String tail = "</CBSERPPGK>";
        for (Object o : array) {
            JSONObject object = (JSONObject) o;
            String BUSNBR = object.getString("BUSNBR");
            String single = "<ERAGNSTAX>" +
                    "<BUSNBR>" + BUSNBR + "</BUSNBR>" +
                    "</ERAGNSTAX>";
            body.append(single);
        }

        String postXml = head + body.toString() + tail;
        System.out.println("cbsBatchStatus: " + postXml);
        //发送请求
        String result = postToServer(postXml);
        //将结果由GBK转为UTF-8
        JSONObject responseJsonObject = getResponseResult(result, "utf8String: cbsBatchStatus: ");
        System.out.println("responseJsonObject" + responseJsonObject.toJSONString());
        return responseJsonObject.toJSONString();

    }

    private static String getXmlKey(String key, String str) {
        String regexp = "<" + key + ">(.*?)</" + key + ">";
        Pattern pattern = Pattern.compile(regexp);
        Matcher m = pattern.matcher(str);
        if (m.find()) {
            return m.group(1);
        }
        return "";
    }

    private String voucherPost(JSONObject jsonObject) throws Exception {
        String postResult = "";
        try {
            postResult = YofcUtils.httpPostString(VOUCHERNEWURL, null, jsonObject.toJSONString(),
                    "UTF-8", "UTF-8");
        } catch (Exception e) {
            logger.error("voucherPost === error：", e);
            throw new Exception(e.getMessage());
        }
        return postResult;
    }

    private String voucherPostArray(JSONArray jsonArray) throws Exception {
        String postResult = "";
        try {
            postResult = YofcUtils.httpPostString(VOUCHERNEWURL, null, jsonArray.toJSONString(),
                    "UTF-8", "UTF-8");
        } catch (Exception e) {
            logger.error("voucherPostArray === error：", e);
            throw new Exception(e.getMessage());
        }
        return postResult;
    }

    private String concatAPPAYSAVX(String REFNBR, String CLTACC, BigDecimal TRSAMT, String TRSUSE, String CHKFLG, String TRSINF, String RACINF) {
        return "<APPAYSAVX>" +
               "<OPRTYP>203</OPRTYP>" +
               "<OPRMOD>3</OPRMOD>" +
               "<PAYTYP>2</PAYTYP>" +
               "<PAYSON>N</PAYSON>" +
               "<TRSINF>" + TRSINF + "</TRSINF>" +
               "<REFNBR>" + REFNBR + "</REFNBR>" +
               "<CLTACC>" + CLTACC + "</CLTACC>" +
               "<TRSAMT>" + TRSAMT.toString() + "</TRSAMT>" +
               "<TRSUSE>" + TRSUSE + "</TRSUSE>" +
               "<CHKFLG>" + CHKFLG + "</CHKFLG>" + // 代发渠道
               "<RACINF>" + RACINF + "</RACINF>" + // 代发交易类型
               "</APPAYSAVX>";
    }

    private String concatAPSALSAVX(String REFNBR, String documentNum, BigDecimal totalPayAmount, String REVACC, String REVNAM, String receiveBankName, String CADFLG) {
        return "<APSALSAVX>" +
                "<BNKTYP>" + "" + "</BNKTYP>" +
                "<BNKFLG>" + "" + "</BNKFLG>" +
                "<REFNBR>" + REFNBR + "</REFNBR>" +
                "<EXTTX1>" + documentNum + "</EXTTX1>" +
                "<NUSAGE>" + documentNum + "</NUSAGE>" +
                "<REVACC>" + REVACC + "</REVACC>" +
                "<REVNAM>" + REVNAM + "</REVNAM>" +
                "<WAGMNY>" + totalPayAmount.toString() + "</WAGMNY>" +
                "<CADFLG>" + CADFLG + "</CADFLG>" + // 卡折标志
                "</APSALSAVX>";
    }

    @Override
    public JSONArray cbsPayStart(JSONObject inputJson, int userId) throws Exception {
        String LOG_KEY = "长飞cbs支付==";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYYMMddHHmmss");
        JSONArray headerIdList = inputJson.getJSONArray("header_id");
        JSONArray documentIdList = new JSONArray();
        int accountId = inputJson.getInteger("account_id");
        JSONArray littleArray = new JSONArray();
        JSONArray toBArray = new JSONArray();
        List<Integer> toWaiArray = new ArrayList<>();
        //金额为0的单据直接变为已支付状态
        Iterator<Object> iterator = headerIdList.iterator();
        while (iterator.hasNext()) {
            int headerId = (int) iterator.next();
            Integer documentId = yofcMapper.getDocumentIdByHeaderId(headerId);
            documentIdList.add(documentId);
            TypeNum typeNum = yofcMapper.getTypeCodeByDocumentId(documentId);
            String typeCode = typeNum.getTypeCode();
            //当前单据支付信息
            YofcPayElement payInfo = yofcMapper.getPayInfo(documentId);
            if (PRIVATEPAYTYPE.equals(typeCode) && (new BigDecimal(0)).compareTo(payInfo.getTotalPayAmount()) == 0) {
                //对私
                yofcMapper.updatePayStatus(String.valueOf(documentId), "paid");
                //关闭该单据和前序单据
                yofcMapper.closeClaim(String.valueOf(documentId));

                //对私业务关闭前序单据（报销单-确认单-支付单）
                //确认单单据id
                String paymentHeaderId = yofcMapper.getLinkHeaderId(String.valueOf(documentId));
                //报销单单据id
                String linkHeaderId = yofcMapper.getLinkHeaderId(paymentHeaderId);
                //确认单单据信息
                PayInfo paymentInfo = yofcMapper.getPaymentInfo(paymentHeaderId);
                //后续已关闭的支付单金额总计
                BigDecimal sumTotalPayAmount = yofcMapper.getSumTotalPayAmount(paymentHeaderId);
                List<String> type = Arrays.asList("request_advance", "request_travel_advance", "request_supplier");
                //校验金额，若所有支付单都已关闭，则关闭前序的确认单
                if (type.contains(paymentInfo.getInternalType())) {
                    if (paymentInfo.getAdvanceAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭确认单
                        yofcMapper.closeClaim(paymentHeaderId);
                        //关闭前序的报销单
                        yofcMapper.closeClaim(linkHeaderId);
                    }
                } else {
                    if (paymentInfo.getTotalPayAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭确认单
                        yofcMapper.closeClaim(paymentHeaderId);
                        //关闭前序的报销单
                        yofcMapper.closeClaim(linkHeaderId);
                    }
                }
                iterator.remove();
            } else if (PUBLICPAYTYPE.equals(typeCode) && (new BigDecimal(0)).compareTo(payInfo.getTotalPayAmount()) == 0) {
                //对公
                yofcMapper.updatePayStatus(String.valueOf(documentId), "paid");
                //关闭该单据和前序单据
                yofcMapper.closeClaim(String.valueOf(documentId));

                //对公业务关闭前序单据（付款付-支付单）
                //前序付款单单据id
                String paymentHeaderId = yofcMapper.getLinkHeaderId(String.valueOf(documentId));
                //付款单单据信息
                PayInfo paymentInfo = yofcMapper.getPaymentInfo(paymentHeaderId);
                //后续已关闭的支付单金额总计
                BigDecimal sumTotalPayAmount = yofcMapper.getSumTotalPayAmount(paymentHeaderId);
                List<String> type = Arrays.asList("request_advance", "request_travel_advance", "request_supplier");
                //校验金额，若所有支付单都已关闭，则关闭前序的付款单
                if (type.contains(paymentInfo.getInternalType())) {
                    if (paymentInfo.getAdvanceAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭付款单
                        yofcMapper.closeClaim(paymentHeaderId);
                    }
                } else {
                    if (paymentInfo.getTotalPayAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭付款单
                        yofcMapper.closeClaim(paymentHeaderId);
                    }
                }
                iterator.remove();
            }
        }
        logger.info("{}document_id={}", LOG_KEY, documentIdList);
        inputJson.put("document_id", documentIdList);

        FndUserAccount branchAccount = yofcMapper.getBranchAccountNumberById(accountId);
        if(null == branchAccount || null ==branchAccount.getAccountNumber() || null == branchAccount.getBankCode()){
            JSONObject error = new JSONObject();
            error.put("success",false);
            error.put("message", "付款账号不存在所有单据未发送");
            littleArray.add(error);
            //处理支付结果
            updatePayStatusByDocumentIds(inputJson, "付款账号不存在所有单据未发送");
            return littleArray;
        }
        // TRSINF字段值列表映射
        Map<String, String> trsinfMap = new HashMap<>();
        List<FndLovValue> lovValues = yofcMapper.yofcLovValues("TRSINF BCM");
        if(!CollUtil.isEmpty(lovValues)) {
            trsinfMap = lovValues.stream().collect(Collectors.toMap(FndLovValue::getValueCode, FndLovValue::getValueMeaning));
        }

        for (Object o : documentIdList) {
            int documentId = (int) o;
            TypeNum typeNum = yofcMapper.getTypeCodeByDocumentId(documentId);
            String typeCode = typeNum.getTypeCode();
            if (PRIVATEPAYTYPE.equals(typeCode)) {
                toBArray.add(documentId);
            } else if (PUBLICPAYTYPE.equals(typeCode)) {
                toWaiArray.add(documentId);
            } else {
                JSONObject error = new JSONObject();
                error.put("success",false);
                error.put("documentNum",typeNum.getDocumentNum());
                error.put("message", "请使用线下支付，所有单据未发送");
                littleArray.add(error);
                //处理支付结果
                updatePayStatusByDocumentIds(inputJson, "请使用线下支付，所有单据未发送");
                return littleArray;
            }
        }
        if(toBArray.size()>0 && toWaiArray.size()>0){
            JSONObject error = new JSONObject();
            error.put("success",false);
            error.put("documentNum","批量调用失败");
            error.put("message", "请选择同一种付款单据类型，所有单据未发送");
            littleArray.add(error);
            //处理支付结果
            updatePayStatusByDocumentIds(inputJson, "请选择同一种付款单据类型，所有单据未发送");
            return littleArray;
        }

        //对公处理
        if (handleWaiData(simpleDateFormat, branchAccount.getBankSwiftId(), littleArray, toWaiArray, branchAccount)) {
            //处理支付结果
            writeBack(inputJson, littleArray);
            return littleArray;
        }

        if(toBArray.size()>0){
            String userColumn1 = yofcMapper.getUserIdentityColumn1(userId);
            String nowDatetime = DateTimeFormatter.ofPattern("yyyyMMddHHmmss").format(LocalDateTime.now());
            String REFNBR = "DF" + nowDatetime;
            String TRSUSE = userColumn1 + "-" + nowDatetime;
            BigDecimal allTotalPayAmount = new BigDecimal(0);
            String postData = "<CBSERPPGK>\n" +
                    "    <INFO>\n" +
                    "        <FUNNAM>ERAGNOPR</FUNNAM>\n" +
                    "    </INFO>";
            String postDataContent = "";
            String CHKFLG = "";// 代发渠道
            Set<String> branchSet = new HashSet<>();
            Set<String> branchBankNameSet = new HashSet<>();
            for (Object o : toBArray) {
                int documentId = (int) o;
                //前序单据支付信息
                YofcPayElement yofcPayElement = yofcMapper.getDocumentPayInfo2(documentId);
                //当前单据支付信息
                YofcPayElement payInfo = yofcMapper.getPayInfo(documentId);
                String transSerialNum =  yofcMapper.getTransSerialNum(documentId);
                if (null == yofcPayElement) {
                    JSONObject error = new JSONObject();
                    error.put("success",false);
                    error.put("documentNum", yofcPayElement.getDocumentNum());
                    error.put("message", "单据未找到，所有对私单据未发送");
                    littleArray.add(error);
                    //处理支付结果
                    updatePayStatusByDocumentIds(inputJson, "单据未找到，所有对私单据未发送");
                    return littleArray;
                }
                if (null != transSerialNum) {
                    JSONObject error = new JSONObject();
                    error.put("success",false);
                    error.put("documentNum", yofcPayElement.getDocumentNum());
                    error.put("message", "该单据已推送，所有对私单据未发送");
                    littleArray.add(error);
                    //处理支付结果
                    updatePayStatusByDocumentIds(inputJson, "该单据已推送，所有对私单据未发送");
                    return littleArray;
                }
                BigDecimal totalPayAmount = payInfo.getTotalPayAmount();
                String documentNum = yofcPayElement.getDocumentNum();
                // 可能会拆单的前序单据
                if(StrUtil.equals(yofcPayElement.getHeaderTypeCode(), "T006")) {
                    documentNum = payInfo.getDocumentNum();
                }
                String receiveAccountNumber = payInfo.getReceiveAccountNumber();
                String receiveAccountName = payInfo.getReceiveAccountName();
                String receiveBankName = payInfo.getReceiveBankName();
                // 支付目标是外部人员
                if(StrUtil.isEmpty(receiveAccountName)) {
                    receiveAccountNumber = payInfo.getExternalReceiveAccountNumber();
                    receiveAccountName = payInfo.getExternalReceiveAccountName();
                    receiveBankName = payInfo.getExternalReceiveBankName();
                }
                if (null == receiveAccountNumber) {
                    JSONObject error = new JSONObject();
                    error.put("success",false);
                    error.put("documentNum", documentNum);
                    error.put("message", "收款账号未找到，所有对私单据未发送");
                    littleArray.add(error);
                    //处理支付结果
                    updatePayStatusByDocumentIds(inputJson, "收款账号未找到，所有对私单据未发送");
                    return littleArray;
                }
                if (null == receiveAccountName) {
                    JSONObject error = new JSONObject();
                    error.put("success",false);
                    error.put("documentNum", documentNum);
                    error.put("message", "收款账户未找到，所有对私单据未发送");
                    littleArray.add(error);
                    //处理支付结果
                    updatePayStatusByDocumentIds(inputJson, "收款账户未找到，所有对私单据未发送");
                    return littleArray;
                }
                allTotalPayAmount = allTotalPayAmount.add(totalPayAmount);
                if(OVER_BRANCH_LIST.contains(payInfo.getDepartmentCode())) {
                    CHKFLG = "B";// 超网代发 todo 存在一笔超网代发则全部走超网代发
                }
                branchSet.add(payInfo.getDepartmentCode());
                branchBankNameSet.add(payInfo.getBranchBankName());
                String CADFLG = "";
                String branch = payInfo.getDepartmentCode();
                if(BANKCOMM_BRANCH_LIST.contains(branch)) {
                    CADFLG = "01";
                }
                postDataContent += concatAPSALSAVX(REFNBR, documentNum, totalPayAmount, receiveAccountNumber, receiveAccountName, receiveBankName, CADFLG);
            }
            if(branchSet.size() > 1) {
                JSONObject error = new JSONObject();
                error.put("success",false);
                error.put("message", "同一批次不允许有多家机构");
                littleArray.add(error);
                //处理支付结果
                updatePayStatusByDocumentIds(inputJson, "同一批次不允许有多家机构");
                return littleArray;
            }
            String TRSINF;
            String RACINF = "";
            String branch = branchSet.iterator().next();
            String branchBankName = branchBankNameSet.iterator().next();
            if(BANKCOMM_BRANCH_BANKNAME_LIST.contains(branchBankName)) {
                TRSUSE = "代发其它款项";
                RACINF = "S";
                CHKFLG = "A";
            }
            TRSINF = trsinfMap.get(branch);
            if(TRSINF == null) {
                TRSINF = "代发其他";
            }

            postData += concatAPPAYSAVX(REFNBR, branchAccount.getAccountNumber(), allTotalPayAmount, TRSUSE, CHKFLG, TRSINF, RACINF) + postDataContent +
                    "</CBSERPPGK>";
            logger.info("{}代发请求={}", LOG_KEY, postData);
            JSONObject responseJson = postAndHandleResult(LOG_KEY + "代发", postData);
            logger.info("{}代发响应={}", LOG_KEY, responseJson);
            if (responseJson == null) {
                List<String> documentIds = new ArrayList<>();
                for (Object o : toBArray) {
                    int documentId = (int) o;
                    JSONObject tmpObject = new JSONObject();
                    tmpObject.put("success", true);
                    tmpObject.put("documentId", documentId);
                    tmpObject.put("message", "未收到CBS回应,网络异常,不能确认CBS是否已收到支付信息");
                    littleArray.add(tmpObject);
                    documentIds.add(String.valueOf(documentId));
                }
                yofcMapper.updatePayStatusByDocumentIds(documentIds, "paying", "未收到CBS回应,网络异常,不能确认CBS是否已收到支付信息");
            } else {
                JSONObject errorJsonObject = responseJson.getJSONObject("CBSERPPGK");
                JSONObject handleResult = processResult(errorJsonObject, postData, "代发批量调用失败", toBArray, branchAccount.getBankAddress(), true, REFNBR);
                Boolean sucess = Optional.ofNullable(handleResult.getBoolean("success"))
                                 .orElse(Boolean.FALSE);
                if (sucess) {
                    List<String> documentIds = Lists.newArrayList();
                    for (Object o : toBArray) {
                        int documentId = (int) o;
                        JSONObject tmpObject = new JSONObject();
                        tmpObject.put("success", true);
                        tmpObject.put("documentId", documentId);
                        littleArray.add(tmpObject);
                        documentIds.add(String.valueOf(documentId));
                    }
                    // 更新单据头上column44
//                    yofcMapper.updateColumnByDocumentIds(documentIds, branchAccount.getBankAddress());
                } else {
                    littleArray.add(handleResult);
                    updatePayStatusByDocumentIds(inputJson, handleResult.getString("message"));
                }
            }
        }
        //处理支付结果
        writeBack(inputJson, littleArray);
        return littleArray;
    }

    /**
     * 根据documentId批量更新单据支付状态
     * @param inputJson
     * @param message
     */
    private void updatePayStatusByDocumentIds(JSONObject inputJson, String message) {
        List<String> documentIds = JSONObject.parseArray(inputJson.get("document_id").toString(), String.class);
        MqQueue mqQueue = new MqQueue();
        mqQueue.setId(inputJson.getIntValue("mqId"));
        mqQueue.setStatus(3);
        logger.info("updatePayStatusByDocumentIds === documentIds：" + documentIds);
        yofcMapper.updatePayStatusByDocumentIds(documentIds, "error", message);
        mqMapper.updateMqStatus(mqQueue);
    }

    /**
     * CBS支付修改单据支付状态和回写message
     * @param inputJson
     * @param littleArray
     */
    private void writeBack(JSONObject inputJson, JSONArray littleArray) {
        for (int i = 0; i < littleArray.size(); i++) {
            JSONObject jsonObject = littleArray.getJSONObject(i);
            String documentNum = jsonObject.getString("documentNum");
            String message = jsonObject.getString("message");
            boolean flag = (boolean) jsonObject.get("success");
            MqQueue mqQueue = new MqQueue();
            mqQueue.setId(inputJson.getIntValue("mqId"));
            if (!flag) {
                logger.info("cbsPayStart ====== 支付失败的单据：" + documentNum);
                yofcMapper.updatePayStatusByDocumentNum(documentNum, "error", message);
                mqQueue.setStatus(3);
                mqMapper.updateMqStatus(mqQueue);
            } else {
                mqQueue.setStatus(2);
                mqMapper.updateMqStatus(mqQueue);
            }
        }
    }

    private boolean handleWaiData(SimpleDateFormat simpleDateFormat, String bankAccountCode, JSONArray littleArray, List<Integer> toWaiArray, FndUserAccount branchAccount) {
        List<PayingData> payingDatas = new ArrayList<>();

        for (Integer documentId : toWaiArray) {
            String nowTime = simpleDateFormat.format(new Date());
            SupplierAccount supplierAccount = yofcMapper.yofcgetSupplierAccount(documentId);
            String documentNum = supplierAccount.getDocumentNum();
            //校验账户信息
            if (null == supplierAccount) {
                JSONObject error = new JSONObject();
                error.put("success", false);
                error.put("documentNum", documentNum);
                error.put("message", "该单据不存在");
                littleArray.add(error);
                continue;
            }
            if (null == supplierAccount.getBankCode()) {
                JSONObject error = new JSONObject();
                error.put("success", false);
                error.put("documentNum", documentNum);
                error.put("message", "该单据无银行代码");
                littleArray.add(error);
                continue;
            }
            if (null == supplierAccount.getColumn2()) {
                JSONObject error = new JSONObject();
                error.put("success", false);
                error.put("documentNum", documentNum);
                error.put("message", "该单据无所属客户号");
                littleArray.add(error);
                continue;
            }

            PayingData payingData = new PayingData();
            //获取发送信息
            String postData = getPostWaiData(branchAccount, nowTime, supplierAccount, payingData);

            payingData.setDocumentNum(supplierAccount.getDocumentNum());
            payingData.setPostData(postData);
            payingData.setDocumentId(documentId);
            payingDatas.add(payingData);
        }

        for (PayingData payingData : payingDatas) {
            String payingDataStr = payingData.getPostData();
            String documentNum = payingData.getDocumentNum();
            String refnbr = payingData.getRefnbr();

            String column45 = yofcMapper.yofcIsPostFuKuan(payingData.getDocumentId());
            if (null != column45){
                JSONObject error = new JSONObject();
                error.put("success",false);
                error.put("documentNum",  documentNum);
                error.put("message", "该单据已发送");
                littleArray.add(error);
                continue;
            }
            logger.info("CBS对公支付=====" + documentNum + ":入参" + payingDataStr);
            String postResult = getAPResult(payingDataStr);
            if (null == postResult) {
                JSONObject error = new JSONObject();
                error.put("success", true);
                error.put("documentNum", documentNum);
                error.put("message", "未收到CBS回应,网络异常,不能确认CBS是否已收到支付信息");
                littleArray.add(error);
                yofcMapper.updatePayStatusByDocumentNum(documentNum, "paying", "未收到CBS回应,网络异常,不能确认CBS是否已收到支付信息");
                continue;
            }

            logger.info("CBS对公支付=====" + documentNum + ":结果" + postResult);
            JSONObject errorJsonObject = null;
            try{
                JSONObject responseJsonObject = parseResult(payingData, postResult);
                errorJsonObject = responseJsonObject.getJSONObject("CBSERPPGK")
                        .getJSONObject("APPAYSAVZ");
            }catch (Exception e){
                logger.error("handleWaiData ==== error：", e);
                JSONObject error = new JSONObject();
                error.put("success",false);
                error.put("documentNum",  documentNum);
                error.put("message", "调用cbs接口异常:"+postResult);
                littleArray.add(error);
                continue;
            }
            Integer documentId = payingData.getDocumentId();
            JSONObject ret = new JSONObject();
            ret.put("success", false);
            if (null == errorJsonObject) {
                ret.put("message", "接口报错，未获取到有效信息");
                ret.put("documentNum", documentNum);
                littleArray.add(ret);
                continue;
            }
            String busNbr = errorJsonObject.getString("BUSNBR");

            if (StringUtils.isNotBlank(busNbr)) {
                boolean b = checkCbsExist(payingDataStr);
                ret.put("success", b);
                if(b){
                    SimpleDateFormat dayFormate = new SimpleDateFormat("yyyy-MM-dd");
                    yofcMapper.yofcUpdateColumn45(busNbr, bankAccountCode, documentId,refnbr,dayFormate.format(new Date()));
                }
            } else {
                ret.put("message", errorJsonObject.getString("ERRMSG"));
                ret.put("documentNum", documentNum);
            }

            ret.put("documentId", payingData.getDocumentId());
            littleArray.add(ret);
        }
        return false;
    }

    private JSONObject parseResult(PayingData payingData, String postResult) {
        //将结果由GBK转为UTF-8
        String utf8String = getUTF8StringFromGBKString(postResult);
        String xmlData = utf8String.replace("<?xml version=\"1.0\" encoding=\"GBK\"?>", "")
                .replace("<PGK>", "").replace("</PGK>", "")
                .replace("<DATA>", "").replace("</DATA>", "\n")
                .replace("]]>", "")
                .replace("<![CDATA[<?xml version=\"1.0\" encoding =\"GBK\"?>", "");
        //将结果转为JSON格式
        org.json.JSONObject xmlJSONObj = XML.toJSONObject(xmlData);
        JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
        System.out.println("responseJsonObject_1: " + responseJsonObject.toJSONString());
        try {
            //每次查询都要记录到log表中
            JSONObject content = new JSONObject();
            content.put("paying_data", payingData);
            content.put("result_data", responseJsonObject);
            yofcMapper.normalInsertLog(16895, "yofc_bank_payment", content.toJSONString());
        } catch (Exception e) {
            logger.error("parseResult === error：", e);
        }
        return responseJsonObject;
    }

    private String getAPResult(String payingDataStr) {
        // 报文头
        String head = "<?xml version='1.0' encoding='GBK'?><PGK><DATA><![CDATA[";
        // 密钥:与设置的一样
        String key = CBSKEY;
        // 校验码
        String checkCode = YofcUtils.GetCheckSumWithCRC32(key, payingDataStr);
        // 报文尾
        String end = "]]></DATA><CHECKCODE>" + checkCode + "</CHECKCODE></PGK>";
        //整理数据
        String postData = head + payingDataStr + end;
        logger.info("getAPResult ====== postData：" + postData);
        String postResult = "";
        try {
            postResult = YofcUtils.httpPostString(CBSURL, null, postData,
                    "GBK", "GBK");
        } catch (Exception e) {
            logger.error("getAPResult === error：", e);
            return null;
        }
        return postResult;
    }

    private String getPostWaiData(FndUserAccount branchAccount, String nowTime, SupplierAccount supplierAccount, PayingData payingData) {
        String BNKTYP = supplierAccount.getBankAddress();
        String REVCIT = supplierAccount.getCity();
        String REVPRV = supplierAccount.getState();
        if (StringUtils.isBlank(BNKTYP)) {
            BNKTYP = "";
        }
        if (StringUtils.isBlank(REVCIT)) {
            REVCIT = "";
        }
        if (StringUtils.isBlank(REVPRV)) {
            REVPRV = "";
        }

//        if (null != supplierAccount.getBankHqCode()  &&  null != supplierAccount.getBankName()) {
//            BNKTYP = yofcMapper.getAbbreviation(supplierAccount.getBankHqCode(),supplierAccount.getBankName());
//
//        }

        Integer CTYFLG = 1;
        if(null != supplierAccount.getCity() && supplierAccount.getCity().equals(branchAccount.getCity())){
            CTYFLG = 0;
        }
        String PAYSON = "N";
        String thirdBankCode = branchAccount.getBankCode().substring(0, 3);
        if(thirdBankCode.equals("104")){
            PAYSON = "Y";
        }
        String TRASTY = "";
        if(thirdBankCode.equals("105")){
            if(supplierAccount.getBankCode().substring(0,3).equals(thirdBankCode)){
                TRASTY  = "5";
            }else if(supplierAccount.getTotalPayAmount().compareTo(new BigDecimal("50000.00")) == 1){
                TRASTY = "3";
            }else {
                TRASTY = "2";
            }
        }
        String refnbr = supplierAccount.getDocumentNum()+nowTime;
        payingData.setRefnbr(refnbr);
        String preColumn47 = null;
        //前序单号
        String linkDocumentNum = null;
        //前序单据财务审批人
        String financeUser = null;
        if (null != supplierAccount.getLinkHeaderId()) {
            preColumn47 = yofcMapper.getColumn47(supplierAccount.getLinkHeaderId());
            UserInfo financeUserInfo = yofcMapper.getFinanceUser(supplierAccount.getLinkHeaderId());
            if(StrUtil.isNotEmpty(financeUserInfo.getColumn1())) {
                financeUser = financeUserInfo.getColumn1();
            } else {
                // 转换为拼音首字母缩写
                financeUser = new ChineseCharToEn().getAllFirstLetter(financeUserInfo.getFullName());
            }
            linkDocumentNum = yofcMapper.getdDocumentNumById(supplierAccount.getLinkHeaderId());
            linkDocumentNum = linkDocumentNum.substring(0, 1) + linkDocumentNum.substring(8);
        }
        if (StringUtils.isBlank(preColumn47)) {
            preColumn47 = "null";
        }

        return "<CBSERPPGK>\r\n"+
                "<INFO>\r\n" +
                "<FUNNAM>ERPAYSAV</FUNNAM>\r\n" +
                "</INFO>\r\n"+
                "<APPAYSAVX>\r\n" +
                "<RECNUM>1</RECNUM>\r\n" +
                "<OPRTYP>202</OPRTYP>\r\n" +
                "<BUSTYP>0</BUSTYP>\r\n" +
                "<REFNBR>" + refnbr + "</REFNBR>\r\n" +
                "<EPTDAT>" + "" + "</EPTDAT>\r\n" +
                "<EPTTIM>" + "" + "</EPTTIM>\r\n" +
                "<CLTACC>" + branchAccount.getAccountNumber() + "</CLTACC>\r\n" +
                "<CLTNBR>" + supplierAccount.getColumn2() + "</CLTNBR>\r\n" +
                "<TRSAMT>" + supplierAccount.getTotalPayAmount() + "</TRSAMT>\r\n" +
                "<CCYNBR>" + "10" + "</CCYNBR>\r\n" +
                "<TRSUSE>" + preColumn47 + financeUser + linkDocumentNum + "</TRSUSE>\r\n" +
                "<EXTTX1>" + supplierAccount.getDocumentNum()+nowTime.substring(8,14) + "</EXTTX1>\r\n" +
                "<INNACC>" + "" + "</INNACC>\r\n" +
                "<REVACC>" + supplierAccount.getAccountNumber() + "</REVACC>\r\n" +
                "<REVBNK>" +  supplierAccount.getBankBranch()+ "</REVBNK>\r\n" +
                "<REVNAM>" +  supplierAccount.getAccountName()+ "</REVNAM>\r\n" +
                "<REVPRV>" + REVPRV + "</REVPRV>\r\n" +
                "<REVCIT>" + REVCIT + "</REVCIT>\r\n" +
                "<BNKTYP>" + BNKTYP + "</BNKTYP>\r\n" +
                "<BNKPRM>" + "" + "</BNKPRM>\r\n" +
                "<BRDNBR>" + supplierAccount.getBankCode() + "</BRDNBR>\r\n" +
                "<OPRMOD>" + "3" + "</OPRMOD>\r\n" +
                "<PAYTYP>" + "2" + "</PAYTYP>\r\n" +
                "<COPFLG>" + "2" + "</COPFLG>\r\n" +
                "<CTYFLG>" + CTYFLG + "</CTYFLG>\r\n" +
                "<PAYSON>" + PAYSON + "</PAYSON>\r\n" +
                "<TRASTY>" + TRASTY + "</TRASTY>\r\n" +
                "</APPAYSAVX>\r\n"+
                "</CBSERPPGK>";
    }

    @Override
    public List<Map<String, Object>> getPaymentVouchers(List<Integer> headerIds, int userId, String date, int number) {
        //生成付款凭证人员的column1
        String operatorIdentity = yofcMapper.getUserIdentityColumn1(userId);

        String yearMonthDay = date.replaceAll("-", "");
        String periodName = date.substring(0, 7);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("YYYY-MM-dd");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));

        // List<YofcPaymentVoucher> vouchers = yofcMapper.getPaymentVouchers(headerIds);
        //查询REQ单据数据
        List<YofcPaymentVoucher> vouchers = yofcMapper.getPaymentVouchersNew(headerIds);
        List<Map<String, Object>> erpVouchers = new ArrayList<>();
        int drLineNumber = 1;
        int crLineNumber = 1;
        Map<String, Map<String, Object>> summaryCr = new HashMap<>();
        for (YofcPaymentVoucher voucher : vouchers) {
//            if (null != voucher.getDocumentNum()){
//              String frontDocumentNum =  yofcMapper.getFrontDocumentNum(voucher.getDocumentNum());
//              if (null != frontDocumentNum){
//                  voucher.setDocumentNum(frontDocumentNum);
//              }
//            }
            //查询PAY单据headerId
            PayInfo payInfo = yofcMapper.getNextHeaderId(voucher.getHeaderId());
            Integer payHeaderId = payInfo.getHeaderId();
            //查询支付账号信息
            FndUserAccount fndUserAccount = yofcMapper.getBranchAccountNumberById(payInfo.getBankAccountId());
            //取PAY单据第一行的借方科目代码
            String drAccountCode = yofcMapper.getLineFirstDrAccount(payHeaderId);
            //币种
            String currencyCode = Optional.ofNullable(payInfo.getCurrencyCode()).orElse("CNY");
            BranchInfo branchInfo = yofcMapper.getBranchByCode(voucher.getOrgId());
            //dr
            Map<String, Object> drLine = new HashMap<>();
            drLine.put("source_doc_num", voucher.getDocumentNum());
            drLine.put("org_id", voucher.getOrgId());
            setCommonValues(drLine, operatorIdentity, yearMonthDay, periodName, number, voucher.getTypeCode());
            drLine.put("currency_code", currencyCode);
            drLine.put("line_num", drLineNumber);
            String accountCode = voucher.getFdColumn4() + "." + voucher.getFdColumn5() + "." + drAccountCode
                    + ".00.00000.00000";
            drLine.put("account_code", accountCode);
            drLine.put("line_amount", voucher.getLineAmount());
            String lineDescription;
            if ("T001".equals(voucher.getTypeCode())) {
                lineDescription = voucher.getChargeUserName() + simpleDateFormat.format(voucher.getStartDate())
                        + "到" + simpleDateFormat.format(voucher.getEndDate()) + voucher.getCityName() + "差旅费付款";
            } else {
                lineDescription  = voucher.getChargeUserName() + "费用报销付款";
            }
            drLine.put("line_description", lineDescription);
            erpVouchers.add(translate(branchInfo.getCurrencyCode(), drLine));
            drLineNumber++;

            //cr
            Map<String, Object> crLine = new HashMap<>();
            if (StringUtils.isNotEmpty(voucher.getPipelineNumber())) {
                if (summaryCr.containsKey(voucher.getPipelineNumber())) {
                    crLine = summaryCr.get(voucher.getPipelineNumber());
                    crLine.put("line_amount", ((BigDecimal) crLine.get("line_amount")).add(voucher.getLineAmount().negate()));
                    continue;
                }
                crLine.put("source_doc_num", voucher.getPipelineNumber());
            } else {
                crLine.put("source_doc_num", voucher.getDocumentNum());
            }


            crLine.put("org_id", voucher.getOrgId());
            setCommonValues(crLine, operatorIdentity, yearMonthDay, periodName, number, voucher.getTypeCode());
            crLine.put("currency_code", currencyCode);
            // 贷方科目从值列表映射
//            List<String> crAccountCodes = yofcMapper.yofcLovValueMeaning("DSXXZF", fndUserAccount.getAccountNumber());
            crLine.put("account_code", fndUserAccount.getBankAddress());//

            crLine.put("line_amount", voucher.getLineAmount().negate());
            crLine.put("line_num", crLineNumber);

            if (StringUtils.isNotEmpty(voucher.getPipelineNumber())) {
                summaryCr.put(voucher.getPipelineNumber(), crLine);
                crLine.put("line_description", "个人报销招行代发");
            } else if ("01.10100.********.0000.00.00000.00000".equals(fndUserAccount.getBankAddress())){
                crLine.put("line_description", "现金");
            } else {
                crLine.put("line_description", "");
            }
            erpVouchers.add(translate(branchInfo.getCurrencyCode(), crLine));
            crLineNumber++;

        }
        return erpVouchers;
    }

    private void setCommonValues(Map<String, Object> line, String operatorIdentity,
                            String yearMonthDay, String periodName, int number, String typeCode) {
        String yearMonth = yearMonthDay.substring(0, 6);
        String batchName;
        String journalName;
        if(StrUtil.equals("T003", typeCode)) {// 多人报销
            batchName = operatorIdentity + "-" + yearMonth + "-40-" + number;
            journalName = yearMonthDay + "多人报销付款";
        } else {
            batchName = operatorIdentity + "-" + yearMonth + "-30-" + number;
            journalName = yearMonthDay + "个人报销付款";
        }
        line.put("batch_name", batchName);
        line.put("journal_name", journalName);
        line.put("period_name", periodName);
        line.put("journal_description", yearMonthDay + "付款");
    }

    @Override
    public String postPaymentVouchers(List<Integer> headerIds, int userId, String date, int number) throws Exception {

        List<Map<String, Object>> vouchers = getPaymentVouchers(headerIds, userId, date, number);
        //获取长飞token
        Map<String, String> tokenMap = getTokenMap(null);
        for (Map<String, Object> voucher : vouchers) {
            voucher.put("TOKEN",tokenMap.get("TOKEN"));
        }
        String postStr = JSONObject.toJSONString(vouchers);
        logger.info("{}对私单据{}，AP付款老接口，入参={}", LogHolder.getLogKey(), headerIds, postStr);
        String result = YofcUtils.httpPostString(VOUCHERNEWURL, null, postStr,"UTF-8","UTF-8");
        logger.info("{}对私单据{}，AP付款老接口，返参={}", LogHolder.getLogKey(), headerIds, result);
        JSONArray array= JSONArray.parseArray(JSONObject.toJSONString(headerIds));
        return afterPost(result, array, false,null, date);
    }

    @Override
    public void cbsPayStatusPublic() {
        String LOG_KEY = "长飞对公支付状态查询===";
        // 对公需要查询支付结果的单据列表
        List<ErPaySta> lists = yofcMapper.yofcgetPayingDoc();
        Integer size = lists == null ? 0 : lists.size();
        logger.info("{}获取到对公单据{}条", LOG_KEY, size);
        // 对私需要查询支付结果的单据列表
        if (CollectionUtils.isEmpty(lists)) {
            logger.info("{}本次未查询到单据！", LOG_KEY);
            return;
        }
        for (ErPaySta erPaySta : lists) {
            try {
                // 获取cbs支付结果
                JSONObject detail = getCbsPayDetail(LOG_KEY, erPaySta);
                if (detail == null) {
                    continue;
                }
                String columnJson = yofcMapper.yofcgetColumn54(erPaySta.getHeaderId());
                Map<String, Object> columnMap = new HashMap<>();
                if (null != columnJson) {
                    columnMap = JSON.parseObject(columnJson, Map.class);
                }
                Long operationTime = System.currentTimeMillis();
                if (null == columnMap.get("column54")) {
                    columnMap.put("column54", operationTime);
                    yofcMapper.updateColumnJson(erPaySta.getHeaderId(), JSON.toJSONString(columnMap, SerializerFeature.WriteMapNullValue));
                }
                /* 支付结果STATUS:
                 3，则支付单状态保持为支付中
                 2，支付单状态立马撤回“待支付”，如果“支付失败”的状态开发后，则进入支付失败
                 1，如果STATUS在48小时后仍然是 “1”，则进入支付完成列表。如果状态变成4，则进入支付失败列表
                 4，则进入“支付失败”列表
                */
                String status = detail.getString("STATUS");
                String optstu = detail.getString("OPTSTU");
                if ("2".equals(status) || "4".equals(status) ||
                   ("3".equals(status) && OPT_STU_ERROR_LIST.contains(optstu))) {
                    yofcMapper.updatePayStatus(erPaySta.getHeaderId(), "error");
                    yofcMapper.deleteTransSerialNum(erPaySta.getHeaderId());// 清理流水号以便可以重新发起支付
                } else if ("1".equals(status) || ("3".equals(status) && OPT_STU_SUCCESS_LIST.contains(optstu))) {
                    //关闭当前单据
                    yofcMapper.closeClaim(erPaySta.getHeaderId());
                    logger.info("{}单据{}状态关闭完成", LOG_KEY, erPaySta.getDocumentNum());
                    //登记流水
                    String payDate = yofcMapper.getColumn31(erPaySta.getHeaderId());
                    ApPaymentBatch apPaymentBatch = new ApPaymentBatch();
                    apPaymentBatch.setCompanyId(companyId.toString());
                    apPaymentBatch.setPaymentDate(payDate);
                    yofcMapper.addApPaymentBatch(apPaymentBatch);
                    logger.info("{}单据{}登记ap_payment_batch完成", LOG_KEY, erPaySta.getDocumentNum());
                    //登记流水
                    Integer batchId = apPaymentBatch.getBatchId();
                    yofcMapper.updateHeaderBatchId(erPaySta.getHeaderId(), batchId);
                    yofcMapper.addGlJePayBatchAssign(companyId, batchId, erPaySta.getHeaderId());
                    logger.info("{}单据{}登记gl_je_pay_batch_assign完成", LOG_KEY, erPaySta.getDocumentNum());
                    //关闭前序单据：对公（付款单<-支付单）， 对私（报销单<-确认单<-支付单）
                    //前序单据: 付款单（对公）、确认单（对私）
                    String linkeHeaderId1 = yofcMapper.getLinkHeaderId(erPaySta.getHeaderId());
                    //付款单（对公）、确认单（对私）单据信息
                    PayInfo payInfo1 = yofcMapper.getPaymentInfo(linkeHeaderId1);
                    BigDecimal advanceAmount1 = Optional.ofNullable(payInfo1.getAdvanceAmount()).orElse(BigDecimal.ZERO);
                    if(!StringUtils.isEmpty(payInfo1.getColumn48())) {
                        advanceAmount1 = new BigDecimal(payInfo1.getColumn48());
                    }
                    //后续已关闭的支付单金额总计
                    BigDecimal sumTotalPayAmount1 = yofcMapper.getSumTotalPayAmount(linkeHeaderId1);
                    logger.info("{}单据{}前序单据1={}待支付金额={}，已支付金额={}", LOG_KEY, erPaySta.getDocumentNum(),
                            linkeHeaderId1, advanceAmount1, sumTotalPayAmount1);
                    //校验金额，若所有支付单都已关闭，则关闭前序单据1
                    if (advanceAmount1.compareTo(sumTotalPayAmount1) == 0) {
                        //关闭前序单据：付款单（对公）、确认单（对私）
                        yofcMapper.closeClaim(linkeHeaderId1);
                        logger.info("{}单据{}关闭前序单据{}完成", LOG_KEY, erPaySta.getDocumentNum(), linkeHeaderId1);
                    }
                    //前前序单据: 报销单（对私）
                    /*String linkeHeaderId2 = yofcMapper.getLinkHeaderId(linkeHeaderId1);
                    if(linkeHeaderId2 != null) {
                        //报销单（对私）单据信息
                        PayInfo payInfo2 = yofcMapper.getPaymentInfo(linkeHeaderId2);
                        BigDecimal advanceAmount2 = Optional.ofNullable(payInfo2.getAdvanceAmount()).orElse(BigDecimal.ZERO);
                        if(!StringUtils.isEmpty(payInfo2.getColumn48())) {
                            advanceAmount2 = new BigDecimal(payInfo2.getColumn48());
                        }
                        //后续已关闭的支付单金额总计
                        BigDecimal sumTotalPayAmount2 = yofcMapper.getSumTotalPayAmount(linkeHeaderId2);
                        logger.info("{}单据{}前前序单据={}待支付金额={}，已支付金额={}", LOG_KEY, erPaySta.getDocumentNum(),
                                linkeHeaderId2, advanceAmount2, sumTotalPayAmount2);
                        //校验金额，若所有支付单都已关闭，则关闭前前序单据
                        if (advanceAmount2.compareTo(sumTotalPayAmount2) == 0) {
                            //关闭前前序单据：报销单（对私）
                            yofcMapper.closeClaim(linkeHeaderId2);
                            logger.info("{}单据{}关闭前前序单据={}完成", LOG_KEY, erPaySta.getDocumentNum(), linkeHeaderId2);
                        }
                    }*/
                }
            } catch (Exception e) {
                logger.error("{}单据{}出现未知异常：{}", LOG_KEY, erPaySta.getDocumentNum(), e.getMessage(), e);
                continue;
            }
        }
    }

    @Override
    public void cbsPayStatusPrivate() {
        String LOG_KEY = "长飞对私支付状态查询===";
        List<ErPaySta> lists = yofcMapper.yofcgetPayingSiDoc();
        Integer size = lists == null ? 0 : lists.size();
        logger.info("{}获取到对私单据{}条", LOG_KEY, size);
        if (CollectionUtils.isEmpty(lists)) {
            return;
        }
        // 补充前序单据信息
        MultiValueMap<String, ErPaySta>   linkMap = new LinkedMultiValueMap<>();
        MultiValueMap<String, ErPaySta> busNbrMap = new LinkedMultiValueMap<>();
        for(ErPaySta pay : lists) {
            Pay3Header pay3Header = yofcMapper.getLinkPay3Header(pay.getHeaderId());
            if(pay3Header == null) {
                logger.warn("{}单据{}前序单据不存在", LOG_KEY, pay.getDocumentNum());
                continue;
            }
            pay.setLinkDocumentNum(pay3Header.getDocumentNum());
            pay.setLinkHeaderTypeCode(pay3Header.getTypeCode());
            linkMap.add(pay.getLinkDocumentNum(), pay);
            busNbrMap.add(pay.getBusnbr(), pay);
        }
        //按流水号busnbr归并
        for (String busnbr : busNbrMap.keySet()) {
            Map<String, ErPaySta> payMap = busNbrMap.get(busnbr).stream().collect(Collectors.toMap(ErPaySta::getDocumentNum, Function.identity()));
            try {
                logger.info("{}流水号busnbr={},单据={}", LOG_KEY, busnbr, payMap);
                //数据拼接
                String head = "<?xml version=\"1.0\" encoding =\"GBK\"?><CBSERPPGK><INFO><FUNNAM>ERAGNSTA</FUNNAM></INFO>";
                String body = "<ERAGNSTAX><BUSNBR>" + busnbr + "</BUSNBR></ERAGNSTAX>";
                String tail = "</CBSERPPGK>";
                String postXml = head + body + tail;
                logger.info("{}流水号busnbr={}调用外部接口入参={}", LOG_KEY, busnbr, postXml);
                //发送请求
                String result = postToServer(postXml);
                JSONObject responseJsonObject = getResponseResult(result, "utf8String: cbsBatchStatus: ");
                logger.info("{}流水号busnbr={}调用外部接口结果={}", LOG_KEY, busnbr, responseJsonObject.toJSONString());
                Object json = responseJsonObject.getJSONObject("CBSERPPGK").get("ERAGNSTAY");
                String optstu = "";
                if(responseJsonObject.getJSONObject("CBSERPPGK").get("ERAGNSTAZ") != null) {
                    JSONObject ERAGNSTAZ = responseJsonObject.getJSONObject("CBSERPPGK").getJSONObject("ERAGNSTAZ");
                    optstu = ERAGNSTAZ.get("OPTSTU") == null ? "" : ERAGNSTAZ.getString("OPTSTU");
                }
                //判断结果类型
                if (json instanceof JSONArray) {
                    JSONArray arr = (JSONArray)json;
                    for(int i = 0; i < arr.size(); i++) {
                        JSONObject detail = arr.getJSONObject(i);
                        String documentNum = detail.getString("EXTTX1");
                        ErPaySta payTarget = getPayTarget(documentNum, linkMap, payMap);
                        afterHandle(LOG_KEY, detail, payTarget, optstu);
                    }
                } else if (json instanceof JSONObject) {
                    JSONObject detail = (JSONObject)json;
                    String documentNum = detail.getString("EXTTX1");
                    ErPaySta payTarget = getPayTarget(documentNum, linkMap, payMap);
                    afterHandle(LOG_KEY, detail, payTarget, optstu);
                } else {
                    logger.error("无法识别的返回格式");
                    return;
                }

            } catch (Exception e) {
                logger.info("{}流水号busnbr={},单据={}, 出现未知异常={}", LOG_KEY, busnbr, e.getMessage(), e);
            }
        }
    }

    @Override
    public void cbsPayDate() {
        String LOG_KEY = "长飞支付日期查询===";
        //需要查询已支付的单据列表
        List<ErPaySta> lists = Lists.newArrayList();
        // 对公需要查询支付结果的单据列表
//        List<ErPaySta> lists1 = yofcMapper.yofcgetPayingDoc();
//        Integer size1 = lists1 == null ? 0 : lists1.size();
//        logger.info("{}获取到对公单据{}条，处理中状态", LOG_KEY, size1);
//        lists.addAll(lists1);
        // 对私需要查询支付结果的单据列表
//        List<ErPaySta> lists2 = yofcMapper.yofcgetPayingSiDoc();
//        Integer size2 = lists2 == null ? 0 : lists2.size();
//        logger.info("{}获取到对私单据{}条，处理中状态", LOG_KEY, size2);
        // 对私需要查询支付结果的单据列表
        List<ErPaySta> lists3 = yofcMapper.yofcgetPaidSiDoc();
        Integer size3 = lists3 == null ? 0 : lists3.size();
        logger.info("{}获取到对私单据{}条，已支付但无支付时间", LOG_KEY, size3);
        lists.addAll(lists3);
        if (CollectionUtils.isEmpty(lists)) {
            logger.info("{}本次未查询到单据！", LOG_KEY);
            return;
        }
        for (ErPaySta erPaySta : lists) {
            try {
                // 获取cbs支付结果
                JSONObject detail = getCbsPayDetail(LOG_KEY, erPaySta);
                if (detail == null) {
                    continue;
                }
            } catch (Exception e) {
                logger.error("{}单据{}出现未知异常：{}", LOG_KEY, erPaySta.getDocumentNum(), e.getMessage(), e);
                continue;
            }
        }
    }

    /**
     * 获取cbs支付结果，成功时更新单据相关支付信息
     * @param LOG_KEY
     * @param erPaySta
     * @return
     * @throws Exception
     */
    private JSONObject getCbsPayDetail(String LOG_KEY, ErPaySta erPaySta) throws Exception {
        String documentNum = erPaySta.getDocumentNum();//单据号
        String refnbr = erPaySta.getRefnbr();//业务号
        String transSerialNum = erPaySta.getBusnbr();//流水号
        logger.info("{}单据{}开始处理", LOG_KEY, documentNum);
        if (StringUtils.isBlank(transSerialNum)) {
            logger.error("{}单据{}流水号为空", LOG_KEY, documentNum);
            return null;
        }
        if (erPaySta.getType() == 1 && Strings.isNullOrEmpty(refnbr)) {
            logger.error("{}单据{}对公单据业务号为空", documentNum);
            return null;
        }
        //数据拼接
        StringBuilder data = new StringBuilder();
        data.append("<?xml version=\"1.0\" encoding =\"GBK\"?>");
        data.append("<CBSERPPGK><INFO><FUNNAM>ERPAYSTA</FUNNAM></INFO><ERPAYSTAX>");
        if(!Strings.isNullOrEmpty(refnbr))
            data.append("<REFNBR>" + refnbr + "</REFNBR>");
        data.append("<BUSNBR>" + transSerialNum + "</BUSNBR>");
        data.append("</ERPAYSTAX></CBSERPPGK>");
        String postXml = data.toString();
        logger.info("{}单据{}调用外部接口入参：{}", LOG_KEY, documentNum, postXml);
        //发送请求
        String result = postToServer(postXml);
        logger.info("{}单据{}调用外部接口出参：{}", LOG_KEY, documentNum, result);
        JSONObject responseJsonObject = getResponseResult(result, "utf8String: cbsBatchStatus: ");
        JSONObject detail = responseJsonObject.getJSONObject("CBSERPPGK").getJSONObject("ERPAYSTAZ");
        if(detail == null) {
            logger.error("{}单据{}支付结果解析异常！", LOG_KEY, documentNum);
            return null;
        }
        //支付结果数据
        String status = detail.getString("STATUS");
        String paytim = detail.getString("PAYTIM");// 实际支付时间 格式2017-05-04 15:09:28.929
        logger.info("{}单据{}支付状态：{}，支付时间：{}", LOG_KEY, erPaySta.getDocumentNum(), status, paytim);
        //此处只处理成功的结果
        if ("1".equals(status)) {
            Date paymentDate = null;
            if(!Strings.isNullOrEmpty(paytim)) {
                Date payDate = DateUtils.parseDate(paytim, new String[] {"yyyy-MM-dd HH:mm:ss.SSS"});
                paymentDate = DateUtils.addHours(payDate, -8);// 对方是东八区时间
            }
            yofcMapper.updatePayResult(erPaySta.getHeaderId(), "paid", paymentDate);
        }

        return detail;
    }

    public static JSONObject getResponseResult(String result, String s) {
        //将结果由GBK转为UTF-8
        String utf8String = getUTF8StringFromGBKString(result);
        logger.info(s + utf8String);
        //结果转json
        org.json.JSONObject jsonObj = XML.toJSONObject(utf8String);
        StringBuilder dataStr = new StringBuilder();
        Object data = jsonObj.getJSONObject("PGK").get("DATA");
        //根据是否嵌套CDATA会出现2中返回数据结构
        if(data instanceof org.json.JSONArray) {
            org.json.JSONArray dataArr = ((org.json.JSONArray)data).getJSONArray(0);
            for(int i = 0; i < dataArr.length(); i++) {
                dataStr.append(dataArr.getString(i));
            }
        } else if(data instanceof String) {
            dataStr.append((String)data);
        }
        org.json.JSONObject dataObj = XML.toJSONObject(dataStr.toString());

        return JSONObject.parseObject(dataObj.toString());
    }

    @Override
    public String yofcFaPiaoPosted(String documentId,String date,String userId) {
        LogCtx.setLogKey("长飞对公财务凭证==单据" + documentId);
        logger.info("{}开始处理date={},userId={}", LogCtx.getLogKey(), date, userId);
        String errorMsg = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyyMM");
        List<FaPiaoHeaderInfo> hederInfos = yofcMapper.yofcPostHeaderData(documentId);
        logger.info("{}发票列表={}", LogCtx.getLogKey(), JSON.toJSONString(hederInfos));
        String userIdentityColumn1 = yofcMapper.getUserIdentityColumn1(Integer.valueOf(userId));
        logger.info("{}用户Column1={}", LogCtx.getLogKey(), userIdentityColumn1);
        // T65费用行中发票中最晚的发票日期
        Long receiptTimeLastOne = 0L;
        // 第一行的发票日期
        Long receiptTimeFirstLine = 0L;

        if(null != hederInfos && hederInfos.size()>0){
            FaPiaoHeaderInfo faPiaoHeaderInfo = hederInfos.get(0);
            // 是否重复处理
            if(!Arrays.asList("pending","error").contains(faPiaoHeaderInfo.getGlStatus())) {
                logger.info("单据{}，对公凭证状态{}，忽略处理", faPiaoHeaderInfo.getDucumentNum(), faPiaoHeaderInfo.getGlStatus());
                return null;
            }
            String headerId = faPiaoHeaderInfo.getHeaderId();
            yofcMapper.updateGlStatus(Integer.valueOf(headerId), "generating");
            //
            Map<String, Object> HeaderMap = new HashMap<>();
            List<String> dess7 = null;
            List<String> dess17 = null;
            List<String> name13 = null;

            String branchCol4 = faPiaoHeaderInfo.getBranchColumn4();
            String branchCol5 = faPiaoHeaderInfo.getBranchColumn5();
//            String column79 = obj2Str(getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column79"));
            String column79 = "00";
            if(null != faPiaoHeaderInfo.getColumn7()){
                dess7 = yofcMapper.yofcCol7Descri(faPiaoHeaderInfo.getColumn7());
            }
            if(null != faPiaoHeaderInfo.getColumnJson()){
                String valueCode = (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column64");
                dess17 = yofcMapper.yofcCol7Descri(valueCode);
            }
            if(null != faPiaoHeaderInfo.getColumn13()){
                name13 = yofcMapper.yofcCol13Name(faPiaoHeaderInfo.getColumn13());
            }
            HeaderMap.put("CUX_AP_FINAL_TYPE","INVOICE");
            String batchDate = null;
            try {
                batchDate = dateFormat2.format(dateFormat.parse(date));
            } catch (ParseException e) {
                logger.error("{}凭证日期格式异常", LogCtx.getLogKey());
                errorMsg = "凭证日期格式异常";
                return errorMsg;
            }
            HeaderMap.put("BATCH_NAME",userIdentityColumn1+"-"+batchDate+"-" + faPiaoHeaderInfo.getBranchColumn4());

            HeaderMap.put("GL_DATE",date);
            HeaderMap.put("ATTRIBUTE1",faPiaoHeaderInfo.getSupplierCode());

            if("T017".equals(faPiaoHeaderInfo.getTypeCode())) {
                HeaderMap.put("ATTRIBUTE2",faPiaoHeaderInfo.getColumn32());
            }

            HeaderMap.put("VENDOR_SITE_CODE",null != name13 && name13.size()>0? name13.get(0):"");
            HeaderMap.put("PAYMENT_METHOD_CODE",null != dess7 && dess7.size()>0? dess7.get(0):"");
            HeaderMap.put("TERMS_ID",null != dess17 && dess17.size()>0? dess17.get(0):"");
            //ORG_ID取单据头上branch_id对应的department_code
            HeaderMap.put("ORG_ID", faPiaoHeaderInfo.getFdCode());

            List<FaPiaoLineInfo> LineInfos =  yofcMapper.yofcPostLineData(faPiaoHeaderInfo.getHeaderId());
            logger.info("{}费用行明细={}", LogCtx.getLogKey(), JSON.toJSONString(LineInfos));

            //预付款单据推送PO号
            if("T018".equals(faPiaoHeaderInfo.getTypeCode())) {
                List<Integer>  linkHeaderIds = yofcMapper.getLinkHeaderIds(companyId, faPiaoHeaderInfo.getHeaderId());
                if(CollUtil.isEmpty(linkHeaderIds)) {
                    logger.error("{}前序单据为空异常", LogCtx.getLogKey());
                    errorMsg = "前序单据为空异常";
                    return errorMsg;
                }
                List<ClaimHeader> linkClaims = yofcMapper.yofcGetClaimHeaderListByHeaderIds(linkHeaderIds);
                List<String>       poNumbers = new ArrayList<>();
                for(ClaimHeader linkClaim : linkClaims) {
                    poNumbers.add(linkClaim.getColumn47());
                }
                HeaderMap.put("PREPAY_PO_NUMBER", StrUtil.join("/", poNumbers));
                if(StringUtils.isNotEmpty(faPiaoHeaderInfo.getColumn30()) && faPiaoHeaderInfo.getColumn30().equals("是")){
                    HeaderMap.put("CC_CODE",faPiaoHeaderInfo.getSubmitDepartment());
                }else if(StringUtils.isNotEmpty(faPiaoHeaderInfo.getColumn30()) && faPiaoHeaderInfo.getColumn30().equals("否")){
                    HeaderMap.put("CC_CODE",LineInfos.get(0).getCostCenter());
                }
            }

            HeaderMap.put("DESCRIPTION",faPiaoHeaderInfo.getDescription());// 摘要

            HeaderMap.put("IMP_SRC_SYSTEM","ECM");
            HeaderMap.put("SOURCE","ECM");
            HeaderMap.put("IMP_SRC_ORDER_NUM",faPiaoHeaderInfo.getDucumentNum());
            HeaderMap.put("VALIDATION_FLAG","Y");
            HeaderMap.put("APPLY_PREPAY_FLAG","N");

            String column61 = "";
            String customsDeclNum = "";// 报关单号
            String finDescription = "";// 财务摘要
            if(null != faPiaoHeaderInfo.getColumnJson()){
                customsDeclNum = (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column85");
                finDescription = (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column86");
                column61 = (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column61");
            }
            HeaderMap.put("CUSTOMS_DECL_NUM", customsDeclNum);
            HeaderMap.put("FIN_DESCRIPTION", finDescription);
            HeaderMap.put("PREPAYMENT_RATIO", faPiaoHeaderInfo.getColumn20());// 预付比例

            String contractNo = "";// 合同编号
            String poNumber = "";// PO号
            String purchaseApplicant = "";// 采购申请人
            String budgetNumber = "";// 预算号
            String ecmApplicant = "";//
            if(Arrays.asList("T017", "T018").contains(faPiaoHeaderInfo.getTypeCode())) {
                contractNo = faPiaoHeaderInfo.getColumn15();
                poNumber = faPiaoHeaderInfo.getColumn47();
                List<Integer> linkHeaderIds = yofcMapper.getLinkHeaderIdByHeaderId(Integer.valueOf(faPiaoHeaderInfo.getHeaderId()));
                if(linkHeaderIds != null && linkHeaderIds.size() > 0) {
                    for(Integer linkId : linkHeaderIds) {
                        FaPiaoLinkHeaderInfo linkHeaderInfo = yofcMapper.getLinkHeader(linkId);
                        if("T012".equals(linkHeaderInfo.getTypeCode())) {
                            purchaseApplicant = Optional.ofNullable(linkHeaderInfo.getSubmitUser()).orElse("");
                            break;
                        }
                    }
                }

                budgetNumber = getBudgetNumber(faPiaoHeaderInfo);
                ecmApplicant = faPiaoHeaderInfo.getChargeUser();
            } else if(Arrays.asList("RQ03", "RQ04", "RQ07").contains(faPiaoHeaderInfo.getTypeCode())) {
                contractNo = faPiaoHeaderInfo.getColumn32();
                budgetNumber = getBudgetNumber(faPiaoHeaderInfo);
                ecmApplicant = faPiaoHeaderInfo.getChargeUser();
                if(Arrays.asList("RQ03").contains(faPiaoHeaderInfo.getTypeCode())){
                    HeaderMap.put("CC_CODE",faPiaoHeaderInfo.getChargeDepartment());
                }
            }
            HeaderMap.put("CONTRACT_NO", contractNo);
            HeaderMap.put("PO_NUMBER", poNumber);
            HeaderMap.put("PURCHASE_APPLICANT", purchaseApplicant);
            HeaderMap.put("BUDGET_NUMBER", budgetNumber);
            HeaderMap.put("ECM_APPLICANT", ecmApplicant);
            HeaderMap.put("DOC_TYPE", faPiaoHeaderInfo.getTypeCode());

            Object column80 = getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column80");
            Object column81 = getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column81");
            Object column82 = getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column82");
            if(column80 != null) {
                HeaderMap.put("ATTRIBUTE4", column80);// 票据号
            }
            if(column81 != null) {
                HeaderMap.put("ATTRIBUTE5", column81);// 出票日期
            }
            if(column82 != null) {
                HeaderMap.put("FUTURE_PAY_DUE_DATE", column82);// 结束日期
            }
            // 核销项
            HeaderMap.put("APPLY_PREPAY_FLAG", getRepayFlag(faPiaoHeaderInfo));
            //会计科目第一段取值逻辑 #19445
            String jie1 = faPiaoHeaderInfo.getFdColumn4();
            if (StringUtils.isBlank(jie1)) {
                errorMsg = faPiaoHeaderInfo.getDucumentNum() + "会计科目第一段(branchId -> fd.column4)值为空！";
                return errorMsg;
            }
            String jie5 = yofcMapper.yofcPPColumn3(faPiaoHeaderInfo.getHeaderId());
            UserInfo userInfo = yofcMapper.yofcuserEmploy(faPiaoHeaderInfo.getHeaderId());
            String userEmploy = userInfo.getEmployeeNumber();
            /*if(null != userEmploy && userEmploy.length()>6){
                userEmploy = userEmploy.substring(0,1) + userEmploy.substring(3,7);
            }else {
                userEmploy = null;
            }*/
            List<Map<String, Object>> lines = new ArrayList<>();
            List<FaPiaoLineInfo>    pgLines = new ArrayList<>();// pg项目触发对私凭证行
            // 发票类型
            HeaderMap.put("INVOICE_TYPE_LOOKUP_CODE", getInvoiceType(faPiaoHeaderInfo, LineInfos));
            //发票号码优先取单据头column36
            String invoiceNum = faPiaoHeaderInfo.getColumn36();
            if (PREPAYMENT_TYPE_LIST.contains(faPiaoHeaderInfo.getTypeCode())) {
                if (StringUtils.isEmpty(invoiceNum)) {
                    invoiceNum = generateInvoiceNum(faPiaoHeaderInfo.getDucumentNum(), date, faPiaoHeaderInfo.getFdCode());
                    if(StringUtils.isEmpty(invoiceNum)) {
                        errorMsg = faPiaoHeaderInfo.getDucumentNum() + "自动生成发票号失败";
                        return errorMsg;
                    }
                }
            } else {
                if (StringUtils.isEmpty(invoiceNum)) {
                    invoiceNum = "";
                    //取费用行绑定票据上的发票号码
                    List<String> invoiceNums = new ArrayList<>();
                    if (yofcMapper.getInvoiceNums(documentId) != null) {
                        invoiceNums = yofcMapper.getInvoiceNums(documentId);
                    }
                    //取最前面的四个invoice_number，中间用／隔开
                    int i = 0;
                    for (String num : invoiceNums) {
                        invoiceNum = invoiceNum + num;
                        i++;
                        if (i == 4) {
                            break;
                        }
                        invoiceNum = invoiceNum + "/";
                    }
                }
            }
            HeaderMap.put("INVOICE_NUM", invoiceNum);

            //#74270
            if(Arrays.asList("RQ08").contains(faPiaoHeaderInfo.getTypeCode())) {
                if(null != faPiaoHeaderInfo.getColumnJson()){
                    String column89 = (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column89");
                    column89 = LocalDateTimeUtil.of(Long.valueOf(column89), ZoneId.of("GMT+8")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    HeaderMap.put("EXPECTED_RECOVERY_TIME",column89);
                }
                HeaderMap.put("RECOVERY_PERSON", faPiaoHeaderInfo.getChargeUser());
            }

            String T65taxCode = null;
            Boolean flag = true;
            Integer i = 0;
            Integer y = 0;
            BigDecimal allReceiptAmount = new BigDecimal("0.00");
            String currencyCode = "CNY";
            Map<String, Object> heXiaoMap = new HashMap<>();
            heXiaoMap.put("IMP_SRC_ORDER_NUM",faPiaoHeaderInfo.getDucumentNum());
            heXiaoMap.put("GL_DATE",date);
            List<Map<String, Object>> lineHeXiaoLists = new ArrayList<>();
            // 循环，获取发票日期
            if(null != LineInfos && LineInfos.size()>0){
                // 设置第一行的发票日期,需要考虑部分类型的费用行，不参与计算
                for (FaPiaoLineInfo lineInfo : LineInfos) {
                    if(!INTERNALTYPElIST.contains(lineInfo.getInternalType()) && lineInfo.getReceiptDate() != null){
                        receiptTimeFirstLine = lineInfo.getReceiptDate().getTime();
                        break;
                    }
                }
                // 获取T65费用类型的最晚发票日期
                for (FaPiaoLineInfo lineInfo : LineInfos) {
                    if ("T65".equals(lineInfo.getTypeCode())){
                        if (lineInfo.getReceiptDate() != null) {
                            receiptTimeLastOne = receiptTimeLastOne > lineInfo.getReceiptDate().getTime() ? receiptTimeLastOne : lineInfo.getReceiptDate().getTime();
                        }
                    }
                }
            }

            if(null != LineInfos && LineInfos.size()>0){
                for (FaPiaoLineInfo lineInfo : LineInfos) {
                    if(!StrUtil.equals(lineInfo.getInternalType(), "advance") && StrUtil.isNotEmpty(lineInfo.getCostCenter()) && StrUtil.equals(lineInfo.getCostCenterEnabledFlag(), "N")) {
                        logger.error("{}行成本中心{}已失效", lineInfo.getCostCenter(), LogCtx.getLogKey());
                        errorMsg = "行成本中心" + lineInfo.getCostCenter() + "已失效";
                        return errorMsg;
                    }
                    if (Arrays.asList("T40","EX322").contains(lineInfo.getTypeCode()) && "Y".equals(HeaderMap.get("APPLY_PREPAY_FLAG"))){
                        Map<String, Object> lineHeXiaoMap = new HashMap<>();
                        String frontDocumentNum = null;
                        if (null != lineInfo.getLinkHeaderId()){
                            frontDocumentNum =  yofcMapper.getdDocumentNumById(lineInfo.getLinkHeaderId());
                        }
                        lineHeXiaoMap.put("IMP_SRC_ORD_NUM_PREPAY",frontDocumentNum);
                        lineHeXiaoMap.put("PREPAY_AMOUNT_APPLIED",lineInfo.getReceiptAmount());
                        lineHeXiaoMap.put("GL_DATE",date);
                        lineHeXiaoMap.put("IMP_SRC_SYSTEM","ECM");
                        lineHeXiaoLists.add(lineHeXiaoMap);
                    }
                    // redmine=55055 预扣税 过滤
                    if("EX334".equals(lineInfo.getTypeCode())) {
                        logger.info("{}过滤预扣税行lineId={}，单据号={}", LogCtx.getLogKey(), lineInfo.getLineId(), faPiaoHeaderInfo.getDucumentNum());
                        continue;
                    }

                    if (!(null != faPiaoHeaderInfo.getTypeCode() && (PREPAYMENT_TYPE_LIST.contains(faPiaoHeaderInfo.getTypeCode())))) {
                        //当费用类型不为T40,T65,T90时创建费用行
                        if ("T40".equals(lineInfo.getTypeCode()) || "EX322".equals(lineInfo.getTypeCode()) || "T65".equals(lineInfo.getTypeCode()) || "T90".equals(lineInfo.getTypeCode())  || "T100".equals(lineInfo.getTypeCode())){
                            continue;
                        }
                    }
                    if (null != lineInfo.getCurrencyCode()){
                        currencyCode = lineInfo.getCurrencyCode();
                    }
                    y++;
                    if(null != lineInfo.getReceiptAmount()){
                        if (lineInfo.getPayMethodId() == null) {
                            allReceiptAmount = allReceiptAmount.add(lineInfo.getReceiptAmount());
                        }
                        else {
                            switch (lineInfo.getPayMethodId()) {
                                case PAYMETHODID1 :
                                    allReceiptAmount = allReceiptAmount.add(lineInfo.getReceiptAmount());
                                    break;
                                case PAYMETHODID2 :
                                    allReceiptAmount = allReceiptAmount.subtract(lineInfo.getReceiptAmount());
                                    break;
                            }
                        }
                    }

                    boolean skipZeroTaxFlag = true;// 是否忽略金额0税行标记
                    BigDecimal taxAmount = getAmount(faPiaoHeaderInfo, lineInfo, "TAX"); // 税额
                    BigDecimal netAmount = getAmount(faPiaoHeaderInfo, lineInfo, "ITEM");// 不含税金额
                    if(null != netAmount && netAmount.compareTo(BigDecimal.ZERO) > 0){
                        i++;
                        List<String> projectCodes =  yofcMapper.yofcProjectCode(lineInfo.getLineId());
                        String projectCode = CollectionUtils.isEmpty(projectCodes) ? "" : projectCodes.get(0);
                        String column4 = yofcMapper.yofcProjectColumn4(lineInfo.getLineId());
                        String aliasName = Optional.ofNullable(lineInfo.getAliasName()).orElse("");
                        //
                        String pgAdjustType = getPgAdjustType(faPiaoHeaderInfo, lineInfo);// pg调整类型
                        //
                        String jie2;
                        if(!"N/A".equals(projectCode) && "Y".equals(column4)) {
                            jie2 = yofcMapper.yofcCostCode(lineInfo.getLineId());
                        } else if(aliasName.contains("公司公共")) {
                            jie2 = yofcMapper.getGsggColumn1(faPiaoHeaderInfo.getBranchId());
                        } else {
                            String erpCode = yofcMapper.yofcErpCodeLine(lineInfo.getLineId());
                            if (null != erpCode){
                                jie2 = erpCode;
                            }else {
                                jie2 = yofcMapper.yofcErpCode(faPiaoHeaderInfo.getHeaderId());
                            }
                        }
                        String jie4;
                        ProjectInfo projectInfo1 =null;
                        if(lineInfo.getProjectCode()!=null) projectInfo1= yofcMapper.yofcProjectInfo(lineInfo.getProjectCode());
                        if(projectInfo1 !=null && ((!StrUtil.equals(lineInfo.getTypeCode(), "T50") && projectInfo1.getColumn10().equals("PA01")) ||
                                projectInfo1.getColumn10().equals("PA06"))
                                && StrUtil.equals(faPiaoHeaderInfo.getSupplierColumn5(), "是")) {
                            jie4 = faPiaoHeaderInfo.getSupplierColumn6();
                        } else {
                            jie4 = StringUtils.isEmpty(column79) ? "00" : column79;
                        }
                        // 判断是否为个人核算
                        boolean isPersonalAccounting = (null != lineInfo.getAliasName() && lineInfo.getAliasName().equals("个人核算") ? true : false);

                        String typeCode = Optional.ofNullable(lineInfo.getTypeCode()).orElse("");
                        String column2 = userInfo.getColumn2();
                        if(isPersonalAccounting && StringUtils.isNotEmpty(column2)) {
                            jie5 = column2;
                        } else if("T40".equals(typeCode)) {
                            jie5 = "00000";
                        } else {
                            String ppColumn3 = yofcMapper.yofcPPColumn3Line(lineInfo.getLineId());
                            if (null != ppColumn3){
                                jie5 = ppColumn3;
                            }
                        }

                        String jie6;
                        if(Arrays.asList("RQ10","RQ04","T017","RQ07").contains(faPiaoHeaderInfo.getTypeCode())) {
                            UserInfo lineUserInfo = yofcMapper.yofcLineUserEmploy(lineInfo.getLineId());
                            if(lineUserInfo != null) {
                                String empNum = Optional.ofNullable(lineUserInfo.getEmployeeNumber()).orElse("");
                                /*userEmploy = StrUtil.sub(empNum, 0, 1) +
                                             StrUtil.sub(empNum, -4, empNum.length());*/
                                userEmploy = empNum;
                            }
                        }
                        if (isPersonalAccounting && null != userEmploy) {
                            jie6 = userEmploy;
                        } else {
                            jie6 = "00000";
                        }
                        String column57 = JSON.parseObject(lineInfo.getColumnJson()).getString("column57");
                        String pgJie6 = getPgJie6(faPiaoHeaderInfo.getFdCode(), "Pub", column57, projectCode);
                        if(pgJie6 != null) {
                            jie6 = pgJie6;
                        }

                        Map<String, Object> lineNetMap = new HashMap<>();
                        lineNetMap.put("LINE_NUMBER", i);
                        lineNetMap.put("LINE_TYPE_LOOKUP_CODE", "ITEM");
                        //发票行新增字段
                        lineNetMap.put("QUANTITY_INVOICED", lineInfo.getColumn30());
                        lineNetMap.put("RCV_TRANSACTION_ID", lineInfo.getColumn40());
                        //项目编号
                        if(Arrays.asList("RQ01","RQ02","RQ03","RQ04").contains(faPiaoHeaderInfo.getTypeCode())) {
                            ProjectInfo projectInfo = yofcMapper.yofcProjectInfo(lineInfo.getProjectCode());
                            if(projectInfo != null && !"PA08".equals(projectInfo.getColumn10())) {
                                lineNetMap.put("ATTRIBUTE3", projectInfo.getColumn10());
                            }
                        }
                        // 行上记账日期逻辑，redmine 27149,
                        // 除T017和RQ04单据以外全部使用当前日期
                        // T017单据类型使用T65费用行的最后的发票日期。
                        // RQ04使用发票日期
                        if ("T017".equals(faPiaoHeaderInfo.getTypeCode())) {
                            lineNetMap.put("ACCOUNTING_DATE", dateFormat.format(DateUtils.addHours(new Date(receiptTimeLastOne), 8)));
                            // redmine=55055
                            lineNetMap.put("FSCODE", obj2Str(getColumnVal(lineInfo.getColumnJson(), "column61")));// 发货指令编号
                            lineNetMap.put("ORDERLINENO", obj2Str(getColumnVal(lineInfo.getColumnJson(), "column62")));// SO行号
                            lineNetMap.put("FEXPENSETYPE", lineInfo.getTypeCode());// 费用类型
                            Object column56 = getColumnVal(lineInfo.getColumnJson(), "column56");
                            lineNetMap.put("ATTRIBUTE15",column56);
                        } else if (lineInfo.getReceiptDate() == null){
                            lineNetMap.put("ACCOUNTING_DATE",date);
                        } else if (Arrays.asList("RQ04", "RQ07").contains(faPiaoHeaderInfo.getTypeCode())) {
                            lineNetMap.put("ACCOUNTING_DATE", dateFormat.format(DateUtils.addHours(lineInfo.getReceiptDate(), 8)));
                        } else{
                            lineNetMap.put("ACCOUNTING_DATE",date);
                        }
                        if (lineInfo.getPayMethodId() == null) {
                            lineNetMap.put("AMOUNT", netAmount);
                        } else {
                            switch (lineInfo.getPayMethodId()) {
                                case PAYMETHODID1 :
                                    lineNetMap.put("AMOUNT", netAmount);
                                    break;
                                case PAYMETHODID2 :
                                    lineNetMap.put("AMOUNT", netAmount.negate());
                                    break;
                                case PAYMETHODID0:
                                    lineNetMap.put("AMOUNT", BigDecimal.ZERO);
                            }
                        }
                        lineNetMap.put("DESCRIPTION", lineInfo.getComments());
                        // 项目号
                        lineNetMap.put("PROJECT_NUMBER", getProjectNumber(faPiaoHeaderInfo,lineInfo));
                        lineNetMap.put("LINE_GROUP_NUMBER", y);
                        // 预扣税组
                        lineNetMap.put("PAY_AWT_GROUP_NAME", obj2Str(lineInfo.getColumn28()));
                        //
                        if (PREPAYMENT_TYPE_LIST.contains(faPiaoHeaderInfo.getTypeCode())){
                            lineNetMap.put("DIST_CODE_CONCATENATED", jie1 + "." + faPiaoHeaderInfo.getBranchColumn5() + "." + lineInfo.getDrAccountCode() + "." + jie4 +"." + jie5 + "." + jie6);
                        }else {
                            lineNetMap.put("DIST_CODE_CONCATENATED", jie1 + "." + jie2 + "." + lineInfo.getDrAccountCode() + "." + jie4 + "." + jie5 + "." + jie6);
                        }
                        //
                        if("RQ04".equals(faPiaoHeaderInfo.getTypeCode()) && "EX381".equals(lineInfo.getTypeCode())) {
                            lineNetMap.put("ATTRIBUTE10", getColumnVal(lineInfo.getColumnJson(), "column110"));
                            lineNetMap.put("ATTRIBUTE11", getColumnVal(lineInfo.getColumnJson(), "column111"));
                        }

                        lineNetMap.put("CC_CODE", getCCcode(faPiaoHeaderInfo,lineInfo));

                        BigDecimal amount = (BigDecimal) lineNetMap.get("AMOUNT");
                        if(amount != null && amount.compareTo(BigDecimal.ZERO) == 0) {
                            logger.warn("{}忽略费用行，单据号={}，line={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), JSON.toJSONString(lineInfo));
                        } else {
                            skipZeroTaxFlag = false;// 存在费用行的情况下，哪怕税行金额为0也不能忽略
                            // 当费用行上字段column37=01（是否为PG项目），补充科目行
                            if("01".equals(lineInfo.getColumn37())) {
                                String column38 = lineInfo.getColumn38();
                                logger.info("{}行column37为01增加补充科目行，单据号={}，pg调整类型={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), pgAdjustType);
                                if(StrUtil.equals("1", pgAdjustType)) {
                                    String distCodeConcatenated = (String)lineNetMap.get("DIST_CODE_CONCATENATED");
                                    List oldList = StrUtil.split(distCodeConcatenated, ".");
                                    List newList = new ArrayList();
                                    for(int j = 0; j < oldList.size(); j++) {
                                        if(j == oldList.size() - 1) {// 替换倒数第1个字段
                                            String tail = "00000";
                                            if(StrUtil.equals("01", column57)) {
                                                tail = "00001";
                                            } else if(StrUtil.equals("02", column57)) {
                                                tail = "00002";
                                            }
                                            newList.add(tail);
                                        } else if(j == oldList.size() - 2){// 替换倒数第2个字段
                                            newList.add(column38);
                                        } else {
                                            newList.add(oldList.get(j));
                                        }
                                    }
                                    lineNetMap.put("DIST_CODE_CONCATENATED", StrUtil.join(".", newList));
                                    // 需要走补充对私凭证的行
                                    lineInfo.setReceiptAmount(netAmount.add(taxAmount));
                                    lineInfo.setTaxAmount(taxAmount);
                                    pgLines.add(lineInfo);
                                } else if(StrUtil.equals("2", pgAdjustType)) {
                                    String distCodeConcatenated = (String)lineNetMap.get("DIST_CODE_CONCATENATED");
                                    List oldList = StrUtil.split(distCodeConcatenated, ".");
                                    List newList = new ArrayList();
                                    for(int j = 0; j < oldList.size(); j++) {
                                        if(j == oldList.size() - 2) {// 替换倒数第2个字段
                                            newList.add(column38);
                                        } else {
                                            newList.add(oldList.get(j));
                                        }
                                    }
                                    lineNetMap.put("DIST_CODE_CONCATENATED", StrUtil.join(".", newList));
                                } else {
                                    // 补充行
                                    String lineJson = JSON.toJSONString(lineNetMap);
                                    Map<String, Object> drLine = JSON.parseObject(lineJson, Map.class);
                                    Map<String, Object> crLine = JSON.parseObject(lineJson, Map.class);
                                    // 补充借方
                                    String column39 = lineInfo.getColumn39();
                                    // String column57 = obj2Str(getColumnVal(lineInfo.getColumnJson(), "column57"));
                                    cn.hutool.json.JSONObject jsonObj = new cn.hutool.json.JSONObject()
                                            .putOpt("type", "Pub")
                                            .putOpt("adjustType", pgAdjustType)
                                            .putOpt("documentKey", faPiaoHeaderInfo.getDucumentNum())
                                            .putOpt("branchCol4", branchCol4)
                                            .putOpt("branchCol5", branchCol5)
                                            .putOpt("column79", column79)
                                            .putOpt("column38", column38)
                                            .putOpt("column39", column39)
                                            .putOpt("column57", column57)
                                            .putOpt("projectCode", lineInfo.getProjectCode())
                                            .putOpt("pgAdjustType",pgAdjustType)
                                            ;
                                    String extDrAccountCode = getExtDrAccountCode(jsonObj);
                                    if(extDrAccountCode == null) {
                                        errorMsg = faPiaoHeaderInfo.getDucumentNum() + "补充科目借方行数据校验失败lineId=" + lineInfo.getLineId();
                                        return errorMsg;
                                    }
                                    drLine.put("DIST_CODE_CONCATENATED", extDrAccountCode);
                                    drLine.put("LINE_NUMBER", ++i);
                                    drLine.put("AMOUNT", netAmount);
                                    // 补充贷方
                                    String column53 = obj2Str(getColumnVal(lineInfo.getColumnJson(), "column53"));
                                    cn.hutool.json.JSONObject jsonObj2 = new cn.hutool.json.JSONObject()
                                            .putOpt("type", "Pub")
                                            .putOpt("adjustType", pgAdjustType)
                                            .putOpt("documentKey", faPiaoHeaderInfo.getDucumentNum())
                                            .putOpt("branchCol4", branchCol4)
                                            .putOpt("branchCol5", branchCol5)
                                            .putOpt("column53", column53)
                                            .putOpt("column79", column79)
                                            .putOpt("projectCode", lineInfo.getProjectCode())
                                            .putOpt("pgAdjustType",pgAdjustType)
                                            ;
                                    String extCrAccountCode = getExtCrAccountCode(jsonObj2);
                                    if(extCrAccountCode == null) {
                                        errorMsg = faPiaoHeaderInfo.getDucumentNum() + "补充科目贷方行数据校验失败lineId=" + lineInfo.getLineId();
                                        return errorMsg;
                                    }
                                    crLine.put("DIST_CODE_CONCATENATED", extCrAccountCode);
                                    crLine.put("LINE_NUMBER", ++i);
                                    crLine.put("AMOUNT", netAmount.negate());
                                    lines.add(drLine);
                                    lines.add(crLine);
                                }
                            }

                            lines.add(lineNetMap);
                        }
                    }

                    if (!(null != faPiaoHeaderInfo.getTypeCode() && ("RQ01".equals(faPiaoHeaderInfo.getTypeCode())|| "RQ03".equals(faPiaoHeaderInfo.getTypeCode())))) {
                        i++;
                        Map<String, Object> lineTaxMap = new HashMap<>();
                        lineTaxMap.put("LINE_NUMBER",i);
                        lineTaxMap.put("LINE_TYPE_LOOKUP_CODE","TAX");
                        lineTaxMap.put("ACCOUNTING_DATE", date);
                        if ("T017".equals(faPiaoHeaderInfo.getTypeCode())) {
                            // redmine=55055
                            lineTaxMap.put("FSCODE", obj2Str(getColumnVal(lineInfo.getColumnJson(), "column61")));// 发货指令编号
                            lineTaxMap.put("ORDERLINENO", obj2Str(getColumnVal(lineInfo.getColumnJson(), "column62")));// SO行号
                            lineTaxMap.put("FEXPENSETYPE", lineInfo.getTypeCode());// 费用类型
                        }
                        // 预扣税组
                        lineTaxMap.put("PAY_AWT_GROUP_NAME", obj2Str(lineInfo.getColumn28()));
                        //
                        if (lineInfo.getPayMethodId() == null) {
                            lineTaxMap.put("AMOUNT", lineInfo.getTaxAmount());
                        }
                        else {
                            switch (lineInfo.getPayMethodId()) {
                                case PAYMETHODID1 :
                                    lineTaxMap.put("AMOUNT", taxAmount);
                                    break;
                                case PAYMETHODID2 :
                                    lineTaxMap.put("AMOUNT", taxAmount.negate());
                                    break;
                                case PAYMETHODID0:
                                    lineTaxMap.put("AMOUNT", BigDecimal.ZERO);
                            }
                        }

                        /*if(UNION_DESC_TYPE_LIST.contains(faPiaoHeaderInfo.getTypeCode())) {
                            lineTaxMap.put("DESCRIPTION", description);
                        } else {
                            lineTaxMap.put("DESCRIPTION",faPiaoHeaderInfo.getDucumentNum() +"-"+ faPiaoHeaderInfo.getChargeUser()+"-"+lineInfo.getComments()+"-"+faPiaoHeaderInfo.getColumn32()+"-"+faPiaoHeaderInfo.getColumn42());
                        }*/
                        lineTaxMap.put("DESCRIPTION", lineInfo.getComments());
                        // 项目号
                        lineTaxMap.put("PROJECT_NUMBER", getProjectNumber(faPiaoHeaderInfo,lineInfo));
                        lineTaxMap.put("DIST_CODE_CONCATENATED", jie1 + "."+ faPiaoHeaderInfo.getBranchColumn5() + "." + lineInfo.getTaxAccountCode() + ".00.00000.00000");
                        lineTaxMap.put("LINE_GROUP_NUMBER",y);
                        if (flag) {
                            lineTaxMap.put("TAX_RATE_CODE", lineInfo.getTaxCode());
                        } else {
                            lineTaxMap.put("TAX_RATE_CODE", T65taxCode);
                        }
                        //
                        lineTaxMap.put("CC_CODE", getCCcode(faPiaoHeaderInfo,lineInfo));
                        BigDecimal amount = (BigDecimal) lineTaxMap.get("AMOUNT");
                        if(amount != null && amount.compareTo(BigDecimal.ZERO) == 0 && skipZeroTaxFlag) {
                            logger.warn("{}忽略税行，单据号={}，line={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), JSON.toJSONString(lineInfo));
                        } else {
                            lines.add(lineTaxMap);
                        }
                    }
                }
            }

            HeaderMap.put("INVOICE_CURRENCY_CODE",currencyCode);
            if (faPiaoHeaderInfo.getTypeCode() != null) {
                if (("RQ01".equals(faPiaoHeaderInfo.getTypeCode()) || "RQ03".equals(faPiaoHeaderInfo.getTypeCode()))) {
                    HeaderMap.put("INVOICE_DATE", date);
                } else if ("T017".equals(faPiaoHeaderInfo.getTypeCode())) {
                    // redmine 27149
                    HeaderMap.put("INVOICE_DATE", dateFormat.format(DateUtils.addHours(new Date(receiptTimeLastOne), 8)));
                }
                else if (Arrays.asList("RQ04", "RQ07").contains(faPiaoHeaderInfo.getTypeCode())) {
                    // redmine 27149
                    HeaderMap.put("INVOICE_DATE", dateFormat.format(DateUtils.addHours(new Date(receiptTimeFirstLine), 8)));
                } else {
                    HeaderMap.put("INVOICE_DATE", date);
                }
            }
            HeaderMap.put("INVOICE_AMOUNT",allReceiptAmount);
            HeaderMap.put("CUX_AP_LINES_INTERFACE",lines);
            //核销
            if ("Y".equals(HeaderMap.get("APPLY_PREPAY_FLAG"))){
                heXiaoMap.put("CUX_AP_APPLY_PREPAY_IF_L",lineHeXiaoLists);
                HeaderMap.put("CUX_AP_APPLY_PREPAY_IF",heXiaoMap);
            }
            //获取长飞token
            Map<String, String> tokenMap = getTokenMap(faPiaoHeaderInfo.getDucumentNum());
            if (null != tokenMap){
                HeaderMap.put("TOKEN",tokenMap.get("TOKEN"));
                LinkedHashMap<String, Object> stringObjectLinkedHashMap = new LinkedHashMap<>(HeaderMap);
                String postData = JSON.toJSONString(stringObjectLinkedHashMap);
                logger.info("{}调用长飞接口请求，单据号={}，入参={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), postData);
                Map<String, String> identity = new HashMap<>();
                identity.put("Content-Type", "application/json");
                String responseStr = null;
                try {
                    responseStr = HttpUtil.HttpPostString(APFAPIAOURL,identity, postData,null,null);
                } catch (Exception e) {
                    logger.error("{}调用长飞接口异常，单据号={}，错误={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), e.getMessage(), e);
                    errorMsg= faPiaoHeaderInfo.getDucumentNum()+"调用长飞发票接口报错";
                    return errorMsg;
                }
                logger.info("{}调用长飞接口响应，单据号={}，返参={}", LogCtx.getLogKey(), faPiaoHeaderInfo.getDucumentNum(), responseStr);
                Map map = JSON.parseObject(responseStr, Map.class);
                if ("false".equals(map.get("STATUS").toString())){
                    errorMsg= faPiaoHeaderInfo.getDucumentNum()+":"+map.get("MESSAGE");
                    return errorMsg;
                } else {
                    String batchName = null != HeaderMap.get("BATCH_NAME") ? HeaderMap.get("BATCH_NAME").toString() : null;
                    String voucherNumber = map.get("X_DOCSEQUENCE_NO") == null ? "" : map.get("X_DOCSEQUENCE_NO").toString();
                    // 回写凭证
                    voucherBack(faPiaoHeaderInfo.getDucumentNum(), voucherNumber, "generated", "",
                                DateUtil.parse(date, "yyyy-MM-dd").getTime(), faPiaoHeaderInfo.getLedgerName());
                    // gl_je_batch
//                    GlJeBatch glJeBatch = new GlJeBatch();
//                    yofcMapper.addGlJe("16895", voucherNumber, userId, glJeBatch, faPiaoHeaderInfo.getLedger1());
                    // 更新column93
                    String columnJson = yofcMapper.yofcColumnJson(faPiaoHeaderInfo.getHeaderId());
                    JSONObject columnJsonObj = Strings.isNullOrEmpty(columnJson) ?
                            new JSONObject() : JSONObject.parseObject(columnJson);
                    columnJsonObj.put("column93", batchName);
                    yofcMapper.updateColumnJson(faPiaoHeaderInfo.getHeaderId(), columnJsonObj.toJSONString());
                    // gl_je_batch_assign
//                    Integer batchId = glJeBatch.getBatch_id();
//                    yofcMapper.addBatch("16895", batchId, faPiaoHeaderInfo.getHeaderId());
                    // 更新journal_num, gl_date
                    yofcMapper.updateByVoucher(voucherNumber,date,userId, "16895", faPiaoHeaderInfo.getHeaderId());
                }
                // pg项目补充对私凭证接口
                if(!pgLines.isEmpty()) {
                    postPgLines(faPiaoHeaderInfo, pgLines, userIdentityColumn1, batchDate);
                }
            } else {
                errorMsg= faPiaoHeaderInfo.getDucumentNum()+"获取长飞token失败";
                return errorMsg;
            }

        }
        logger.info("{}结束处理", LogCtx.getLogKey());
        return errorMsg;
    }

    private String getProjectNumber(FaPiaoHeaderInfo faPiaoHeaderInfo, FaPiaoLineInfo lineInfo) {
        if(checkPG(lineInfo)) {
          return "";
        } else if(Arrays.asList("RQ03").contains(faPiaoHeaderInfo.getTypeCode())){
            if(faPiaoHeaderInfo.getColumn13().equals("CT01")){

                return lineInfo.getProjectCode()!=null && !lineInfo.getProjectCode().equals("N/A")?
                        lineInfo.getProjectCode()
                        :null;

            }else if(faPiaoHeaderInfo.getColumn13().equals("CT02")){

                return lineInfo.getProjectCode()!=null && !lineInfo.getProjectCode().equals("N/A") && lineInfo.getProjectColumn3()!=null ?
                        lineInfo.getProjectColumn3()
                        :(faPiaoHeaderInfo.getProjectCode()!=null && !faPiaoHeaderInfo.getProjectCode().equals("N/A")?faPiaoHeaderInfo.getProjectCode():null);

            }
            return null;
        }else if(Arrays.asList("T018").contains(faPiaoHeaderInfo.getTypeCode())){
            if(faPiaoHeaderInfo.getColumn30().equals("是")){

                return lineInfo.getProjectCode()!=null && !lineInfo.getProjectCode().equals("N/A") ?
                        lineInfo.getProjectCode()
                        : (faPiaoHeaderInfo.getProjectCode()!=null && !faPiaoHeaderInfo.getProjectCode().equals("N/A")?faPiaoHeaderInfo.getProjectCode():null);

            }else if(faPiaoHeaderInfo.getColumn30().equals("否")){

                //若前序单据(T014)的头字段column13为CT01，则取行上项目的project_code；
                //若前序单据(T014)的头字段column13为CT02，且行上project_name不为无（N/A），则取PO第一行（行字段column4=1） 项目字段（project.column3）；
                //若行上project_name为无（N/A），则不传。
                FaPiaoLinkHeaderInfo t014Header = getLinkClaimHeaderByTypeCode(faPiaoHeaderInfo, "T014");
                List<FaPiaoLineInfo> t014 = getLinkClaimByTypeCode(faPiaoHeaderInfo, "T014");
                FaPiaoLineInfo linkLineInfo = null;
                if(t014!=null && t014.size()>0) {
                    List<FaPiaoLineInfo> collect = t014.stream().filter(e -> e.getColumn4() != null && e.getColumn4().equals("1")).collect(Collectors.toList());
                    if(collect!=null && collect.size()>0){
                        linkLineInfo = collect.get(0);
                    }
                }
                if(t014Header.getColumn13().equals("CT01")){

                     return linkLineInfo!=null && linkLineInfo.getProjectCode()!=null && !linkLineInfo.getProjectCode().equals("N/A") ?
                            linkLineInfo.getProjectCode()
                            :null;

                }else if(t014Header.getColumn13().equals("CT02")){

                     return linkLineInfo!=null && linkLineInfo.getProjectCode()!=null && !linkLineInfo.getProjectCode().equals("N/A") && linkLineInfo.getProjectColumn3()!=null ?
                            linkLineInfo.getProjectColumn3()
                            :null;

                    }

                }

            return null;
        }else{
            return lineInfo.getProjectCode()!=null && !lineInfo.getProjectCode().equals("N/A") ?
                    lineInfo.getProjectCode()
                    : (faPiaoHeaderInfo.getProjectCode()!=null && !faPiaoHeaderInfo.getProjectCode().equals("N/A")?faPiaoHeaderInfo.getProjectCode():null);
        }
    }

    private String getCCcode(FaPiaoHeaderInfo faPiaoHeaderInfo,FaPiaoLineInfo lineInfo) {

        if(Arrays.asList("RQ03").contains(faPiaoHeaderInfo.getTypeCode())){
            return faPiaoHeaderInfo.getChargeDepartmentColumn1();
        }else if(Arrays.asList("T018").contains(faPiaoHeaderInfo.getTypeCode())){
            if(faPiaoHeaderInfo.getColumn30().equals("是")){
                return faPiaoHeaderInfo.getSubmitDepartmentColumn1();
            }else if(faPiaoHeaderInfo.getColumn30().equals("否")){
                List<FaPiaoLineInfo> t014 = getLinkClaimByTypeCode(faPiaoHeaderInfo, "T014");
                if(t014!=null && t014.size()>0){
                    List<FaPiaoLineInfo> collect = t014.stream().filter(e -> e.getColumn4()!=null && e.getColumn4().equals("1")).collect(Collectors.toList());
                    if(collect!=null && collect.size()>0){
                        return collect.get(0).getCostCenterColumn1();
                    }
                }
            }
        }else{
            return lineInfo.getCostCenterColumn1();
        }
        return "";
    }

    private String getBudgetNumber(FaPiaoHeaderInfo faPiaoHeaderInfo) {
        if(Arrays.asList("T017").contains(faPiaoHeaderInfo.getTypeCode())){
            return  (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column61");
        }else if(Arrays.asList("RQ04","RQ07","RQ03").contains(faPiaoHeaderInfo.getTypeCode())){
            return faPiaoHeaderInfo.getColumn26();
        }else if(Arrays.asList("T018").contains(faPiaoHeaderInfo.getTypeCode())){
            if(faPiaoHeaderInfo.getColumn30().equals("是")){
                return  (String)getColumnVal(faPiaoHeaderInfo.getColumnJson(), "column61");
            }else if(faPiaoHeaderInfo.getColumn30().equals("否")){
                //暂定取关联的前序单据采购订单(T014)PO第一行（行字段column4=1）的预算科目字段budget_id找对应的budget_code,截取前面一段（去掉最前面的公司代码部分，比如01.11230100.0000要变成 11230100.0000）。
                List<FaPiaoLineInfo> t014 = getLinkClaimByTypeCode(faPiaoHeaderInfo, "T014");
                if(t014!=null && t014.size()>0){
                    List<FaPiaoLineInfo> collect = t014.stream().filter(e -> e.getColumn4()!=null && e.getColumn4().equals("1")).collect(Collectors.toList());
                    if(collect!=null && collect.size()>0){
                        String budgetCode = collect.get(0).getBudgetCode();
                        if(StringUtils.isNotEmpty(budgetCode) && budgetCode.indexOf(".")>0){
                            return budgetCode.substring(budgetCode.indexOf(".")+1);
                        }
                    }
                }
            }
        }
        return "";
    }

    private List<FaPiaoLineInfo> getLinkClaimByTypeCode(FaPiaoHeaderInfo faPiaoHeaderInfo,String typeCode){
        Integer linkHeaderId = null;

        FaPiaoLinkHeaderInfo linkClaimHeaderByTypeCode = getLinkClaimHeaderByTypeCode(faPiaoHeaderInfo, typeCode);
        if(linkClaimHeaderByTypeCode!=null){
            linkHeaderId = faPiaoHeaderInfo.getLinkHeaderId();
        }

        if(linkHeaderId != null){
            return yofcMapper.yofcPostLineData(linkHeaderId.toString());
        }
        return null;
    }

    private FaPiaoLinkHeaderInfo getLinkClaimHeaderByTypeCode(FaPiaoHeaderInfo faPiaoHeaderInfo,String typeCode){
        if(faPiaoHeaderInfo.getLinkHeaderId()==null){
            return null;
        }

        FaPiaoLinkHeaderInfo linkHeaderInfo = yofcMapper.getLinkHeader(faPiaoHeaderInfo.getLinkHeaderId());
        if(typeCode.equals(linkHeaderInfo.getTypeCode())) {
            return linkHeaderInfo;
        }
        return null;
    }

    /**
     * 获取pg项目调整类型
     * @param head
     * @param line
     * @return
     */
    private String getPgAdjustType(FaPiaoHeaderInfo head, FaPiaoLineInfo line) {
        String adjustType = "";// 调整类型
        String column13 = head.getColumn13();
        String column38 = line.getColumn38();
        if(StrUtil.equals("CT02", column13)) {
            FndLovValue column38Lov = yofcMapper.yofcLovValue("PG项目", column38);
            if (column38Lov != null && StrUtil.equals("否", column38Lov.getColumn1())) {
                adjustType = "1";
            } else {
                adjustType = "3";
            }
        } else if(StrUtil.equals("CT01", column13)) {
            adjustType = "2";
        }
        return adjustType;
    }

    private void postPgLines(FaPiaoHeaderInfo header, List<FaPiaoLineInfo> lines, String userCol1, String batchDate) {
        logger.info("{}PG项目补推对私接口开始，单据号={}，header={}，line={}", LogCtx.getLogKey(), header.getDucumentNum(), header, lines);
        JSONArray lineArray = new JSONArray();
        for(FaPiaoLineInfo line : lines) {
            // 借方
            JSONObject lineObjectDr = new JSONObject();
            BigDecimal receiptAmount = Optional.ofNullable(line.getReceiptAmount()).orElse(BigDecimal.ZERO);
            BigDecimal taxAmount = Optional.ofNullable(line.getTaxAmount()).orElse(BigDecimal.ZERO);
            BigDecimal lineAmount = receiptAmount.subtract(taxAmount);
            lineObjectDr.put("line_amount", lineAmount);
            // yyyyMM转yyyy-MM
            if(StrUtil.isNotEmpty(batchDate) && !StrUtil.contains(batchDate, "-")) {
                lineObjectDr.put("period_name",
                        StrUtil.sub(batchDate, 0, 4) +
                        "-" +
                        StrUtil.sub(batchDate, 4, 6));
            }
            lineObjectDr.put("currency_code",header.getCurrencyCode());
            lineObjectDr.put("last_updated_by","");
            lineObjectDr.put("line_description","");
            lineObjectDr.put("TOKEN","");
            lineObjectDr.put("last_update_date","");
            lineObjectDr.put("error_message","");
            lineObjectDr.put("attribute_category","");
            lineObjectDr.put("line_num",1);
            lineObjectDr.put("last_update_login","");

            String journalName = header.getDucumentNum() + header.getSupplierName();
            /*if(StrUtil.equals("T017", header.getTypeCode())) {
                if(StrUtil.equals("否", header.getColumn30())) {
                    journalName = header.getDucumentNum() + header.getChargeUser() + header.getDescription() + header.getColumn15();
                } else {
                    journalName = header.getDucumentNum() + header.getChargeUser() + header.getDescription();
                }
            } else if(StrUtil.equals("RQ04", header.getTypeCode())) {
                journalName = header.getDucumentNum() + header.getChargeUser() + header.getDescription() + header.getColumn32() + header.getColumn42();
            }*/
            lineObjectDr.put("journal_name",journalName);

            lineObjectDr.put("journal_description",header.getDucumentNum() + header.getChargeUser() + line.getComments() + header.getColumn32() + header.getColumn42());
            lineObjectDr.put("creation_date","");
            lineObjectDr.put("created_by","");
            lineObjectDr.put("batch_name", userCol1 + batchDate + "01");// todo 点击生成凭证人员column1-生成凭证时选择日期的年月-01
            lineObjectDr.put("source_doc_num", header.getDucumentNum());
            lineObjectDr.put("attribute5","");
            lineObjectDr.put("project_number",line.getProjectCode());
            lineObjectDr.put("attribute4","");
            lineObjectDr.put("dr_cr","DR");
            lineObjectDr.put("org_id", header.getFdCode());
            lineObjectDr.put("source_doc_type","");
            lineObjectDr.put("attribute1","");
            ProjectInfo projectInfo1 =null;
            String jie4="00";
            if(line.getProjectCode()!=null) projectInfo1= yofcMapper.yofcProjectInfo(line.getProjectCode());
            if(projectInfo1!=null && projectInfo1.getColumn10().equals("PA06") && StrUtil.equals(header.getSupplierColumn5(), "是")) {
                jie4 = header.getSupplierColumn6();
            }
            lineObjectDr.put("account_code", header.getBranchColumn4() + "." + line.getProjectColumn2() + "." + line.getDrAccountCode() + "." + jie4 + "." + line.getProjectColumn3() + "." + "00000");
            lineObjectDr.put("attribute3","");
            lineObjectDr.put("attribute2","");
            lineObjectDr.put("status","");
            lineArray.add(lineObjectDr);
            // 贷方
            JSONObject lineObjectCr = new JSONObject();
            lineObjectCr.putAll(lineObjectDr);
            JSONObject columnJson;
            if(JSON.isValid(line.getColumnJson())) {
                columnJson = JSONObject.parseObject(line.getColumnJson());
            } else {
                columnJson = new JSONObject();
            }
            lineObjectCr.put("account_code", header.getBranchColumn4() + "." + line.getProjectColumn2() + "." + "********.0000" + "." + "00" + "." + "00000" + "." + "00000");
            lineObjectCr.put("line_amount", lineAmount.negate());
            lineObjectDr.put("dr_cr","CR");
            lineObjectDr.put("line_num",2);
            lineArray.add(lineObjectCr);
        }
        post2YofcPrivate(lineArray, header.getDucumentNum());
        logger.info("{}PG项目补推对私接口结束，单据号={}", LogCtx.getLogKey(), header.getDucumentNum());
    }

    /**
     * 数字不足位数补0
     *
     * @param str
     * @param strLength
     * @return
     */
    private static String addZeroForNum(String str, int strLength) {
        int strLen = str.length();
        if (strLen < strLength) {
            while (strLen < strLength) {
                StringBuffer sb = new StringBuffer();
                sb.append("0").append(str);//左补0
                // sb.append(str).append("0");//右补0
                str = sb.toString();
                strLen = str.length();
            }
        }
        return str;
    }

    @Override
    public String postPaymentVouchersPay003(List<Integer> documentIds, int userId, String date, int number) {
        StringBuilder errorMsg = new StringBuilder();
        for (Integer documentId : documentIds) {
            String msg = null;
            try {
                if(!UniLock.lock(documentId, 30)) {
                    logger.error("单据{}出现重复请求，忽略", documentId);
                    continue;
                }
                Pay3Header pay3Header = yofcMapper.yofcHeaderPay(documentId);
                // 获取账号对应银行swift码
                String accountNumber = "N/A";// 默认账号
                if(pay3Header.getBranchAccountId() != null) {
                    FndUserAccount branchAccount = yofcMapper.getBranchAccountNumberById(pay3Header.getBranchAccountId());
                    accountNumber = branchAccount.getAccountNumber();
                }
                FndLovValue bankAccount = yofcMapper.yofcLovValue("INTERNAL_BANK_ACCOUNT_ID", accountNumber);
                if(bankAccount != null) {
                    pay3Header.setColumn44(bankAccount.getValueMeaning());
                }
                if (null != pay3Header.getColumn44()){
                    if (null != pay3Header.getTotal_pay_amount() && pay3Header.getTotal_pay_amount().compareTo(BigDecimal.ZERO) == 1){
                        //前序单据信息
                        Pay3Header linkHeader = yofcMapper.getLinkPay3Header(pay3Header.getHeaderId());
                        String column14 =  linkHeader.getColumn14();
                        String frontDocument = linkHeader.getDocumentNum();
                        String typeCode = linkHeader.getTypeCode();
                        Map<String, Object> postMap = new HashMap<>();
                        postMap.put("CUX_AP_FINAL_TYPE","PAYMENT");
                        postMap.put("INVOICE_DATE",pay3Header.getColumn31());
                        // 支付方式
                        String acct = accountNumber;
                        String flag = acct.substring(acct.length() - 1);
                        if("X".equals(flag)) {
                            postMap.put("PAYMENT_METHOD_CODE","TRADE_BILLS_PAYABLE");
                        } else if("Y".equals(flag)) {
                            postMap.put("PAYMENT_METHOD_CODE","BILLS_PAYABLE");
                        } else {
                            postMap.put("PAYMENT_METHOD_CODE","WIRE");
                        }
                        //
                        FndLovValue paymentType = yofcMapper.yofcLovValue("Payment Type", pay3Header.getPaymentType());
                        postMap.put("PAYMENT_METHOD_CODE", paymentType == null ? "WIRE" : paymentType.getDescription());

                        postMap.put("PAY_AMOUNT",pay3Header.getTotal_pay_amount());
                        postMap.put("SOURCE","ECM");
                        postMap.put("PAY_FLAG","Y");
                        postMap.put("IMP_SRC_ORDER_NUM", frontDocument);
                        if (Arrays.asList("RQ05","RQ06","T019").contains(typeCode)) {
                            //取前序的前序的单据号
                            List<Integer> linkHeaderIds = yofcMapper.getLinkHeaderIds(companyId, linkHeader.getHeaderId());
                            if(CollUtil.isNotEmpty(linkHeaderIds)) {
                                List<ClaimHeader> linkClaims = yofcMapper.yofcGetClaimHeaderListByHeaderIds(linkHeaderIds);
                                List<String> documentNums = linkClaims.stream().map(ClaimHeader::getDocumentNum).collect(Collectors.toList());
                                postMap.put("IMP_SRC_ORDER_NUM", StrUtil.join(",", documentNums));
                            }
                        }
                        postMap.put("INTERNAL_BANK_ACCOUNT_ID",pay3Header.getColumn44());
                        postMap.put("PAYMENT_PROFILE_ID","161");
                        // 支付时间
                        postMap.put("ACTUAL_PAY_DATE", DateFormatUtils.format(DateUtils.addHours(
                                pay3Header.getActualPaymentDate(), 8), "yyyy-MM-dd"));// 对方是东八区时间
                        // 票据号
                        String column41 = obj2Str(pay3Header.getColumn41());
                        String column79 = obj2Str(getColumnVal(pay3Header.getColumnJson(), "column79"));
                        String column80 = obj2Str(getColumnVal(pay3Header.getColumnJson(), "column80"));
                        if(StrUtil.equals(column41, "DONE")) {
                            postMap.put("DESCRIPTION", column79);
                            postMap.put("PAYMENT_METHOD_CODE","WIRE");
                        } else if(StringUtils.isNotEmpty(column80)) {
                            postMap.put("DESCRIPTION", "#" + column80);
                        } else {
                            postMap.put("DESCRIPTION", "");
                        }
                        // 出票日期
                        postMap.put("ATTRIBUTE5", obj2Str(getColumnVal(pay3Header.getColumnJson(), "column81")));
                        // 结束日期
                        postMap.put("FUTURE_PAY_DUE_DATE", obj2Str(getColumnVal(pay3Header.getColumnJson(), "column82")));
                        // 接口处理
                        Map<String, String> tokenMap = getTokenMap(pay3Header.getDocumentNum());
                        if (null != tokenMap) {
                            postMap.put("TOKEN", tokenMap.get("TOKEN"));
                            String postData = JSON.toJSONString(postMap,SerializerFeature.WriteMapNullValue);
                            logger.info("{}对公单据{}，AP付款，入参={}", LogHolder.getLogKey(), pay3Header.getDocumentNum(),
                                    JSON.toJSONString(postMap,SerializerFeature.WriteMapNullValue));
                            Map<String, String> identity = new HashMap<>();
                            identity.put("Content-Type", "application/json");
                            try {
                                String responseStr = HttpUtil.HttpPostString(APFUKUANURL, identity, postData, null, null);
                                logger.info("{}对公单据{}，AP付款，返参={}", LogHolder.getLogKey(), pay3Header.getDocumentNum(), responseStr);
                                Map responseMap = JSON.parseObject(responseStr,Map.class);
                                if ("false".equals(responseMap.get("STATUS").toString())){
                                    //失败
                                    logger.error("{}对公单据{}，调用长飞接口返回失败:{}", LogHolder.getLogKey(), pay3Header.getDocumentNum(),
                                            responseMap.get("MESSAGE"));
                                    msg = "对公单据"+pay3Header.getDocumentNum()+"调用长飞接口返回失败:" + responseMap.get("MESSAGE");
                                    errorMsg.append(msg + "\n");
                                }else{
                                    //成功
                                    logger.info("{}对公单据{}，调用长飞接口返回成功:{}", LogHolder.getLogKey(), pay3Header.getDocumentNum(),
                                            responseMap.get("MESSAGE"));
//                                    JSONObject data = new JSONObject();
//                                    data.put("documentId", documentId);
//                                    data.put("voucherNumber", responseMap.get("X_DOCSEQUENCE_NO"));
//                                    update(data, "apichange");
                                    String voucherNum = (String)responseMap.get("X_DOCSEQUENCE_NO");
                                    ClaimHeader claimHeader = yofcMapper.yofcGetClaimHeaderByDocumentId(documentId.toString());
                                    // 更新凭证号
                                    yofcMapper.updateJournalNum(documentId, voucherNum);
                                    voucherBack(claimHeader.getDocumentNum(), voucherNum, "generated", "",
                                            DateUtil.parse(date, "yyyy-MM-dd").getTime(), null);
                                }
                            } catch (Exception e) {
                                logger.error("{}对公单据{}，调用长飞接口出现异常:{}", LogHolder.getLogKey(), pay3Header.getDocumentNum(), e.getMessage(), e);
                                msg = "对公单据"+pay3Header.getDocumentNum()+"调用长飞接口出现异常:" + e.getMessage();
                                errorMsg.append(msg + "\n");
                            }
                        }
                    } else {
                        //成功
                        logger.warn("{}对公单据{}，AP付款，total_pay_amount为0自动生成凭证", LogHolder.getLogKey(), pay3Header.getDocumentNum());
                        JSONObject data = new JSONObject();
                        data.put("documentId", documentId);
                        update(data, "apichange");
                    }
                } else {
                    //成功
                    logger.warn("{}对公单据{}，AP付款，column44无值自动生成凭证", LogHolder.getLogKey(), pay3Header.getDocumentNum());
                    JSONObject data = new JSONObject();
                    data.put("documentId", documentId);
                    update(data, "apichange");
                }
            } catch (Exception e) {
                logger.error("{}对公单据{}处理出现系统异常: {}", LogHolder.getLogKey(), documentId, e.getMessage(), e);
                msg = "对公单据"+documentId+"处理出现系统异常";
                errorMsg.append(msg + "\n");
            } finally {
                UniLock.unlock(documentId);
                if(msg != null) {
                    updateGlMessage(documentId, "error", msg);
                }
            }
        }

        Map<String, Object> resMap = new HashMap<>();
        if(errorMsg.length()>2){
            resMap.put("exception_level",999);
            resMap.put("message",errorMsg);
        }else{
            resMap.put("exception_level",0);
            resMap.put("message","成功");
        }

        return JSON.toJSONString(resMap);
    }


    @Override
    public List<ClaimCopyInfo> getCopyClaim() {

       List<ClaimCopyInfo> infos =  yofcMapper.getCopyClaim();
        Iterator<ClaimCopyInfo> iterator = infos.iterator();
        while (iterator.hasNext()) {
            ClaimCopyInfo copyInfo = iterator.next();

            if ("RQ05".equals(copyInfo.getTypeCode())){
                copyInfo.setDocumentNum(yofcMapper.getFrontDocument(copyInfo.getHeaderId()));
            }

            copyInfo.setNewTypeId(yofcMapper.gettypeId(typeMapping.get(copyInfo.getTypeCode())));
            copyInfo.setUserId(yofcMapper.getworkFlowUser(copyInfo.getHeaderId()));
        }

        return infos;
    }


    @Override
    public ExpClaimHeader copyClaim(String agent_id, String inputJson,String userId,ClaimCopyInfo copyInfo,JSONObject jsonObject) {
        // check if user's company info matches
        ExpClaimHeader ech = new ExpClaimHeader();
        JSONObject columnJson = null;
        try{
            if (null == userId){
                logger.info("{}单据{}没有从工作流中获取到审批用户", LogHolder.getLogKey(), copyInfo.getHeaderId());
//                upDateColumn16(copyInfo, "success");
                return ech;
            }
            columnJson = JSON.parseObject(copyInfo.getColumnJson());
            if(columnJson == null) {
                columnJson = new JSONObject();
            }

            FndUser user = new FndUser();
            FndCompany company = new FndCompany();
            user.setUserId(Integer.valueOf(userId));
            ech.setCreatedBy2(user);
            ech.setLastUpdatedBy2(user);

            company.setCompanyId(companyId);
            ech.setFndCompany(company);
            ech.setInput(inputJson);
            ech.setAgentId(Integer.valueOf(agent_id));
            ech.setIgnoreWarning("Y");
            ech.setUserId(Integer.valueOf(userId));

            yofcMapper.saveNew(ech);
            if ("S".equals(ech.getReturnCode())) {
                // 刷新行上调整金额
                JSONArray lineArray = jsonObject.getJSONArray("claim_line");
                BigDecimal totalPayAmount = copyInfo.getTotalPayAmount();
                BigDecimal totalPayCurrencyAmount = BigDecimal.ZERO;
                try {
                    if (null != lineArray){
                        for (Object o : lineArray) {
                            Map line =  (Map)o;

                            UpdateLine updateLine = new UpdateLine();
                            updateLine.setColumn40(null != line.get("line_id") ? line.get("line_id").toString() : "");
                            updateLine.setFinExchangeRate(null != line.get("fin_exchange_rate") ? line.get("fin_exchange_rate").toString() : null);
                            updateLine.setFinPayAmount(null != line.get("fin_pay_amount") ? line.get("fin_pay_amount").toString() : null);
                            updateLine.setFinPayClaimAmount(null != line.get("fin_pay_claim_amount") ? line.get("fin_pay_claim_amount").toString() : null);
                            updateLine.setFinPayCurrencyAmount(null != line.get("fin_pay_currency_amount") ? line.get("fin_pay_currency_amount").toString() : null);
                            updateLine.setFinReceiptAmount(null != line.get("fin_receipt_amount") ? line.get("fin_receipt_amount").toString() : null);
                            updateLine.setFinTaxAmount(null != line.get("fin_tax_amount") ? line.get("fin_tax_amount").toString() : null);
                            updateLine.setFinTaxCodeId(null != line.get("fin_tax_code_id") ? line.get("fin_tax_code_id").toString() : null);
                            updateLine.setFinClaimAmount(null != line.get("fin_claim_amount") ? line.get("fin_claim_amount").toString() : null);
                            updateLine.setFinNetAmount(null != line.get("fin_net_amount") ? line.get("fin_net_amount").toString() : null);

//                            if(!StringUtils.isEmpty(updateLine.getFinPayAmount())) {
//                                totalPayAmount = totalPayAmount.add(new BigDecimal(updateLine.getFinClaimAmount()));
//                            }
                            if(!StringUtils.isEmpty(updateLine.getFinPayCurrencyAmount())) {
                                totalPayCurrencyAmount = totalPayCurrencyAmount.add(new BigDecimal(updateLine.getFinPayCurrencyAmount()));
                            }
                            yofcMapper.updateLine(updateLine);

                        }
                    }
                    yofcMapper.updateTotalPayAmount(totalPayAmount.toString(), totalPayCurrencyAmount.toString(), ech.getDocumentNum());
                } catch (Exception e){
                    columnJson.put("column87", "一些特殊字段单独处理失败：" + e.getMessage());
                    copyInfo.setColumnJson(columnJson.toJSONString());
                    upDateColumn16(copyInfo, "success");
                    return ech;
                }
                // 单据提交，触发审批流，创建支付单
                yofcMapper.submitNew(ech);
                if (!"S".equals(ech.getReturnCode())){
                    logger.info("{}单据{}提交失败：{}", LogHolder.getLogKey(), copyInfo.getHeaderId(), ech);
                    columnJson.put("column87", "单据提交失败：" + ech.getReturnMessage());
                    copyInfo.setColumnJson(columnJson.toJSONString());
                    upDateColumn16(copyInfo, "success");
                }
            } else {
                logger.info("{}单据{}保存失败：{}", LogHolder.getLogKey(), copyInfo.getHeaderId(), ech);
                columnJson.put("column87", "单据保存失败：" + ech.getReturnMessage());
                copyInfo.setColumnJson(columnJson.toJSONString());
                upDateColumn16(copyInfo, "success");
                return ech;
            }
            logger.info("{}单据{}复制成功", LogHolder.getLogKey(), copyInfo.getHeaderId());
        } catch (Exception e){
            logger.error("{}单据{}出现异常：{}", LogHolder.getLogKey(), copyInfo.getHeaderId(), e.getMessage(), e);
            if(columnJson != null) {
                columnJson.put("column87", "单据保存失败：" + ech.getReturnMessage());
                copyInfo.setColumnJson(columnJson.toJSONString());
                upDateColumn16(copyInfo, "success");
            }
        }

        return ech;
    }

    @Override
    public void updateGlMessage(Integer documentId, String glStatus, String glMessage) {
        yofcMapper.updateGlMessageByDocumentId(documentId, glStatus,
                   glMessage != null && glMessage.length() > 255 ? glMessage.substring(0, 255) : glMessage);
    }

    private void upDateColumn16(ClaimCopyInfo copyInfo, String column16) {

        yofcMapper.updateColumn16(copyInfo.getHeaderId(), column16, copyInfo.getColumnJson());
    }

    private void  afterHandle(String LOG_KEY, JSONObject detail, ErPaySta erPaySta, String optStatus) {
        if (null != detail && erPaySta != null) {
//                        a. 当ERAGNSTAY的 status 为1， 单据状态变成支付成功
//                        b. status为2，单据状态变为支付失败
//                        c. status为3，单据状态保持不变
            String status = detail.getString("STATUS");
            String optstu = detail.getString("OPTSTU");
            logger.info("{}单据{}对私业务关闭支付单，支付结果状态={}", LOG_KEY, erPaySta.getDocumentNum(), status);
            if ("2".equals(status) || ("3".equals(status) && (OPT_STU_ERROR_LIST.contains(optstu) || OPT_STU_ERROR_LIST.contains(optStatus)))){
                yofcMapper.updatePayStatus(erPaySta.getHeaderId(),"error");
                yofcMapper.deleteTransSerialNum(erPaySta.getHeaderId());// 清理流水号以便可以重新发起支付
            }else if ("1".equals(status) || ("3".equals(status) && OPT_STU_SUCCESS_LIST.contains(optstu))){
                yofcMapper.updatePayStatus(erPaySta.getHeaderId(),"paid");
                yofcMapper.closeClaim(erPaySta.getHeaderId());
                logger.info("{}单据{}关闭成功", LOG_KEY, erPaySta.getDocumentNum());
                ApPaymentBatch apPaymentBatch = new ApPaymentBatch();
                //apPaymentBatch.setAccountId();
                apPaymentBatch.setCompanyId("16895");
                yofcMapper.addApPaymentBatch(apPaymentBatch);
                Integer batchId = apPaymentBatch.getBatchId();
                yofcMapper.addGlJePayBatchAssign(16895,batchId,erPaySta.getHeaderId());
                yofcMapper.updateHeaderBatchId(erPaySta.getHeaderId(),batchId);
                //对私业务关闭前序单据（报销单-确认单-支付单）
                //确认单单据id
                String paymentHeaderId = yofcMapper.getLinkHeaderId(erPaySta.getHeaderId());
                //报销单单据id
                String linkHeaderId = yofcMapper.getLinkHeaderId(paymentHeaderId);
                //确认单单据信息
                PayInfo paymentInfo = yofcMapper.getPaymentInfo(paymentHeaderId);
                //后续已关闭的支付单金额总计
                BigDecimal sumTotalPayAmount = yofcMapper.getSumTotalPayAmount(paymentHeaderId);
                List<String> type = Arrays.asList("request_advance", "request_travel_advance", "request_supplier");
                //校验金额，若所有支付单都已关闭，则关闭前序的确认单
                if (type.contains(paymentInfo.getInternalType())) {
                    if (paymentInfo.getAdvanceAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭支付单前序
                        yofcMapper.closeClaim(paymentHeaderId);
                        logger.info("{}单据{}的前序{}关闭成功", LOG_KEY, erPaySta.getDocumentNum(), paymentInfo.getDocumentNum());
                        //关闭前序的报销单
//                        yofcMapper.closeClaim(linkHeaderId);
//                        logger.info("{}单据{}的前序报销单{}关闭成功", LOG_KEY, erPaySta.getDocumentNum(), linkHeaderId);
                    }
                } else {
                    if (paymentInfo.getTotalPayAmount().compareTo(sumTotalPayAmount) == 0) {
                        //关闭支付单前序
                        yofcMapper.closeClaim(paymentHeaderId);
                        logger.info("{}单据{}的前序{}关闭成功", LOG_KEY, erPaySta.getDocumentNum(), paymentInfo.getDocumentNum());
                        //关闭前序的报销单
//                        yofcMapper.closeClaim(linkHeaderId);
//                        logger.info("{}单据{}的前序报销单{}关闭成功", LOG_KEY, erPaySta.getDocumentNum(), linkHeaderId);
                    }
                }
            }
        }
    }

    private Map<String, String> getTokenMap(String documentNum) {
        Map<String,String>  responseMap = null;
        try {
            Map<String, Object> tokenMap = new HashMap<>();
            tokenMap.put("USERID","FK");
            Map<String, String> identity = new HashMap<>();
            identity.put("Content-Type", "application/json");
            String responseStr =  HttpUtil.HttpPostString(CHANGFEITOKENURL,identity, JSON.toJSONString(tokenMap),null,null);
            logger.error("fapiaoPost ==== " + documentNum + " 获取长飞token结果：{}", responseStr);
            responseMap = JSON.parseObject(responseStr,Map.class);
        } catch (Exception e) {
            logger.error("fapiaoPost ==== " + documentNum + " 获取长飞token失败error：", e);
        }
        return responseMap;
    }

    private Map<String,String> typeMapping =  new HashMap<String,String>(){
        {
            put("T001","TCM01");
            put("T002","TCM02");
            put("T003","TCM03");
            put("RQ01","CM01");
            put("RQ02","CM02");
            put("RQ03","CM03");
            put("RQ04","CM04");
            put("RQ05","CM05");
            put("RQ06","CM06");
            put("RQ07","CM07");
        }
    };

    /**
     * 转换汇率值格式
     * @param rate
     * @return
     */
    public static String convert2Rate(String rate) {
        if(StringUtils.isEmpty(rate))
            return "";
        try {
            return new BigDecimal(rate).multiply(BigDecimal.valueOf(100)).stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            logger.error("汇率值转换源数据有误[{}]: {}", rate, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取column_json指定字段值
     * @param columnJson
     * @param columnName
     * @return
     */
    private Object getColumnVal(String columnJson, String columnName) {
        if(columnJson == null) {
            return null;
        }
        return JSON.parseObject(columnJson).get(columnName);
    }

    private String obj2Str(Object obj) {
        return obj == null ? "" : obj.toString();
    }

    /**
     * 获取补充借方科目
     * @param jsonObj
     * @return
     */
    private String getExtDrAccountCode(cn.hutool.json.JSONObject jsonObj) {
        String        type = jsonObj.getStr("type");
        Object documentKey = jsonObj.getStr("documentKey");
        String  branchCol4 = jsonObj.getStr("branchCol4");
        String  branchCol5 = jsonObj.getStr("branchCol5");
        String  branchCode = jsonObj.getStr("branchCode");
        String    column79 = jsonObj.getStr("column79");
        String    column38 = jsonObj.getStr("column38");
        String    column39 = jsonObj.getStr("column39");
        String    column57 = jsonObj.getStr("column57");
        String projectCode = jsonObj.getStr("projectCode");
        String pgAdjustType = jsonObj.getStr("pgAdjustType");
        if(StringUtils.isEmpty(column38) || StringUtils.isEmpty(column39) || StringUtils.isEmpty(branchCol4) || StringUtils.isEmpty(branchCol5) || StringUtils.isEmpty(column79)) {
            logger.error("{}单据{}补充科目借方行数据校验失败，column38={}, column39={}, branchCol4={}, branchCol5={}, column79={}",
                    LogCtx.getLogKey(), documentKey, column38, column39, branchCol4, branchCol5, column79);
            return null;
        } else {
            ProjectInfo projectInfo = null;
            if(projectCode != null) {
                projectInfo = yofcMapper.yofcProjectInfo(projectCode);
            }
            // 计算段2
            String jie2 = branchCol5;
            if(StrUtil.equals("3", pgAdjustType)) {
                jie2 = branchCol5;
            } else if(projectInfo != null && !StrUtil.equals(projectCode, "N/A") && StrUtil.equals(projectInfo.getColumn4(), "Y")) {
                jie2 = projectInfo.getColumn2();
            }
            // 计算段6
            String jie6 = getPgJie6(branchCode, type, column57, projectCode);
            jie6 = Optional.ofNullable(jie6).orElse("00000");

            return branchCol4 + "." + jie2 + "." + column39 + "." + column79 + "." + column38 + "." + jie6;
        }
    }

    /**
     * 获取补充贷方科目
     * @param jsonObj
     * @return
     */
    private String getExtCrAccountCode(cn.hutool.json.JSONObject jsonObj) {
        Object documentKey = jsonObj.get("documentKey");
        String    column53 = jsonObj.getStr("column53");
        String  branchCol4 = jsonObj.getStr("branchCol4");
        String  branchCol5 = jsonObj.getStr("branchCol5");
        String    column79 = jsonObj.getStr("column79");
        String projectCode = jsonObj.getStr("projectCode");
        String pgAdjustType = jsonObj.getStr("pgAdjustType");
        if(StringUtils.isEmpty(column53) || StringUtils.isEmpty(branchCol4) || StringUtils.isEmpty(branchCol5) || StringUtils.isEmpty(column79)) {
            logger.error("{}单据{}补充科目贷方行数据校验失败，column53={}, branchCol4={}, branchCol5={}, column79={}",
                    LogCtx.getLogKey(), documentKey, column53, branchCol4, branchCol5, column79);
            return null;
        } else {
            ProjectInfo projectInfo = null;
            if(projectCode != null) {
                projectInfo = yofcMapper.yofcProjectInfo(projectCode);
            }
            // 计算段2
            String str2 = branchCol5;
            if(StrUtil.equals("3", pgAdjustType)) {
                str2 = branchCol5;
            } else if(projectInfo != null && !StrUtil.equals(projectCode, "N/A") && StrUtil.equals(projectInfo.getColumn4(), "Y")) {
                str2 = projectInfo.getColumn2();
            }

            return branchCol4 + "." + str2 + "." + column53 + "." + column79 + "." + "00000" + "." + "00000";
        }
    }

    /**
     * 获取对私补充科目
     * @param drCr 借贷方向
     * @param branchInfo 机构信息
     * @param lineInfo 行数据
     * @return
     */
    private String getPriPgAccountCode(String drCr, BranchInfo branchInfo, FaPiaoLineInfo lineInfo) {
        try {
            // column38对应column1
            FndLovValue lovValue = yofcMapper.yofcLovValue("PG项目", lineInfo.getColumn38());
            String column38_1 = Optional.ofNullable(lovValue).map(FndLovValue::getColumn1).orElse("");
            // 计算段1
            String seg1 = branchInfo.getColumn4();
            // 计算段2
            String seg2 = getPriPgSeg2(column38_1, branchInfo, lineInfo);
            // 计算段3
            String seg3 = getPriPgSeg3(drCr, column38_1, lineInfo);
            // 计算段4
            String seg4 = "00";
            // 计算段5
            String seg5 = getPriSeg5(drCr, lineInfo);
            // 计算段6
            String seg6 = getPriSeg6(drCr, column38_1, lineInfo);

            return seg1 + "." + seg2 + "." + seg3 + "." + seg4 + "." + seg5 + "." + seg6;
        } catch (Exception e) {
            logger.error("获取对私补充借方科目异常：{}", e.getMessage(), e);
            return null;
        }
    }

    private String getPriPgSeg2(String column38_1, BranchInfo branchInfo, FaPiaoLineInfo lineInfo) {
        if(StrUtil.equals(column38_1, "否")) {
            ProjectInfo projectInfo = yofcMapper.yofcProjectInfo(lineInfo.getProjectCode());
            return projectInfo.getColumn2();
        } else {
            return branchInfo.getColumn5();
        }
    }

    private String getPriPgSeg3(String drCr, String column38_1, FaPiaoLineInfo lineInfo) {
        if(StrUtil.equals(column38_1, "否")) {
            if(StrUtil.equals(drCr, "dr")) {
                return lineInfo.getDrAccountCode();
            } else {
                return obj2Str(getColumnVal(lineInfo.getColumnJson(), "column53"));
            }
        } else {
            if(StrUtil.equals(drCr, "dr")) {
                return lineInfo.getColumn39();
            } else {
                return obj2Str(getColumnVal(lineInfo.getColumnJson(), "column53"));
            }
        }
    }

    private String getPriSeg5(String drCr, FaPiaoLineInfo lineInfo) {
        if(StrUtil.equals(drCr, "dr")) {
            return lineInfo.getColumn38();
        } else {
            return "00000";
        }
    }

    private String getPriSeg6(String drCr, String column38_1, FaPiaoLineInfo lineInfo) {
        if(StrUtil.equals(column38_1, "否")) {
            if(StrUtil.equals(drCr, "dr")) {
                String column57 = obj2Str(getColumnVal(lineInfo.getColumnJson(), "column57"));
                FndLovValue lovValue = yofcMapper.yofcLovValue("PG_Type", column57);
                return lovValue != null ? lovValue.getDescription() : "";
            }
        }
        return "00000";
    }


    /**
     * 获取pg类型的第6段科目编码
     * @param branchCode
     * @param type
     * @param column57
     * @param projectCode
     * @return
     */
    private String getPgJie6(String branchCode, String type, String column57, String projectCode) {
        if(branchCodes.isEmpty()) {
            List<FndLovValue> lovValues = yofcMapper.yofcLovValues("Branch_code");
            if(!CollUtil.isEmpty(lovValues)) {
                branchCodes = lovValues.stream().map(FndLovValue::getValueCode).collect(Collectors.toList());
            }
        }
        String jie6 = null;
        boolean column57Flag = false;
        if(branchCodes.contains(branchCode) && StrUtil.equals(type, "Pub")) {
            if(projectCode == null || StrUtil.isEmpty(column57)) {
                logger.error("{}补充科目借方行数据校验失败，projectCode={}，column57={}", LogCtx.getLogKey(), projectCode, column57);
                return jie6;
            }
            ProjectInfo projectInfo = null;
            if(projectCode != null) {
                projectInfo = yofcMapper.yofcProjectInfo(projectCode);
            }
            if(StrUtil.equals(projectInfo.getColumn6(), "Y") && !StrUtil.equals(projectInfo.getColumn10(), "Research Project")) {
                column57Flag = true;
            }
        } else if(StrUtil.equals(type, "Pri")) {
            column57Flag = true;
        }
        if(column57Flag && StrUtil.isNotEmpty(column57)) {
            FndLovValue lovValue = yofcMapper.yofcLovValue("PG_Type", column57);
            if(lovValue != null) {
                jie6 = lovValue.getDescription();
            }
        }

        return jie6;
    }

    /**
     * 自动生成发票号码
     * @param documentNum
     * @param date
     * @return
     */
    private String generateInvoiceNum(String documentNum, String date, String fdCode) {
        String invoiceNum = null;
        String yyyyMMdd = date.replace("-", "");// 年月日
        //根据时间和操作人生成一个发票号码
        String yyyyMM = yyyyMMdd.substring(0, 6);// 年月
        String code = yyyyMM + "_" + fdCode;
        // 获取流水号锁键值
        String lockKey = "PRE_INVOICE_NUM_BRANCH" + "_" + code;
        try {
            // 加锁
            boolean lockSuccess = SyncUtil.lock(lockKey, 5, 1, 5);
            if(!lockSuccess) {
                logger.error("{}自动生成发票号码获取流水号加锁失败", documentNum);
                return invoiceNum;
            }
            // 获取流水号计数
            FndLovValue lovValue = yofcMapper.yofcLovValue("PRE_INVOICE_NUM_BRANCH", code);
            if(lovValue == null || lovValue.getValueMeaning() == null) {
                logger.error("{}自动生成发票号码获取流水号配置失败", documentNum);
                return invoiceNum;
            }
            Integer num = Integer.valueOf(lovValue.getValueMeaning());
            String sequence = StringUtils.leftPad(String.valueOf(num+1), 3, '0');
            // 当前日期
//            String yyyyMMdd = DateFormatUtils.format(new Date(), "yyyyMMdd");
            // 自动生成发票号
            invoiceNum = "PRE" + yyyyMMdd + sequence;
            // 更新流水号计数
            yofcMapper.yofcUpdateLovValueMeaning(lovValue.getValueId(), sequence, "zh_CN");
        } catch (Exception e) {
            logger.error("{}自动生成发票号码异常：{}", documentNum, e.getMessage(), e);
        } finally {
            SyncUtil.unlock(lockKey);
        }
        logger.info("{}自动生成发票号码结果={}", documentNum, invoiceNum);
        return invoiceNum;
    }

    /**
     * 获取行金额
     * @param head
     * @param line
     * @param code
     * @return
     */
    private BigDecimal getAmount(FaPiaoHeaderInfo head, FaPiaoLineInfo line, String code) {
        String taxCode = "TAX";
        BigDecimal amount = BigDecimal.ZERO;
        if(Arrays.asList("RQ04","T017").contains(head.getTypeCode()) && Arrays.asList("487","386","808","728").contains(head.getFdCode())) {
            JSONObject lineJson = JSONObject.parseObject(line.getColumnJson());
            String column107 = lineJson.getString("column107");
            String column23 = line.getColumn23();
            if(NumberUtil.isNumber(column107)) {
                BigDecimal column107Amt = new BigDecimal(column107);// 税额
                if(StrUtil.equals(taxCode, code)) {
                    amount = column107Amt;
                } else {
                    amount = line.getReceiptAmount().subtract(column107Amt);
                }
            } else if(NumberUtil.isNumber(column23)) {
                BigDecimal column23Amt = new BigDecimal(column23);// 不含税金额
                if(StrUtil.equals(taxCode, code)) {
                    amount = new BigDecimal(line.getReceiptAmount2()).subtract(column23Amt);
                } else {
                    amount = column23Amt;
                }
            }
        } else {
            BigDecimal taxAmount = Optional.ofNullable(line.getTaxAmount()).orElse(BigDecimal.ZERO);
            if(StrUtil.equals(taxCode, code)) {
                if(!StrUtil.equals("RQ01", head.getTypeCode()) && !StrUtil.equals("RQ03", head.getTypeCode())) {
                    amount = taxAmount;
                }
            } else {
                amount = line.getReceiptAmount().subtract(taxAmount);
            }
        }

        return amount;
    }

    /**
     * openapi凭证回写接口
     * @param documentNum
     * @param journalNum
     * @param glStatus
     * @param glMessage
     * @param glDate
     */
    private void voucherBack(String documentNum, String journalNum, String glStatus, String glMessage, Long glDate, String ledgerName) {
        String host = RB.getString("yj_host");
        String clientId = RB.getString("client_id");
        String clientSecret = RB.getString("client_secret");
        cn.hutool.json.JSONObject reqObj = new cn.hutool.json.JSONObject();
        reqObj.putOpt("bizId", UUID.randomUUID().toString());
        reqObj.putOpt("timestamp", System.currentTimeMillis());
        cn.hutool.json.JSONObject data = new cn.hutool.json.JSONObject();
        data.putOpt("journal_num", journalNum);
        data.putOpt("gl_status", glStatus);
        data.putOpt("gl_message", glMessage);
        data.putOpt("unique", documentNum);
        data.putOpt("gl_date", glDate);
        data.putOpt("ledger_name", ledgerName);
        reqObj.putOpt("data", data);
        logger.info("单据{}凭证回写请求={}", documentNum, reqObj);
        cn.hutool.json.JSONObject res = yjOpenApiService.api(host, clientId, clientSecret, "/common/voucher/back", HttpMethod.POST, reqObj.toString());
        logger.info("单据{}凭证回写响应={}", documentNum, res);
    }

    private ErPaySta getPayTarget(String documentNum, MultiValueMap<String, ErPaySta> linkMap, Map<String, ErPaySta> payMap) {
        // 按PAY支付的单据
        ErPaySta payTarget = payMap.get(documentNum);
        if(payTarget == null) {
            // 按EXP支付的单据
            List<ErPaySta> payTargets = Optional.ofNullable(linkMap.get(documentNum)).orElse(new ArrayList<>());
            payTarget = payTargets.size() > 0 ? payTargets.get(0) : null;
        }

        return payTarget;
    }

    /**
     * 判定是否PG项目
     *
     * @param lineInfo
     * @return
     */
    private boolean isPG(FaPiaoLineInfo lineInfo) {
        return lineInfo != null && StrUtil.equals(lineInfo.getColumn37(), "01");
    }

    private boolean checkPG(FaPiaoLineInfo lineInfo) {
        if(!isPG(lineInfo)) {
            return false;
        }
        if(lineInfo.getColumn38() == null) {
            return false;
        }
        FndLovValue column38Lov = yofcMapper.yofcLovValue("PG项目", lineInfo.getColumn38());
        if (column38Lov != null && StrUtil.equals("否", column38Lov.getColumn1())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 发票类型
     * @param header
     * @param lines
     * @return
     */
    private String getInvoiceType(FaPiaoHeaderInfo header, List<FaPiaoLineInfo> lines) {
        if (PREPAYMENT_TYPE_LIST.contains(header.getTypeCode())) {
            return "PREPAYMENT";
        } else if ("T017".equals(header.getTypeCode()) && lines != null){
            for (FaPiaoLineInfo lineInfo : lines){
                if ((!Arrays.asList("T40","T65","T90","T100").contains(lineInfo.getTypeCode())) && lineInfo.getPayMethodFactor() == -1){
                    return "MIXED";
                }
            }
        } else if ("T023".equals(header.getTypeCode())) {
            return "MIXED";
        }
        return "STANDARD";
    }

    /**
     * APPLY_PREPAY_FLAG
     * @param header
     * @return
     */
    private String getRepayFlag(FaPiaoHeaderInfo header) {
        //当单据类型为付款(T019)/应付发票(T017)时，带出核销段
        if (Arrays.asList("T019","T017","RQ09","T023").contains(header.getTypeCode())) {
            return "Y";
        }
        List <String> types =  yofcMapper.getFrontTypes(header.getHeaderId());
        if (null != types && types.size() > 0){
            if (types.contains("RQ03")){
                return "Y";
            }
        }

        return "N";
    }

    /**
     * 中文翻译到英文
     * @param currency
     * @param source
     * @return
     */
    private String translate(String currency, String source) {
        if(Objects.equals(currency, "CNY")) {
            return source;
        }
        List<FndLovValue> lovValues = yofcMapper.yofcLovValues("Translation");//
        if(lovValues == null || lovValues.size() == 0) {
            return source;
        }
        logger.info("中文翻译前：{}", source);
        Map<String, String> map = new HashMap<>();
        for(FndLovValue lov : lovValues) {
            map.put(lov.getValueCode(), lov.getValueMeaning());
        }
        HashMap<String, String> sortedMap = sortByLength(map);
        logger.info("翻译目标池：{}", JSONUtil.toJsonStr(sortedMap));
        for(Map.Entry<String, String> entry : sortedMap.entrySet()) {
            String key = entry.getKey();
            String val = entry.getValue();
            source = source.replaceAll(key, val);
        }
        logger.info("中文翻译后：{}", source);
        return source;
    }


    /**
     * 中文翻译到英文
     * @param currency
     * @param line
     * @return
     */
    private Map<String, Object> translate(String currency, Map<String, Object> line) {
        if(Objects.equals(currency, "CNY")) {
            return line;
        }
        String json = JSON.toJSONString(line);
        String newJson = translate(currency, json);
        return JSON.parseObject(newJson, Map.class);
    }

    /**
     * 根据key长度倒叙排序
     * @param map
     * @return
     */
    private HashMap<String, String> sortByLength(Map<String, String> map) {
        // 将HashMap的条目转移到列表中
        List<Map.Entry<String, String>> entries = new ArrayList<>(map.entrySet());
        // 按照键的长度倒序排序
        entries.sort((o1, o2) -> o2.getKey().length() - o1.getKey().length());
        // 创建一个新的HashMap来存储排序后的结果
        HashMap<String, String> sortedMap = new LinkedHashMap<>();
        // 将排序后的列表放回HashMap
        for (Map.Entry<String, String> entry : entries) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        return sortedMap;
    }


    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("我我我", "aaa");
        map.put("哈哈哈", "aaa");
        map.put("你你你你", "bbbb");
        map.put("他", "c");
        System.out.println(map);
        System.out.println("******");
        // 将HashMap的条目转移到列表中
        List<Map.Entry<String, String>> entries = new ArrayList<>(map.entrySet());
        // 按照键的长度倒序排序
        entries.sort((o1, o2) -> o2.getKey().length() - o1.getKey().length());
        // 创建一个新的HashMap来存储排序后的结果
        HashMap<String, String> sortedMap = new LinkedHashMap<>();
        // 将排序后的列表放回HashMap
        for (Map.Entry<String, String> entry : entries) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }
        // 打印排序后的HashMap
        System.out.println(sortedMap);
        System.out.println("******");
        // 替换
        String source = "3123213213我我我343243你你你你34343他";
        System.out.println(source);
        System.out.println("******");
        for(Map.Entry<String, String> entry : sortedMap.entrySet()) {
            String key = entry.getKey();
            String val = entry.getValue();
            System.out.println(key);
            source = source.replaceAll(key, val);
        }
        System.out.println(source);
    }

}
