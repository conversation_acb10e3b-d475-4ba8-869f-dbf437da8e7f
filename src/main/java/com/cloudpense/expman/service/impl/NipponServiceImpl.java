package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.EmployeeWhoLeft;
import com.cloudpense.expman.entity.ExpClaimAttachment;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.hsf.HsfNtf;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.LovMapper;
import com.cloudpense.expman.mapper.NipponMapper;
import com.cloudpense.expman.service.NipponService;
import com.cloudpense.expman.util.*;
import com.cloudpense.expman.vo.nippon.TravelExpenseVo;
import com.cloudpense.expman.webService.nippon.*;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.axis2.AxisFault;
import org.apache.axis2.client.Options;
import org.apache.axis2.databinding.types.Time;
import org.apache.axis2.transport.http.HTTPConstants;
import org.apache.axis2.transport.http.impl.httpclient3.HttpTransportPropertiesImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

@Service
public class NipponServiceImpl implements NipponService {
    private NipponMapper nipponMapper;
    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    private static final Logger logger = LoggerFactory.getLogger(NipponServiceImpl.class);
    private String moreApprove = rb.getString("nippon.approve.email.moreApprove");
    private static final Integer companyId = 15220;
    private static final String CLAIM_PUSH_LOCK = "ClaimPushLock";

    private String env = rb.getString("global.env");
    @Autowired
    private SendUtil sendUtil;
    @Autowired
    private LovMapper lovMapper;


    @Autowired
    public NipponServiceImpl(NipponMapper nipponMapper) {
        this.nipponMapper = nipponMapper;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCompany() {
        final String KEYNAME = "立邦同步公司数据===";
        logger.info("{}开始处理...", KEYNAME);
        try {
            SAPCompanyStub stub = new SAPCompanyStub(rb.getString("nippon.company"));
            SAPCompanyStub.ZCompanyCdReqE reqE = new SAPCompanyStub.ZCompanyCdReqE();
            SAPCompanyStub.ZCompanyCdReq req = new SAPCompanyStub.ZCompanyCdReq();
            SAPCompanyStub.BUKRS_type1 bukrs_type1 = new SAPCompanyStub.BUKRS_type1();
            bukrs_type1.setBUKRS_type0("");
            req.setBUKRS(bukrs_type1);
            reqE.setZCompanyCdReq(req);

            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("WEB_WSUSER");
            auth.setPassword("Nipponpaint");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);
            //认证代码 end

//            SAPCompanyStub.ZCompanyCdResultE resultE = stub.zCompanyCdReq(reqE);
            SAPCompanyStub.ZCompanyCdResultE resultE = sendUtil.zCompanyCdReq("updateCompany", stub, reqE);
            SAPCompanyStub.ZCompanyCdResult result = resultE.getZCompanyCdResult();
            SAPCompanyStub.List_type0[] listType0 = result.getList();
            logger.info("{}获取到结果数据{}条", KEYNAME, listType0.length);
            int i = 0;
            for (SAPCompanyStub.List_type0 t : listType0
            ) {
                logger.info("{}开始处理第{}条数据={}", KEYNAME, i, JSONObject.toJSONString(t));
                nipponMapper.updateCompany(t.getBUKRS().getBUKRS_type2(), t.getBUTXT().getBUTXT_type0(), t.getTYPE().getTYPE_type0(), t.getATTRIBUTE4().getATTRIBUTE4_type0(), t.getATTRIBUTE1().getATTRIBUTE1_type0(), t.getATTRIBUTE2().getATTRIBUTE2_type0());
                i++;
            }
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCostCenter() {
        final String KEYNAME = "立邦同步成本中心===";
        logger.info("立邦成本中心----开始处理...");
        try {
            SAPCostCenterStub stub = new SAPCostCenterStub(rb.getString("nippon.cost_center"));
            SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER costCenter = new SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER();
            SAPCostCenterStub.DT_FK2ERP_COSTANDPROFITCENTER costCenter2 = new SAPCostCenterStub.DT_FK2ERP_COSTANDPROFITCENTER();
            SAPCostCenterStub.INPUT_type0 param = new SAPCostCenterStub.INPUT_type0();
            param.setHIGH("");
            param.setLOW("");
            param.setOPTION("");
            param.setZZJNAME("");
            param.setSIGN("|");
            costCenter2.addINPUT(param);
            costCenter.setMT_FK2ERP_COSTANDPROFITCENTER(costCenter2);

            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("WEB_WSUSER");
            auth.setPassword("Nipponpaint");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

//            SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER_RESP resp = stub.sI_FK2ERP_COSTANDPROFITCENTER_T(costCenter);
            SAPCostCenterStub.MT_FK2ERP_COSTANDPROFITCENTER_RESP resp = sendUtil.sI_FK2ERP_COSTANDPROFITCENTER_T("updateCostCenter", stub, costCenter);
            logger.info("更新立邦成本中心updateCostCenter------resp:" + JSONObject.toJSONString(resp));
            SAPCostCenterStub.DT_FK2ERP_COSTANDPROFITCENTER_RESP resp2 = resp.getMT_FK2ERP_COSTANDPROFITCENTER_RESP();
            SAPCostCenterStub.OUPUT_type0 output = resp2.getOUPUT();
            SAPCostCenterStub.COST_type0[] costCenterList = output.getCOST();
            logger.info("立邦成本中心---{}获取到结果数据{}条", KEYNAME, costCenterList.length);
            JSONArray array = new JSONArray();
            JSONObject temp;
            for (SAPCostCenterStub.COST_type0 t : costCenterList
            ) {
                logger.info("立邦成本中心---{}开始处理数据={}", KEYNAME, JSONObject.toJSONString(t));
                temp = new JSONObject();
                temp.put("code", t.getKOSTL());
                temp.put("name", t.getKTEXT());
                temp.put("dept_id", t.getBUKRS());
                array.add(temp);
                if (array.size() == 1000) {
                    nipponMapper.updateCostCenter(array.toJSONString());
                    array = new JSONArray();
                }
            }
            nipponMapper.updateCostCenter(array.toJSONString());
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}---完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateSupplier() {
        final String KEYNAME = "立邦同步供应商数据===";
        logger.info("{}开始处理...", KEYNAME);
        JSONArray result = new JSONArray();
        JSONObject supplier;
        String name1;
        try {
            SupplierStub stub = new SupplierStub(rb.getString("nippon.supplier"));
            SupplierStub.ZfmGetLifnrUndeleted req = new SupplierStub.ZfmGetLifnrUndeleted();
            SupplierStub.TableOfZsrmLifnrRange req1 = new SupplierStub.TableOfZsrmLifnrRange();
            SupplierStub.TableOfZsrmGetLifnr req3 = new SupplierStub.TableOfZsrmGetLifnr();
            SupplierStub.ZsrmLifnrRange req2 = new SupplierStub.ZsrmLifnrRange();
            SupplierStub.ZsrmGetLifnr req4 = new SupplierStub.ZsrmGetLifnr();
            SupplierStub.Char10 high = new SupplierStub.Char10();
            SupplierStub.Char10 low = new SupplierStub.Char10();
            SupplierStub.Char10 partner = new SupplierStub.Char10();
            SupplierStub.Char35 name = new SupplierStub.Char35();
            SupplierStub.Char2 option = new SupplierStub.Char2();
            SupplierStub.Char1 sign = new SupplierStub.Char1();
            high.setChar10("");
            low.setChar10("*");
            option.setChar2("CP");
            sign.setChar1("I");
            partner.setChar10("");
            name.setChar35("");
            req2.setHigh(high);
            req2.setLow(low);
            req2.setOption(option);
            req2.setSign(sign);
            req4.setNameOrg1(name);
            req4.setPartner(partner);
            req3.addItem(req4);
            req1.addItem(req2);
            req.setEtResult(req3);
            req.setEtLifnr(req1);

            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("NP_CLOUD");
            auth.setPassword("12345678");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

//            SupplierStub.ZfmGetLifnrUndeletedResponse response = stub.zfmGetLifnrUndeleted(req);
            SupplierStub.ZfmGetLifnrUndeletedResponse response = sendUtil.zfmGetLifnrUndeleted("updateSupplier", stub, req);
            SupplierStub.TableOfZsrmGetLifnr resultList = response.getEtResult();
            SupplierStub.ZsrmGetLifnr[] resultArray = resultList.getItem();
            logger.info("{}获取到结果数据{}条", KEYNAME, resultArray.length);
            for (SupplierStub.ZsrmGetLifnr current : resultArray) {
                logger.info("{}开始处理数据={}", KEYNAME, JSONObject.toJSONString(current));
                supplier = new JSONObject();
                name1 = current.getNameOrg1().getChar35().replaceAll("\\r", "").replaceAll("\\t", "").replaceAll("\\n", "").replaceAll("\\\\", "").replaceAll("\'", "");
                supplier.put("name", name1);
                supplier.put("code", current.getPartner().getChar10());
                if (supplier.get("code") != null && !supplier.get("code").equals("")) {
                    result.add(supplier);
                }
                if (result.size() >= 1000) {
                    nipponMapper.updateSupplier(result.toJSONString());
                    result = new JSONArray();
                }
            }
            nipponMapper.updateSupplier(result.toJSONString());
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCustomer() {
        final String KEYNAME = "立邦同步客户数据===";
        logger.info("{}开始处理...", KEYNAME);
        JSONArray result = new JSONArray();
        JSONObject customer;
        String name1;
        try {
            CustomerStub stub = new CustomerStub(rb.getString("nippon.customer"));
            CustomerStub.ZFM_SD0005 req = new CustomerStub.ZFM_SD0005();
            CustomerStub.ZRANGE_KUNAG req1 = new CustomerStub.ZRANGE_KUNAG();
            CustomerStub.TABLE_OF_ZSSD0001 req3 = new CustomerStub.TABLE_OF_ZSSD0001();
            CustomerStub.RANGE_KUNAG req2 = new CustomerStub.RANGE_KUNAG();
            CustomerStub.ZSSD0001 req4 = new CustomerStub.ZSSD0001();
            CustomerStub.Char10 high = new CustomerStub.Char10();
            CustomerStub.Char10 low = new CustomerStub.Char10();
            CustomerStub.Char10 kunnr = new CustomerStub.Char10();
            CustomerStub.Char35 name = new CustomerStub.Char35();
            CustomerStub.Char2 option = new CustomerStub.Char2();
            CustomerStub.Char1 sign = new CustomerStub.Char1();
            high.setChar10("");
            low.setChar10("*");
            option.setChar2("CP");
            sign.setChar1("I");
            kunnr.setChar10("");
            name.setChar35("");
            req2.setHIGH(high);
            req2.setLOW(low);
            req2.setOPTION(option);
            req2.setSIGN(sign);
            req4.setNAME1(name);
            req4.setKUNNR(kunnr);
            req3.addItem(req4);
            req1.addItem(req2);
            req.setOUT(req3);
            req.setS_KUNNR(req1);

            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("NP_CLOUD");
            auth.setPassword("12345678");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

//            CustomerStub.ZFM_SD0005Response response = stub.zFM_SD0005(req);
            CustomerStub.ZFM_SD0005Response response = sendUtil.zFM_SD0005("updateCustomer", stub, req);
            CustomerStub.TABLE_OF_ZSSD0001 resultList = response.getOUT();
            CustomerStub.ZSSD0001[] resultArray = resultList.getItem();
            logger.info("{}获取到结果数据{}条", KEYNAME, resultArray.length);
            for (CustomerStub.ZSSD0001 current : resultArray) {
                logger.info("{}开始处理数据={}", KEYNAME, JSONObject.toJSONString(current));
                customer = new JSONObject();
                name1 = current.getNAME1().getChar35().replaceAll("\\r", "").replaceAll("\\t", "").replaceAll("\\n", "").replaceAll("\\\\", "").replaceAll("\'", "");
                customer.put("name", name1);
                customer.put("code", current.getKUNNR().getChar10());
                if (customer.get("code") != null && !customer.get("code").equals("")) {
                    result.add(customer);
                }
                if (result.size() >= 1000) {
                    nipponMapper.updateCustomer(result.toJSONString());
                    result = new JSONArray();
                }
            }
            nipponMapper.updateCustomer(result.toJSONString());
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updatePhoneLimit() {
        final String KEYNAME = "立邦同步手机限额数据===";
        logger.info("{}开始处理...", KEYNAME);
        ArrayList<String> companyCodeList = nipponMapper.queryCompanyCodeList();
        try {
            PhoneExpenseLimitStub stub = new PhoneExpenseLimitStub(rb.getString("nippon.phone_cost_limit"));
            PhoneExpenseLimitStub.ZWebExpenseLimitReq req = new PhoneExpenseLimitStub.ZWebExpenseLimitReq();
            PhoneExpenseLimitStub.ZNotesExpenseLimitReq req1 = new PhoneExpenseLimitStub.ZNotesExpenseLimitReq();
            PhoneExpenseLimitStub.ZWebExpenseLimitResE resE;
            PhoneExpenseLimitStub.ZWebExpenseLimitRes res;
            PhoneExpenseLimitStub.LIST_type0[] list;
            StringBuffer array;
            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("WEB_WSUSER");
            auth.setPassword("Nipponpaint");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

            for (String code : companyCodeList
            ) {
                array = new StringBuffer();
                array.append("[");
                req1.setCOMPANYCODE(code);
                req.setZWebExpenseLimitReq(req1);
//                resE = stub.zWebExpenseLimit(req);
                resE = sendUtil.zWebExpenseLimit("updatePhoneLimit", stub, req);
                res = resE.getZWebExpenseLimitRes();
                list = res.getLIST();
                logger.info("{}CompanyCode={}获取到结果数据{}条", KEYNAME, code, list.length);
                for (PhoneExpenseLimitStub.LIST_type0 t : list
                ) {
                    if (!(t.getEID_LIST() == null || t.getLIMIT_LIST() == null || t.getEID_LIST().equals("") || "".equals(t.getLIMIT_LIST()))) {
                        array.append("{\"emp_number\":\"").append(t.getEID_LIST()).append("\",\"limit\":\"").append(t.getLIMIT_LIST()).append("\"},");
                    }
                }
                logger.info("{}CompanyCode={}待更新结果数据={}", KEYNAME, code, array.toString());
                if (array.length() > 15) {
                    array.deleteCharAt(array.length() - 1);
                    array.append("]");
                    nipponMapper.updatePhoneLimit(array.toString());
                }
            }

        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateFrameOrder(boolean fullSync) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        String end;
        String start;
        if (!fullSync) {
            end = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, -15);
            start = sdf.format(calendar.getTime());
            partUpdateFrame(start, end);
        } else {
            while (calendar.get(Calendar.YEAR) > 2015) {
                end = sdf.format(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -15);
                start = sdf.format(calendar.getTime());
                partUpdateFrame(start, end);
            }
        }

    }


    private void partUpdateFrame(String start, String end) {
        final String KEYNAME = "立邦同步用户框架订单数据===";
        logger.info("{}开始处理...", KEYNAME);
        StringBuilder array = new StringBuilder();
        try {
            FrameOrderStub stub = new FrameOrderStub(rb.getString("nippon.frame_order"));
            FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER order = new FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER();
            FrameOrderStub.DT_012_WEB_ECC_FRAME_ORDER order2 = new FrameOrderStub.DT_012_WEB_ECC_FRAME_ORDER();
            FrameOrderStub.EBELN_type3 eblen = new FrameOrderStub.EBELN_type3();
            FrameOrderStub.MONTH_type3 month = new FrameOrderStub.MONTH_type3();
            FrameOrderStub.STADATE_type3 stadate = new FrameOrderStub.STADATE_type3();
            FrameOrderStub.ENDDATE_type3 enddate = new FrameOrderStub.ENDDATE_type3();
            eblen.setEBELN_type2("");
            month.setMONTH_type2("");
            stadate.setSTADATE_type2(start);
            enddate.setENDDATE_type2(end);
            order2.setEBELN(eblen);
            order2.setMONTH(month);
            order2.setSTADATE(stadate);
            order2.setENDDATE(enddate);
            order.setMT_012_WEB_ECC_FRAME_ORDER(order2);

            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("WEB_WSUSER");
            auth.setPassword("Nipponpaint");
            Options options = stub._getServiceClient().getOptions();
            options.setTimeOutInMilliSeconds(600000);//设置超时(单位是毫秒)
            stub._getServiceClient().setOptions(options);
            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

//            FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER_RESP resp = stub.mI_012_O_WEB_ECC_FRAME_ORDER(order);
            FrameOrderStub.MT_012_WEB_ECC_FRAME_ORDER_RESP resp = sendUtil.mI_012_O_WEB_ECC_FRAME_ORDER("partUpdateFrame", stub, order);
            if (resp != null) {
                FrameOrderStub.DT_012_WEB_ECC_FRAME_ORDER_RESP resp1 = resp.getMT_012_WEB_ECC_FRAME_ORDER_RESP();
                FrameOrderStub.LIST_type0[] list = resp1.getLIST();
                logger.info("{}获取到结果数据{}条", KEYNAME, list.length);
                array.append("[");
                for (FrameOrderStub.LIST_type0 t : list) {
                    if ("FL010004".equals(t.getFYLX().getFYLX_type0())) {
                        array.append("{\"empNo\":\"").append(t.getPERNR().getPERNR_type0())
                                .append("\",\"costTypeCode\":\"").append(t.getFYLX().getFYLX_type0())
                                .append("\",\"costTypeName\":\"").append(t.getFYLX_DESC().getFYLX_DESC_type0())
                                .append("\",\"start\":\"").append(t.getSTADATE().getSTADATE_type0())
                                .append("\",\"end\":\"").append(t.getENDDATE().getENDDATE_type0()).append("\"},");
                    }
                }
                array.deleteCharAt(array.length() - 1);
                array.append("]");
                logger.info("{}待更新结果数据={}", KEYNAME, array.toString());
                nipponMapper.updateFrameOrder(array.toString());
            }
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void freshStatus() {
        nipponMapper.freshFrameStatus();
        //更新单据投递状态
        //1.查询所有符合条件的单据(两周以内)
        List<String> headers = nipponMapper.queryHeaderToUpdateSendStatus();
        RestTemplate restTemplate = new RestTemplate();
        if (headers != null && headers.size() > 0) {
            for (String sapNo : headers) {
                try {
                    //2.根据单据号从指定链接获取投递状态内容
//                    String result = restTemplate.getForObject(rb.getString("nippon.sent_status") + "?djh=" + sapNo, String.class);
                    String result = sendUtil.getForObject("freshStatus", rb.getString("nippon.sent_status") + "?djh=" + sapNo);

                    //3.对获取到的内容进行处理并回写数据库
                    result = result.substring(0, result.indexOf("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">")).trim();
                    while (result.lastIndexOf("<br/>") > 0) {
                        if (result.endsWith("<br/>")) {
                            result = result.substring(0, result.lastIndexOf("<br/>"));
                        } else {
                            result = result.substring(result.lastIndexOf("<br/>") + 5);
                        }
                    }
                    if (result.contains("没有找到") || result.length() < 9) {
                        result = "暂无";
                    }
                    nipponMapper.updateSendStatus(sapNo, result);
                } catch (Exception e) {
                    logger.error("sso_sync_error", "获取单据投递状态数据出现异常:\n错误SAP单号:" + sapNo + "\n错误信息:\n", e);
                    nipponMapper.updateSendStatus(sapNo, "暂无");
                }
            }
        }

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateData(String type) {
        String KEYNAME = "立邦" + type + "数据信息同步===";
        logger.info("{}处理开始", KEYNAME);
        String url = rb.getString("nippon.forwarding_server") + type;
        String requestBody, result;
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> request;
        JSONArray resultArray;
        HttpHeaders headers;
        int page = 1;
        do {
            try {
                headers = new HttpHeaders();
                requestBody = "{\"page\":" + page + "}";
                //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
                headers.setContentType(MediaType.APPLICATION_JSON);
                //RestTemplate带参传的时候要用HttpEntity<?>对象传递
                request = new HttpEntity<>(requestBody, headers);
                logger.info("{}获取第{}页数据请求url={}, request={}", KEYNAME, page, url, JSON.toJSONString(request));
                result = sendUtil.postForObject("updateData", url, request);
                logger.info("{}获取第{}页数据响应result={}", KEYNAME, page, JSON.toJSONString(result));
//                result = restTemplate.postForObject(url, request, String.class);
                if (!result.contains("No data")) {
                    resultArray = JSON.parseObject(result).getJSONArray("data");
                    nipponMapper.updateData(resultArray.toJSONString(), type);
                    logger.info("{}同步第{}页数据结束", KEYNAME, page);
                }
            } catch (Exception e) {
                result = "";
                logger.error("{}同步第{}页数据出现异常={}", KEYNAME, page, e.getStackTrace());
            }
            page++;
        } while (!result.contains("\"success\":0") && page < 511);

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateDataDeptleader() {
        String KEYNAME = "立邦deptleader数据信息同步===";
        logger.info("{}处理开始", KEYNAME);
        String url = rb.getString("nippon.forwarding_server") + "deptleader";
        String requestBody, result;
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> request;
        JSONArray resultArray;
        HttpHeaders headers;
        int page = 1;
        do {
            try {
                headers = new HttpHeaders();
                String deptLeaderRequestJsonArray = nipponMapper.deptLeaderRequestJsonArray();
                requestBody = "{\"page\":" + page + ",\"doaMapRuleElemList\":" + deptLeaderRequestJsonArray + "}";
                //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
                headers.setContentType(MediaType.APPLICATION_JSON);
                //RestTemplate带参传的时候要用HttpEntity<?>对象传递
                request = new HttpEntity<>(requestBody, headers);
                logger.info("{}获取第{}页数据请求url={}, request={}", KEYNAME, page, url, JSON.toJSONString(request));
                result = sendUtil.postForObject("updateData", url, request);
                logger.info("{}获取第{}页数据响应result={}", KEYNAME, page, JSON.toJSONString(result));
//                result = restTemplate.postForObject(url, request, String.class);
                if (!result.contains("No data")) {
                    resultArray = JSON.parseObject(result).getJSONArray("data");
                    nipponMapper.updateData(resultArray.toJSONString(), "deptleader");
                    logger.info("{}同步第{}页数据结束", KEYNAME, page);
                }
            } catch (Exception e) {
                result = "";
                logger.error("{}同步第{}页数据出现异常={}", KEYNAME, page, e.getStackTrace());
            }
            page++;
        } while (!result.contains("\"success\":0") || page < 511);

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateInterOrder() {
        JSONObject tem;
        JSONArray array = new JSONArray();
        try {
            InterOrderStub stub = new InterOrderStub(rb.getString("nippon.internal_order"));
            stub._getServiceClient().getOptions().setTimeOutInMilliSeconds(600000L);
            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("WEB_WSUSER");
            auth.setPassword("Nipponpaint");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);

            InterOrderStub.ZIntOrderReqE reqE = new InterOrderStub.ZIntOrderReqE();
            InterOrderStub.ZIntOrderReq req = new InterOrderStub.ZIntOrderReq();
            InterOrderStub.AULNR_type1 a = new InterOrderStub.AULNR_type1();
            a.setAULNR_type0("");
            InterOrderStub.BUKUS_type1 b = new InterOrderStub.BUKUS_type1();
            b.setBUKUS_type0("");
            req.setAULNR(a);
            req.setBUKUS(b);
            reqE.setZIntOrderReq(req);

//            InterOrderStub.ZIntOrderResultE resultE = stub.zIntOrderReq(reqE);
            InterOrderStub.ZIntOrderResultE resultE = sendUtil.zIntOrderReq("updateInterOrder", stub, reqE);

            InterOrderStub.List_type0[] list_type0s = resultE.getZIntOrderResult().getList();
            for (InterOrderStub.List_type0 temp : list_type0s) {
                tem = new JSONObject();
                tem.put("code", temp.getAULNR().getAULNR_type2());
                tem.put("desc", temp.getKTEXT().getKTEXT_type0());
                tem.put("company_code", temp.getBUKRS().getBUKRS_type0());
                tem.put("cost_center", temp.getCOSTCENTERNO());
                tem.put("contract_type", temp.getAUART().getAUART_type0());
                tem.put("contract_number", temp.getAUFEX().getAUFEX_type0());
                tem.put("apportion_money", temp.getACCRUED().getACCRUED_type0());
                array.add(tem);
                if (array.size() >= 100) {
                    nipponMapper.updateData(array.toJSONString(), "int_order");
                    array = new JSONArray();
                }
            }
            logger.info("internal order response: {}", array);
            nipponMapper.updateData(array.toJSONString(), "int_order");
        } catch (RemoteException axisFault) {
            logger.error("sso_sync_error", "同步内部订单数据出现异常:\n错误信息:\n", axisFault);
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateTraining(boolean fullsync) {
        final String KEYNAME = "立邦同步培训数据===";
        logger.info("{}处理开始", KEYNAME);
        try {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-M");
            JSONObject training;
            JSONArray array = new JSONArray();
            TrainingStub stub = new TrainingStub(rb.getString("nippon.training"));
            stub._getServiceClient().getOptions().setProperty(HTTPConstants.CHUNKED, false);
            TrainingStub.STRYEARMONTH date = new TrainingStub.STRYEARMONTH();
            date.setSTRYEARMONTH(sdf.format(calendar.getTime()));
            TrainingStub.GETLISTReturn returns;
            String[] TIDs;
            String[] names;
            String[] EIDs;

            do {
                date = new TrainingStub.STRYEARMONTH();
                date.setSTRYEARMONTH(sdf.format(calendar.getTime()));
//                returns = stub.gETLIST(date);
                returns = sendUtil.gETLIST("updateTraining", stub, date);
                TIDs = returns.getGETLISTReturn().getTID_LIST();
                names = returns.getGETLISTReturn().getNAME_LIST();
                EIDs = returns.getGETLISTReturn().getEID_LIST();
                logger.info("{}获取到结果数据TID_LIST={}", KEYNAME, TIDs);
                logger.info("{}获取到结果数据NAME_LIST={}", KEYNAME, names);
                logger.info("{}获取到结果数据EID_LIST={}", KEYNAME, EIDs);
                for (int i = 0; i < TIDs.length && i < names.length && i < EIDs.length; i++) {
                    training = new JSONObject();
                    training.put("id", TIDs[i]);
                    training.put("name", names[i]);
                    training.put("employee", EIDs[i]);
                    array.add(training);
                    if (array.size() >= 1000) {
                        nipponMapper.updateData(array.toJSONString(), "train");
                        array = new JSONArray();
                    }

                }
                logger.info("{}待更新结果数据={}", KEYNAME, array.toJSONString());
                nipponMapper.updateData(array.toJSONString(), "train");
                if (!fullsync) {
                    break;
                } else {
                    calendar.add(Calendar.MONTH, -1);
                }
            } while (calendar.get(Calendar.YEAR) > 2017);
        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateOutWork() {
        try {
            OutWorkStub stub = new OutWorkStub(rb.getString("nippon.on_business"));
//            OutWorkStub.GETLISTReturn result = stub.gETLIST();
            OutWorkStub.GETLISTReturn result = sendUtil.gETLIST("updateOutWork", stub);
            OutWorkStub.CLASS_RESULTLIST list = result.getGETLISTReturn();
            String[] ids = list.getEID_LIST();
            String[] starts = list.getSDATE_LIST();
            String[] ends = list.getEDATE_LIST();
            String[] times = list.getTIMES_LIST();
            JSONObject temp;
            JSONArray array = new JSONArray();

            for (int i = 0; i < ids.length; i++) {
                temp = new JSONObject();
                temp.put("id", ids[i]);
                temp.put("start", starts[i]);
                temp.put("end", ends[i]);
                temp.put("time", times[i]);
                array.add(temp);
                if (array.size() >= 1000) {
                    nipponMapper.updateOutWork(array.toJSONString());
                    array = new JSONArray();
                }
            }
            nipponMapper.updateOutWork(array.toJSONString());
        } catch (RemoteException axisFault) {
            logger.error("sso_sync_error", "同步外派数据出现异常:\n错误信息:\n", axisFault);
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateCredit() {
        try {
            JSONArray array;
            CreditStub stub = new CreditStub(rb.getString("nippon.credit"));
            CreditStub.GetEmploeesCredit req = new CreditStub.GetEmploeesCredit();
            int i = 0;
            do {
                req.setEmployeesNumber("");
                req.setBeginNum(i);
                req.setEndNum(i + 1000);
//                CreditStub.GetEmploeesCreditResponse response = stub.getEmploeesCredit(req);
                CreditStub.GetEmploeesCreditResponse response = sendUtil.getEmploeesCredit("updateCredit", stub, req);

                array = JSONObject.parseObject(response.getGetEmploeesCreditResult()).getJSONArray("JsonList");
                nipponMapper.updateCredit(array.toJSONString());
                i += 1000;
            } while (array.size() > 0);

        } catch (RemoteException axisFault) {
            logger.error("sso_sync_error同步信用数据出现异常:\n错误信息:\n", axisFault);
        }

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateRate() {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, 1);
            RateStub stub = new RateStub(rb.getString("nippon.exchange_rate"));
            RateStub.BAPI_EXCHANGERATE_GETDETAIL req = new RateStub.BAPI_EXCHANGERATE_GETDETAIL();

            RateStub.Date10 date = new RateStub.Date10();
            date.setDate10(format.format(calendar.getTime()));

            RateStub.Cuky5 fromCur = new RateStub.Cuky5();
            RateStub.Cuky5 toCur = new RateStub.Cuky5();
            fromCur.setCuky5("USD");
            toCur.setCuky5("CNY");

            RateStub.Char4 type = new RateStub.Char4();
            type.setChar4("M");

            req.setDATE(date);
            req.setFROM_CURR(fromCur);
            req.setRATE_TYPE(type);
            req.setTO_CURRNCY(toCur);

//            RateStub.BAPI_EXCHANGERATE_GETDETAILResponse res = stub.bAPI_EXCHANGERATE_GETDETAIL(req);
            RateStub.BAPI_EXCHANGERATE_GETDETAILResponse res = sendUtil.bAPI_EXCHANGERATE_GETDETAIL("updateRate", stub, req);
            BigDecimal nowRate = res.getEXCH_RATE().getEXCH_RATE().getDecimal95();
            String lastUpdate = res.getEXCH_RATE().getVALID_FROM().getDate10();

            nipponMapper.updateExRate(String.valueOf(nowRate), lastUpdate);
        } catch (RemoteException axisFault) {
            logger.error("sso_sync_error同步外币档案出现异常:\n错误信息:\n", axisFault);
        }

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateWbs(boolean fullSync) {
        final String KEYNAME = "立邦同步WBS项目数据===";
        logger.info("{}处理开始", KEYNAME);
        ArrayList<String> companyCodeList = nipponMapper.queryCompanyCodeList();
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            Calendar calendar = Calendar.getInstance();

            WbsProjectStub stub = new WbsProjectStub(rb.getString("nippon.wbs_project"));
            WbsProjectStub.ZPrjReqE reqE = new WbsProjectStub.ZPrjReqE();
            WbsProjectStub.ZPrjReq req = new WbsProjectStub.ZPrjReq();
            WbsProjectStub.POSID_type1 POSID = new WbsProjectStub.POSID_type1();
            POSID.setPOSID_type0("");
            WbsProjectStub.PBUKR_type1 PBUKR = new WbsProjectStub.PBUKR_type1();
            calendar.add(Calendar.YEAR, 1);
            calendar.set(Calendar.MONTH, 1);
            calendar.set(Calendar.DATE, 1);
            req.setE_TIME(format.format(calendar.getTime()));
            calendar.add(Calendar.YEAR, -1);
            req.setS_TIME(format.format(calendar.getTime()));
            if (fullSync) {
                while (calendar.get(Calendar.YEAR) >= 2000) {
                    updateWbsProcess(companyCodeList, stub, reqE, req, POSID, PBUKR);
                    req.setE_TIME(format.format(calendar.getTime()));
                    calendar.add(Calendar.YEAR, -1);
                    req.setS_TIME(format.format(calendar.getTime()));
                }
            } else {
                updateWbsProcess(companyCodeList, stub, reqE, req, POSID, PBUKR);
            }


        } catch (Exception e) {
            logger.error("{}出现未知异常={}", KEYNAME, e);
        }
        logger.info("{}完成处理", KEYNAME);
    }

    private void updateWbsProcess(ArrayList<String> companyCodeList, WbsProjectStub stub, WbsProjectStub.ZPrjReqE reqE, WbsProjectStub.ZPrjReq req, WbsProjectStub.POSID_type1 POSID, WbsProjectStub.PBUKR_type1 PBUKR) throws RemoteException {
        JSONArray array = new JSONArray();
        JSONObject temp;
        for (String code : companyCodeList
        ) {
            PBUKR.setPBUKR_type0(code);
            req.setPBUKR(PBUKR);
            req.setPOSID(POSID);
            reqE.setZPrjReq(req);

//            WbsProjectStub.ZPrjResultE resE = stub.zPrjReq(reqE);
            WbsProjectStub.ZPrjResultE resE = sendUtil.zPrjReq("updateWbsProcess", stub, reqE);
            WbsProjectStub.ZPrjResult res = resE.getZPrjResult();
            WbsProjectStub.List_type0[] resultList = res.getList();
            if (resultList != null) {
                for (WbsProjectStub.List_type0 result : resultList) {
                    temp = new JSONObject();
                    temp.put("department_code", result.getPBUKR().getPBUKR_type2());
                    temp.put("project_id_sap", result.getPSPNR().getPSPNR_type0());
                    temp.put("project_code", result.getPOSID().getPOSID_type2());
                    temp.put("project_name", result.getPOST1().getPOST1_type0());
                    temp.put("change_status", result.getCHANGE_ID());
                    temp.put("status", result.getSTAT().getSTAT_type0());
                    array.add(temp);
                    if (array.size() >= 100) {
                        logger.info("wbs response: {}", array.toJSONString());
                        nipponMapper.updateWbs(array.toJSONString());
                        array = new JSONArray();
                    }
                }
                logger.info("wbs response: {}", array.toJSONString());
                nipponMapper.updateWbs(array.toJSONString());
            }
        }
    }

    @SuppressWarnings("ConstantConditions")
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ArrayList<JSONObject> reimClaimToJson() {
        ArrayList<JSONObject> result = new ArrayList<>();
        NumberFormat nf = NumberFormat.getInstance();
        nf.setGroupingUsed(false);
        nf.setMaximumIntegerDigits(3);
        nf.setMinimumIntegerDigits(3);
        DecimalFormat df1 = new DecimalFormat("#.00");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat formatD = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
        formatD.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        ArrayList<ExpClaimHeader> claimHeaders;
        List<ExpClaimLine> claimLines;
        nipponMapper.updateReimStatus();
        claimHeaders = nipponMapper.getUnsentReimClaim();
        logger.info("reimClaimToJson------claimHeaders:" + claimHeaders);
        JSONObject claimBody;
        String SAPNo, fromCity, toCity = "";
        claimLoop:
        for (ExpClaimHeader current : claimHeaders) {
            String documentNum = current.getDocumentNum();
            logger.info("{}开始处理", documentNum);
            logger.info("{}reimClaimToJson------current:{}", documentNum, JSONObject.toJSONString(current));
            toCity = "";
            // SAPNo在单据提交生成
            SAPNo = current.getColumn9();
            if (StringUtils.isEmpty(SAPNo)) {
                logger.error("{}缺少sap单号column9", documentNum);
                continue;
            }
            ExpClaimHeader linkHeader = nipponMapper.queryLinkHeader(current.getHeaderId());
            if (linkHeader == null) {
                linkHeader = new ExpClaimHeader();
            }
            fromCity = nipponMapper.queryCityName(current.getDestinationCity());
            List<String> toCityList = nipponMapper.queryCityNames(current.getHeaderId());
            if (toCityList != null && toCityList.size() > 0) {
                for (String city : toCityList) {
                    toCity += city + "、";
                }
                toCity = toCity.substring(0, toCity.length() - 1);
            }
            claimBody = new JSONObject();
            claimBody.put("id", current.getHeaderId());
            claimLines = nipponMapper.queryClaimLine(current.getHeaderId());

            logger.info("{}reimClaimToJson------分摊行claimLines:{}", documentNum, JSONObject.toJSONString(claimLines));

            String submitUser, chargeUser, lastUpdateUser, branchCode;
            branchCode = nipponMapper.queryDeptCode(current.getBranchId());
            chargeUser = nipponMapper.queryEmployeeNum(current.getPayUser());
            if (chargeUser == null || "".equals(chargeUser)) {
                chargeUser = nipponMapper.queryEmployeeNum(current.getChargeUser());
            }
            if (chargeUser == null || "".equals(chargeUser)) {
                chargeUser = nipponMapper.queryEmployeeNum(current.getSubmitUser());
            }
            try {
                int user;
                user = current.getPayUser();
                if (user == 0) {
                    user = current.getChargeUser();
                }
                if (user == 0) {
                    user = current.getSubmitUser();
                }
                if (user != 0) {
                    nipponMapper.updateClaimCredit(user, current.getHeaderId());
                }
            } catch (Exception e) {
            }
            submitUser = nipponMapper.queryEmployeeNum(current.getSubmitUser());
            lastUpdateUser = nipponMapper.queryEmployeeNum(current.getLastUpdatedBy());
            String flag = nipponMapper.queryUserFlag(chargeUser);
            JSONObject headModel = new JSONObject();
            headModel.put("OrderNumber", SAPNo);//SAP单据号
            headModel.put("TOrderNumber", (current.getDocumentNum() != null) ? current.getDocumentNum() : "");//单号
            headModel.put("EmployeesNumber", (chargeUser != null) ? chargeUser : "");
            headModel.put("OrderType", "03");
            if ("modified".equals(current.getExternalStatus())) {
                headModel.put("OrderState", "08");
                headModel.put("ModifyDate", formatD.format(new Date().getTime() + (8 * 60 * 60 * 1000)));
                headModel.put("ModifyBy", submitUser);
            } else {
                headModel.put("OrderState", "03");
                headModel.put("ModifyDate", "");
                headModel.put("ModifyBy", "");
            }
            headModel.put("OrderCreateBy", (submitUser != null) ? submitUser : "");
            headModel.put("CurrencyCode", (current.getCurrencyCode() != null) ? current.getCurrencyCode() : "CNY");
            headModel.put("SumMoney", current.getTotalAmount());
            headModel.put("ExpensesAssumeCompany", (branchCode != null) ? branchCode : "");
            headModel.put("Summary", (current.getDescription() != null) ? current.getDescription() : "");
            headModel.put("ApprovalWay", "01");
            headModel.put("CreateBy", (submitUser != null) ? submitUser : "");
            headModel.put("CreateDate", (current.getCreationDate() != null) ? format.format(current.getCreationDate().getTime() + (8 * 60 * 60 * 1000)) : "");
            headModel.put("LastUpdateBy", (lastUpdateUser != null) ? lastUpdateUser : "");
            headModel.put("LastUpdateDate", (current.getLastUpdateDate() != null) ? format.format(current.getLastUpdateDate().getTime() + (8 * 60 * 60 * 1000)) : "");
            headModel.put("SubmitDate", (current.getSubmitDate() != null) ? format.format(current.getSubmitDate().getTime() + (8 * 60 * 60 * 1000)) : "");
            headModel.put("QueryUrl", rb.getString("nippon.sso_page") + "?mob=0&aw-id=" + current.getHeaderId());

            ArrayList<Integer> lineIds = new ArrayList<>();
            ArrayList<String> approve;
            ArrayList<ExpClaimAttachment> files;

            JSONArray approveList = new JSONArray();

            JSONArray filesList = new JSONArray();
            JSONArray detailsList = new JSONArray();
            JSONArray DZKPList = new JSONArray();
            JSONObject taxDetails = new JSONObject(), project = null;
            HashSet<Integer> invoices = null;
            List<Map> taxList = null;


            switch (current.getHeaderTypeId()) {
                case 126805:
                    //总监以下
                case 126810:
                    //总监以上
                    JSONObject tripPlanList = new JSONObject();
                    tripPlanList.put("OrderNumber", SAPNo);
                    try {
                        tripPlanList.put("BeginTime", (current.getStartDatetime() != null) ? format2.format(current.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)) : format2.format(linkHeader.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)));
                        tripPlanList.put("EndTime", (current.getEndDatetime() != null) ? format2.format(current.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)) : format2.format(linkHeader.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    ArrayList<JSONObject> invoiceLineList = new ArrayList<>();
                    JSONArray tripPlanListList = new JSONArray();
                    tripPlanListList.add(tripPlanList);
                    claimBody.put("TripPlanList", tripPlanListList);
                    int j = 1;

                    //itermed费用类型
                    List<String> typeList = new ArrayList<>();
                    typeList.add("C341");
                    typeList.add("C302");
                    typeList.add("C351");
                    typeList.add("C352");
                    typeList.add("C354");
                    typeList.add("C353");
                    typeList.add("C355");
                    //是否为itermed单据
                    boolean typeFlag = false;
                    //汇总行成本中心
                    Map<String, List<ExpClaimLine>> costCenterCodeLine = new HashMap<>();
                    for (ExpClaimLine currLine : claimLines) {
                        String costCenterCode = (nipponMapper.queryDeptCode(currLine.getCostCenterId()) != null) ? nipponMapper.queryDeptCode(currLine.getCostCenterId()) : "";
                        List<ExpClaimLine> currLineList = new ArrayList<>();
                        if (costCenterCodeLine.isEmpty() || !costCenterCodeLine.containsKey(costCenterCode)) {
                            currLineList.add(currLine);
                            costCenterCodeLine.put(costCenterCode, currLineList);
                        } else {
                            currLineList = costCenterCodeLine.get(costCenterCode);
                            currLineList.add(currLine);
                            costCenterCodeLine.put(costCenterCode, currLineList);
                        }
                        if (typeList.contains(currLine.getExpType().getTypeCode())) {
                            typeFlag = true;
                        }
                    }
                    Set<String> costCenterCodeKeySet = costCenterCodeLine.keySet();
                    JSONArray travelListList = new JSONArray();
                    // 计算发票分配结果, 通过行上税额相对占比计算实际占用金额（行上税额小于发票上税额的情况，立邦要求传发票上税额）
                    Map<String, BigDecimal> invoiceValidMap = Maps.newHashMap();// 去重票据，value为发票金额
                    MultiValueMap<String, Integer> invoiceLineMap = new LinkedMultiValueMap<>();// 发票和行关联关系
                    Map<String, BigDecimal> invoiceTaxMap = Maps.newHashMap();// 每张票关联的所有行金额汇总
                    Map<Integer, BigDecimal> lineTaxMap = Maps.newHashMap();// 每张票分配金额

                    for (String costCenterCodeKey : costCenterCodeKeySet) {
                        List<ExpClaimLine> lines = costCenterCodeLine.get(costCenterCodeKey);
                        for (ExpClaimLine currLine : lines) {
                            List<String> lineInvoiceList = nipponMapper.queryLineInvoice(currLine.getLineId());
                            if (lineInvoiceList != null && lineInvoiceList.size() > 0) {
                                for (String lineInvoice : lineInvoiceList) {
                                    JSONObject invoiceJson = JSONObject.parseObject(lineInvoice);
                                    BigDecimal tmpTax = invoiceJson.getBigDecimal("tax");
                                    String tmpCode = invoiceJson.getString("code");
                                    invoiceValidMap.put(tmpCode, tmpTax);
                                    invoiceLineMap.add(tmpCode, currLine.getLineId());
                                    BigDecimal finTaxAmt = BigDecimal.valueOf(currLine.getFinTaxAmount());
                                    if (invoiceTaxMap.get(tmpCode) == null) {
                                        invoiceTaxMap.put(tmpCode, finTaxAmt);
                                    } else {
                                        invoiceTaxMap.put(tmpCode, invoiceTaxMap.get(tmpCode).add(finTaxAmt));
                                    }
                                    lineTaxMap.put(currLine.getLineId(), finTaxAmt);
                                }
                            }
                        }
                    }
                    logger.info("{}reimClaimToJson------invoiceValidMap:{}，invoiceLineMap:{}，invoiceTaxMap:{}，lineTaxMap:{}",
                            documentNum, JSONObject.toJSONString(invoiceValidMap), JSONObject.toJSONString(invoiceLineMap)
                            , JSONObject.toJSONString(invoiceTaxMap), JSONObject.toJSONString(lineTaxMap));
                    // 发票最终分配结果
                    Map<String, Map<Integer, BigDecimal>> invoiceActuralMap = Maps.newHashMap();
                    for (String code : invoiceLineMap.keySet()) {
                        BigDecimal invoiceValidAmt = invoiceValidMap.get(code);// 发票实际税额
                        BigDecimal invoiceTaxAmt = invoiceTaxMap.get(code);// 行上计算税额总额
                        BigDecimal invoiceLeftAmt = BigDecimal.ZERO.add(invoiceValidAmt);// 发票剩余税额
                        List<Integer> list = invoiceLineMap.get(code);
                        Map<Integer, BigDecimal> actualLineTaxMap = Maps.newHashMap();
                        for (Integer i = 0; i < list.size(); i++) {
                            Integer lineId = list.get(i);
                            if (i < list.size() - 1) {
                                BigDecimal lineTaxAmt = lineTaxMap.get(lineId);
                                BigDecimal amt = lineTaxAmt.multiply(invoiceValidAmt).divide(invoiceTaxAmt, 2, RoundingMode.HALF_UP);
                                actualLineTaxMap.put(lineId, amt);
                                invoiceLeftAmt = invoiceLeftAmt.subtract(amt);
                            } else {
                                actualLineTaxMap.put(lineId, invoiceLeftAmt);
                            }
                        }
                        invoiceActuralMap.put(code, actualLineTaxMap);
                    }
                    logger.info("{}reimClaimToJson------invoiceActuralMap:{}", documentNum, JSONObject.toJSONString(invoiceActuralMap));
                    List<String> validInvoiceList = new ArrayList<>(invoiceValidMap.keySet());// 登记有效发票，去重，防止组合发票多记
                    for (String costCenterCodeKey : costCenterCodeKeySet) {
                        // 携程酒店/税金映射
                        HashMap<String, Double> hotelInvoiceTaxMap = new HashMap<>();
                        // 携程酒店服务费税金映射
                        HashMap<String, Double> hotelServiceInvoiceTaxMap = new HashMap<>();

                        JSONObject details = new JSONObject();
                        invoices = new HashSet<>();
                        taxList = new ArrayList<>();
                        List<ExpClaimLine> lines = costCenterCodeLine.get(costCenterCodeKey);
                        double trainFees = 0,
                                airFees = 0,
                                airGJFees = 0,
                                cityTrafficFees = 0,
                                cityTrafficQTFees = 0,
                                cityTrafficWLFees = 0,
                                hotelFees = 0,
                                hotelServerFees = 0,
                                grantFees = 0,
                                DTZJ = 0,
                                DTZJGL = 0,
                                DTZJQZ = 0,
                                otherFees = 0,
                                travelOtherFees = 0,
                                airOtherFee = 0,
                                airRYFees = 0,
                                airServerFees = 0,
                                airQTFees = 0,
                                travelTrainReturnFees = 0,
                                travelSaluteCheckFees = 0,
                                travelDepartureFees = 0,
                                tax = 0,
                                taxInInvoices = 0,
                                airFeesSJ = 0,
                                trainFeesSJ = 0,
                                otherFeesSJ = 0,
                                cityTrafficFeesSJ = 0,
                                grantFeesSJ = 0,
                                DTZJGLSJ = 0,
                                DTZJQZSJ = 0;

                        for (ExpClaimLine currLine : lines) {
                            logger.info("{}reimClaimToJson------currLine:{}", documentNum, JSONObject.toJSONString(currLine));
                            String costTypeCode;
                            lineIds.add(currLine.getLineId());
                            if (current.getProjectName() != null && (!"".equals(current.getProjectName()))) {
                                project = JSONObject.parseObject(nipponMapper.queryProjectInfo(Integer.parseInt(current.getProjectName())));

                            } else if (linkHeader.getProjectName() != null && (!"".equals(linkHeader.getProjectName()))) {
                                project = JSONObject.parseObject(nipponMapper.queryProjectInfo(Integer.parseInt(linkHeader.getProjectName())));
                            }

                            details.put("EmployeesNumber", (chargeUser != null) ? chargeUser : "");
                            taxDetails.put("EmployeesNumber", (chargeUser != null) ? chargeUser : "");
                            details.put("OrderNumber", SAPNo);
                            taxDetails.put("OrderNumber", SAPNo);
                            details.put("FromLocation", (fromCity != null) ? fromCity : "");
                            taxDetails.put("FromLocation", (fromCity != null) ? fromCity : "");
                            details.put("ToLocation", (toCity != null) ? toCity : "");
                            taxDetails.put("ToLocation", (toCity != null) ? toCity : "");

                            details.put("IsLocalTravel", (current.getColumn12() != null && current.getColumn12().equals("Y")) ? "1" : "0");
                            taxDetails.put("IsLocalTravel", (current.getColumn12() != null && current.getColumn12().equals("Y")) ? "1" : "0");

                            costTypeCode = (current.getColumn3() != null) ? current.getColumn3() : "";
                            if (linkHeader.getHeaderTypeId() == 126812 || linkHeader.getHeaderTypeId() == 126813) {
                                costTypeCode = "CL19";
                            } else {
                                switch (costTypeCode) {
                                    case "P001":
                                        costTypeCode = "CL17";
                                        break;
                                    case "P002":
                                        costTypeCode = "CL16";
                                        break;
                                    default:
                                        costTypeCode = "CL18";
                                }
                            }
                            details.put("CostTypeCode", costTypeCode);
                            taxDetails.put("CostTypeCode", "N001");
                            try {
                                details.put("FromDate", (current.getStartDatetime() != null) ? format.format(current.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)) : format.format(linkHeader.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)));
                                taxDetails.put("FromDate", (current.getStartDatetime() != null) ? format.format(current.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)) : format.format(linkHeader.getStartDatetime().getTime() + (8 * 60 * 60 * 1000)));
                                details.put("ToDate", (current.getEndDatetime() != null) ? format.format(current.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)) : format.format(linkHeader.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)));
                                taxDetails.put("ToDate", (current.getEndDatetime() != null) ? format.format(current.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)) : format.format(linkHeader.getEndDatetime().getTime() + (8 * 60 * 60 * 1000)));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            if (project != null) {
                                details.put("ProjectID", (project.get("id") != null) ? project.get("id") : "");
                                taxDetails.put("ProjectID", (project.get("id") != null) ? project.get("id") : "");
                                details.put("ProjectCode", (project.get("code") != null) ? project.get("code") : "");
                                taxDetails.put("ProjectCode", (project.get("code") != null) ? project.get("code") : "");
                                details.put("ProjectDescription", (project.get("name") != null) ? project.get("name") : "");
                                taxDetails.put("ProjectDescription", (project.get("name") != null) ? project.get("name") : "");
                            }
                            if (current.getColumn7() != null && (!"".equals(current.getColumn7())) && (!"null".equals(current.getColumn7()))) {
                                details.put("HWCode", current.getColumn7());
                                taxDetails.put("HWCode", current.getColumn7());
                            } else if (linkHeader.getColumn7() != null && (!"".equals(linkHeader.getColumn7())) && (!"null".equals(current.getColumn7()))) {
                                details.put("HWCode", linkHeader.getColumn7());
                                taxDetails.put("HWCode", linkHeader.getColumn7());
                            }
                            if (current.getColumn8() != null && (!"".equals(current.getColumn8())) && (!"null".equals(current.getColumn8()))) {
                                details.put("InternalOrderCode", current.getColumn8());
                                taxDetails.put("InternalOrderCode", current.getColumn8());
                            } else if (linkHeader.getColumn8() != null && (!"".equals(linkHeader.getColumn8())) && (!"null".equals(current.getColumn8()))) {
                                details.put("InternalOrderCode", linkHeader.getColumn8());
                                taxDetails.put("InternalOrderCode", linkHeader.getColumn8());
                            }

                            //获取本行发票信息
                            JSONObject invoice = new JSONObject();
                            String invoiceNum = "";
                            double taxAmount = 0;
                            List<String> lineInvoiceList = nipponMapper.queryLineInvoice(currLine.getLineId());
                            if (lineInvoiceList != null && lineInvoiceList.size() > 0) {
                                for (String lineInvoice : lineInvoiceList) {
                                    Double tmpTax = JSONObject.parseObject(lineInvoice).getDouble("tax");
                                    String tmpCode = JSONObject.parseObject(lineInvoice).getString("code");
//                                    if(Strings.isNullOrEmpty(tmpCode) || validInvoiceList.contains(tmpCode)) {
//                                        continue;// 去重，防止组合发票多记税金
//                                    } else {
//                                        validInvoiceList.add(tmpCode);
//                                    }
                                    if (invoiceActuralMap.get(tmpCode) != null && invoiceActuralMap.get(tmpCode).get(currLine.getLineId()) != null)
                                        tmpTax = invoiceActuralMap.get(tmpCode).get(currLine.getLineId()).doubleValue();// 从计算结果中获取金额
                                    taxAmount += tmpTax;
                                    Map<String, Object> taxMap = new HashMap<>();
                                    taxMap.put("tax", tmpTax);
                                    taxMap.put("code", tmpCode);
                                    taxList.add(taxMap);
                                    invoiceNum += Strings.isNullOrEmpty(invoiceNum) ? tmpCode : "," + tmpCode;
                                }
                                invoice = JSONObject.parseObject(lineInvoiceList.get(0));
                            }

                            logger.info("{}reimPush获取本行发票信息，数量：{}; 去重后：{}; 发票号：{}", documentNum, lineInvoiceList.size(), validInvoiceList, invoiceNum);

                            //根据预付现付决定是否要获取详情
                            int payMethod = currLine.getPayMethodId();
                            logger.info("{}reimClaimToJson------currLine.typeCode:{}", documentNum, currLine.getExpType().getTypeCode());
                            if (currLine.getSource() != null && (currLine.getSource().contains("meiya") || currLine.getSource().contains("ctrip"))) {
                                try {
                                    HttpEntity<String> request;
                                    HttpHeaders headers = new HttpHeaders();
                                    headers.setContentType(MediaType.APPLICATION_JSON);
                                    request = new HttpEntity<>(headers);
                                    String tokenResult = sendUtil.postForObject("reimClaimToJson", rb.getString("nippon.get_cp_token") + "?grant_type=" + rb.getString("nippon.grant_type") + "&client_id=" + rb.getString("nippon.client_id") + "&client_secret=" + rb.getString("nippon.client_secret"), request);
                                    logger.info("获取token响应：{}", tokenResult);
                                    JSONObject selfToken = JSONObject.parseObject(tokenResult);
                                    String tmcUrl = rb.getString("nippon.tmc") + "?access_token=" + selfToken.getString("access_token");
                                    String eTicketUrl = rb.getString("nippon.tmc.ticket") + "?access_token=" + selfToken.getString("access_token");
                                    JSONObject expenseId = new JSONObject();
                                    expenseId.put("expense_id", currLine.getExpenseId());
                                    request = new HttpEntity<>(expenseId.toJSONString(), headers);
                                    String ticketResult = sendUtil.postForObject("reimClaimToJson", eTicketUrl, request);
                                    logger.info("tmc ticket接口响应：{}", ticketResult);
                                    JSONObject ticketsObj = JSON.parseObject(ticketResult);
                                    JSONArray ticketsArr = ticketsObj.getJSONArray("ticket_no");
                                    if (ticketsArr != null) {
                                        for (Object o : ticketsArr) {
                                            try {

                                                String code = (String) o;
                                                if (code != null && code.length() > 0) {
                                                    JSONObject DZKPObj = new JSONObject();
                                                    DZKPObj.put("OrderNumber", SAPNo);
                                                    DZKPObj.put("Code", code);
                                                    DZKPObj.put("CreateDate", (current.getCreationDate() != null) ? formatD.format(current.getCreationDate().getTime() + (8 * 60 * 60 * 1000)) : "");
                                                    DZKPObj.put("State", "");
                                                    DZKPList.add(DZKPObj);

                                                }
                                            } catch (Exception ignored) {
                                            }
                                        }
                                    }
                                    if (payMethod == 1) {

                                        String type = currLine.getExpType().getTypeCode();
                                        if (null == type) {
                                            throw new ValidationException("invalid type");
                                        }

                                        if (type.equals("C301") || type.equals("C356")) {
                                            logger.info("请求tmc token接口");
                                            String resultStr = sendUtil.postForObject("reimClaimToJson", tmcUrl, request);
                                            logger.info("tmc token请求接口响应：{}", resultStr);
                                            JSONObject expDetail = JSONObject.parseObject(resultStr);
                                            JSONObject feeDetail = expDetail.getJSONObject("detail");
                                            if (null == feeDetail) {
                                                throw new ValidationException("no data received");
                                            }
                                            float hotelTicketPrice = (feeDetail.getFloatValue("amount") - feeDetail.getFloatValue("service_price"));
                                            if (type.equals("C301")) {
                                                hotelFees += currLine.getNetAmount();
                                                // 携程酒店税金作为行
//                                                hotelServerFees += feeDetail.getFloatValue("service_price") - (Math.round(feeDetail.getFloatValue("service_price") / 1.06 * 0.06 * 100) * 0.01d);
                                                double taxAmountCurr = currLine.getTaxAmount();
                                                hotelFees = Double.parseDouble(df1.format(hotelFees));
                                                hotelServerFees = Double.parseDouble(df1.format(hotelServerFees));
                                                taxAmountCurr = Double.parseDouble(df1.format(taxAmountCurr));
                                                tax += taxAmountCurr;

                                                Double hotelTax = hotelInvoiceTaxMap.get(currLine.getExpenseId());
                                                if (hotelTax == null) {
                                                    hotelInvoiceTaxMap.put(currLine.getExpenseId() + ";" + currLine.getCostCenterId(), taxAmountCurr);
                                                } else {
                                                    hotelInvoiceTaxMap.put(currLine.getExpenseId() + ";" + currLine.getCostCenterId(), taxAmountCurr + hotelTax);
                                                }
                                            }
                                            if (type.equals("C356")) {
                                                hotelServerFees += currLine.getNetAmount();
                                                // 计算服务费作为行
                                                double taxAmountCurr = currLine.getTaxAmount();
                                                hotelFees = Double.parseDouble(df1.format(hotelFees));
                                                hotelServerFees = Double.parseDouble(df1.format(hotelServerFees));
                                                taxAmountCurr = Double.parseDouble(df1.format(taxAmountCurr));
                                                tax += taxAmountCurr;
                                                hotelServiceInvoiceTaxMap.putIfAbsent(currLine.getExpenseId() + ";" + currLine.getCostCenterId(), taxAmountCurr);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            } else if (payMethod == 1) {
                                String type = currLine.getExpType().getTypeCode();

                                if ("C302".equals(type) &&
                                        (linkHeader.getHeaderTypeId() == 126807 || linkHeader.getHeaderTypeId() == 126809)) {
                                    airFees += currLine.getClaimAmount();
                                    airFeesSJ += currLine.getTaxAmount();
                                } else if ("C302".equals(type)) {
                                    airGJFees += currLine.getClaimAmount();
                                } else if (type.equals("C301")) {
                                    if (taxAmount > 0 && !invoiceNum.equals("")) {
                                        hotelFees += currLine.getNetAmount();
                                        if (taxList.size() <= 0) {
                                            invoiceLineList.add(invoice);
                                            taxInInvoices += taxAmount;
                                        }
                                    } else {
                                        hotelFees += currLine.getClaimAmount();
                                        tax += currLine.getTaxAmount();
                                    }
                                }
                            }


                            String type = currLine.getExpType().getTypeCode();
                            if (type != null) {
                                switch (type) {
                                    case "C314":
                                        if (!"".equals(invoiceNum)) {
                                            cityTrafficQTFees += currLine.getNetAmount();
                                        } else {
                                            cityTrafficQTFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C313":
                                        if (!"".equals(invoiceNum)) {
                                            cityTrafficWLFees += currLine.getNetAmount();
                                        } else {
                                            cityTrafficWLFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C311":
                                    case "C312":
                                    case "C318":
                                        if (!"".equals(invoiceNum)) {
                                            cityTrafficFees += currLine.getNetAmount();
                                        } else {
                                            cityTrafficFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C303":
                                        trainFees += currLine.getClaimAmount();
                                        break;
                                    case "C304":
                                        if (!"".equals(invoiceNum)) {
                                            otherFees += currLine.getNetAmount();
                                        } else {
                                            otherFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C331":
                                    case "C332":
                                        if (!"".equals(invoiceNum)) {
                                            grantFees += currLine.getNetAmount();
                                        } else {
                                            grantFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C342":
                                        if (!"".equals(invoiceNum)) {
                                            travelTrainReturnFees += currLine.getNetAmount();
                                        } else {
                                            travelTrainReturnFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C343":
                                        if (!"".equals(invoiceNum)) {
                                            travelSaluteCheckFees += currLine.getNetAmount();
                                        } else {
                                            travelSaluteCheckFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C344":
                                        if (!"".equals(invoiceNum)) {
                                            travelDepartureFees += currLine.getNetAmount();
                                        } else {
                                            travelDepartureFees += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C321":
                                        if (!"".equals(invoiceNum)) {
                                            DTZJ += currLine.getNetAmount();
                                        } else {
                                            DTZJ += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C322":
                                        if (!"".equals(invoiceNum)) {
                                            DTZJGL += currLine.getNetAmount();
                                        } else {
                                            DTZJGL += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C323":
                                        if (!"".equals(invoiceNum)) {
                                            DTZJQZ += currLine.getNetAmount();
                                        } else {
                                            DTZJQZ += currLine.getClaimAmount();
                                        }
                                        break;
                                    case "C301":
                                    case "C302":
                                    case "C341":
                                    case "C351":
                                    case "C352":
                                    case "C353":
                                    case "C354":
                                    case "C356":
                                        break;
                                    default:
                                        if (!"".equals(invoiceNum)) {
                                            travelOtherFees += currLine.getNetAmount();
                                        } else {
                                            travelOtherFees += currLine.getClaimAmount();
                                        }
                                }

                                if (payMethod == 1 && typeFlag) {
                                    switch (type) {
                                        case "C341":
                                        case "C302":
                                            airFees += currLine.getClaimAmount();
                                            break;
                                        case "C351":
                                        case "C352":
                                        case "C354":
                                            airQTFees += currLine.getClaimAmount();
                                            break;
                                        case "C353":
                                            airRYFees += currLine.getClaimAmount();
                                            break;
                                    }
                                }
                            }
                            if (currLine.getExpenseId() < 0) {
                                invoices.add(Math.abs(currLine.getExpenseId()));
                            }

                            //酒店住宿天数
                            if ("hotel".equals(currLine.getInternalType())) {
                                double HotelDays = currLine.getQuantity();
                                if (details.get("HotelDays") != null) {
                                    HotelDays += details.getDouble("HotelDays");
                                }
                                details.put("HotelDays", HotelDays);
                            }

                        }
//                        double sum = current.getTotalAmount() - tax - taxInInvoices;
                        double plusSum = trainFees + airFees + airGJFees + airOtherFee + airRYFees + airServerFees + airQTFees + cityTrafficFees + cityTrafficFeesSJ + cityTrafficQTFees + cityTrafficWLFees + hotelFees + hotelServerFees + airFeesSJ + trainFeesSJ + otherFeesSJ + travelOtherFees + travelTrainReturnFees + travelSaluteCheckFees + travelDepartureFees + grantFees + grantFeesSJ + DTZJ + DTZJGL + DTZJQZ + DTZJGLSJ + DTZJQZSJ + otherFees;
                        plusSum = Double.parseDouble(df1.format(plusSum));
//                        sum = Double.parseDouble(df1.format(sum));
                        details.put("SumFees", Double.parseDouble(df1.format(plusSum)));
                        details.put("TrainFees", Double.parseDouble(df1.format(trainFees)));
                        details.put("AirFees", Double.parseDouble(df1.format(airFees)));
                        details.put("AirGJFees", Double.parseDouble(df1.format(airGJFees)));
                        details.put("AirOtherFee", Double.parseDouble(df1.format(airOtherFee)));
                        details.put("AirRYFees", Double.parseDouble(df1.format(airRYFees)));
                        details.put("AirServerFees", Double.parseDouble(df1.format(airServerFees)));
                        details.put("AirQTFees", Double.parseDouble(df1.format(airQTFees)));
                        details.put("CityTrafficFees", Double.parseDouble(df1.format(cityTrafficFees)));
                        details.put("CityTrafficFeesSJ", Double.parseDouble(df1.format(cityTrafficFeesSJ)));
                        details.put("CityTrafficQTFees", Double.parseDouble(df1.format(cityTrafficQTFees)));
                        details.put("CityTrafficWLFees", Double.parseDouble(df1.format(cityTrafficWLFees)));
                        details.put("HotelFees", Double.parseDouble(df1.format(hotelFees)));
                        details.put("HotelServerFees", Double.parseDouble(df1.format(hotelServerFees)));
                        details.put("AirFeesSJ", Double.parseDouble(df1.format(airFeesSJ)));
                        details.put("TrainFeesSJ", Double.parseDouble(df1.format(trainFeesSJ)));
                        details.put("OtherFeesSJ", Double.parseDouble(df1.format(otherFeesSJ)));
                        details.put("TravelOtherFees", Double.parseDouble(df1.format(travelOtherFees)));
                        details.put("TravelTrainReturnFees", Double.parseDouble(df1.format(travelTrainReturnFees)));
                        details.put("TravelSaluteCheckFees", Double.parseDouble(df1.format(travelSaluteCheckFees)));
                        details.put("TravelDepartureFees", Double.parseDouble(df1.format(travelDepartureFees)));
                        details.put("GrantFees", Double.parseDouble(df1.format(grantFees)));
                        details.put("GrantFeesSJ", Double.parseDouble(df1.format(grantFeesSJ)));
                        details.put("DTZJ", Double.parseDouble(df1.format(DTZJ)));
                        details.put("DTZJGL", Double.parseDouble(df1.format(DTZJGL)));
                        details.put("DTZJQZ", Double.parseDouble(df1.format(DTZJQZ)));
                        details.put("DTZJGLSJ", Double.parseDouble(df1.format(DTZJGLSJ)));
                        details.put("DTZJQZSJ", Double.parseDouble(df1.format(DTZJQZSJ)));
                        details.put("OtherFees", Double.parseDouble(df1.format(otherFees)));
                        details.put("RowID", nf.format(j));
                        j++;
                        details.put("Summary", (current.getDescription() != null) ? current.getDescription() : "");
                        details.put("CostCenterCode", costCenterCodeKey);
                        taxDetails.put("CostCenterCode", costCenterCodeKey);
                        //查询成本中心名称
                        String name = nipponMapper.queryDeptNameByCode(costCenterCodeKey);
                        details.put("CostCenterDescription", name);
                        taxDetails.put("CostCenterDescription", name);

                        //培训号
                        if (current.getHeaderTypeId() == 126805 || current.getHeaderTypeId() == 126810) {
                            details.put("TrainNo", current.getColumn6());
                        }


                        travelListList.add(details);
                        for (int x = 0; x < taxList.size(); x++) {
                            Map taxAmountCurr = taxList.get(x);
                            if ("0.0".equals(taxAmountCurr.get("tax").toString())) {
                                continue;
                            }
                            DecimalFormat df = new DecimalFormat("000");
                            String RowID = df.format(j);
                            j++;
                            taxDetails.put("RowID", RowID);
                            taxDetails.put("SJ", taxAmountCurr.get("tax").toString());
                            taxDetails.put("SumFees", taxAmountCurr.get("tax").toString());
                            taxDetails.put("Summary", taxAmountCurr.get("code").toString());

                            JSONObject taxDetailsCurr = (JSONObject) taxDetails.clone();
                            travelListList.add(taxDetailsCurr);
                        }
                        taxList = new ArrayList<>();
                        if (invoiceLineList.size() > 0) {
                            for (int i = 0; i < invoiceLineList.size(); i++) {
                                JSONObject object = invoiceLineList.get(i);
                                JSONObject tempResult = JSONObject.parseObject(taxDetails.toString());
                                tempResult.put("Summary", object.getString("code"));
                                tempResult.put("SJ", object.getDoubleValue("tax"));
                                tempResult.put("SumFees", object.getDoubleValue("tax"));
                                if (tax > 0) {
                                    tempResult.put("RowID", nf.format(j));
                                } else {
                                    tempResult.put("RowID", nf.format(j));
                                }
                                j++;
                                travelListList.add(tempResult);
                            }
                        }
//                        if (plusSum != sum) {
//                            String con = "单据" + current.getDocumentNum() + "差旅报销合计金额有误!\n详情:\n sum：" + sum + ", plusSum：" + plusSum + ", details: " + details + ", claimBody: " + claimBody;
//                            logger.info("nippon_claim_amount_error",con);
//                            nipponMapper.updateExternalStatus(current.getHeaderId(), "error","差旅报销合计金额有误!");
//                            continue claimLoop;
//                        }
                        taxList = new ArrayList<>();
                        // 携程酒店税金
                        for (String key : hotelInvoiceTaxMap.keySet()) {
                            Map<String, Object> taxMap = new HashMap<>();
                            taxMap.put("tax", hotelInvoiceTaxMap.get(key));
                            taxMap.put("code", "携程酒店税金");
                            taxList.add(taxMap);
                        }
                        //携程服务费税金
                        for (String key : hotelServiceInvoiceTaxMap.keySet()) {
                            Map<String, Object> taxMap = new HashMap<>();
                            taxMap.put("tax", hotelServiceInvoiceTaxMap.get(key));
                            taxMap.put("code", "携程服务费税金");
                            taxList.add(taxMap);
                        }
                        for (Map taxAmountCurr : taxList) {
                            if ("0.0".equals(taxAmountCurr.get("tax").toString())) {
                                continue;
                            }
                            DecimalFormat df = new DecimalFormat("000");
                            String RowID = df.format(j);
                            j++;
                            taxDetails.put("RowID", RowID);
                            taxDetails.put("SJ", taxAmountCurr.get("tax").toString());
                            taxDetails.put("SumFees", taxAmountCurr.get("tax").toString());
                            taxDetails.put("Summary", taxAmountCurr.get("code").toString());

                            JSONObject taxDetailsCurr = (JSONObject) taxDetails.clone();
                            travelListList.add(taxDetailsCurr);
                        }
                    }

                    claimBody.put("TravelList", travelListList);
                    claimBody.put("DZKPList", DZKPList);
                    break;
                case 126806:
                    j = 0;
                    //汇总行成本中心
                    costCenterCodeLine = new HashMap<>();
                    for (ExpClaimLine currLine : claimLines) {
                        String costCenterCode = (nipponMapper.queryDeptCode(currLine.getCostCenterId()) != null) ? nipponMapper.queryDeptCode(currLine.getCostCenterId()) : "";
                        List<ExpClaimLine> currLineList = new ArrayList<>();
                        if (costCenterCodeLine.isEmpty() || !costCenterCodeLine.containsKey(costCenterCode)) {
                            currLineList.add(currLine);
                            costCenterCodeLine.put(costCenterCode, currLineList);
                        } else {
                            currLineList = costCenterCodeLine.get(costCenterCode);
                            currLineList.add(currLine);
                            costCenterCodeLine.put(costCenterCode, currLineList);
                        }
                    }
                    costCenterCodeKeySet = costCenterCodeLine.keySet();
                    for (String costCenterCodeKey : costCenterCodeKeySet) {
                        JSONObject details = new JSONObject();
                        invoices = new HashSet<>();
                        taxList = new ArrayList<>();
                        List<ExpClaimLine> lines = costCenterCodeLine.get(costCenterCodeKey);

                        TravelExpenseVo travelExpenseVo = new TravelExpenseVo();
                        StringBuffer comments = null;
                        String type = "";
                        for (ExpClaimLine currLine : lines) {
                            details = new JSONObject();
                            taxDetails = new JSONObject();
                            lineIds.add(currLine.getLineId());
                            project = null;
                            if (currLine.getProjectName() != null && (!"".equals(currLine.getProjectName()))) {
                                project = JSONObject.parseObject(nipponMapper.queryProjectInfo(Integer.parseInt(currLine.getProjectName())));

                            } else if (linkHeader.getProjectName() != null && !"".equals(linkHeader.getProjectName())) {
                                project = JSONObject.parseObject(nipponMapper.queryProjectInfo(Integer.parseInt(linkHeader.getProjectName())));
                            }
                            String glp = "";
                            String duration = currLine.getDuration();

                            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
                            try {
                                if (duration != null) {
                                    if (Pattern.matches("\\d{4}-\\d{2}", duration)) {
                                        SimpleDateFormat sdfIn = new SimpleDateFormat("yyyy-MM");
                                        glp = sdf.format(sdfIn.parse(duration));
                                    } else if (Pattern.matches("\\d{13}", duration)) {
                                        glp = sdf.format(new Date(Long.parseLong(duration)));
                                    } else if (Pattern.matches("\\d{4}-\\d", duration)) {
                                        SimpleDateFormat sdfIn = new SimpleDateFormat("yyyy-M");
                                        glp = sdf.format(sdfIn.parse(duration));
                                    } else if (Pattern.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.\\d{3}.*", duration)) {
                                        SimpleDateFormat sdfIn = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                                        glp = sdf.format(sdfIn.parse(duration));
                                    }
                                }
                            } catch (Exception e) {
                                glp = "";
                            }

                            details.put("EmployeesNumber", (chargeUser != null) ? chargeUser : "");
                            taxDetails.put("EmployeesNumber", (chargeUser != null) ? chargeUser : "");
                            details.put("OrderNumber", SAPNo);
                            taxDetails.put("OrderNumber", SAPNo);
                            String costType = (currLine.getExpType().getTypeCode() != null) ? currLine.getExpType().getTypeCode() : "";
                            switch (costType) {
                                case "N111":
                                    costType = "N111";
                                    break;
                                case "N012":
                                    costType = "N012";
                                    break;
                                case "clsyf2":
                                case "clsyf":
                                case "N058":
                                case "gltxf-crf":
                                    costType = "N058";
                                    break;
                                case "N091a":
                                    costType = "N091a";
                                    break;
                                case "N091b":
                                    costType = "N091b";
                                    break;
                                case "N091c":
                                    costType = "N091c";
                                    break;
                                case "N091d":
                                    costType = "N091d";
                                    break;
                                case "N050":
                                    costType = "N050";
                                    break;
                                case "N050C":
                                    costType = "CL18";
                                    break;
                            }
                            details.put("CostTypeCode", costType);
                            taxDetails.put("CostTypeCode", "N001");

                            details.put("ExpenseMoney", df1.format(new BigDecimal(String.valueOf(currLine.getNetAmount())).setScale(2, 4)));
                            details.put("AccruedExpenses", df1.format(new BigDecimal(String.valueOf(currLine.getNetAmount())).setScale(2, 4)));
                            List<String> lineInvoiceOtherList = nipponMapper.queryLineInvoiceOther(currLine.getLineId());
                            logger.info("lineInvoiceOtherList------" + lineInvoiceOtherList);
                            if (lineInvoiceOtherList != null && lineInvoiceOtherList.size() > 0) {

                                details.put("ExpenseMoney", df1.format(new BigDecimal(String.valueOf(currLine.getClaimAmount())).setScale(2, 4)));
                                details.put("AccruedExpenses", df1.format(new BigDecimal(String.valueOf(currLine.getClaimAmount())).setScale(2, 4)));
                            }
//                            if ("N058".equals(costType)) {
//                                details.put("ExpenseMoney", String.valueOf(currLine.getClaimAmount()));
//                                details.put("AccruedExpenses", String.valueOf(currLine.getClaimAmount()));
//                            }
//                            taxDetails.put("ExpenseMoney", String.valueOf(currLine.getTaxAmount()));
//                            taxDetails.put("AccruedExpenses", String.valueOf(currLine.getTaxAmount()));
                            details.put("CostCenterCode", costCenterCodeKey);
                            taxDetails.put("CostCenterCode", costCenterCodeKey);
                            details.put("CostPeriod", glp);
                            taxDetails.put("CostPeriod", glp);
                            details.put("Summary", (currLine.getComments() != null) ? currLine.getComments() : "");
                            details.put("OverrunThat", (currLine.getColumn16() != null) ? currLine.getColumn16() : "");

                            String summary = "";
                            List<String> lineInvoiceList = nipponMapper.queryLineInvoice(currLine.getLineId());
                            if (lineInvoiceList != null && lineInvoiceList.size() > 0) {
                                for (String lineInvoice : lineInvoiceList) {
                                    Map<String, Object> taxMap = new HashMap<>();
                                    taxMap.put("tax", JSONObject.parseObject(lineInvoice).getDouble("tax"));
                                    taxMap.put("code", JSONObject.parseObject(lineInvoice).getString("code"));
                                    taxList.add(taxMap);
                                    JSONObject invoiceCurr = JSONObject.parseObject(lineInvoice);
                                    if (invoiceCurr != null) {
                                        summary += "," + (invoiceCurr.getString("code"));
                                    }
                                }
                            }
                            logger.info("reimPush" + "获取本行发票信息，数量：" + lineInvoiceList.size() + ";发票号：" + summary);

                            if (!"".equals(summary)) {
                                summary = summary.substring(1, summary.length());
                            }
                            taxDetails.put("Summary", summary);
                            if (project != null) {
                                details.put("ProjectID", (project.get("id") != null) ? project.get("id") : "");
                                details.put("ProjectCode", (project.get("code") != null) ? project.get("code") : "");
                                details.put("ProjectDescription", (project.get("name") != null) ? project.get("name") : "");
                                taxDetails.put("ProjectID", (project.get("id") != null) ? project.get("id") : "");
                                taxDetails.put("ProjectCode", (project.get("code") != null) ? project.get("code") : "");
                                taxDetails.put("ProjectDescription", (project.get("name") != null) ? project.get("name") : "");
                            }
                            if (currLine.getColumn35() != null && (!"".equals(currLine.getColumn35())) && (!"null".equals(current.getColumn35()))) {
                                details.put("HWCode", currLine.getColumn35());
                                taxDetails.put("HWCode", currLine.getColumn35());
                            } else if (linkHeader.getColumn7() != null && (!"".equals(linkHeader.getColumn7()))) {
                                details.put("HWCode", linkHeader.getColumn7());
                                taxDetails.put("HWCode", linkHeader.getColumn7());
                            }
                            if (currLine.getColumn22() != null && (!"".equals(currLine.getColumn22())) && (!"null".equals(current.getColumn22()))) {
                                details.put("InternalOrderCode", currLine.getColumn22());
                                taxDetails.put("InternalOrderCode", currLine.getColumn22());
                            } else if (linkHeader.getColumn8() != null && (!"".equals(linkHeader.getColumn8())) && (!"null".equals(current.getColumn8()))) {
                                details.put("InternalOrderCode", linkHeader.getColumn8());
                                taxDetails.put("InternalOrderCode", linkHeader.getColumn8());
                            }

                            if (!"N050".equals(costType) && !"CL18".equals(costType)) {

                                NipponHelper.setSumAmountBySummary(travelExpenseVo, currLine, summary);
                                details.put("RowID", nf.format(j + 501));
                                j++;
                                detailsList.add(details);
                                for (Map taxAmountCurr : taxList) {
                                    if ("0.0".equals(taxAmountCurr.get("tax").toString())) {
                                        continue;
                                    }
                                    taxDetails.put("RowID", nf.format(j + 501));
                                    j++;
                                    Double tax = (Double) taxAmountCurr.get("tax");
                                    taxDetails.put("AccruedExpenses", df1.format(new BigDecimal(String.valueOf(tax)).setScale(2, 4)));
                                    taxDetails.put("ExpenseMoney", df1.format(new BigDecimal(String.valueOf(tax)).setScale(2, 4)));
                                    JSONObject taxDetailsCurr = (JSONObject) taxDetails.clone();
                                    detailsList.add(taxDetailsCurr);
                                }

                                taxList = new ArrayList<>();
                            } else {
                                String travelType = currLine.getColumn40();
                                if (travelType != null) {
                                    if ("CL18".equals(costType)) {
                                        type = "CL18";
                                    }
                                    //按照travelType设置相关费用
                                    NipponHelper.setValuesByTravelType(travelExpenseVo, currLine, travelType);
                                    if (currLine.getComments() != null && (!"".equals(currLine.getComments()))) {
                                        if (comments == null) {
                                            comments = new StringBuffer();
                                            comments.append(currLine.getComments());
                                        } else {
                                            comments.append("|");
                                            comments.append(currLine.getComments());
                                        }
                                    }
                                }
                            }
                            //获取发票列表
                            if (currLine.getExpenseId() < 0) {
                                invoices.add(Math.abs(currLine.getExpenseId()));
                            }
                        }
                        JSONObject tempDetails;
                        tempDetails = JSON.parseObject(details.toString());

                        tempDetails.remove("InternalOrderCode");
                        tempDetails.remove("ProjectCode");
                        tempDetails.remove("ProjectDescription");
                        tempDetails.remove("ProjectID");
                        if (comments == null) {
                            tempDetails.put("Summary", "");
                        } else {
                            tempDetails.put("Summary", comments.toString());
                        }
                        j = NipponHelper.buildTempObject(travelExpenseVo, tempDetails, j, type, detailsList);

                    }
                    claimBody.put("ExpenseList", detailsList);

            }
            if (lineIds.size() > 0) {
                String lines = lineIds.toString().replaceAll("\\[", "(").replaceAll("]", ")");
                files = nipponMapper.queryFileList(current.getHeaderId(), lines);

                for (ExpClaimAttachment currAtt : files
                ) {
                    JSONObject att = new JSONObject();
                    att.put("FileName", currAtt.getFileName());
                    att.put("Name", currAtt.getAttachmentUrl());
                    att.put("OrderNumber", SAPNo);
                    att.put("TOrderNumber", current.getDocumentNum());
                    att.put("FileSize", "0");
                    att.put("CreateDate", formatD.format(currAtt.getCreationDate().getTime() + (8 * 60 * 60 * 1000)));
                    filesList.add(att);
                }
            }
            approve = nipponMapper.queryApproveList(current.getHeaderId(), SAPNo, current.getDocumentNum());
            for (String approver :
                    approve) {
                JSONObject object = JSONObject.parseObject(approver);
                if (object.getString("ApproveEmployeesNumber") != null) {
                    approveList.add(object);
                }
            }
            if (approveList.size() == 0) {
                // 获取值列表配置
                List<String> remitTypeList = nipponMapper.queryLovValueList("Remit Type");
                boolean remitFlag = false;
                if (remitTypeList != null && remitTypeList.size() > 0 &&
                        claimLines != null && claimLines.size() > 0) {
                    for (ExpClaimLine line : claimLines) {
                        String typeCode = line.getExpType() == null ? "" : line.getExpType().getTypeCode();
                        if (remitTypeList.contains(typeCode)) {
                            remitFlag = true;
                            break;
                        }
                    }
                }
                if (!"N".equals(flag) && !remitFlag) {
                    nipponMapper.rejectHeader(current.getHeaderId(), "系统未找到您的上级审批人，请添加审批人审批，或者联系HR系统管理员。");
                    continue;
                }
            }
            approveList.sort((o1, o2) -> {
                JSONObject object1 = (JSONObject) o1;
                JSONObject object2 = (JSONObject) o2;
                return object1.getInteger("StartTime") - object2.getInteger("StartTime");
            });
            for (int i = 0; i < approveList.size(); i++) {
                JSONObject object = (JSONObject) approveList.get(i);
                object.put("ApproveLevel", i + 1);
            }
            Date approvedDate = nipponMapper.queryLastApprovedTime(current.getHeaderId());
            if (approvedDate == null) {
                approvedDate = new Date();
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(approvedDate);
            calendar.add(Calendar.HOUR, 8);
            approvedDate = calendar.getTime();
            if (approvedDate != null) {
                headModel.put("succeedDate", sdf1.format(approvedDate));
                headModel.put("PIUpdateTimeStamp", sdf1.format(approvedDate));
            }
            invoices.addAll(nipponMapper.queryClaimInvoice(current.getHeaderId()));
            String invoiceList = invoices.toString().replaceAll("\\[", "").replaceAll("]", "").replaceAll(" ", "");
            claimBody.put("HeadModel", headModel);
            claimBody.put("InvoiceList", (invoiceList != null) ? invoiceList : "");
            claimBody.put("FilesList", filesList);
            claimBody.put("ApproveList", approveList);

            result.add(JSONObject.parseObject(claimBody.toString()));
        }

        result.forEach(doc -> {
            try {
                JSONArray expenseList = doc.getJSONArray("ExpenseList");
                if (expenseList != null) {
                    expenseList.forEach(expense->{
                        JSONObject jsonObj = (JSONObject) expense;
                        jsonObj.put("AccruedExpenses",df1.format(new BigDecimal((String)jsonObj.get("AccruedExpenses")).setScale(2,4)));
                        jsonObj.put("ExpenseMoney",df1.format(new BigDecimal((String)jsonObj.get("ExpenseMoney")).setScale(2,4)));
                    });
                }
            } catch (Exception e) {
                logger.error("单据精度转换错误");
            }
        });
        return result;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void reimPush(boolean fullsync) {
        LogHolder.setLogKey("立邦单据推送==");
        Integer lovId;
        try {
            // 加锁，防止任务调度冲突 fixme: 不适用于分布式环境
            synchronized (this) {
                lovId = lovMapper.getLovId("System Param", companyId);
                String flag = lovMapper.getValueEnableFlag(lovId, CLAIM_PUSH_LOCK);
                if(flag == null) {
                    lovMapper.insertValue(lovId, companyId, CLAIM_PUSH_LOCK, "Y");
                } else if("Y".equals(flag)) {
                    logger.error("{}已存在处理中任务，忽略处理", LogHolder.getLogKey());
                    LogHolder.clearLogKey();
                    return;
                } else {
                    lovMapper.setValueEnableFlag(lovId, CLAIM_PUSH_LOCK, "Y");
                }
            }
            logger.info("{}获取任务锁成功", LogHolder.getLogKey());
        } catch (Exception e) {
            logger.error("{}获取任务锁失败：{}", LogHolder.getLogKey(), e.getMessage(), e);
            LogHolder.clearLogKey();
            return;
        }
        try {
            ReimOrderStub stub = new ReimOrderStub(rb.getString("nippon.claim_push"));
            ReimOrderStub.SendTravelOrder reqData = new ReimOrderStub.SendTravelOrder();
            ReimOrderStub.SendTravelOrderResponse response;
            String message;
            ArrayList<JSONObject> datas = reimClaimToJson();
            List<Integer> needSendMailIds=new ArrayList<>();
            logger.info("{}获取到推送目标数据{}条", LogHolder.getLogKey(), datas.size());
            for (JSONObject data : datas) {
                reqData.setJsonList(data.toString());
                logger.info("{}开始处理：{}", LogHolder.getLogKey(), data.toJSONString());
                try {
                    response = sendUtil.sendTravelOrder("reimPush", stub, reqData);

                    JSONObject result = JSONObject.parseObject(response.getSendTravelOrderResult());
                    if (result.getString("Message") != null) {
                        if (result.getString("Message").length() > 100) {
                            message = result.getString("Message").substring(0, 100);
                        } else {
                            message = result.getString("Message");
                        }
                    } else {
                        message = "";
                    }
                    if (result.getString("Message") != null
                            && (
                            ("成功".equals(result.getString("Message")))
                                    || (result.getString("Message").contains("报销单已存在")))) {
                        nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "success", message);
                        nipponMapper.updateWorkFlow(Integer.parseInt(data.getString("id")));
                        logger.info("nippon_claim_push result: " + result.toString());
                        needSendMailIds.add(Integer.parseInt(data.getString("id")));
                    } else {
                        nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "failed", message);
                        String con = "单据推送费控系统失败\n移动报销请求详情:\n" + data.toJSONString() + "\n费控响应详情:\n" + result.toString();
                        logger.info("nippon_claim_push_error", con);
                    }
                } catch (Exception e) {
                    if (e.getMessage() != null) {
                        if (e.getMessage().length() > 100) {
                            message = e.getMessage().substring(0, 100);
                        } else {
                            message = e.getMessage();
                        }
                    } else {
                        message = "";
                    }
                    nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "failed", message);
                    logger.info("nippon_claim_push_error", e);
                }
            }
            //needSendMailIds 中的单据调用定制工程发邮件
            String url = rb.getString("nippon.customer_sendmail");
            String requestBody = JSON.toJSONString(needSendMailIds);
            HttpHeaders headers = new HttpHeaders();
            MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
            headers.setContentType(type);
            HttpEntity<String> formEntity = new HttpEntity<>(requestBody, headers);
            sendUtil.postForObject("customer_sendmail", url, formEntity);

        } catch (RemoteException axisFault) {
            logger.error("{}出现RemoteException异常: {}", LogHolder.getLogKey(), axisFault.getMessage(), axisFault);
        } catch (Exception e) {
            logger.error("{}出现未知错误：{}", LogHolder.getLogKey(), e.getMessage(), e);
        } finally {
            if(lovId != null) {
                lovMapper.setValueEnableFlag(lovId, CLAIM_PUSH_LOCK, "N");
            }
            logger.info("{}释放任务锁结束", LogHolder.getLogKey());
            LogCtx.clearLogKey();
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void travelPush() {
        final String INTEGERFACE_NAME = "立邦推送考勤系统travelPush===";
        logger.info("{}开始执行...", INTEGERFACE_NAME);
        try {
            List<Integer> departmentIds = nipponMapper.queryZznppCompanyGroup();
            if (CollectionUtils.isEmpty(departmentIds)) {
                return;
            }
            logger.info("{}获取到待处理部门集合={}", INTEGERFACE_NAME, JSON.toJSONString(departmentIds));

            SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
            String errmsg = "unknown", message = "unknown";
            AbsenceStub stub = new AbsenceStub(rb.getString("nippon.absence_sync"));
            HttpTransportPropertiesImpl.Authenticator auth = new HttpTransportPropertiesImpl.Authenticator();
            auth.setUsername("NP_CLOUD");
            auth.setPassword("12345678");

            stub._getServiceClient().getOptions().setProperty(HTTPConstants.AUTHENTICATE, auth);
            AbsenceStub.ZHR_ABSENCE ZHRABSENCE;
            AbsenceStub.Response response;
            AbsenceStub.ZZDT_ABSENCE_RESP_ITEM[] items;

            ArrayList<ExpClaimHeader> subHeaders = nipponMapper.queryTravel(departmentIds);
            if (subHeaders == null || subHeaders.size() == 0) {
                logger.info("{}本次无待处理单据", INTEGERFACE_NAME);
                return;
            }
            logger.info("{}获取到待处理单据{}条", INTEGERFACE_NAME, subHeaders.size());
            // 接口获取考勤关闭时间检查结果
            String close = sendUtil.getForObject("travelTimeCheck", rb.getString("nippon.travel_time_check"));
            if (close != null && "Y".equals(close)) {
                logger.info("{}推送考勤接口处于关闭窗口期间，待下次推送", INTEGERFACE_NAME);
                for (ExpClaimHeader travel : subHeaders) {
                    logger.info("{}开始处理单据{}", INTEGERFACE_NAME, JSON.toJSONString(travel));
                    nipponMapper.updateExternalStatus(travel.getHeaderId(), "failed", "推送考勤接口处于关闭窗口期间，待下次推送");
                }
                return;
            }
            // 正常推送
            for (ExpClaimHeader travel : subHeaders) {
                try {
                    logger.info("{}开始处理单据{}", INTEGERFACE_NAME, JSON.toJSONString(travel));
                    HashMap<String, Object> result;
                    String env = "";
                    ZHRABSENCE = new AbsenceStub.ZHR_ABSENCE();
                    AbsenceStub.ZZMT_ABSENCE_REQ zzmtAbsenceReq = new AbsenceStub.ZZMT_ABSENCE_REQ();
                    AbsenceStub.ZZDT_ABSENCE_REQ zzdtAbsenceReq = new AbsenceStub.ZZDT_ABSENCE_REQ();
                    AbsenceStub.PRXCTRLTAB prxctrltab = new AbsenceStub.PRXCTRLTAB();
                    AbsenceStub.PRXCTRL prxctrl = new AbsenceStub.PRXCTRL();
                    AbsenceStub.PRXCTRL[] prxctrls = new AbsenceStub.PRXCTRL[1];
                    AbsenceStub.Char30 char30 = new AbsenceStub.Char30();
                    AbsenceStub.Char1 char1 = new AbsenceStub.Char1();
                    AbsenceStub.Char1 char2 = new AbsenceStub.Char1();
                    if ("approved".equals(travel.getStatus())) {
                        char1.setChar1("");
                        char2.setChar1("");
                    } else if ("submitted".equals(travel.getStatus())) {
                        char1.setChar1("");
                        char2.setChar1("");
                    } else if ("closed".equals(travel.getStatus())) {
                        char1.setChar1("");
                        char2.setChar1("X");
                    }
                    if ("1".equals(travel.getColumn5()) && !"closed".equals(travel.getStatus())) {//修改
                        char2.setChar1("U");// todo 考勤接口不支持更新，所以只能先删后写
                    }
                    char30.setChar30("");
                    prxctrl.setFIELD(char30);
                    prxctrl.setVALUE(char1);
                    prxctrls[0] = prxctrl;
                    prxctrltab.setItem(prxctrls);

                    AbsenceStub.Char4 type = new AbsenceStub.Char4();
                    AbsenceStub.Char8 empNum = new AbsenceStub.Char8();
                    AbsenceStub.Char40 notesID = new AbsenceStub.Char40();
                    type.setChar4(travel.getColumn1());
                    empNum.setChar8(travel.getColumn2());
                    notesID.setChar40(travel.getDocumentNum());
                    zzdtAbsenceReq.setABSENCETYPE(type);
                    zzdtAbsenceReq.setEMPLOYEENUMBER(empNum);
                    zzdtAbsenceReq.setNOTESID(notesID);
                    zzdtAbsenceReq.setSIMULATE(char1);
                    zzdtAbsenceReq.setLOEKZ(char2);

                    AbsenceStub.ZZDT_ABSENCE_REQ_OA_HR_ABSENCE hrAbsence = new AbsenceStub.ZZDT_ABSENCE_REQ_OA_HR_ABSENCE();
                    AbsenceStub.ZZDT_ABSENCE_REQ_ITEM_TAB itemTab = new AbsenceStub.ZZDT_ABSENCE_REQ_ITEM_TAB();
                    AbsenceStub.ZZDT_ABSENCE_REQ_ITEM[] reqItems = new AbsenceStub.ZZDT_ABSENCE_REQ_ITEM[1];
                    AbsenceStub.Xsddate startDay = new AbsenceStub.Xsddate();
                    startDay.setXsddate(travel.getStartDatetime());
                    AbsenceStub.Xsddate endDay = new AbsenceStub.Xsddate();
                    endDay.setXsddate(travel.getEndDatetime());
                    AbsenceStub.Xsdtime startTime = new AbsenceStub.Xsdtime();
                    startTime.setXsdtime(new Time("08:30:00"));
                    AbsenceStub.Xsdtime endTime = new AbsenceStub.Xsdtime();
                    endTime.setXsdtime(new Time("17:30:00"));
                    reqItems[0] = new AbsenceStub.ZZDT_ABSENCE_REQ_ITEM();
                    reqItems[0].setVALIDITYBEGIN(startDay);
                    reqItems[0].setVALIDITYEND(endDay);
                    reqItems[0].setSTART(startTime);
                    reqItems[0].setEND(endTime);
                    int absenceHours = ((int) (travel.getEndDatetime().getTime() - travel.getStartDatetime().getTime())) / 1000 / 60 / 60 + 1;
                    AbsenceStub.Decimal72 ab = new AbsenceStub.Decimal72();
                    AbsenceStub.Char20 char20 = new AbsenceStub.Char20();
                    char20.setChar20("");
                    AbsenceStub.Char220 char220 = new AbsenceStub.Char220();
                    char220.setChar220("");
                    ab.setDecimal72(new BigDecimal(absenceHours));
                    reqItems[0].setABSENCEHOURS(ab);
                    reqItems[0].setCONTROLLER(prxctrltab);
                    reqItems[0].setHRDOCNR(char20);
                    reqItems[0].setMESSAGE(char220);
                    itemTab.setItem(reqItems);
                    hrAbsence.setITEM(itemTab);
                    hrAbsence.setCONTROLLER(prxctrltab);

                    zzdtAbsenceReq.setOA_HR_ABSENCE(hrAbsence);
                    zzdtAbsenceReq.setCONTROLLER(prxctrltab);

                    zzmtAbsenceReq.setZMT_ABSENCE_REQ(zzdtAbsenceReq);
                    zzmtAbsenceReq.setCONTROLLER(prxctrltab);

                    ZHRABSENCE.setINPUT(zzmtAbsenceReq);

                    logger.info("{}调用外部webservice接口处理开始时间：{}", INTEGERFACE_NAME, System.currentTimeMillis());
                    int curr = (int) (1 + Math.random() * (100 - 1 + 1));
                    logger.info("{}调用外部webservice接口请求：{}", INTEGERFACE_NAME, JSON.toJSONString(ZHRABSENCE));
//                    result = stub.getResponseString(ZHRABSENCE);
                    result = sendUtil.getResponseString("travelPush", stub, ZHRABSENCE);
                    logger.info("{}调用外部webservice接口返回：{}", INTEGERFACE_NAME, JSON.toJSONString(result));
                    response = (AbsenceStub.Response) result.get("res");
                    env = (String) result.get("req");
                    errmsg = response.getOUTPUT().getZMT_ABSENCE_RESP().getERR_MSG().getChar220();
                    items = response.getOUTPUT().getZMT_ABSENCE_RESP().getOA_HR_ABSENCE().getITEM().getItem();
                    if (items != null) {
                        for (AbsenceStub.ZZDT_ABSENCE_RESP_ITEM item : items) {
                            if (item != null && item.getMESSAGE() != null && item.getMESSAGE().getChar220() != null) {
                                message = item.getMESSAGE().getChar220();
                                if (message != null && message.length() > 200) {
                                    message = message.substring(0, 200);
                                }
                            }
                        }
                    }
                    logger.info("{}解析webservice接口返回结果：req={}, res={}", INTEGERFACE_NAME, env, errmsg + ":" + message);
                    if (errmsg != null && (errmsg.contains("写入成功"))) {
                        nipponMapper.updateFndWorkflowPath(travel.getPathId(), "", 171100, 0, "zh_CN", "approved", "", "");
                        nipponMapper.updateExternalStatus(travel.getHeaderId(), "success", errmsg + ":" + message);
                    } else if (errmsg != null && errmsg.contains("删除成功")) {
                        nipponMapper.rejectHeader(travel.getHeaderId(), "该单据已被主动撤回");
                        nipponMapper.updateExternalStatus(travel.getHeaderId(), "failed", errmsg + ":" + message);
                    } else if (errmsg != null && errmsg.contains("非工作")) {
//                        nipponMapper.rejectHeader(travel.getHeaderId(), errmsg + ":" + message);
                        nipponMapper.updateExternalStatus(travel.getHeaderId(), "success", errmsg + ":" + message);
                    } else if (errmsg != null && errmsg.contains("模拟失败")) {
//                        nipponMapper.rejectHeader(travel.getHeaderId(), errmsg + ":" + message);
                        nipponMapper.updateExternalStatus(travel.getHeaderId(), "success", errmsg + ":" + message);
                    } else if (errmsg == null || !errmsg.contains("模拟成功")) {
                        if (message != null && message.contains("非工作")) {
//                            nipponMapper.rejectHeader(travel.getHeaderId(), errmsg + ":" + message);
                            nipponMapper.updateExternalStatus(travel.getHeaderId(), "success", errmsg + ":" + message);
                        } else if (errmsg != null && errmsg.contains("已存在")) {
                            nipponMapper.rejectHeader(travel.getHeaderId(), "考勤系统:该时间段已存在请假记录");
                            nipponMapper.updateExternalStatus(travel.getHeaderId(), "rejected", errmsg + ":" + message);
                        } else {
                            nipponMapper.rejectHeader(travel.getHeaderId(), errmsg + ":" + message);
                            nipponMapper.updateExternalStatus(travel.getHeaderId(), "failed", errmsg + ":" + message);
                        }
                    } else {
                        nipponMapper.rejectHeader(travel.getHeaderId(), errmsg + ":" + message);
                        nipponMapper.updateExternalStatus(travel.getHeaderId(), "failed", errmsg + ":" + message);
                    }
                    logger.info("{}调用外部webservice接口处理结束时间：{}", INTEGERFACE_NAME, System.currentTimeMillis());
                } catch (Exception e) {
                    logger.error("{}推送考勤出现异常!, 请求: {}, 异常：{}", INTEGERFACE_NAME, travel, e.getMessage(), e);
                    nipponMapper.updateExternalStatus(travel.getHeaderId(), "failed", "Exception");
                }
            }
        } catch (Exception axisFault) {
            logger.error("{}出现未知异常：{}", INTEGERFACE_NAME, axisFault.getMessage(), axisFault);
            axisFault.printStackTrace();
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public String getSAPNo(int companyID) {
        Map<String, String> result = new HashMap<>();
        result.put("dept", String.valueOf(companyID));
        nipponMapper.getSAPNo(result);
        return result.get("result");
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateBudget() {
        try {
            nipponMapper.updateBudgetYear();
            Calendar calendar = Calendar.getInstance();
            BudgetStub stub = new BudgetStub(rb.getString("nippon.budget"));
            BudgetStub.GETLISTReturn aReturn;
            BudgetStub.GETBUDGETLISTReturn bReturn;
            BudgetStub.STRYEARMONTH yearMonth;
            BudgetStub.STRYEAR year;
            String[] aYear, aMonth, aCostCenter, aAmount, aRegion, bYear, bMonth, bCostCenter, bAmount, bRegion;
            stub._getServiceClient().getOptions().setProperty(HTTPConstants.CHUNKED, false);
            for (int monthNum = 0; monthNum < 12; monthNum++) {
                yearMonth = new BudgetStub.STRYEARMONTH();
                String dateTime = preMonth(monthNum);
                yearMonth.setSTRYEARMONTH(dateTime);

                try {
                    JSONArray array = new JSONArray();
                    JSONObject object;
                    aReturn = stub.gETLIST(yearMonth);
                    aYear = aReturn.getGETLISTReturn().getYEAR_LIST();
                    aMonth = aReturn.getGETLISTReturn().getMONTH_LIST();
                    aCostCenter = aReturn.getGETLISTReturn().getCOSTCENTERCODE_LIST();
                    aAmount = aReturn.getGETLISTReturn().getAMOUNT_LIST();
                    aRegion = aReturn.getGETLISTReturn().getREGION_LIST();

                    HashMap<String, BigDecimal> map = new HashMap<>();
                    if (aYear != null && (aYear.length > 1 || !"".equals(aYear[0]))) {
                        for (int j = 0; j < aYear.length; j++) {
                            String aKey = "\"month\": \"" + aMonth[j] + "\"," +
                                    "\"region\": \"" + aRegion[j] + "\"," +
                                    "\"cost\": \"" + aCostCenter[j] + "\"," +
                                    "\"type\": \"2372\"," +
                                    "\"year\": \"" + aYear[j] + "\"," +
                                    "\"amount\":";

                            BigDecimal value = new BigDecimal(aAmount[j]);
                            BigDecimal amount = map.get(aKey);
                            if (amount != null) {
                                amount = amount.add(value);
                            } else {
                                amount = value;
                            }
                            map.put(aKey, amount);
                        }

                        for (Map.Entry<String, BigDecimal> entry : map.entrySet()) {
                            object = JSONObject.parseObject("{" + entry.getKey() + "\"" + entry.getValue().toString() + "\"}");
                            array.add(object);
                        }
                        nipponMapper.updateBudget(array.toJSONString());
                    }
                } catch (Exception e) {
                    continue;
                }
            }

        } catch (RemoteException axisFault) {
            logger.info("sso_sync_error同步预算数据出现异常:\n错误信息:\n", axisFault);
            axisFault.printStackTrace();
        }
    }

    @Override
    public JSONObject getToken(JSONObject json) throws Exception {
        String LOG_KEY = "立邦sso==";
        logger.info("{}开始执行，入参={}", LOG_KEY, json);
        String wechatAuthUrl = rb.getString("nippon.wechat_auth");
        String pcAuthUrl = rb.getString("nippon.pc_auth");
        String pcProfileUrl = rb.getString("nippon.pc_auth_profile");
        JSONObject result;
        String id = null;
        String openId = null;
        String number = null;
        if (json != null) {
            if ("1".equals(json.getString("mob"))) {
                //手机端
                if (json.getString("code") != null && (!"".equals(json.getString("code")))) {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.parseMediaType("application/json; charset=UTF-8"));
                    headers.add("Accept", MediaType.APPLICATION_JSON.toString());
                    JSONObject request = new JSONObject();
                    request.put("Ticket", json.getString("code"));
                    HttpEntity<String> jsonEntity = new HttpEntity<>(request.toJSONString(), headers);
                    logger.info("{}获取立邦移动端auth请求url={}, params={}", LOG_KEY, wechatAuthUrl, jsonEntity);
                    String authRes = sendUtil.postForObject("getToken", wechatAuthUrl, jsonEntity);
                    logger.info("{}获取立邦移动端auth响应response={}", LOG_KEY, authRes);
                    JSONObject response = JSONObject.parseObject(authRes);
                    id = response.getJSONObject("Data").getString("Account");
                    openId = response.getJSONObject("Data").getString("OpenID");
                    number = response.getJSONObject("Data").getString("Number");
                    if (id == null && number == null && openId != null) {
                        logger.error("{}未获取到微信帐号信息", LOG_KEY);
                        throw new ValidationException("请将微信号与立邦系统帐号绑定!");
                    }
                } else {
                    logger.error("{}未获取到微信token", LOG_KEY);
                    throw new ValidationException("获取立邦微信Ticket失败");
                }
            } else {
                //电脑端
                if (json.getString("code") != null && (!"".equals(json.getString("code")))) {
                    String redirectUri = rb.getString("nippon.sso_page");
//                    redirectUri = URLEncoder.encode(redirectUri, StandardCharsets.UTF_8.toString());
                    String params = "client_id=" + rb.getString("nippon.sso.client_id") +
                                    "&client_secret=" + rb.getString("nippon.sso.client_secret") +
                                    "&code=" + json.getString("code") +
                                    "&grant_type=" + rb.getString("nippon.sso.grant_type") +
                                    "&redirect_uri=" + redirectUri;
                    logger.info("{}获取立邦电脑端token请求url={}, params={}", LOG_KEY, pcAuthUrl, params);
                    String authRes = sendUtil.postForObj("getToken", pcAuthUrl + "?" + params,null);
                    logger.info("{}获取立邦电脑端token响应response={}", LOG_KEY, authRes);
                    JSONObject auth = JSONObject.parseObject(authRes);
                    if(auth.getString("access_token")!=null){
                        authRes="access_token="+auth.getString("access_token");
                    }
                    logger.info("{}获取立邦电脑端auth请求url={}, params={}", LOG_KEY, pcProfileUrl, authRes);
                    String profileRes = sendUtil.getForObj("getAuth", pcProfileUrl + "?" + authRes);
                    logger.info("{}获取立邦电脑端auth响应response={}", LOG_KEY, profileRes);
                    JSONObject response = JSONObject.parseObject(profileRes);
                    if (response != null) {
                        id = response.getString("id");
                    }
                } else {
                    logger.error("{}未获取到PC端单一登陆帐号信息", LOG_KEY);
                    throw new ValidationException("获取立邦SSO认证信息失败");
                }
            }
            if (number != null) {
                String email = nipponMapper.queryUserName(number);
                if (email != null && email.length() > 0 && email.contains("@")) {
                    id = email.substring(0, email.indexOf('@'));
                }
            }
            if (id != null && (!"".equals(id))) {
                String encrypted = Encrypt(id + "," + System.currentTimeMillis(), rb.getString("nippon.encrypt_secret"));
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("corp", rb.getString("nippon.encrypt_corp_id"));
                jsonObject.put("code", encrypted);
                String oauthUrl = rb.getString("nippon.cp_sso");
                logger.info("{}用户{}获取云简token请求url={}, request={}", LOG_KEY, id, oauthUrl, jsonObject.toJSONString());
                String results = sendUtil.httpPostString("getToken", oauthUrl, null, jsonObject.toJSONString());
                logger.info("{}用户{}获取云简token响应response={}", LOG_KEY, id, results);
                result = JSONObject.parseObject(results);
                result.put("user", id);
                if (openId != null) {
                    result.put("open_id", openId);
                }
                if (result.get("token") == null || "".equals(result.getString("token"))) {
                    logger.error("{}用户{}帐号token不一致", LOG_KEY, id);
                    throw new ValidationException("登录失败");
                }
            } else {
                logger.error("{}未获取到帐号信息", LOG_KEY);
                throw new ValidationException("用户不存在或未与该微信账号绑定");
            }
        } else {
            logger.error("{}单一登录请求内容为空,无法登陆,请检查前端对应API", LOG_KEY);
            throw new ValidationException("登录失败");
        }
        logger.info("{}结束执行，返参={}", LOG_KEY, result);
        return result;
    }

    static String Encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            System.out.print("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));

        return new org.apache.commons.codec.binary.Base64().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void invoicePush() {
        try {
            String url = rb.getString("nippon.invoice_update");
            String invoiceJsonArray = nipponMapper.getUpdatedInvoice();
            if (invoiceJsonArray != null) {
                String requestBody = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\"><soapenv:Header/><soapenv:Body><tem:SendInvoiceDate><tem:jsonList>" + invoiceJsonArray + "</tem:jsonList></tem:SendInvoiceDate></soapenv:Body></soapenv:Envelope>";
                RestTemplate restTemplate = new RestTemplate();
                HttpHeaders headers = new HttpHeaders();
                MediaType type = MediaType.parseMediaType("text/xml;charset=UTF-8");
                headers.setContentType(type);
                HttpEntity<String> formEntity = new HttpEntity<>(requestBody, headers);
//                String result = restTemplate.postForObject(url, formEntity, String.class);
                String result = sendUtil.postForObject("invoicePush", url, formEntity);

                System.out.println(result);
                logger.info("update_invoice" + requestBody + result);
            }
        } catch (Exception e) {
            logger.info("update_invoice_error", "主动推送发票数据出现错误" + e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void ntfPush() {
        logger.info("global env: " + env);
        try {
            nipponMapper.freshNtfStatus(15220);
        } catch (Exception e) {
            e.printStackTrace();
        }
        //推送待办通知之前先差旅申请单刷新状态
//        try {
//            travelPush();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        ArrayList<HsfNtf> ntfList = new ArrayList<>();
        JSONObject response, content = null, result = null;
        try {
            NipponWechatStub stub = new NipponWechatStub(rb.getString("nippon.wechat_ntf"));
            NipponWechatStub.DoProcess doProcess;
            NipponWechatStub.UserValidationSoapHeaderE headerE;
            NipponWechatStub.UserValidationSoapHeader header;
            NipponWechatStub.DoProcessResponse processResponse;
            StringBuffer xml;
            // 待办推送目标
            ArrayList<HsfNtf> ntfListApprove = nipponMapper.queryNtfCompany();
            ntfList.addAll(ntfListApprove == null ? new ArrayList<>() : ntfListApprove);
            logger.info("立邦待办推送目标: {} 条", ntfListApprove.size());
            // 邮件推送目标
            ArrayList<HsfNtf> ntfListEmail = nipponMapper.queryNtfCompanyEmail();
            ntfList.addAll(ntfListEmail == null ? new ArrayList<>() : ntfListEmail);
            logger.info("立邦邮件推送目标: {} 条", ntfListEmail.size());
            for (HsfNtf ntf : ntfList) {
                try {
                    if ("nippon_approve".equals(ntf.getType())) {
                        doProcess = new NipponWechatStub.DoProcess();
                        headerE = new NipponWechatStub.UserValidationSoapHeaderE();
                        header = new NipponWechatStub.UserValidationSoapHeader();
                        xml = new StringBuffer();

                        content = JSON.parseObject(ntf.getContent());
                        String approverUserName = "";
                        String claimDocNum = content.getString("doc");
                        String submitDate = content.getString("date");
                        String approverName = content.getString("name");
                        String approveStartTime = content.getString("time");
                        String approveType = content.getString("type");
                        String submitUserEmpNo = content.getString("a_emp");
                        String approverEmail = content.getString("email");
                        if (approverEmail != null && approverEmail.contains("@")) {
                            approverUserName = approverEmail.substring(0, approverEmail.indexOf("@"));
                        }
                        String approveTitle = content.getString("title");
                        String creator = content.getString("creator");
                        String submitUserName = content.getString("a_name");
                        String approverEmpNo = content.getString("emp_num");
                        String approveContent = content.getString("content");
                        String pathId = content.getString("path_id");
                        String claimId = content.getString("claim_id");
                        String mailText = content.getString("mail_text");
                        String wxUrl = rb.getString("nippon.sso_page") + "?mob=1&amp;path=" + pathId + "&amp;header=" + claimId + "&amp;user=" + approverUserName;
                        String pcUrl = rb.getString("nippon.sso_page") + "?mob=0&path=" + pathId;
//                      注释 企业微信代办重构
//                        if (ntf.getStatus() == 0 || ntf.getStatus() == 3) {
//                            if (ntf.getStatus() == 0) {
//                                String subject = "请审批来自 " + creator + " 的待审批单据 " + claimDocNum;
//                                mailText = mailText + "<p><a title=\"点击此处进入审批页面\" href=\"" + pcUrl + "\">点击此处进入审批页面</a></p>"
//                                        + "<p><a title=\"更多审批待办\" href=\"" + moreApprove + "\">更多审批待办</a></p>";
//                                if ("prod".equals(env)) {
//                                    MailUtil.sendMailFromService(approverEmail, subject, mailText);
//                                    logger.info("prod env, send mail ");
//                                }
//                            }
//                            try {
//                                header.setAppId("YUNJIAN");
//                                header.setSecretKey("");
//                                headerE.setUserValidationSoapHeader(header);
//                                xml.append("<DATA><PROCINSTNO>")
//                                        .append(claimDocNum)
//                                        .append(pathId)
//                                        .append("</PROCINSTNO><TOPIC>")
//                                        .append(approveContent)
//                                        .append("</TOPIC><APPLYEMPNO>")
//                                        .append(submitUserEmpNo)
//                                        .append("</APPLYEMPNO><APPLYEMPNAME>")
//                                        .append(submitUserName)
//                                        .append("</APPLYEMPNAME><STARTDATE>")
//                                        .append(submitDate)
//                                        .append("</STARTDATE><STATUS>审批中</STATUS><FORMURL>")
//                                        .append(wxUrl)
//                                        .append("</FORMURL><JOBLIST><ITEM><JOBID>")
//                                        .append(pathId)
//                                        .append("</JOBID><USERID>")
//                                        .append(approverEmpNo)
//                                        .append("</USERID><ARRIVALDATE>")
//                                        .append(approveStartTime)
//                                        .append("</ARRIVALDATE><STATUS>0</STATUS></ITEM></JOBLIST></DATA>");
//                                System.out.println(xml.toString());
//                                doProcess.setXml(xml.toString());
//                                doProcess.setAPPID("YUNJIAN");
//                                doProcess.setFormHTML("");
//                                doProcess.setApproveHTML("");
//
//
////                                processResponse = stub.doProcess(doProcess, headerE);
//                                if ("prod".equals(env)) {
//                                    processResponse = sendUtil.doProcess("ntfPush", stub, doProcess, headerE);
//                                    response = JSONObject.parseObject(processResponse.getDoProcessResult());
//                                    logger.info("nippon_ntf_log" + "请求:\n" + xml.toString() + "\n响应:\n" + response.toString());
//                                    if (response.getIntValue("StautsCode") == 200 || "200".equals(response.getString("StautsCode"))) {
//                                        nipponMapper.updateNtfStatus(ntf.getId(), 2, content.toJSONString());
//                                    } else {
//                                        nipponMapper.updateNtfStatus(ntf.getId(), 3, content.toJSONString());
//                                    }
//                                }
//
//                            } catch (Exception e) {
//                                e.printStackTrace();
//                                nipponMapper.updateNtfStatus(ntf.getId(), 3, content.toJSONString());
//                            }
//                        } else if (ntf.getStatus() >= 10) {
//                            String resultString;
//                            switch (ntf.getStatus()) {
//                                case 14:
//                                    resultString = "审批拒绝";
//                                    break;
//                                case 15:
//                                    resultString = "单据撤回";
//                                    break;
//                                default:
//                                    resultString = "审批通过";
//                            }
//                            try {
//                                header.setAppId("YUNJIAN");
//                                header.setSecretKey("");
//                                headerE.setUserValidationSoapHeader(header);
//                                xml.append("<DATA><PROCINSTNO>")
//                                        .append(claimDocNum)
//                                        .append(pathId)
//                                        .append("</PROCINSTNO><TOPIC>")
//                                        .append(approveTitle)
//                                        .append("</TOPIC><APPLYEMPNO>")
//                                        .append(submitUserEmpNo)
//                                        .append("</APPLYEMPNO><APPLYEMPNAME>")
//                                        .append(submitUserName)
//                                        .append("</APPLYEMPNAME><STARTDATE>")
//                                        .append(submitDate)
//                                        .append("</STARTDATE><STATUS>")
//                                        .append(resultString)
//                                        .append("</STATUS><FORMURL>")
//                                        .append(wxUrl)
//                                        .append("</FORMURL><JOBLIST><ITEM><JOBID>")
//                                        .append(pathId)
//                                        .append("</JOBID><USERID>")
//                                        .append(approverEmpNo)
//                                        .append("</USERID><ARRIVALDATE>")
//                                        .append(approveStartTime)
//                                        .append("</ARRIVALDATE><STATUS>4</STATUS></ITEM></JOBLIST></DATA>");
//                                System.out.println(xml.toString());
//                                doProcess.setXml(xml.toString());
//                                doProcess.setAPPID("YUNJIAN");
//                                doProcess.setFormHTML("");
//                                doProcess.setApproveHTML("");
//
//
////                                processResponse = stub.doProcess(doProcess, headerE);
//                                processResponse = sendUtil.doProcess("ntfPush", stub, doProcess, headerE);
//
//                                response = JSONObject.parseObject(processResponse.getDoProcessResult());
//                                logger.info("nippon_ntf_log" + "请求:\n" + xml.toString() + "\n响应:\n" + response.toString());
//                                if (response.getIntValue("StautsCode") == 200 || "200".equals(response.getString("StautsCode"))) {
////                                    nipponMapper.updateNtfStatus(ntf.getId(), 12, content.toJSONString());
//                                    nipponMapper.deleteNtf(ntf.getId());
//                                } else {
//                                    nipponMapper.updateNtfStatus(ntf.getId(), ntf.getStatus(), content.toJSONString());
//                                }
//                            } catch (Exception e) {
//                                nipponMapper.updateNtfStatus(ntf.getId(), ntf.getStatus(), ntf.getContent());
//                                logger.error("nippon_ntf_error消息推送出现异常异常:\n", e);
//                            }
//                        }
                        boolean flag=false;
                        try {
                            if (ntf.getStatus() == 0) {
                                logger.info("ntfPush 审批流代办邮件 request is {}",ntf);
                                String subject = "请审批来自 " + creator + " 的待审批单据 " + claimDocNum;
                                mailText = mailText + "<p><a title=\"点击此处进入审批页面\" href=\"" + pcUrl + "\">点击此处进入审批页面</a></p>"
                                        + "<p><a title=\"更多审批待办\" href=\"" + moreApprove + "\">更多审批待办</a></p>";
                                if ("prod".equals(env)) {
                                    flag=MailUtil.sendMailFromService(approverEmail, subject, mailText);
                                    logger.info("prod env, send mail flag is {}",flag);
                                }
                                logger.info("ntfPush 审批流代办邮件 end  {}",ntf.getId());
                            }
                        }catch (Exception e){
                            logger.info("nippon_approve send mail is error {}",e);
                            continue;
                        }finally {
                            if(flag){
                                nipponMapper.updateNtfStatus(ntf.getId(), 2, content.toJSONString());
                            }else{
                                nipponMapper.updateNtfStatus(ntf.getId(), 3, content.toJSONString());
                            }
                        }

                    } else if ("nippon_mail".equals(ntf.getType())) {
                        logger.info("ntfPush 催办邮件 request is {}",ntf);
                        JSONObject mailInfo = JSONObject.parseObject(ntf.getContent());
                        String reciver = null, title = null, mail = null;
                        if (mailInfo != null && ntf.getStatus() == 0) {
                            reciver = mailInfo.getString("mail_to");
                            title = mailInfo.getString("subject");
                            mail = mailInfo.getString("message");
                            if (reciver != null && title != null && mail != null) {
                                if ("prod".equals(env)) {
                                    MailUtil.sendMailFromService(reciver, title, mail);
                                    logger.info("prod env, send mail ");
                                }
                                //todo 测试环境邮件推送暂时关闭
                            }
                        }
                        nipponMapper.updateNtfStatus(ntf.getId(), 2, ntf.getContent());
                        logger.info("ntfPush 催办邮件 end  {}",ntf.getId());
                    }
                } catch (Exception e) {
                    logger.error("企业微信推送出现异常异常:", e);
                }
            }
        } catch (AxisFault axisFault) {
            logger.error("nippon_ntf_error消息推送出现异常异常:\n", axisFault);
        }

    }

    @Override
    public void putDoaTargetEnableFlagN() {
        String KEYNAME = "立邦设置DoaTarget职位标志为N===";
        logger.info("{}处理开始", KEYNAME);
        String url = rb.getString("nippon.forwarding_server") + "position-flag-n";
        String requestBody, result;
        HttpEntity<String> request;
        HttpHeaders headers;
        try {
            headers = new HttpHeaders();
            requestBody = "";
            //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
            headers.setContentType(MediaType.APPLICATION_JSON);
            //RestTemplate带参传的时候要用HttpEntity<?>对象传递
            request = new HttpEntity<>(requestBody, headers);
            logger.info("{}立邦设置DoaTarget职位标志为N url={}, request={}", KEYNAME, url, JSON.toJSONString(request));
            result = sendUtil.postForObject("updateData", url, request);
            logger.info("{}立邦设置DoaTarget职位标志为N result={}", KEYNAME, JSON.toJSONString(result));
        } catch (Exception e) {
            logger.error("{}立邦设置DoaTarget职位标志为N出现异常={}", KEYNAME, e);
        }
        logger.info("{}处理结束", KEYNAME);
    }

    @Override
    public void updateEmployeeWhoLeft() {
        String KEYNAME = "立邦employeeWhoLeft数据信息同步===";
        logger.info("{}处理开始", KEYNAME);
        String url = rb.getString("nippon.forwarding_server") + "employeeWhoLeft";
        String requestBody, result;
        RestTemplate restTemplate = new RestTemplate();
        HttpEntity<String> request;
        JSONArray resultArray;
        HttpHeaders headers;
        int page = 1;
        do {
            try {
                headers = new HttpHeaders();
                requestBody = "{\"page\":" + page + "}";
                //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
                headers.setContentType(MediaType.APPLICATION_JSON);
                //RestTemplate带参传的时候要用HttpEntity<?>对象传递
                request = new HttpEntity<>(requestBody, headers);
                logger.info("{}获取第{}页数据请求url={}, request={}", KEYNAME, page, url, JSON.toJSONString(request));
                result = sendUtil.postForObject("updateData", url, request);
                logger.info("{}获取第{}页数据响应result={}", KEYNAME, page, JSON.toJSONString(result));
//                result = restTemplate.postForObject(url, request, String.class);
                if (!result.contains("No data")) {
                    resultArray = JSON.parseObject(result).getJSONArray("data");
                    for (Object o : resultArray) {
                        EmployeeWhoLeft employeeWhoLeft = ((JSONObject) o).toJavaObject(EmployeeWhoLeft.class);
                        nipponMapper.updateEmployeeWhoLeft(employeeWhoLeft);
                        nipponMapper.updateEmployeeWhoLeftInactive(employeeWhoLeft);
                    }
                    logger.info("{}同步第{}页数据结束", KEYNAME, page);
                }
            } catch (Exception e) {
                result = "";
                logger.error("{}同步第{}页数据出现异常={}", KEYNAME, page, e.getStackTrace());
                logger.error("nippon_sync_error 数据同步出现错误!\n错误同步类型:employeeWhoLeft\n错误页:" + page + "\n错误信息:\n", e.getMessage());
            }
            page++;
        } while (!result.contains("\"success\":0") && page < 511);

        logger.info("{}处理结束", KEYNAME);
    }

    @Override
    public String toggleTravelPush(String status) {
        if (status != null && (status.equals("N") || status.equals("Y"))) {
            nipponMapper.toggleTravelPush(status);
        }
        status = nipponMapper.queryTravelPush();
        if (!"Y".equals(status)) {
            status = "N";
        }
        status = "{\"status\":\"" + status + "\"}";
        return status;
    }

    @Override
    public String toggleTravelPush(String status, String businessGroup) {
        if (status != null && (status.equals("N") || status.equals("Y"))) {
            nipponMapper.toggleTravelPushByGroup(status, businessGroup);
        }
        status = nipponMapper.queryTravelPushByGroup(businessGroup);
        if (!"Y".equals(status)) {
            status = "N";
        }
        status = "{\"status\":\"" + status + "\"}";
        return status;
    }

    /**
     * @return java.lang.String
     * <AUTHOR>
     * @Description 获取未来monthNum的日期
     * @Date 2020/11/25 15:09
     * @Param [monthNum]
     **/
    public String preMonth(Integer monthNum) {
        //转换格式
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) + monthNum);
        Date time = calendar.getTime();
        return simpleDateFormat.format(time);
    }

    @Override
    public String pushClaimWithJson(String json) {
        ArrayList<String> respMsg = new ArrayList<>();
        try {
            // 调用立邦接口的WebService服务
            ReimOrderStub stub = new ReimOrderStub(rb.getString("nippon.claim_push"));
            ReimOrderStub.SendTravelOrder reqData = new ReimOrderStub.SendTravelOrder();
            ReimOrderStub.SendTravelOrderResponse response;
            String message;
            // 生成推送接口的json
            List<JSONObject> datas = JSONObject.parseArray(json, JSONObject.class);
            logger.info("nippon_claim_push request: " + JSONArray.toJSONString(datas));

            for (int i = 0, datasSize = datas.size(); i < datasSize; i++) {
                JSONObject data = datas.get(i);
                reqData.setJsonList(data.toString());
                try {
//                    response = stub.sendTravelOrder(reqData);
                    response = sendUtil.sendTravelOrder("reimPush", stub, reqData);

                    JSONObject result = JSONObject.parseObject(response.getSendTravelOrderResult());
                    if (result.getString("Message") != null) {
                        if (result.getString("Message").length() > 100) {
                            message = result.getString("Message").substring(0, 100);
                        } else {
                            message = result.getString("Message");
                        }
                    } else {
                        message = "";
                    }
                    if (result.getString("Message") != null
                            && (
                            ("成功".equals(result.getString("Message")))
                                    || (result.getString("Message").contains("报销单已存在")))) {
                        nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "success", message);
                        nipponMapper.updateWorkFlow(Integer.parseInt(data.getString("id")));
                        logger.info("nippon_claim_push result: " + result.toString());
                        respMsg.add("nippon_claim_push result: " + result.toString());
                    } else {
                        nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "failed", message);
                        String con = "单据推送费控系统失败\n移动报销请求详情:\n" + data.toJSONString() + "\n费控响应详情:\n" + result.toString();
                        logger.info("nippon_claim_push_error: " + con);
                        respMsg.add("第" + i + "个单据推送错误。" + con);
                    }
                } catch (Exception e) {
                    if (e.getMessage() != null) {
                        if (e.getMessage().length() > 100) {
                            message = e.getMessage().substring(0, 100);
                        } else {
                            message = e.getMessage();
                        }
                    } else {
                        message = "";
                    }
                    nipponMapper.updateExternalStatus(Integer.parseInt(data.getString("id")), "failed", message);
                    String con = "单据推送费控系统失败\n移动报销请求详情:\n" + data.toJSONString() + "\n错误详情:\n" + e.getMessage();
                    logger.info("nippon_claim_push_error: " + con);
                    respMsg.add("第" + i + "个单据推送错误。" + con);
                }

            }
        } catch (RemoteException axisFault) {
            logger.error("nippon_claim_push_error: ", axisFault);
        }
        return JSONObject.toJSONString(respMsg);
    }
}
