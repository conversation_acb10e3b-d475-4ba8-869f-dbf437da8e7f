package com.cloudpense.expman.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.CustomUserDetails;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.shangfa.HandleEntity;
import com.cloudpense.expman.entity.shangfa.OnBusinessUser;
import com.cloudpense.expman.entity.shangfa.VoucherHeader;
import com.cloudpense.expman.entity.shangfa.VoucherLine;
import com.cloudpense.expman.exception.JsonError;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.ShangfaMapper;
import com.cloudpense.expman.service.ShangFaService;


import com.cloudpense.expman.util.CalculationMethod;
import com.cloudpense.expman.util.HttpHelper;
import com.cloudpense.expman.util.HttpUtil;
import jdk.nashorn.internal.ir.IfNode;
import org.json.XML;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;

import java.util.*;


import static java.util.Arrays.asList;

/**
 * <AUTHOR>
 */
@Service
public class ShangFaServiceImpl implements ShangFaService {

    private static  ResourceBundle RB = ResourceBundle.getBundle("shangfaurl");
    private static  final String  CLOCKSYNCHRONIZATIONURL = RB.getString("clock-synchronization-url");
    private static  final String  VOUCHERURL = RB.getString("voucher-url");

    private ShangfaMapper shangfaMapper;

    private List<String> specialTypes = asList("SF012", "SF042", "SF014","SF043");

    private SimpleDateFormat dayFormate = new SimpleDateFormat("yyyy-MM-dd");

    private SimpleDateFormat yearFormate = new SimpleDateFormat("yyyy");

    private static Logger logger = LoggerFactory.getLogger(ShangFaServiceImpl.class);

    private List<String> voucherTypes = asList("SF002", "SF032", "SF004","SF006","SF035","SF051","SF010","SF008",
                                                "SF011","SF015","SF017","SF098","SF099","SF028","SF027","SF030");



    @Autowired
    public ShangFaServiceImpl(ShangfaMapper shangfaMapper) {
        this.shangfaMapper = shangfaMapper;
    }

    @Override
    public void sapUserStatus() {

     List<OnBusinessUser> headerUsers = shangfaMapper.shangfaHeaderUser();

     if (null != headerUsers && headerUsers.size()>0){
         for (OnBusinessUser headerUser : headerUsers) {
             String externalStatus = headerUser.getExternalStatus();
             String externalMessage = headerUser.getExternalMessage();
             Map<String,Object> statusMap = new HashMap<>();
             Map<String,Object> messageMap = new HashMap<>();
             if (null !=externalStatus){
                 statusMap = JSON.parseObject(externalStatus, Map.class);
                 if ("S".equals(statusMap.get("kq"))){
                     continue;
                 }
             }

             if (null != externalMessage){
                 messageMap = JSON.parseObject(externalMessage, Map.class);
             }

             String startTime = dayFormate.format(headerUser.getStartDatetime());
             String endTime = dayFormate.format(headerUser.getEndDatetime());


             if(specialTypes.contains(headerUser.getTypeCode())){
                 List<OnBusinessUser> lineUsers = shangfaMapper.shangfaLineUser(headerUser.getHeaderId());
                 if(null != lineUsers && lineUsers.size() >0){
                     for (OnBusinessUser lineUser : lineUsers) {
                         if ("S".equals(statusMap.get("kq"+lineUser.getLineId()))){
                            continue;
                         }

                         if(null != lineUser.getEmployeeNumber()){
                             String body="<PERNR>"+lineUser.getEmployeeNumber()+"</PERNR>\n"+
                                     "<AWART>0100</AWART>\n"+
                                     "<BEGDA>"+startTime+"</BEGDA>\n"+
                                     "<ENDDA>"+endTime+"</ENDDA>\n";
                             String postData = getKaoQinHead() + body + getKaoQinEnd();
                             logger.info("userStatus===="+headerUser.getDocumentNum()+"入参：\n"+postData);
                             sendInfoToSap(postData,headerUser.getDocumentNum(),headerUser.getHeaderId(),statusMap,messageMap,lineUser.getLineId());
                         }else {
                             statusMap.put("kq"+lineUser.getLineId(),"E");
                             messageMap.put("kq"+lineUser.getLineId(),"行上没有目标员工");
                             shangfaMapper.shangfaUpdateExternal(JSON.toJSONString(statusMap),JSON.toJSONString(messageMap),headerUser.getHeaderId());
                             logger.error("userStatus===="+headerUser.getDocumentNum()+"行上没有目标员工");
                         }
                     }
                 }
             }else{
                 if(null != headerUser.getEmployeeNumber()){
                     String body="<PERNR>"+headerUser.getEmployeeNumber()+"</PERNR>\n"+
                             "<AWART>0100</AWART>\n"+
                             "<BEGDA>"+startTime+"</BEGDA>\n"+
                             "<ENDDA>"+endTime+"</ENDDA>\n";

                     String postData = getKaoQinHead() + body + getKaoQinEnd();
                     sendInfoToSap(postData,headerUser.getDocumentNum(),headerUser.getHeaderId(),statusMap,messageMap,null);
                     logger.info("userStatus===="+headerUser.getDocumentNum()+"入参：\n"+postData);

                 }else {
                     statusMap.put("kq","E");
                     messageMap.put("kq","没有提交人");
                     shangfaMapper.shangfaUpdateExternal(JSON.toJSONString(statusMap),JSON.toJSONString(messageMap),headerUser.getHeaderId());
                     logger.error("userStatus===="+headerUser.getDocumentNum()+"没有提交人");
                 }
             }
         }
     }

    }

    private void sendInfoToSap(String postData,String documentNum,Integer headerId,Map<String,Object> statusMap,Map<String,Object> messageMap,Integer lineId) {
        Map<String, String> identity = new HashMap<>();
        identity.put("Content-Type", "text/xml");
        try {
            String responseStr = HttpUtil.HttpPostString(CLOCKSYNCHRONIZATIONURL,
                    identity, postData, null, null);
            org.json.JSONObject xmlJSONObj = XML.toJSONObject(responseStr);
            JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
            JSONObject jsonObject = responseJsonObject.getJSONObject("SOAP:Envelope").getJSONObject("SOAP:Body").getJSONObject("ns0:MT_TRAVEL_INFO_RESP").getJSONObject("ET_TRAVEL_RETURN");
            String type = (String)jsonObject.get("TYPE");
            String message = (String)jsonObject.get("MESSAGE");

            if (null != lineId){
                statusMap.put("kq"+lineId,type);
                messageMap.put("kq"+lineId,message);
            }else {
                statusMap.put("kq",type);
                messageMap.put("kq",message);
            }
            shangfaMapper.shangfaUpdateExternal(JSON.toJSONString(statusMap),JSON.toJSONString(messageMap),headerId);
            if ("S".equals(type)){
                if (null != lineId){
                    logger.info("userStatus===="+documentNum+lineId+"已传输成功");
                }else{
                    logger.info("userStatus===="+documentNum+"已传输成功");
                }
            }else{
                if (null != lineId){
                    logger.info("userStatus===="+documentNum+lineId+jsonObject.get("MESSAGE"));
                }else {
                    logger.info("userStatus===="+documentNum+jsonObject.get("MESSAGE"));
                }
            }
            System.err.println(jsonObject);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("userStatus===="+documentNum+"商发服务有问题");
        }
    }

    @Override
    public String shangFaVoucher(JSONObject jsonObject) {
        List<String> dess =  shangfaMapper.shangfaVoucherDes();

        Set<String> desSets = new HashSet<>();
        for (String s : dess) {
            String[] split = s.split("\\+");
            for (String s1 : split) {
                desSets.add(s1);
            }
        }
        String s = JSON.toJSONString(desSets);
        System.err.println(s);
        org.json.JSONObject input = new org.json.JSONObject(jsonObject.getString("input"));
        String userId = jsonObject.getString("userId");
        String date = input.getString("date");
        Integer seriousNum = input.getInt("num");
        List documentIds = input.getJSONArray("document_id").toList();
        String userName =  shangfaMapper.shangfaGetUserName(userId);
        StringBuilder allErrorMsgs = new StringBuilder();
        for (Object o : documentIds) {
            HandleEntity voucherPostData = getVoucherPostData(Integer.parseInt(o.toString()), userName, allErrorMsgs);
            StringBuilder errorMsg = voucherPostData.getErrorMsg();
            String postData = voucherPostData.getPostData();
            Boolean isPostFlag = voucherPostData.getPostFlag();
            if (isPostFlag){
                Map<String, String> identity = new HashMap<>();
                identity.put("Content-Type", "text/xml");
                try {
                    String responseStr = HttpUtil.HttpPostString(VOUCHERURL,
                            identity, postData, null, null);
                    org.json.JSONObject xmlJSONObj = XML.toJSONObject(responseStr);
                    JSONObject responseJsonObject = JSONObject.parseObject(xmlJSONObj.toString());
                    JSONArray responseJsonArray = responseJsonObject.getJSONObject("SOAP:Envelope").getJSONObject("SOAP:Body").getJSONObject("ns0:ZFI_INT_008.Response").getJSONObject("ZBAPIRET2").getJSONArray("item");

                    Boolean flag = true;
                    for (Object item : responseJsonArray) {
                        if (!"S".equals(((Map)item).get("TYPE"))){
                            flag = false;
                            errorMsg.append(((Map)item).get("MESSAGE")).append(";");
                        }
                    }
                    if (flag){

                    }else {
                        allErrorMsgs.append(errorMsg).append("\n");
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    errorMsg.append("链接商发接口出错;");
                    allErrorMsgs.append(errorMsg).append("\n");
                    logger.error(e.getMessage());
                }
            }
        }
        return allErrorMsgs.toString();
    }

    @Override
    public String shangfaBuTie(String stringInput, ExpClaimHeader ech, CustomUserDetails activeUser, String locale) throws Exception {
        JSONObject jsonInput = JSONObject.parseObject(stringInput);
        String saveCalBtn = new String();
        Integer flightTId = new Integer(0);
        Integer trainTId = new Integer(0);
        Integer allowTId = new Integer(0);
        if (activeUser.getCompanyId() == 16894) {
            saveCalBtn = shangfaMapper.findSaveCalBtn(jsonInput.getInteger("header_type_id"), activeUser.getCompanyId());

            logger.info("---------saveCalBtn:" + saveCalBtn);
            if (!StringUtils.isEmpty(saveCalBtn) && saveCalBtn.equals("cal_allowance_shangfa") && jsonInput.containsKey("claim_line")) {
                flightTId = shangfaMapper.findTypeIdSSF("T001");
                trainTId = shangfaMapper.findTypeIdSSF("T002");
                allowTId = shangfaMapper.findTypeIdSSF("T000");
                logger.info("-------flightTId:" + flightTId + "-------trainTId:" + trainTId + "--------allowTId:" + allowTId);
                Map<Integer, Double> allowanceAmountMap = new HashMap<>();
                JSONArray jsonLines = jsonInput.getJSONArray("claim_line");
                JSONArray jsonLinesNew = new JSONArray();
                for (int i = 0; i < jsonLines.size(); i++) {
                    JSONObject jsonLine = jsonLines.getJSONObject(i);

                    if (jsonLine.getInteger("type_id").equals(allowTId)) {
                        continue;
                    } else if (jsonLine.getInteger("type_id").equals(flightTId)) {
                        Integer destinationCity = jsonLine.getInteger("destination_city");
                        Integer destinationCityTo = jsonLine.getInteger("destination_city_to");
                        String detailStr = shangfaMapper.findallowanceDetail(activeUser.getCompanyId());
                        JSONArray detail = JSON.parseArray(detailStr);
                        logger.info(detailStr);
                        //TODO: 测试先写死
                        String valueCode = "MU";
                        Double rate = 0.5;
//                        if (detail != null) {
//                            Double rate = detail.getJSONObject(0).getJSONObject("reference").getDouble("rate");
//                            rate = (rate != null ? rate : 0);
//                            Integer airlineName = detail.getJSONObject(0).getJSONObject("reference").getInteger("airlineName");
//                            String valueCode = echMapper.findFlightValueCide(airlineName, activeUser.getCompanyId(), locale);
                        Double originalPrice = getOriginalAmount(destinationCity, destinationCityTo, 3, valueCode, activeUser.getCompanyId());
                        Double standardPrice = originalPrice * rate;
                        Double originAmount = jsonLine.getDouble("original_amount");
                        Double maxAmount = CalculationMethod.Getmax(standardPrice - originAmount, 0.00);

                        if (allowanceAmountMap.containsKey(jsonLine.getInteger("charge_user"))) {
                            allowanceAmountMap.put(jsonLine.getInteger("charge_user"), allowanceAmountMap.get(jsonLine.getInteger("charge_user")) + maxAmount * 0.5);
                        } else {
                            allowanceAmountMap.put(jsonLine.getInteger("charge_user"), maxAmount * 0.5);
                        }
//                        }
                        jsonLinesNew.add(jsonLine);
                    } else if (jsonLine.getInteger("type_id").equals(trainTId)) {
                        Integer destinationCity = jsonLine.getInteger("destination_city");
                        Integer destinationCityTo = jsonLine.getInteger("destination_city_to");
                        Integer trainTime = getTrainTime(destinationCity, destinationCityTo, activeUser.getCompanyId());
                        if (trainTime > 18000) {
                            String detailStr = shangfaMapper.findallowanceDetail(activeUser.getCompanyId());
                            JSONArray detail = JSON.parseArray(detailStr);
                            if (detail != null) {
                                Double rate = detail.getJSONObject(0).getJSONObject("reference").getDouble("rate");
                                rate = (rate != null ? rate : 0);
                                Integer airlineName = detail.getJSONObject(0).getJSONObject("reference").getInteger("airlineName");
                                String valueCode = shangfaMapper.findFlightValueCide(airlineName, activeUser.getCompanyId(), locale);

                                Double originalPrice = getOriginalAmount(destinationCity, destinationCityTo, 3, valueCode, activeUser.getCompanyId());
                                Double standardPrice = originalPrice * rate;
                                Double originAmount = jsonLine.getDouble("original_amount");
                                Double maxAmount = CalculationMethod.Getmax(standardPrice - originAmount, 0.00);

                                if (allowanceAmountMap.containsKey(jsonLine.getInteger("charge_user"))) {
                                    allowanceAmountMap.put(jsonLine.getInteger("charge_user"), allowanceAmountMap.get(jsonLine.getInteger("charge_user")) + maxAmount * 0.5);
                                } else {
                                    allowanceAmountMap.put(jsonLine.getInteger("charge_user"), maxAmount * 0.5);
                                }
                            }
                        }
                        jsonLinesNew.add(jsonLine);
                    } else {
                        jsonLinesNew.add(jsonLine);
                    }
                }
                logger.info("-------allowanceAmountMap:" + allowanceAmountMap);

                for (Integer key : allowanceAmountMap.keySet()) {
                    JSONObject jsonLineNew = new JSONObject();
                    Double value = allowanceAmountMap.get(key);
                    jsonLineNew.put("company_id", activeUser.getCompanyId());
                    jsonLineNew.put("type_id", allowTId);
                    jsonLineNew.put("internal_type", null);
                    jsonLineNew.put("receipt_amount", value);
                    jsonLineNew.put("receipt_currency", "CNY");
                    jsonLineNew.put("claim_amount", value);
                    jsonLineNew.put("claim_currency", "CNY");
                    jsonLineNew.put("exchange_rate", 1);
                    jsonLineNew.put("comments", "说明：1、计算差价补贴的参考金额，都取自对应城市间汉邦的经济舱的规定折扣价格。2、火车时长大于5小时的火车票才能计算差价补贴。火车市场由出发城市。到达城市确定，非实际乘坐时长。其他大于参考金额的不参与计算。");
                    jsonLineNew.put("pay_method_id", 48845);
                    jsonLineNew.put("charge_user", key);
                    jsonLinesNew.add(jsonLineNew);
                }
                logger.info("--------jsonLinesNew:" + jsonLinesNew);
                jsonInput.put("claim_line", jsonLinesNew);
            }

            //TODO: 商发定制补贴
            Integer allowCount = 0;
            Double sumAllowanceAmount = 0.00;
            //todo 判断用哪个ExpClaimLine
            for (ExpClaimLine expClaimLine : ech.getClaimLines()) {
                if (expClaimLine.getExpLineTypeId() != null && expClaimLine.getExpLineTypeId().equals(545479)) {
                    allowCount++;
                    sumAllowanceAmount = sumAllowanceAmount + expClaimLine.getReceiptAmount();
                }
            }
            String exceptionMsg = ("更新" + allowCount +
                    "行差价补贴，总金额为" + sumAllowanceAmount +
                    "CNY");
            JSONArray exceptions = new JSONArray();
            JSONObject exception = new JSONObject();
            exception.put("exception_level", 0);
            exception.put("exception_message", exceptionMsg);
            exceptions.add(exception);
            jsonInput.put("exceptions",exceptions);
            return JSON.toJSONString(jsonInput);
        }

        return null;
    }

    @Override
    public HandleEntity shangfaVoucherTest(String documentNum, String userName, StringBuilder allErrorMsgs) {
       List<Integer> documentIds =  shangfaMapper.getDocumentId(documentNum);
        return getVoucherPostData(documentIds.get(0),userName,allErrorMsgs);
    }

    private HandleEntity getVoucherPostData(Integer documentId,String userName,StringBuilder allErrorMsgs){
        HandleEntity handleEntity = new HandleEntity();
        StringBuilder errorMsg = new StringBuilder();
        String postData = null;
        Boolean isPostFlag = true;

        Date nowDate = new Date();
        VoucherHeader voucherHeader =  shangfaMapper.shangfaVoucherHeader(documentId);
        Integer headAndLineNo =  shangfaMapper.shagnfaGetAttachmentsCount(voucherHeader.getHeaderId());
        errorMsg.append(voucherHeader.getDocumentNum()).append(":");
        if(voucherTypes.contains(voucherHeader.getTypeCode())){
            List<VoucherLine> lines = shangfaMapper.shangfaVoucherLine(voucherHeader.getHeaderId());
            headAndLineNo = getLineAttachment(voucherHeader, headAndLineNo);

            List<Map<String, Object>> kemuList = new ArrayList<>();
            List<Map<String, Object>> supplierList = new ArrayList<>();
            List<Map<String, Object>> amountList = new ArrayList<>();
            List<Map<String, Object>> fuJiaList = new ArrayList<>();

            int i = 0;
            for (VoucherLine line : lines) {
                String projectId = voucherHeader.getProjectId();
                if (null != line.getProjectId()){
                    projectId = line.getProjectId();
                }
                String xiangMuCode = null;
                if (null != projectId){
                    xiangMuCode = getXiangMuCode(projectId);
                }


                if(null != line.getDrAccountCode()){
                    //借
                    i++;
                    HashMap kemuMap = new HashMap();
                    kemuMap.put("ITEMNO_ACC",i);
                    kemuMap.put("ITEM_TEXT","待定");
                    kemuMap.put("GL_ACCOUNT",line.getDrAccountCode());
                    kemuMap.put("COSTCENTER",line.getDepartmentCode());
                    kemuMap.put("ORDERID",voucherHeader.getColumn12());
                    kemuList.add(kemuMap);
                    if (null != line.getSuppplierCode()){
                        HashMap supplierMap = new HashMap();
                        supplierMap.put("ITEMNO_ACC",i);
                        supplierMap.put("ITEM_TEXT","待定");
                        supplierMap.put("VENDOR_NO",line.getSuppplierCode());
                        supplierList.add(supplierMap);
                    }
                    HashMap amountMap = new HashMap();
                    amountMap.put("ITEMNO_ACC",i);
                    amountMap.put("CURRENCY",line.getReceiptCurrency());
                    amountMap.put("AMT_DOCCUR",line.getOriginalAmount());
                    amountMap.put("AMT_LTCUR",line.getFinNetAmount());
                    amountList.add(amountMap);
                    HashMap fuJiaMap = new HashMap();
                    fuJiaMap.put("ITEMNO_ACC",i);
                    fuJiaMap.put("NUMPG",headAndLineNo);
                    fuJiaMap.put("BSCHL", typeMappJie.get(voucherHeader.getTypeCode()));
                    fuJiaMap.put("RSTGR",line.getColumn41());
                    fuJiaMap.put("ZZPS001",xiangMuCode);
                    if (null != line.getProjectCode()){
                        fuJiaMap.put("ZZBG001",line.getProjectCode());
                    }else{
                        fuJiaMap.put("ZZBG001",voucherHeader.getProjectCode());
                    }
                    fuJiaMap.put("ZZCN001",voucherHeader.getColumn3());
                    fuJiaList.add(fuJiaMap);
                }

                if(null != line.getCrAccountCode()){
                    //贷
                    i++;
                    HashMap kemuMap = new HashMap();
                    kemuMap.put("ITEMNO_ACC",i);
                    kemuMap.put("GL_ACCOUNT",line.getCrAccountCode());
                    kemuMap.put("COSTCENTER",line.getDepartmentCode());
                    kemuMap.put("ITEM_TEXT","待定");
                    kemuMap.put("ORDERID",voucherHeader.getColumn12());
                    kemuList.add(kemuMap);
                    if (null != line.getSuppplierCode()){
                        HashMap supplierMap = new HashMap();
                        supplierMap.put("ITEM_TEXT","待定");
                        supplierMap.put("ITEMNO_ACC",i);
                        supplierMap.put("VENDOR_NO",line.getSuppplierCode());
                        supplierList.add(supplierMap);
                    }
                    HashMap amountMap = new HashMap();
                    amountMap.put("ITEMNO_ACC",i);
                    amountMap.put("AMT_DOCCUR",line.getOriginalAmount());
                    amountMap.put("AMT_LTCUR",line.getFinClaimAmount());
                    amountMap.put("CURRENCY",line.getReceiptCurrency());
                    amountList.add(amountMap);

                    HashMap fuJiaMap = new HashMap();
                    fuJiaMap.put("ITEMNO_ACC",i);
                    fuJiaMap.put("ZZPS001",xiangMuCode);
                    fuJiaMap.put("NUMPG",headAndLineNo);
                    fuJiaMap.put("RSTGR",line.getColumn41());
                    fuJiaMap.put("ZZCN001",voucherHeader.getColumn3());
                    fuJiaMap.put("BSCHL", typeMappDai.get(voucherHeader.getTypeCode()));
                    if (null != line.getProjectCode()){
                        fuJiaMap.put("ZZBG001",line.getProjectCode());
                    }else{
                        fuJiaMap.put("ZZBG001",voucherHeader.getProjectCode());
                    }
                    fuJiaList.add(fuJiaMap);
                }
                if(null != line.getTaxAccountCode()){
                    //税
                    i++;
                    HashMap kemuMap = new HashMap();
                    kemuMap.put("ITEMNO_ACC",i);
                    kemuMap.put("GL_ACCOUNT",line.getTaxAccountCode());
                    kemuMap.put("ITEM_TEXT","待定");
                    kemuMap.put("COSTCENTER",line.getDepartmentCode());
                    kemuMap.put("ORDERID",voucherHeader.getColumn12());
                    kemuList.add(kemuMap);
                    if (null != line.getSuppplierCode()){
                        HashMap supplierMap = new HashMap();
                        supplierMap.put("ITEMNO_ACC",i);
                        supplierMap.put("VENDOR_NO",line.getSuppplierCode());
                        supplierMap.put("ITEM_TEXT","待定");
                        supplierList.add(supplierMap);
                    }
                    HashMap amountMap = new HashMap();
                    amountMap.put("ITEMNO_ACC",i);
                    amountMap.put("AMT_DOCCUR",line.getOriginalAmount());
                    amountMap.put("CURRENCY",line.getReceiptCurrency());
                    amountMap.put("AMT_LTCUR",line.getTaxAccountCode());
                    amountList.add(amountMap);

                    HashMap fuJiaMap = new HashMap();
                    fuJiaMap.put("ITEMNO_ACC",i);
                    fuJiaMap.put("NUMPG",headAndLineNo);
                    fuJiaMap.put("RSTGR",line.getColumn41());
                    fuJiaMap.put("BSCHL", typeMappJie.get(voucherHeader.getTypeCode()));
                    fuJiaMap.put("ZZCN001",voucherHeader.getColumn3());
                    fuJiaMap.put("ZZPS001",xiangMuCode);
                    if (null != line.getProjectCode()){
                        fuJiaMap.put("ZZBG001",line.getProjectCode());
                    }else{
                        fuJiaMap.put("ZZBG001",voucherHeader.getProjectCode());
                    }
                    fuJiaList.add(fuJiaMap);
                }


                String departmentCode = voucherHeader.getDepartmentCode();
                if ("********".equals(departmentCode)){
                    departmentCode = "9000";
                }else if ("10200000".equals(departmentCode)){
                    departmentCode = "9100";
                }

                Calendar cale  = Calendar.getInstance();
                int month = cale.get(Calendar.MONTH) + 1;
                StringBuffer body = new StringBuffer();
                body.append("        <ZBAPIACHE09>\n" +
                        "              <COMP_CODE>"+departmentCode+"</COMP_CODE>\n" +
                        "              <DOC_DATE>"+dayFormate.format(nowDate)+"</DOC_DATE>\n" +
                        "              <PSTNG_DATE>"+dayFormate.format(nowDate)+"</PSTNG_DATE>\n" +
                        "              <FISC_YEAR>"+yearFormate.format(nowDate)+"</FISC_YEAR>\n" +
                        "              <FIS_PERIOD>"+month+"</FIS_PERIOD>\n" +
                        "              <DOC_TYPE>"+typeMapping.get(voucherHeader.getTypeCode())+"</DOC_TYPE>\n" +
                        "              <REF_DOC_NO>"+userName+"</REF_DOC_NO>\n" +
                        "              <HEADER_TXT>"+"代填"+"</HEADER_TXT>\n" +
                        "        </ZBAPIACHE09>\n");



                body.append("        <ZBAPIACGL09>\n");
                for (Map<String, Object> map : kemuList) {
                    body.append("               <item>\n" +
                            "                <ITEMNO_ACC>"+map.get("ITEMNO_ACC")+"</ITEMNO_ACC>\n" +
                            "                <GL_ACCOUNT>"+map.get("GL_ACCOUNT")+"</GL_ACCOUNT>\n" +
                            "                <ITEM_TEXT>"+map.get("ITEM_TEXT")+"</ITEM_TEXT>\n" +
                            "                <COSTCENTER>"+map.get("COSTCENTER")+"</COSTCENTER>\n" +
                            "                <ORDERID>"+map.get("ORDERID")+"</ORDERID>\n" +
                            "               </item>\n"
                    );
                }
                body.append("        </ZBAPIACGL09>\n");

                body.append("        <ZBAPIACAP09>\n");
                for (Map<String, Object> map : supplierList) {
                    body.append("               <item>\n" +
                            "                <ITEMNO_ACC>"+map.get("ITEMNO_ACC")+"</ITEMNO_ACC>\n" +
                            "                <VENDOR_NO>"+map.get("VENDOR_NO")+"</VENDOR_NO>\n" +
                            "                <ITEM_TEXT>"+map.get("ITEM_TEXT")+"</ITEM_TEXT>\n" +
                            "               </item>\n"
                    );
                }
                body.append("        </ZBAPIACAP09>\n");

                body.append("        <ZBAPIACCR09>\n");
                for (Map<String, Object> map : amountList) {
                    body.append("               <item>\n" +
                            "                <ITEMNO_ACC>"+map.get("ITEMNO_ACC")+"</ITEMNO_ACC>\n" +
                            "                <AMT_DOCCUR>"+map.get("AMT_DOCCUR")+"</AMT_DOCCUR>\n" +
                            "                <CURRENCY>"+map.get("CURRENCY")+"</CURRENCY>\n" +
                            "                <AMT_LTCUR>"+map.get("AMT_LTCUR")+"</AMT_LTCUR>\n" +
                            "               </item>\n"
                    );
                }
                body.append("        </ZBAPIACCR09>\n");

                body.append("        <ZBAPIPAREX>\n");
                for (Map<String, Object> map : fuJiaList) {
                    body.append("               <item>\n" +
                            "                <ITEMNO_ACC>"+map.get("ITEMNO_ACC")+"</ITEMNO_ACC>\n" +
                            "                <ZZPS001>"+(null != map.get("ZZPS001") ? map.get("ZZPS001") : "") +"</ZZPS001>\n" +
                            "                <ZZCN001>"+(null != map.get("ZZCN001") ? map.get("ZZCN001") : "") +"</ZZCN001>\n" +
                            "                <ZZBG001>"+(null != map.get("ZZBG001") ? map.get("ZZBG001") : "") +"</ZZBG001>\n" +
                            "                <BSCHL>"  +(null != map.get("BSCHL")? map.get("BSCHL") : "" )+"</BSCHL>\n" +
                            "                <RSTGR>"  +(null != map.get("RSTGR")? map.get("RSTGR") : "" )+"</RSTGR>\n" +
                            "                <NUMPG>"  +(null != map.get("NUMPG")? map.get("NUMPG") : "" )+"</NUMPG>\n" +
                            "               </item>\n"
                    );
                }
                body.append("        </ZBAPIPAREX>\n");
                postData = getVoucherHead()+body.toString()+getVoucherEnd();
                logger.info("商发凭证入参==="+postData);
            }
        }else{
            isPostFlag = false;
            errorMsg.append("该单据类型不符合情况;");
            allErrorMsgs.append(errorMsg).append("\n");
        }
        handleEntity.setPostData(postData);
        handleEntity.setErrorMsg(errorMsg);
        handleEntity.setPostFlag(isPostFlag);

        return handleEntity;
    }



    private String getXiangMuCode(String projectId) {

       Boolean flag = true;
       while (flag){
           String parent =   shangfaMapper.shangfaGetParent(projectId);
           if (null != parent){
               projectId = parent;
           }else {
               flag = false;
           }
       }
       return shangfaMapper.shangfaGetProjectCode(projectId);
    }

    private Integer getLineAttachment(VoucherHeader voucherHeader, Integer headAndLineNo) {
        List<Integer> invoiceAttachments =  shangfaMapper.getInvoiceAttachment(voucherHeader.getHeaderId());
        for (Integer invoiceAttachment : invoiceAttachments) {
            if (null != invoiceAttachment) {
                headAndLineNo = headAndLineNo+invoiceAttachment;
            }
        }
        return headAndLineNo;
    }


    private String getVoucherHead(){
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:sap-com:document:sap:rfc:functions\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <urn:ZFI_INT_008>\n";
    }

    private String getVoucherEnd(){
        return "    </urn:ZFI_INT_008>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }


    private String getKaoQinHead(){
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:hr=\"http://acae.com/xi/HR\">\n" +
                "<soapenv:Header/>\n" +
                "<soapenv:Body>\n" +
                "<hr:MT_TRAVEL_INFO_REQ>\n" +
                "<IT_TRAVELINFOR>\n";
    }


    private String getKaoQinEnd(){

        return "</IT_TRAVELINFOR>\n" +
                "</hr:MT_TRAVEL_INFO_REQ>\n" +
                "</soapenv:Body>\n" +
                "</soapenv:Envelope>\n";
    }

    private Map<String,String> typeMapping =  new HashMap<String,String>(){
        {
            put("SF002","Z0");
            put("SF032","Z0");
            put("SF004","Z1");
            put("SF006","Z2");
            put("SF035","Z3");
            put("SF051","Z4");
            put("SF010","Z5");
            put("SF008","Z6");
            put("SF011","Z7");
            put("SF015","Z8");
            put("SF017","Z9");
            put("SF098","ZA");
            put("SF099","ZB");
            put("SF028","ZC");
            put("SF027","ZD");
            put("SF030","ZE");
        }

    };


    private Map<String,String> typeMappJie =  new HashMap<String,String>(){
        {
            put("SF002","40");
            put("SF032","40");
            put("SF004","40");
            put("SF006","40");
            put("SF035","40");
            put("SF051","40");
            put("SF010","40");
            put("SF008","40");
            put("SF011","40");
            put("SF015","40");
            put("SF017","40");
            put("SF098","40");
            put("SF099","40");
            put("SF028","21");
            put("SF027","21");
            put("SF030","21");
        }

    };

    private Map<String,String> typeMappDai =  new HashMap<String,String>(){
        {
            put("SF002","50");
            put("SF032","50");
            put("SF004","50");
            put("SF006","50");
            put("SF035","50");
            put("SF051","50");
            put("SF010","50");
            put("SF008","50");
            put("SF011","50");
            put("SF015","50");
            put("SF017","50");
            put("SF098","50");
            put("SF099","50");
            put("SF028","50");
            put("SF027","50");
            put("SF030","50");
        }

    };

    public Double getOriginalAmount(Integer departurePoint, Integer destinationPoint, Integer seatNo, String airlineCode, Integer companyId) throws Exception {
        ResourceBundle rb = ResourceBundle.getBundle("thirdparty-url");
        String url = rb.getString("smarttripUrl") +"flight/search";
        JSONObject req = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("departurePoint", departurePoint);
        data.put("destinationPoint", destinationPoint);
        data.put("seatNo", seatNo);
        data.put("airlineCode",airlineCode);
        req.put("company_id", companyId);
        req.put("data",data);
        req.put("platform","product");
        logger.info("get flight messsage" + req);
        JSONObject resp = postThirdParty(url, req);
        if (resp != null){
            return resp.getDouble("originalPrice");
        }else{
            return 0.00;
        }
    }

    public static JSONObject postThirdParty(String url, JSONObject input) throws Exception {
        String result  = HttpHelper.doJsonPost(url,input);
        System.out.println(result);
        JSONObject jsonObject = JSON.parseObject(result);
        JSONArray data = jsonObject.getJSONArray("data");
        int index = 0;
        Double maxPrice = 0.00;
        if (jsonObject.getIntValue("resCode") == 10000){
            if (data.size() == 0){
                return null;
            }
            for (int i = 0; i < data.size(); i++){
                Double tmp = data.getJSONObject(i).getDoubleValue("originalPrice");
                if (tmp > maxPrice){
                    index = i;
                    maxPrice = tmp;
                }
            }
        }
        else{
            throw new ValidationException("query thirdparty error");
        }
        return data.getJSONObject(index);
    }


    public Integer getTrainTime(Integer departurePoint, Integer destinationPoint, Integer companyId) throws Exception {
        ResourceBundle rb = ResourceBundle.getBundle("thirdparty-url");
        String url = rb.getString("smarttripUrl") +"train/search";
        JSONObject req = new JSONObject();
        JSONObject data = new JSONObject();
        data.put("departurePoint", departurePoint);
        data.put("destinationPoint", destinationPoint);
        req.put("company_id", companyId);
        req.put("data",data);
        req.put("platform","product");
        logger.info("get flight messsage" + req);
        JSONObject resp = postThirdParty(url, req);
        if (resp != null){
            return resp.getInteger("train_time");
        }else{
            return 0;
        }
    }

}