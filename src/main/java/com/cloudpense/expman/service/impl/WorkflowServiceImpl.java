package com.cloudpense.expman.service.impl;

import com.cloudpense.expman.mapper.WorkflowMapper;
import com.cloudpense.expman.service.WorkflowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Service
public class WorkflowServiceImpl implements WorkflowService {
    @Autowired
    WorkflowMapper workflowMapper;
    @Override
    public Integer getToDoWorkflowCount(int companyId, String employeeNumber) {
        List<Integer> userIds = workflowMapper.getUserIds(companyId, employeeNumber);
        if (CollectionUtils.isEmpty(userIds)) {
            return 0;
        }
        return workflowMapper.getToDoWorkflowCount(companyId, userIds);
    }
}
