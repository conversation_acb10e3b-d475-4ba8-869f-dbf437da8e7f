package com.cloudpense.expman.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.hsf.HsfNtf;
import com.cloudpense.expman.entity.hsf.claim.repayment.ItemType;
import com.cloudpense.expman.mapper.HsfMapper;
import com.cloudpense.expman.service.HSFService;
import com.cloudpense.expman.util.HSFS3Util;
import com.cloudpense.expman.util.JaxbUtil;
import com.cloudpense.expman.util.RestTemplateUtils;
import com.cloudpense.expman.util.SFTPUtil;
import com.cloudpense.expman.webService.hsf.*;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.ValidationException;
import java.io.File;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class HSFServiceImpl implements HSFService {
    private HsfMapper hsfMapper;
    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");

    private RestTemplate template = RestTemplateUtils.restTemplate();

    @Autowired
    public HSFServiceImpl(HsfMapper hsfMapper) {
        this.hsfMapper = hsfMapper;
    }

    private static Logger logger = LoggerFactory.getLogger(HSFServiceImpl.class);

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void ntfPush() {
        String LOG_KEY = "hsf待办推送OA系统===";
        hsfMapper.freshNtfStatus(15587);
        ArrayList<HsfNtf> ntfList;
        String thirdReceiverId, response = null, formattedContent;
        JSONObject content, bindInfo, result;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> jsonEntity;
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        String oaToken = hsfMapper.getOAToken();
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        ntfList = hsfMapper.queryNtfCompany();
        logger.info("{}本次推送目标={}", LOG_KEY, JSON.toJSONString(ntfList));
        String registerCode = rb.getString("hsf.registerCode");
        String bindUrl = rb.getString("hsf.bindUrl");
        String msgUrl = rb.getString("hsf.msgUrl");
        String taskUrl = rb.getString("hsf.taskUrl");
        String updateUrl = rb.getString("hsf.updateUrl");
        for (HsfNtf ntf : ntfList) {
            if (ntf.getStatus() == 0 || ntf.getStatus() == 3) {
                content = JSON.parseObject(ntf.getContent());
                content.put("taskId", String.valueOf(ntf.getId()));
                content.put("thirdpartyMessageId", String.valueOf(ntf.getId()));
                thirdReceiverId = content.getString("thirdReceiverId");
                bindInfo = JSON.parseObject(hsfMapper.queryPsnID(thirdReceiverId));
                bindInfo.put("thirdUserId", thirdReceiverId);
                bindInfo.put("registerCode", registerCode);
                formattedContent = "{ \"userlist\": [" + bindInfo.toJSONString() + " ] }";
                jsonEntity = new HttpEntity<>(formattedContent, headers);
                try {
                    logger.info("{}user推送，url={}， 请求={}", LOG_KEY, bindUrl, JSONObject.toJSONString(jsonEntity));
                    ResponseEntity<String> reponse = template.postForEntity(bindUrl + "?token=" + oaToken, jsonEntity, String.class);
                    logger.info("{}user推送， url={}， 响应={}", LOG_KEY, bindUrl, JSONObject.toJSONString(reponse));
                } catch (RestClientException e) {
                    logger.error("{}user推送出现异常: {}", LOG_KEY, e.getMessage(), e);
                }

                jsonEntity = new HttpEntity<>(content.toJSONString(), headers);
                try {
                    logger.info("{}msg推送，url={}，请求={}", LOG_KEY, msgUrl, JSONObject.toJSONString(jsonEntity));
                    ResponseEntity<String> entity = template.postForEntity(msgUrl + "?token=" + oaToken, jsonEntity, String.class);
                    logger.info("{}msg推送，url={}，响应={}", LOG_KEY, msgUrl, JSONObject.toJSONString(entity));
                } catch (RestClientException e) {
                    logger.error("{}msg推送出现异常: {}", LOG_KEY, e.getMessage(), e);
                }
                formattedContent = "{\"pendingList\":[" + content.toJSONString() + "]}";
                jsonEntity = new HttpEntity<>(formattedContent, headers);
                try {
                    logger.info("{}task推送，url={}，请求={}", LOG_KEY, taskUrl, JSONObject.toJSONString(jsonEntity));
                    response = template.postForObject(taskUrl + "?token=" + oaToken, jsonEntity, String.class);
                    logger.info("{}task推送，url={}，响应={}", LOG_KEY, taskUrl, JSONObject.toJSONString(response));
                    result = JSON.parseObject(response);
                    if (result.getBoolean("success")) {
                        hsfMapper.updateNtfStatus(ntf.getId(), 2, content.toJSONString(), null);
                    } else {
                        hsfMapper.updateNtfStatus(ntf.getId(), 22, content.toJSONString(), response);
                    }
                    hsfMapper.insert_log("hsf_ntf_log", content.toJSONString() + response);
                } catch (Exception e) {
                    logger.error("{}task推送出现异常: {}", LOG_KEY, e.getMessage(), e);
                    hsfMapper.updateNtfStatus(ntf.getId(), 3, content.toJSONString(), e.getMessage());
                    hsfMapper.insert_log("hsf_ntf_log", content.toJSONString() + e.getMessage());
                }
            } else if (ntf.getStatus() == 10 || ntf.getStatus() == 13 || ntf.getStatus() == 14 || ntf.getStatus() == 15) {
                content = new JSONObject();
                content.put("registerCode", registerCode);
                content.put("taskId", ntf.getId());
                content.put("state", 1);
                switch (ntf.getStatus()) {
                    case 10:
                    case 13:
                        content.put("subState", 0);
                        break;
                    case 14:
                        content.put("subState", 2);
                        break;
                    case 15:
                        content.put("subState", 1);
                        break;

                }
                jsonEntity = new HttpEntity<>(content.toJSONString(), headers);
                try {
                    try {
                        logger.info("oa推送失败=>" + JSONObject.toJSONString(jsonEntity));
                        response = template.postForObject(updateUrl + "?token=" + oaToken, jsonEntity, String.class);
                        logger.info("oa推送response=>" + JSONObject.toJSONString(response));
                    } catch (RestClientException e) {
                        logger.error("oa待办推送失败", e);
                    }
                    result = JSON.parseObject(response);
                    if (result.getBoolean("success")) {
                        hsfMapper.updateNtfStatus(ntf.getId(), 12, ntf.getContent(), "success");
                    } else {
                        if (response.contains("未找到更新的待办数据")) {
                            hsfMapper.updateNtfStatus(ntf.getId(), 12, ntf.getContent(), response);
                        } else {
                            hsfMapper.updateNtfStatus(ntf.getId(), ntf.getStatus(), ntf.getContent(), response);
                        }
                    }
                    hsfMapper.insert_log("hsf_ntf_log", content.toJSONString() + response);
                } catch (Exception e) {
                    hsfMapper.updateNtfStatus(ntf.getId(), ntf.getStatus(), ntf.getContent(), e.getMessage());
                    hsfMapper.insert_log("hsf_ntf_log", content.toJSONString() + e.getMessage());
                    e.printStackTrace();
                }
            }
        }

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void refreshNtf() {
        hsfMapper.freshNtfStatus(15587);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateOAToken() {
        String LOG_KEY = "hsf获取OA系统token===";
        String tokenUrl = rb.getString("hsf.tokenUrl");
        String response = null;
        try {
            logger.info("{}请求={}", LOG_KEY, tokenUrl);
            response = template.getForObject(tokenUrl, String.class);
            logger.info("{}响应={}", LOG_KEY, JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("{}token获取失败: {}", LOG_KEY, e.getMessage(), e);
        }
        if (!(response == null || "".equals(response))) {
            hsfMapper.saveOAToken(response);
        }
    }


    @Override
    public String encrypt(String sSrc) {
        String sKey = "E44262e5739881a9";
        String result;
        if (hsfMapper.queryUserExistsById(sSrc) != 1) {
            result = "\"\"";
        } else {
            byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            try {
                Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
                cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
                sSrc = sSrc + "," + System.currentTimeMillis();
                byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));
                result = "\"" + URLEncoder.encode(Base64.getEncoder().encodeToString(encrypted), "utf8") + "\"";
            } catch (Exception e) {
                result = "\"error\"";
            }
        }
        result = "{\"code\":" + result + "}";
        return result;
    }

    @Override
    public void disableOutdatedData() {
        hsfMapper.disableOutdatedData();
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    //更新公司信息
    public void updateCompany(boolean fullSync) {
        String LOG_KEY = "HSF同步公司信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String start, end, response;

        //WebService 客户端
        ItfGetInfoOfHR infoService = new ItfGetInfoOfHR();
        ItfGetInfoOfHRPortType client = infoService.getItfGetInfoOfHRSOAP11PortHttp();
        JSONObject xmljson = new JSONObject();
        xmljson.put("num", "100");
        xmljson.put("orgCode", "");

        if (fullSync) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            while (calendar.get(Calendar.YEAR) > 2018
                    || (calendar.get(Calendar.YEAR) == 2018 && calendar.get(Calendar.MONTH) >= 10)) {
                end = sdf.format(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -30);
                start = sdf.format(calendar.getTime());
                xmljson.put("startDate", start);
                xmljson.put("endDate", end);

                int page = 0;
                do {
                    page++;
                    xmljson.put("pages", String.valueOf(page));
                    logger.info("{}get page={} company params:", LOG_KEY, page, xmljson.toJSONString());
                    response = client.getOrgInfo(xmljson.toJSONString());
                    response = JSON.parseObject(response).toJSONString();
                    logger.info("{}get page={} company result:", LOG_KEY, page, response);
                    if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                        hsfMapper.updateSubCompany(response);
                    }
                } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
            }
        } else {
            //因服务器时区为UTC0,故凌晨2点同步时服务器时间仍为昨天
            //又NC系统查询结果包含起始日期不包含结束日期
            //故同步日期为 服务器当前日期 到 服务器当前日期+1
            //以实现实际上同步真实时间 昨天 到 今天 的数据
            start = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);

            int page = 0;
            do {
                page++;
                xmljson.put("pages", String.valueOf(page));
                logger.info("{}get page={} get company params:", LOG_KEY, page, xmljson.toJSONString());
                response = client.getOrgInfo(xmljson.toJSONString());
                response = JSON.parseObject(response).toJSONString();
                logger.info("{}get page={} get company result:", LOG_KEY, page, response);
                if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                    hsfMapper.updateSubCompany(response);
                }
            } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
        }

    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateDept(boolean fullSync) {
        String LOG_KEY = "HSF同步部门信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String start, end, response;

        //WebService 客户端
        ItfGetInfoOfHR infoService = new ItfGetInfoOfHR();
        ItfGetInfoOfHRPortType client = infoService.getItfGetInfoOfHRSOAP11PortHttp();
        JSONObject xmljson = new JSONObject();
        xmljson.put("num", "100");

        ArrayList<String> orgCodeList = hsfMapper.queryOrgCode();
        if (fullSync) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);

            while (calendar.get(Calendar.YEAR) > 2018
                    || (calendar.get(Calendar.YEAR) == 2018 && calendar.get(Calendar.MONTH) >= 10)) {
                end = sdf.format(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -30);
                start = sdf.format(calendar.getTime());
                xmljson.put("startDate", start);
                xmljson.put("endDate", end);
                for (String orgCode : orgCodeList) {
                    if (orgCode != null && orgCode.length() > 0) {
                        xmljson.put("orgCode", orgCode);
                        int page = 0;
                        do {
                            page++;
                            xmljson.put("pages", String.valueOf(page));
                            logger.info("{}get page={} departments params:", LOG_KEY, page, xmljson.toJSONString());
                            response = client.getDeptInfo(xmljson.toJSONString());
                            response = JSON.parseObject(response).toJSONString();
                            logger.info("{}get page={} departments result:", LOG_KEY, page, response);
                            if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                                hsfMapper.updateDepartment(response);
                            }
                        } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                    }
                }
            }
        } else {
            start = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);
            for (String orgCode : orgCodeList) {
                if (orgCode != null && orgCode.length() > 0) {
                    xmljson.put("orgCode", orgCode);
                    int page = 0;
                    do {
                        page++;
                        xmljson.put("pages", String.valueOf(page));
                        logger.info("{}get page={} departments params:", LOG_KEY, page, xmljson.toJSONString());
                        response = client.getDeptInfo(xmljson.toJSONString());
                        response = JSON.parseObject(response).toJSONString();
                        logger.info("{}get page={} departments result:", LOG_KEY, page, response);
                        if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                            hsfMapper.updateDepartment(response);
                        }
                    } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void claimPush() {
        ArrayList<ExpClaimHeader> claimList = hsfMapper.queryUnsentClaim();
        claimPush(claimList);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void claimRepush(List<String> documentNums) {
        ArrayList<ExpClaimHeader> claimList = hsfMapper.queryRepushClaim(documentNums);
        claimPush(claimList);
    }

    private void claimPush(ArrayList<ExpClaimHeader> claimList) {
        String LOG_KEY = "hsf单据推送NC系统===";
        int size = claimList == null ? 0 : claimList.size();
        logger.info("{}待推送单据共{}条", LOG_KEY, size);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String extMsg, response, httpBody, branchCode, chargeDeptCode, submitDeptCode, cPsnCode, sPsnCode, lastApprover, lastApproveTime, url = rb.getString("hsf.claim_push");

        ArrayList<ExpClaimLine> lineList;
        ExpClaimHeader linkHeader;
        HttpHeaders headers = new HttpHeaders();
        HttpEntity<String> xmlEntity;
        MediaType type = MediaType.parseMediaType("application/xml; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        RestTemplate restTemplate = new RestTemplate();
        JSONObject approve;
        double repayAmount;
        Calendar calendar = Calendar.getInstance();

        loop:
        for (ExpClaimHeader current : claimList) {
            String documentNum = current.getDocumentNum();
            try {
                logger.info("{}单据{}开始处理...", LOG_KEY, documentNum);
                if (current.getTotalAmount() == 0 && current.getAdvanceAmount() == 0 && current.getTotalPayAmount() == 0) {
                    logger.info("{}单据{}金额为0不予推送", LOG_KEY, documentNum);
                    hsfMapper.updateExternalStatus(current.getHeaderId(), "deprecated", "金额为0,不予推送");
                    continue;
                }
                httpBody = null;
                if (current.getCurrencyCode() == null || "".equals(current.getCurrencyCode().trim())) {
                    current.setCurrencyCode("CNY");
                }
                linkHeader = hsfMapper.queryLinkHeader(current.getHeaderId());
                if (linkHeader == null) {
                    linkHeader = new ExpClaimHeader();
                }
                String approveJson = hsfMapper.getLastApprove(current.getHeaderId());
                if (approveJson != null && (!"".equals(approveJson))) {
                    approve = JSON.parseObject(approveJson);
                    lastApprover = approve.getString("user");
                    lastApproveTime = approve.getString("date");
                } else {
                    lastApprover = null;
                    lastApproveTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                }
                lineList = hsfMapper.queryClaimLine(current.getHeaderId());
                branchCode = hsfMapper.queryHSFOrgCode(current.getBranchId());
                chargeDeptCode = hsfMapper.queryHSFDeptCode(current.getChargeDepartment());
                submitDeptCode = hsfMapper.queryHSFDeptCode(current.getSubmitDepartment());
                cPsnCode = hsfMapper.queryHSFPsnCode(current.getChargeUser());
                sPsnCode = hsfMapper.queryHSFPsnCode(current.getSubmitUser());
                if (cPsnCode == null || "".equals(cPsnCode.trim())) {
                    cPsnCode = sPsnCode;
                }
                calendar.setTime(current.getSubmitDate());
                switch (current.getHeaderTypeCode()) {
                    case "R":
                        //还款单 com.cloudpense.expman.entity.hsf.claim.repayment
                        com.cloudpense.expman.entity.hsf.claim.repayment.UfinterfaceType repayRoot = new com.cloudpense.expman.entity.hsf.claim.repayment.UfinterfaceType();
                        repayRoot.setAccount("nknc");
                        repayRoot.setBilltype("2647");
                        repayRoot.setSender("HKOA");

                        com.cloudpense.expman.entity.hsf.claim.repayment.BillType repayBill = new com.cloudpense.expman.entity.hsf.claim.repayment.BillType();
                        repayBill.setId(current.getDocumentNum());

                        com.cloudpense.expman.entity.hsf.claim.repayment.BillheadType repayHead = new com.cloudpense.expman.entity.hsf.claim.repayment.BillheadType();
                        repayHead.setPkOrg(branchCode);
                        repayHead.setPkPayorg(branchCode);
                        repayHead.setFydwbm(branchCode);
                        repayHead.setFydeptid(chargeDeptCode);
                        repayHead.setDwbm(branchCode);
                        repayHead.setDeptid(submitDeptCode);
                        repayHead.setJkbxr(cPsnCode);
                        repayHead.setOperator("HKOA");
                        repayHead.setDjrq(simpleDateFormat.format(current.getSubmitDate()));
                        repayHead.setHkybje(String.valueOf(current.getAdvanceAmount()));
                        repayHead.setBzbm(current.getCurrencyCode());

                        ArrayList<com.cloudpense.expman.entity.hsf.claim.repayment.ItemType> repayItemArray = new ArrayList<>();
                        com.cloudpense.expman.entity.hsf.claim.repayment.ItemType repayItem;
                        for (ExpClaimLine currentLine : lineList) {
                            repayItem = new ItemType();
                            repayItem.setCjkybje(String.valueOf(currentLine.getFinClaimAmount()));
                            repayItem.setJkbxr(cPsnCode);
                            repayItem.setPkJkd(String.valueOf(hsfMapper.queryDocNum(currentLine.getLinkHeaderId())));
                            repayItem.setPkBusitem(String.valueOf(currentLine.getLinkLineId()));
                            repayItem.setPkOrg(branchCode);
                            repayItem.setDefitem12(getAccSub(currentLine.getDrAccountId()));
                            repayItem.setDefitem13(getAccSub(currentLine.getTaxAccountId()));
                            repayItem.setDefitem14(getAccSub(currentLine.getCrAccountId()));
                            repayItem.setDefitem15(String.valueOf(currentLine.getFinNetAmount()));
                            repayItem.setDefitem16(String.valueOf(currentLine.getFinTaxAmount()));
                            repayItem.setDefitem17(branchCode);

                            repayItemArray.add(repayItem);
                        }

                        repayHead.setErBxcontrast(repayItemArray);
                        repayBill.setBillhead(repayHead);
                        repayRoot.setBill(repayBill);

                        httpBody = JaxbUtil.convertToXml(repayRoot);

                        break;
                    case "L":
                        //借款单 com.cloudpense.expman.entity.hsf.claim.loan

                        com.cloudpense.expman.entity.hsf.claim.loan.UfinterfaceType loanRoot = new com.cloudpense.expman.entity.hsf.claim.loan.UfinterfaceType();
                        loanRoot.setAccount("nknc");
                        loanRoot.setBilltype("263X");
                        loanRoot.setSender("HKOA");

                        com.cloudpense.expman.entity.hsf.claim.loan.BillType loanBill = new com.cloudpense.expman.entity.hsf.claim.loan.BillType();
                        loanBill.setId(current.getDocumentNum());

                        com.cloudpense.expman.entity.hsf.claim.loan.BillheadType loanBillHead = new com.cloudpense.expman.entity.hsf.claim.loan.BillheadType();


                        loanBillHead.setPkFiorg(branchCode);
                        loanBillHead.setPkOrg(branchCode);
                        loanBillHead.setPkGroup("HKJT");
                        loanBillHead.setFydwbm(branchCode);
                        loanBillHead.setFydeptid(chargeDeptCode);
                        loanBillHead.setDwbm(branchCode);
                        loanBillHead.setDeptid(submitDeptCode);
                        loanBillHead.setJkbxr(cPsnCode);
                        loanBillHead.setReceiver(cPsnCode);
                        loanBillHead.setOperator("HKOA");
                        loanBillHead.setCreator("HKOA");
                        loanBillHead.setDjdl("jk");
                        loanBillHead.setDjlxbm("263X-Cxx-001");
                        loanBillHead.setBbje(String.valueOf(current.getAdvanceAmount()));
                        loanBillHead.setYbje(String.valueOf(current.getAdvanceAmount()));
                        loanBillHead.setTotal(String.valueOf(current.getAdvanceAmount()));
                        loanBillHead.setYjye(String.valueOf(current.getAdvanceAmount()));
                        loanBillHead.setDjrq(simpleDateFormat.format(current.getSubmitDate()));
                        loanBillHead.setDjzt("1");
                        loanBillHead.setBzbm(current.getCurrencyCode());
                        if ("CNY".equals(current.getCurrencyCode())) {
                            loanBillHead.setBbhl("1");
                        }
                        if ("approved".equals(current.getStatus())) {
                            loanBillHead.setPayflag("0");
                        } else {
                            loanBillHead.setPayflag("1");
                        }
                        loanBillHead.setIscheck("N");
                        loanBillHead.setSpzt("3");
                        loanBillHead.setQcbz("N");

                        loanBillHead.setKjnd(String.valueOf(calendar.get(Calendar.YEAR)));
                        loanBillHead.setKjqj(String.valueOf(calendar.get(Calendar.MONTH) + 1));
                        loanBillHead.setQzzt("0");
                        loanBillHead.setLoantype("0");
                        loanBillHead.setSxbz("0");
                        loanBillHead.setZyx20(current.getDocumentNum());

                        ArrayList<com.cloudpense.expman.entity.hsf.claim.loan.ItemType> loanItemArray = new ArrayList<>();
                        com.cloudpense.expman.entity.hsf.claim.loan.ItemType loanItem;
                        for (ExpClaimLine currentLine :
                                lineList) {
                            loanItem = new com.cloudpense.expman.entity.hsf.claim.loan.ItemType();
                            loanItem.setPk_jkh(String.valueOf(currentLine.getLineId()));
                            loanItem.setDefitem20(String.valueOf(currentLine.getLineId()));
                            loanItem.setAmount(String.valueOf(currentLine.getFinClaimAmount()));
                            loanItem.setYjye(String.valueOf(currentLine.getFinClaimAmount()));
                            loanItem.setDefitem12(getAccSub(currentLine.getDrAccountId()));
                            loanItem.setDefitem13(getAccSub(currentLine.getTaxAccountId()));
                            loanItem.setDefitem14(getAccSub(currentLine.getCrAccountId()));
                            loanItem.setDefitem15(String.valueOf(currentLine.getFinNetAmount()));
                            loanItem.setDefitem16(String.valueOf(currentLine.getFinTaxAmount()));
                            loanItem.setDefitem17(branchCode);

                            loanItemArray.add(loanItem);
                        }

                        loanBillHead.setJkBusitem(loanItemArray);
                        loanBill.setBillhead(loanBillHead);
                        loanRoot.setBill(loanBill);

                        httpBody = JaxbUtil.convertToXml(loanRoot);
                        break;
                    case "W":
                        //预提单 com.cloudpense.expman.entity.hsf.claim.withholding
                        com.cloudpense.expman.entity.hsf.claim.withholding.UfinterfaceType whRoot = new com.cloudpense.expman.entity.hsf.claim.withholding.UfinterfaceType();
                        whRoot.setAccount("nknc");
                        whRoot.setBilltype("262X");
                        whRoot.setSender("HKOA");

                        com.cloudpense.expman.entity.hsf.claim.withholding.BillType whBill = new com.cloudpense.expman.entity.hsf.claim.withholding.BillType();

                        com.cloudpense.expman.entity.hsf.claim.withholding.BillheadType whHeader = new com.cloudpense.expman.entity.hsf.claim.withholding.BillheadType();

                        whHeader.setPkGroup("HKJT");
                        whHeader.setPkOrg(branchCode);
                        whHeader.setPkCurrtype(current.getCurrencyCode());
                        whHeader.setApprstatus("3");
                        whHeader.setEffectstatus("0");
                        whHeader.setOrgCurrinfo("1");
                        whHeader.setAmount(String.valueOf(current.getTotalAmount()));
                        whHeader.setOrgAmount(String.valueOf(current.getTotalAmount()));
                        whHeader.setRestAmount(String.valueOf(current.getTotalAmount()));
                        whHeader.setOrgRestAmount(String.valueOf(current.getTotalAmount()));
                        whHeader.setApprovetime(lastApproveTime);
//                    whHeader.setApprover(lastApprover);
                        whHeader.setCreator("HKOA");
                        whHeader.setCreationtime(simpleDateFormat.format(current.getCreationDate()));
                        whHeader.setOperatorOrg(branchCode);
                        whHeader.setOperatorDept(submitDeptCode);
                        whHeader.setOperator(sPsnCode);
                        whHeader.setBilldate(lastApproveTime);
                        whHeader.setPkBilltype("262X");
                        whHeader.setPkTradetype("2621");
                        whHeader.setBillno(current.getDocumentNum());
                        whHeader.setReason(current.getDescription());
                        whHeader.setBillstatus("1");
                        whHeader.setIsneedimag("N");
                        whHeader.setIsexpedited("N");

                        ArrayList<com.cloudpense.expman.entity.hsf.claim.withholding.ItemType> whItems = new ArrayList<>();
                        com.cloudpense.expman.entity.hsf.claim.withholding.ItemType whItem;
                        List<JAXBElement<String>> whItemList;
                        com.cloudpense.expman.entity.hsf.claim.withholding.ObjectFactory whof = new com.cloudpense.expman.entity.hsf.claim.withholding.ObjectFactory();
                        for (ExpClaimLine currentLine :
                                lineList) {
                            whItem = new com.cloudpense.expman.entity.hsf.claim.withholding.ItemType();
                            whItemList = whItem.getPkAccruedDetailOrRownoOrAssumeOrg();
                            whItemList.add(whof.createItemTypeAmount(String.valueOf(currentLine.getFinReceiptAmount())));
                            whItemList.add(whof.createItemTypeOrgAmount(String.valueOf(currentLine.getFinReceiptAmount())));
                            whItemList.add(whof.createItemTypeAssumeOrg(branchCode));
                            whItemList.add(whof.createItemTypeAssumeDept(chargeDeptCode));
                            whItemList.add(whof.createItemTypeRestAmount(String.valueOf(currentLine.getFinReceiptAmount())));
                            whItemList.add(whof.createItemTypeOrgRestAmount(String.valueOf(currentLine.getFinReceiptAmount())));
                            whItemList.add(whof.createItemTypeDefitem12(getAccSub(currentLine.getDrAccountId())));
                            whItemList.add(whof.createItemTypeDefitem13(getAccSub(currentLine.getTaxAccountId())));
                            whItemList.add(whof.createItemTypeDefitem14(getAccSub(currentLine.getCrAccountId())));
                            whItemList.add(whof.createItemTypeDefitem15(String.valueOf(currentLine.getFinNetAmount())));
                            whItemList.add(whof.createItemTypeDefitem16(String.valueOf(currentLine.getFinTaxAmount())));
                            whItemList.add(whof.createItemTypeDefitem17(branchCode));
                            whItems.add(whItem);
                        }

                        whHeader.setAccruedDetail(whItems);

                        whBill.setBillhead(whHeader);

                        whRoot.setBill(whBill);
                        httpBody = JaxbUtil.convertToXml(whRoot);
                        break;
                    case "E":
                        //报销单 com.cloudpense.expman.entity.hsf.claim.reimburse
                        com.cloudpense.expman.entity.hsf.claim.reimburse.UfinterfaceType reimRoot = new com.cloudpense.expman.entity.hsf.claim.reimburse.UfinterfaceType();
                        reimRoot.setAccount("nknc");
                        reimRoot.setBilltype("264X");
                        reimRoot.setSender("HKOA");
                        com.cloudpense.expman.entity.hsf.claim.reimburse.BillType reimBill = new com.cloudpense.expman.entity.hsf.claim.reimburse.BillType();
                        reimBill.setId(current.getDocumentNum());
                        com.cloudpense.expman.entity.hsf.claim.reimburse.BillheadType reimHead = new com.cloudpense.expman.entity.hsf.claim.reimburse.BillheadType();
                        reimHead.setPkGroup("HKJT");
                        reimHead.setPkOrg(branchCode);
                        reimHead.setPkPayorg(branchCode);
                        reimHead.setFydwbm(branchCode);
                        reimHead.setFydeptid(chargeDeptCode);
                        reimHead.setDwbm(branchCode);
                        reimHead.setDeptid(submitDeptCode);
                        reimHead.setJkbxr(cPsnCode);
                        reimHead.setOperator("HKOA");
                        reimHead.setZyx30(hsfMapper.getBankAcconut(current.getChargeUser()));
                        reimHead.setReceiver(sPsnCode);
                        reimHead.setDjrq(lastApproveTime);
                        reimHead.setShrq(lastApproveTime);
//                    reimHead.setApprover(lastApprover);
                        reimHead.setJsrq(lastApproveTime);
                        reimHead.setPaydate(lastApproveTime);
                        reimHead.setPayman("NC");
                        reimHead.setPayflag("2");
                        reimHead.setDjlxbm("264X-Cxx-001");
                        reimHead.setBzbm(current.getCurrencyCode());
                        reimHead.setDjzt("1");
                        reimHead.setZy(current.getDescription());
                        reimHead.setTotal(String.valueOf(current.getTotalClaimAmount()));
                        reimHead.setBbje(String.valueOf(current.getTotalClaimAmount()));
                        reimHead.setYbje(String.valueOf(current.getTotalClaimAmount()));
                        reimHead.setZfbbje(String.valueOf(current.getTotalPayAmount()));
                        reimHead.setZfybje(String.valueOf(current.getTotalPayAmount()));
                        reimHead.setIscheck("N");
                        reimHead.setSpzt("3");
                        reimHead.setQcbz("N");
                        reimHead.setKjqj(String.valueOf(calendar.get(Calendar.MONTH) + 1));
                        reimHead.setKjnd(String.valueOf(calendar.get(Calendar.YEAR)));

                        repayAmount = 0;
                        com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType erbsitem;
                        List<com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType> erbsitems = new ArrayList<>();
                        com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType erbxitem;
                        List<com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType> erbxitems = new ArrayList<>();
                        com.cloudpense.expman.entity.hsf.claim.reimburse.ObjectFactory rbof = new com.cloudpense.expman.entity.hsf.claim.reimburse.ObjectFactory();
                        for (ExpClaimLine currentLine : lineList
                        ) {
                            ExpClaimLine linkedLine = hsfMapper.queryLinkLine(currentLine.getLineId());
                            if (currentLine.getExpType().getTypeId() != 461118) {
                                erbsitem = new com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType();
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeBbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeYbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeAmount(String.valueOf(currentLine.getFinClaimAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeZfbbje(String.valueOf(currentLine.getFinPayAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeZfybje(String.valueOf(currentLine.getFinPayAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCjkbbje(String.valueOf(currentLine.getFinClaimAmount() - currentLine.getFinPayAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCjkybje(String.valueOf(currentLine.getFinClaimAmount() - currentLine.getFinPayAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem12(getAccSub(currentLine.getDrAccountId())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem13(getAccSub(currentLine.getTaxAccountId())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem14(getAccSub(currentLine.getCrAccountId())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem15(String.valueOf(currentLine.getFinNetAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem16(String.valueOf(currentLine.getFinTaxAmount())));
                                erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem17(branchCode));
                                if (current.getHeaderTypeId() == 134934) {
                                    erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem18(current.getColumn11()));
                                    erbsitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDefitem19(current.getColumn1()));
                                }
                                erbsitems.add(erbsitem);
                            }
                            if (currentLine.getExpType().getTypeId() == 461118) {
                                repayAmount += currentLine.getFinReceiptAmount();
                                linkHeader = hsfMapper.queryClaimHeader(currentLine.getLinkHeaderId());
                                if (linkHeader == null) {
                                    linkHeader = new ExpClaimHeader();
                                }
                                erbxitem = new com.cloudpense.expman.entity.hsf.claim.reimburse.ItemType();
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypePkBusitem(String.valueOf(linkedLine.getLineId())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypePkJkd(linkHeader.getDocumentNum()));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeJkdjbh(linkHeader.getDocumentNum()));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCxrq(lastApproveTime));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDjlxbm("263X-Cxx-001"));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCjkybje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCjkbbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeBbhl("1"));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeFyybje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeFybbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCxnd(String.valueOf(calendar.get(Calendar.YEAR))));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeCxqj(String.valueOf(calendar.get(Calendar.MONTH) + 1)));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeYbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeBbje(String.valueOf(currentLine.getFinClaimAmount())));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypePkOrg(branchCode));

                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeSxrq(lastApproveTime));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeDeptid(submitDeptCode));
                                erbxitem.getPkReimtypeOrDwbmOrDeptid().add(rbof.createItemTypeJkbxr(sPsnCode));

                                erbxitems.add(erbxitem);
                            }
                        }

                        reimHead.setCjkybje(String.valueOf(repayAmount));
                        reimHead.setCjkbbje(String.valueOf(repayAmount));
                        reimHead.setErBusitem(erbsitems);
                        reimHead.setErBxcontrast(erbxitems);

                        reimBill.setBillhead(reimHead);
                        reimRoot.setBill(reimBill);

                        httpBody = JaxbUtil.convertToXml(reimRoot);
                        break;
                    case "P":
                        //付款单 com.cloudpense.expman.entity.hsf.claim.payment
                        com.cloudpense.expman.entity.hsf.claim.payment.ObjectFactory payOF = new com.cloudpense.expman.entity.hsf.claim.payment.ObjectFactory();
                        com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface payRoot = new com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface();
                        payRoot.setAccount("nknc");
                        payRoot.setBilltype("F3");
                        payRoot.setSender("HKOA");
                        payRoot.setOperator(rb.getString("hsf.claim_push.operator"));

                        String supplierCode = "", supplierAccount, supplierAccountNum;

                        com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill payBill = new com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill();
                        payBill.setId(current.getDocumentNum());
                        com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead payHead = new com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead();
                        List<JAXBElement<?>> payHeadData = payHead.getPkGroupOrPkFiorgOrBillno();
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkGroup("HKJT"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkFiorg(branchCode));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkOrg(branchCode));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkTradetype("D3"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkBilltype("F3"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkCurrtype(current.getCurrencyCode()));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadPkBusitype("HTFK"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadMoney(String.valueOf(current.getTotalAmount())));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadLocalMoney(String.valueOf(current.getTotalAmount())));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadGroupnotaxDe(String.valueOf(current.getTotalAmount())));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadBilldate(lastApproveTime));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadBodysItemObjtype("1"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadSyscode("1"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadSrcSyscode("0"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadBillmaker("HKOA"));
//                        payHeadData.add(payOF.createUfinterfaceBillBillheadRecaccount(supplierAccount));
                        // 24998
                        supplierAccountNum = hsfMapper.querySupplierAccountNum(current.getSupplierAccountId());
                        supplierAccountNum = supplierAccountNum == null ? "" : supplierAccountNum;
                        payHeadData.add(payOF.createUfinterfaceBillBillheadObjtype("1"));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadRecaccount(supplierAccountNum));// 供应商银行账号


                        com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead.Bodys payBody = new com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead.Bodys();
                        List<JAXBElement<String>> payItemData;
                        com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead.Bodys.Item payItem;
                        for (ExpClaimLine currentLine :
                                lineList) {
                            ExpClaimLine linkLine = hsfMapper.queryLinkLine(currentLine.getLineId());
                            if (linkLine == null) {
                                linkLine = new ExpClaimLine();
                            }
                            if (linkHeader.getSupplierId() == 0) {
                                linkHeader = hsfMapper.queryClaimHeader(currentLine.getLinkHeaderId());
                                if (linkHeader == null) {
                                    linkHeader = new ExpClaimHeader();
                                }
                            }
                            if (current.getHeaderTypeId() != 134928) {
                                supplierCode = linkHeader.getColumn11();
                                if (supplierCode == null || "".equals(supplierCode)) {
                                    supplierCode = hsfMapper.querySupplierCode(current.getSupplierId());
                                    supplierAccount = current.getColumn1();
                                }
                            } else {
                                supplierCode = hsfMapper.querySupplierCode(current.getSupplierId());
                                supplierAccount = current.getColumn10();
                            }
                            payItem = new com.cloudpense.expman.entity.hsf.claim.payment.Ufinterface.Bill.Billhead.Bodys.Item();
                            payItemData = payItem.getPkPayitemOrPkGroupOrPkFiorg();

                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemContractno(linkHeader.getColumn3()));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemScomment(currentLine.getComments()));
                            if (current.getHeaderTypeId() == 130826) {
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemPrepay("1"));
                            } else {
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemPrepay("0"));
                            }
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemRate("1"));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemTopBillid(linkHeader.getColumn8()));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemSrcBillid(linkHeader.getColumn8()));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemTopItemid(linkLine.getColumn10()));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemSrcItemid(linkLine.getColumn10()));
                            if (current.getHeaderTypeId() == 134929 || current.getHeaderTypeId() == 130826) {
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemTopBilltype("FCT1"));
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemSrcBilltype("FCT1"));
                            } else {
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemTopBilltype(""));
                                payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemSrcBilltype(""));
                            }
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemPkRecpaytype(linkLine.getColumn11()));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemMoneyDe(String.valueOf(currentLine.getFinClaimAmount())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef12(getAccSub(currentLine.getDrAccountId())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef13(getAccSub(currentLine.getTaxAccountId())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef14(getAccSub(currentLine.getCrAccountId())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef15(String.valueOf(currentLine.getFinNetAmount())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef16(String.valueOf(currentLine.getFinTaxAmount())));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemDef17(branchCode));
                            payItemData.add(payOF.createUfinterfaceBillBillheadBodysItemSupplier(supplierCode));
                            // 24998
                            payItemData.add(payOF.createUfinterfaceBillBillheadObjtype("1"));
                            payItemData.add(payOF.createUfinterfaceBillBillheadPkCurrtype("CNY"));
                            payItemData.add(payOF.createUfinterfaceBillBillheadRecaccount(supplierAccountNum));// 供应商银行账号


                            payBody.getItem().add(payItem);
                        }

                        payHeadData.add(payOF.createUfinterfaceBillBillheadSupplier(supplierCode));
                        payHeadData.add(payOF.createUfinterfaceBillBillheadBodys(payBody));

                        payBill.setBillhead(payHead);
                        payRoot.setBill(payBill);

                        httpBody = JaxbUtil.convertToXml(payRoot);
                        break;
                    default:
                        continue loop;
                }


                if (httpBody == null) {
                    logger.info("{}单据{}推送内容为空不予推送", LOG_KEY, documentNum);
                    continue;
                }
                logger.info("{}单据{}，url={}, 请求={}", LOG_KEY, documentNum, url, httpBody);
                xmlEntity = new HttpEntity<>(httpBody, headers);
                response = restTemplate.postForObject(url, xmlEntity, String.class);
                logger.info("{}单据{}，url={}, 响应={}", LOG_KEY, documentNum, url, response);
                if (response.contains("successful=\"Y\"")) {
                    hsfMapper.updateExternalStatus(current.getHeaderId(), "success", "");
                } else {
                    try {
                        extMsg = ((Element) ((Element) DocumentHelper.parseText(response).getRootElement().elementIterator("sendresult").next()).elementIterator("resultdescription").next()).getStringValue();
                        if (extMsg.length() > 255) {
                            extMsg = extMsg.substring(0, 255);
                        }
                    } catch (DocumentException e) {
                        e.printStackTrace();
                        extMsg = "未知错误";
                    }
                    if (extMsg.contains("相同流水号的单据正在被导入")) {
                        hsfMapper.updateExternalStatus(current.getHeaderId(), "success", extMsg);
                    } else {
                        hsfMapper.updateExternalStatus(current.getHeaderId(), "failed", extMsg);
                    }
                }
            } catch (Exception e) {
                logger.info("{}单据{}出现未知异常：{}", LOG_KEY, documentNum, e.getMessage(), e);
                extMsg = e.getMessage();
                if (extMsg != null && extMsg.length() > 125) {
                    extMsg = extMsg.substring(0, 125);
                }
                hsfMapper.updateExternalStatus(current.getHeaderId(), "failed", extMsg);
            }
        }
    }

    private String getAccSub(int id) {
        String result = "";
        if (id > 0) {
            result = hsfMapper.queryAccountingSubject(id);
        }
        return (result == null) ? "" : result;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public JSONObject saveOAContract(String input) {
        hsfMapper.oaContractLog(input);
        String LOCAL_FILE_PATH = rb.getString("hsf.local_file_path");
        String LOCAL_FILE_FOLDER = LOCAL_FILE_PATH + File.separator;
        //Connect SFTP server
        try {
            String tempPath = LOCAL_FILE_FOLDER + UUID.randomUUID().toString().replaceAll("-", "");
            File dir = new File(tempPath);
            if (!dir.exists()) {
                dir.setWritable(true, false);
                if (!dir.mkdir()) {
                    throw new ValidationException("创建文件夹失败!");
                }
            }

            HSFS3Util hsfs3Util = new HSFS3Util();
            SFTPUtil sftpUtil = new SFTPUtil(rb.getString("hsf.ftp_user_name"), rb.getString("hsf.ftp_password"), rb.getString("hsf.ftp_server_ip"), Integer.parseInt(rb.getString("hsf.ftp_server_port")));
            sftpUtil.login();

            JSONObject json = new JSONObject();
            JSONArray atts = new JSONArray();

            JSONObject result = new JSONObject();
            JSONObject temp;
            String name, path, uuid;
            JSONObject context = JSON.parseObject(input);
            String oaid = context.getString("contract_code");
            ArrayList<String> existedFileList = hsfMapper.queryContractAtts(oaid);

            //从sftp服务器下载文件
            JSONArray fileList = context.getJSONArray("file_list");
            try {
                for (Object file : fileList
                ) {
                    temp = (JSONObject) file;
                    name = temp.getString("file_name");
                    if (existedFileList.contains(name)) {
                        continue;
                    }
                    path = temp.getString("file_path");

                    //Download file from SFTP to local
                    sftpUtil.download(path, name, tempPath + File.separator + name);

                    //Upload file to AmazonS3 server
                    uuid = hsfs3Util.upload(tempPath, name);


                    temp.put("attachment_url", uuid);
                    atts.add(temp);

                    //Delete local file
                    //noinspection ResultOfMethodCallIgnored
                    new File(tempPath + File.separator + name).delete();
                }

                //Update database
                json.put("oafctno", oaid);
                json.put("attachments", atts);
                logger.info("OA合同保存参数：" + json.toJSONString());
                hsfMapper.saveContractOA(json.toJSONString());

                result.put("errcode", 0);
                result.put("errmsg", "ok");
            } catch (Exception e) {
                logger.error("saveOAContract方法异常1", e);
                result.put("errcode", 500);
                result.put("errmsg", "网络异常,请重试");
            } finally {
                sftpUtil.logout();
                for (File file : dir.listFiles()) {
                    file.delete();
                }
                dir.delete();
            }
            return result;
        } catch (Exception e) {
            logger.error("saveOAContract异常2", e);
            return null;
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateNCContract(boolean fullSync) {
        String LOG_KEY = "HSF同步合同信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String start, end, response;
        JSONObject xmljson = new JSONObject();

        ArrayList<String> orgCodeList = hsfMapper.queryOrgCode();
        //WebService 客户端
        try {
            ContractStub stub = new ContractStub();
            ContractStub.QueryFctinfo info = new ContractStub.QueryFctinfo();
            ContractStub.QueryFctinfoResponse infoRep;

            calendar.add(Calendar.DAY_OF_MONTH, 2);
            end = sdf.format(calendar.getTime());
            if (fullSync) {
                calendar.add(Calendar.MONTH, -6);
                start = sdf.format(calendar.getTime());
            } else {
                calendar.add(Calendar.MONTH, -1);
                start = sdf.format(calendar.getTime());
            }
            xmljson.put("begindate", start);
            xmljson.put("enddate", end);

            for (String orgCode : orgCodeList) {
                xmljson.put("orgcode", orgCode);
                info.setJsonstr(xmljson.toJSONString());
                logger.info("NC合同查询参数：" + xmljson.toJSONString());

                logger.info("{}get orgcode={} contract params:", LOG_KEY, orgCode, xmljson.toJSONString());
                infoRep = stub.queryFctinfo(info);
                response = infoRep.get_return();
                logger.info("{}get orgcode={} contract result:", LOG_KEY, orgCode, response);
                response = JSON.parseObject(response).toJSONString();
                logger.info("NC合同查询结果：" + response);

                hsfMapper.saveContractNC(response, orgCode);
            }
        } catch (RemoteException axisFault) {
            logger.error("{}updateNCContract方法====================出现异常", LOG_KEY, axisFault);
        }


    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateBankAccount(boolean fullSync) {
        String LOG_KEY = "HSF同步银行账户信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String start, end, response;

        //WebService 客户端
        ItfGetInformation infoService = new ItfGetInformation();
        ItfGetInformationPortType client = infoService.getItfGetInformationSOAP11PortHttp();
        JSONObject xmljson = new JSONObject();
        xmljson.put("num", "100");

        ArrayList<String> orgCodeList = hsfMapper.queryOrgCode();
        if (fullSync) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);

            while (calendar.get(Calendar.YEAR) > 2018
                    || (calendar.get(Calendar.YEAR) == 2018 && calendar.get(Calendar.MONTH) >= 10)) {
                end = sdf.format(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -30);
                start = sdf.format(calendar.getTime());
                xmljson.put("startDate", start);
                xmljson.put("endDate", end);
                for (String orgCode : orgCodeList) {
                    if (orgCode != null && orgCode.length() > 0) {
                        xmljson.put("orgCode", orgCode);
                        int page = 0;
                        do {
                            page++;
                            xmljson.put("pages", String.valueOf(page));
                            logger.info("{}get page={} bankaccount params:", LOG_KEY, page, xmljson.toJSONString());
                            response = client.getPsnbankInfo(xmljson.toJSONString());
                            logger.info("{}get page={} bankaccount params:", LOG_KEY, page, response);
                            response = JSON.parseObject(response).toJSONString();
                            if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                                hsfMapper.updateBankAcc(response);
                            }
                        } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                    }
                }
            }
        } else {
            start = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);
            for (String orgCode : orgCodeList) {
                if (orgCode != null && orgCode.length() > 0) {
                    xmljson.put("orgCode", orgCode);
                    int page = 0;
                    do {
                        page++;
                        xmljson.put("pages", String.valueOf(page));
                        logger.info("{}get page={} bankaccount params:", LOG_KEY, page, xmljson.toJSONString());
                        response = client.getPsnbankInfo(xmljson.toJSONString());
                        logger.info("{}get page={} bankaccount params:", LOG_KEY, page, response);
                        response = JSON.parseObject(response).toJSONString();
                        if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                            hsfMapper.updateBankAcc(response);
                        }
                    } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateUser(boolean fullSync) {
        String LOG_KEY = "HSF同步用户信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));// 服务器是UTC时间，需换算成东八区处理
        Calendar calendar = Calendar.getInstance();
        String start, end, response;

        //WebService 客户端
        ItfGetInfoOfHR infoService = new ItfGetInfoOfHR();
        ItfGetInfoOfHRPortType client = infoService.getItfGetInfoOfHRSOAP11PortHttp();
        JSONObject xmljson = new JSONObject();
        xmljson.put("num", "100");

        ArrayList<String> orgCodeList = hsfMapper.queryOrgCode();
        if (fullSync) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            start = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);
            for (String orgCode : orgCodeList) {
                if (orgCode != null && orgCode.length() > 0) {
                    xmljson.put("orgCode", orgCode);
                    int page = 0;
                    do {
                        page++;
                        xmljson.put("pages", String.valueOf(page));
                        logger.info("{}get page={} users params:", LOG_KEY, page, xmljson.toJSONString());
                        response = client.getPsnInfo(xmljson.toJSONString());
                        logger.info("{}get page={} users result:", LOG_KEY, page, response);
                        response = JSON.parseObject(response).toJSONString();
                        if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                            hsfMapper.updateUser(response);
                        }
                    } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                }
            }
        } else {
            start = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);
            for (String orgCode : orgCodeList) {
                if (orgCode != null && orgCode.length() > 0) {
                    xmljson.put("orgCode", orgCode);
                    int page = 0;
                    do {
                        page++;
                        xmljson.put("pages", String.valueOf(page));
                        logger.info("{}get page={} users params:", LOG_KEY, page, xmljson.toJSONString());
                        response = client.getPsnInfo(xmljson.toJSONString());
                        logger.info("{}get page={} users result:", LOG_KEY, page, response);
                        response = JSON.parseObject(response).toJSONString();
                        if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                            hsfMapper.updateUser(response);
                        }
                    } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                }
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updatePosition(boolean fullSync) {
        String LOG_KEY = "HSF同步职位信息===";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();
        String start, end, response;

        //WebService 客户端
        ItfGetInfoOfHR infoService = new ItfGetInfoOfHR();
        ItfGetInfoOfHRPortType client = infoService.getItfGetInfoOfHRSOAP11PortHttp();
        JSONObject xmljson = new JSONObject();
        xmljson.put("num", "100");

        ArrayList<String> orgCodeList = hsfMapper.queryOrgCode();
        if (fullSync) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);

            while (calendar.get(Calendar.YEAR) > 2018
                    || (calendar.get(Calendar.YEAR) == 2018 && calendar.get(Calendar.MONTH) >= 10)) {
                end = sdf.format(calendar.getTime());
                calendar.add(Calendar.DAY_OF_MONTH, -30);
                start = sdf.format(calendar.getTime());
                xmljson.put("startDate", start);
                xmljson.put("endDate", end);
                for (String orgCode : orgCodeList) {
                    if (orgCode != null && orgCode.length() > 0) {
                        xmljson.put("orgCode", orgCode);
                        int page = 0;
                        do {
                            page++;
                            xmljson.put("pages", String.valueOf(page));
                            logger.info("{}get page={} position params:", LOG_KEY, page, xmljson.toJSONString());
                            response = client.getPostInfo(xmljson.toJSONString());
                            logger.info("{}get page={} position result:", LOG_KEY, page, response);
                            response = JSON.parseObject(response).toJSONString();
                            if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                                hsfMapper.updatePosition(response, orgCode);
                            }
                        } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                    }
                }
            }
        } else {
            start = sdf.format(calendar.getTime());
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            end = sdf.format(calendar.getTime());
            xmljson.put("startDate", start);
            xmljson.put("endDate", end);
            for (String orgCode : orgCodeList) {
                if (orgCode != null && orgCode.length() > 0) {
                    xmljson.put("orgCode", orgCode);
                    int page = 0;
                    do {
                        page++;
                        xmljson.put("pages", String.valueOf(page));
                        logger.info("{}get page={} position params:", LOG_KEY, page, xmljson.toJSONString());
                        response = client.getPostInfo(xmljson.toJSONString());
                        logger.info("{}get page={} position result:", LOG_KEY, page, response);
                        response = JSON.parseObject(response).toJSONString();
                        if (!(response.contains("\"data\":\"\"") || "".equals(response))) {
                            hsfMapper.updatePosition(response, orgCode);
                        }
                    } while (!(response.contains("\"msg\":\"未找到数据\"") || "".equals(response)));
                }
            }
        }
    }

    @Override
    public JSONObject getErrorPushedClaims(String startDate, String endDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        JSONObject result = new JSONObject();
        try {
            if (StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
                result.put("message", "开始日期或结束日期不能为空");
            } else {
                Date start = format.parse(startDate);
                start = new Date(start.getTime() - 1000 * 3600 * 8);
                Date end = format.parse(endDate);
                end = new Date(end.getTime() - 1000 * 3600 * 8);

                if (differentDaysByMillisecond(start, end) > 7) {
                    result.put("message", "开始结束日期不能超过7天");
                } else {
                    List<Map<String, String>> errorPushedClaims = hsfMapper.getErrorPushedClaims(format2.format(start), format2.format(end));
                    result.put("data", errorPushedClaims);
                }
            }
        } catch (ParseException e) {
            logger.error("查询出错", e);
            result.put("message", "查询异常");
        }

        return result;
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private int differentDaysByMillisecond(Date startDate, Date endDate) {
        int days = (int) ((endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24));
        return days;
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 16);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        String end = sdf.format(calendar.getTime());
        System.out.println("UTC时间：" + end);
        System.out.println("当前时区：" + calendar.getTimeZone());

        calendar.add(Calendar.DAY_OF_MONTH, 1);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        end = sdf.format(calendar.getTime());
        System.out.println("东八区时间(+1)：" + end);

    }
}