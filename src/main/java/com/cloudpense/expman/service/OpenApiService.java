package com.cloudpense.expman.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;

public interface OpenApiService {

    public int authCompany(String accessToken) throws Exception;

    public JSONObject generateRtn(String type, Object data, String errMsg, int errCode) throws Exception;

    public HashMap<String, String> createAllAttachmentsURL(JSONArray requirement, int companyId) throws Exception;

    public void asynchronousBudget(JSONObject input);

    public void changeVoucherStatus(JSONArray claimVos, String status, String glMessage);
}
