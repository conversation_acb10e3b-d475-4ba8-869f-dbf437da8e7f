package com.cloudpense.expman.service;

import com.cloudpense.expman.Request.WshhSapReturnReq;

import java.io.IOException;
import java.util.Date;

/**
 * <AUTHOR>
 * 文思海辉service
 */
public interface WshhService {

    //出差报销单推送
    public void claimPush();
    //推送单据到sap
    String sapPush(Date pushDate, Date pushDateTo) throws IOException;
    //SAP传输结果
    String sapReturn(WshhSapReturnReq wshhSapReturnReq) throws IOException;
}
