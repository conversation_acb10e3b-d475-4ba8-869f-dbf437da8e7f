package com.cloudpense.expman.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.OpenApiMapper;
import com.cloudpense.expman.service.AsynService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.util.HttpForwardUtil;
import com.cloudpense.expman.util.S3.S3Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON_UTF;

@RestController
@RequestMapping(value = "api/v2/common")
public class OpenApiRestController {

    private OpenApiService openApiService;
    private OpenApiMapper openApiMapper;

    private static final Logger logger = LoggerFactory.getLogger(OpenApiRestController.class);

    public static final String patt = "(?<=\\/)[A-z0-9\\-.]*(?=\\?)";
    public static final String patt2 = "(?:[^/][\\d\\w\\.]+)+$";

    // 单据附件下载临时目录
    public static final String savePath = "/home/<USER>/projects/document/attachmentFile";

    @Autowired
    public OpenApiRestController(OpenApiService openApiService,
                                 OpenApiMapper openApiMapper) {
        this.openApiService = openApiService;
        this.openApiMapper = openApiMapper;
    }

    @Autowired
    HttpForwardUtil httpForwardUtil;

    /**
     * @apiDefine apiDepartmentGroup 部门管理
     */

    /**
     * @api {POST} /api/v2/common/depart?access_token=ACCESS_TOKEN 创建部门
     * @apiGroup apiDepartmentGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用部门。以部门代码作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} department_code 部门代码必须在公司内唯一
     * @apiParam (入参) {String} department_name 部门名称
     * @apiParam (入参) {String} type B-分公司，D-部门，C-成本中心
     * @apiParam (入参) {String} enabled_flag Y-启用，N-禁用
     * @apiParam (入参) {String} parent_department_code 父级部门代码
     * @apiParam (入参) {String} supervisor 用于审批流程
     * @apiParam (入参) {String} approval_level 用于审批权限设置：
     * @apiParam (入参) {String} sequence_num 用于显示平级部门顺序
     * @apiParam (入参) {String} [description] 描述
     * @apiParam (入参) {String} [column1] 预留字段，企业自定义字段，最多十个
     * @apiParamExample {json} 请求样例：
    {
    "departments": [
    {
    "department_code": "D010",
    "department_name": "测试部",
    "type": "D",
    "enabled_flag": "Y",
    "description": "产品测试",
    "parent_department_code": "DO02",
    "supervisor": "<EMAIL>",
    "approval_level": "1",
    "sequence_num": "100",
    "email_address": "<EMAIL>"
    }
    ]
    }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */

    @RequestMapping(value = "depart", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateDepartment(@RequestParam(value = "access_token") String accessToken,
                                                @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("departments");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 部门同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("department");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "depart/costcenter/stafflist", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateCostCenterRange(@RequestParam(value = "access_token") String accessToken,
                                            @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        String errorResult = "";
        boolean hasError = false;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("cost_center_range");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 成本中心范围同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("cost_center_range");
            try {
                openApiMapper.commonUpdate(fndQuery);
                if (fndQuery.getReturnCode().equals("S")) {

                } else {
                    hasError = true;
                    errorResult += fndQuery.getReturnMessage();
                }
            } catch (Exception e) {
                logger.error("数据库执行异常", e);
                hasError = true;
                errorResult += "数据库执行异常: " + jsonArray.toJSONString();
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        if (hasError) {
            return openApiService.generateRtn(null, null, errorResult, 1002);
        } else {
            return openApiService.generateRtn(null, null, null, 0);
        }
    }

    /**
     * @apiDefine apiUserGroup 成员管理
     */

    /**
     * @api {POST} /api/v2/common/user?access_token=ACCESS_TOKEN 创建成员
     * @apiGroup apiUserGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用成员。以员工编号作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} company_code 所在公司编码
     * @apiParam (入参) {String} email_address 电子邮箱地址
     * @apiParam (入参) {String} enabled_flag 启用标志
     * @apiParam (入参) {String} full_name 全名
     * @apiParam (入参) {String} department_code 部门代码
     * @apiParam (入参) {String} employee_number 员工编号
     * @apiParam (入参) {String} mobile 手机号
     * @apiParam (入参) {String} [line_manager] 直线经理
     * @apiParam (入参) {String} [gender] 性别
     * @apiParam (入参) {String} [birthday] 生日
     * @apiParam (入参) {String} [date_of_joining] 入职日期
     * @apiParam (入参) {String} [resignation_date] 离职日期
     * @apiParam (入参) {String} [nationality] 国籍
     * @apiParam (入参) {String} [cost_center_code] 成本中心编码
     * @apiParam (入参) {String} [bank_name] 银行名称
     * @apiParam (入参) {String} [bank_branch] 支行名称
     * @apiParam (入参) {String} [bank_code] 银行代码
     * @apiParam (入参) {String} [account_number] 银行卡号
     * @apiParam (入参) {String} [account_name] 账户名
     * @apiParam (入参) {String} [level] 人员等级
     * @apiParam (入参) {String} [position] 职位
     * @apiParam (入参) {String} [column1-50] 预留字段，企业自定义字段，最多五十个
     * @apiParamExample {json} 请求样例：
    {
    "users": [
    {
    "gender": "M",
    "email_address": "<EMAIL>",
    "enabled_flag": "Y",
    "full_name": "王海波",
    "department_code": "D010",
    "employee_number": "E0005",
    "mobile": "***********",
    "gender": "M",
    "birthday": "1993-07-14",
    "date_of_joining": "2017-05-02",
    "resignation_date": "2017-05-03",
    "nationality": "CHN"
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */
    @RequestMapping(value = "user/update", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateUser(@RequestParam(value = "access_token") String accessToken,
                                       @RequestBody String json) throws Exception {
        String KEYNAME = "人员同步数据===";
        logger.info("{}收到请求：{}", KEYNAME, json);
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("users");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("{}companyId: {}, company schema: {}, 人员同步数据为空", KEYNAME, companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("{}输入JSON解析出错{}", KEYNAME, e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("employee");
            openApiMapper.commonUpdate(fndQuery);
            logger.info("{}执行结果: {}", KEYNAME, JSON.toJSONString(fndQuery));
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("{}数据库执行异常{}", KEYNAME, e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @apiDefine apiAccountGroup 财务设置
     */

    /**
     * @api {POST} /api/v2/common/currency?access_token=ACCESS_TOKEN 创建汇率
     * @apiGroup apiAccountGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新汇率，支持批量更新
     * @apiParam (入参) {String} currency_code 货币
     * @apiParam (入参) {String} rate 相对于人民币的汇率
     * @apiParamExample {json} 请求样例：
    {
    "currency": [
    {
    "currency_code": "USD",
    "rate": 6.8917
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */

    @RequestMapping(value = "currency", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject taxUpdate(@RequestParam(value = "access_token") String accessToken,
                                 @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("currency");
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("currency");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @api {POST} /api/v2/common/budgetaccount?access_token=ACCESS_TOKEN 创建预算科目
     * @apiGroup apiAccountGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用预算科目。以科目代码作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} budget_code 预算科目代码
     * @apiParam (入参) {String} budget_name 预算科目名称
     * @apiParam (入参) {String} [parent_budget_code] 父级预算科目编码
     * @apiParam (入参) {String} enabled_flag 是否启用，默认启用
     * @apiParamExample {json} 请求样例：
    {
    "budget_account": [
    {
    "budget_code": "B0000101",
    "budget_name": "预算科目名称",
    "parent_budget_code": "B00001",
    "enabled_flag": "Y"
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */
    @RequestMapping(value = "budgetaccount", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject budgetAccountUpdate(@RequestParam(value = "access_token") String accessToken,
                                @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("budget_account");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 预算科目同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("budget_account");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    /**
     * @api {POST} /api/v2/common/glaccount?access_token=ACCESS_TOKEN 创建预算科目
     * @apiGroup apiAccountGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用会计科目。以科目代码作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} account_code 会计科目代码
     * @apiParam (入参) {String} account_name 会计科目名称
     * @apiParam (入参) {String} enabled_flag 是否启用，默认启用
     * @apiParamExample {json} 请求样例：
    {
    "budget_account": [
    {
    "account_code": "B0000101",
    "account_name": "科目名称",
    "enabled_flag": "Y"
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */

    @RequestMapping(value = "glaccount", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject glAccountUpdate(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("budget_account");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 会计科目数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("gl_account");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @api {POST} /api/v2/common/user?access_token=ACCESS_TOKEN 创建成员银行账号
     * @apiGroup apiUserGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新成员账号，支持批量更新
     * @apiParam (入参) {String} account_number 银行账号
     * @apiParam (入参) {String} bank_name 银行名称
     * @apiParam (入参) {String} bank_code 银行代码
     * @apiParam (入参) {String} account_name 账户名
     * @apiParam (入参) {String} employee_number 员工编号
     * @apiParamExample {json} 请求样例：
    {
    "user_account": [
    {
    "account_number": "6222600263331072444",
    "bank_name": "中国建设银行",
    "bank_code": "************",
    "account_name": "王海波",
    "employee_number": "2000008"
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */
    @RequestMapping(value = "useraccount", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject userAccountUpdate(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("user_account");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 用户银行账号同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("user_account");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    /**
     * @api {POST} /api/v2/common/payment?access_token=ACCESS_TOKEN 付款状态回传
     * @apiGroup apiAccountGroup
     * @apiVersion 0.0.1
     * @apiDescription 付款状态回传
     * @apiParam (入参) {String} document 单据号
     * @apiParam (入参) {String} status 状态，应传固定值为paid，其他值不接受更新
     * @apiParamExample {json} 请求样例：
    {
    "payment_status": [
    {
    "document": "EXP0000000001",
    "status": "paid"
    }
    ]
    }

     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
    {
    "errcode": 0,
    "errmsg": "ok",
    }

     */

    @RequestMapping(value = "payment", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updatePaymentStatus(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            String arrayData = JSONObject.parseObject(json).getString("payment_status");
            jsonArray = JSONObject.parseArray(arrayData);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("payment_status");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @apiDefine apiInvoiceGroup 票据
     */
    /**
     * @api {POST} /api/v2/common/getSingleAttachment?access_token=ACCESS_TOKEN 获取单个附件地址
     * @apiGroup apiInvoiceGroup
     * @apiVersion 0.0.1
     * @apiDescription 获取单个附件地址
     * @apiParam (入参) {String} file_name 文件名
     * @apiParamExample {json} 请求样例：
     * {
     * "file_name": "93eb478c-9001-4641-af3c-40e8552e5f2d.jpg"
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccess (Success 200) {String} url 下载地址(两小时有效)
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * "attachment": {
     * "url": "https://qawhx.s3.cn-north-1.amazonaws.com.cn/93eb478c-9001-4641-af3c-40e8552e5f2d.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20190213T081325Z&X-Amz-SignedHeaders=host&X-Amz-Expires=7200&X-Amz-Credential=%2F20190213%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=38312110f40c0f614b5906bbb595fdf4a85dc3188d879651708b12704733ac7b"
     * }
     * }
     */
    @RequestMapping(value = "getSingleAttachment", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getSingleAttachment(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        openApiService.authCompany(accessToken);
        String fileName;
        try {
            fileName = JSONObject.parseObject(json).getString("file_name");
            if (null == fileName || "".equals(fileName)) {
                throw new ValidationException("无名称");
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url", S3Util.getS3URL(fileName));
        return openApiService.generateRtn("attachment", jsonObject, null, 0);
    }


    /**
     * @api {POST} /api/v2/common/invoice/update?access_token=ACCESS_TOKEN 更新票据状态
     * @apiGroup apiInvoiceGroup
     * @apiVersion 0.0.1
     * @apiDescription 更新票据状态
     * @apiParam (入参) {String} user_id 员工号
     * @apiParam (入参) {String} invoice_num 票据号码
     * @apiParam (入参) {String} status 已使用used，释放unused
     * @apiParamExample {json} 请求样例：
     * {
     * "invoice": [
     * {
     * "user_id": "100000001",
     * "invoice_key": "323",
     * "status": "used"
     * }]
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * }
     */
    @RequestMapping(value = "invoice/update", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject invoiceUpdate(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            String arrayData = JSONObject.parseObject(json).getString("invoice");
            jsonArray = JSONObject.parseArray(arrayData);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        logger.info("mapper start {}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("invoice_update");
            logger.info("invoice_update input: {}", fndQuery);
            openApiMapper.commonUpdate(fndQuery);
            logger.info("invoice_update output: {}", fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @api {POST} /api/v2/common/invoice/query?access_token=ACCESS_TOKEN 查询票据信息
     * @apiGroup apiInvoiceGroup
     * @apiVersion 0.0.1
     * @apiDescription 查询本公司票据信息
     * @apiParam (入参) {String} [user_id] 员工号
     * @apiParam (入参) {String} [category] 票据类型：
     * 01-增值税专用发票
     * 02-货运运输业增值税换用发票
     * 03-机动车销售统一发票
     * 04-增值税普通发票
     * 10-增值税普通发票（电子）
     * 11-增值税普通发票（卷式）
     * 14-增值税普通发票（通行费）
     * 15-二手车销售统一发票
     * 50-通用机打发票
     * 51-定额发票
     * 60-火车票
     * 61-出租车票
     * 62-客运发票
     * 63-通行费专用发票
     * 64-船票
     * 99-其他票据
     * @apiParam (入参) {String} [receipt_date_from] 查询开始日期(发票日期)
     * @apiParam (入参) {String} [receipt_date_to] 查询结束日期(发票日期)
     * @apiParam (入参) {String} [last_update_date_from] 查询开始时间(最后更新时间)
     * @apiParam (入参) {String} [last_update_date_to] 查询结束时间(最后更新时间)
     * @apiParamExample {json} 请求样例：
     * {
     * "user_id": "100000001",
     * "category": "01",
     * "receipt_date_from": "2019-01-01",
     * "receipt_date_to": "2019-01-10",
     * "last_update_date_from": "2019-01-11 11:00:00"
     * "last_update_date_to": "2019-01-11 13:00:00"
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccess (Success 200) {String} category 票据类型：
     * 01-增值税专用发票
     * 02-货运运输业增值税换用发票
     * 03-机动车销售统一发票
     * 04-增值税普通发票
     * 10-增值税普通发票（电子）
     * 11-增值税普通发票（卷式）
     * 14-增值税普通发票（通行费）
     * 15-二手车销售统一发票
     * 50-通用机打发票
     * 51-定额发票
     * 60-火车票
     * 61-出租车票
     * 62-客运发票
     * 63-通行费专用发票
     * 64-船票
     * 99-其他票据
     * @apiSuccess (Success 200) {String} status 票据状态：已验真，已使用，识别中，无法识别
     * @apiSuccess (Success 200) {String} picture 票据影像URL, 生成后两小时内访问有效
     * @apiSuccess (Success 200) {String} picture_filename 票据影像文件名, 之后可调用影像获取接口获得下载地址
     * @apiSuccess (Success 200) {String} comment 票据备注
     * @apiSuccess (Success 200) {String} source_type 票据来源
     * @apiSuccess (Success 200) {String} marks 查验标记
     * @apiSuccess (Success 200) {String} submit_user 创建用户员工号
     * @apiSuccess (Success 200) {String} charge_user 归属用户员工号
     * @apiSuccess (Success 200) {String} create_date 创建时间
     * @apiSuccess (Success 200) {String} last_update_date 更新时间
     * @apiSuccess (Success 200) {json} original_data 票据原始字段
     * @apiSuccess (Success 200) {int} original_data.Category 发票分类：1-增值税专用发票
     * 4-增值税普通发票
     * 10-增值税普通发票（电子）
     * 11-增值税普通发票（卷式）
     * @apiSuccess (Success 200) {String} original_data.Area 省市地区
     * @apiSuccess (Success 200) {String} original_data.Code 发票代码
     * @apiSuccess (Success 200) {String} original_data.No 发票号码
     * @apiSuccess (Success 200) {Double} original_data.SummaryAmount 价税合计
     * @apiSuccess (Success 200) {Double} original_data.Amount 金额
     * @apiSuccess (Success 200) {Double} original_data.TaxAmount 税额
     * @apiSuccess (Success 200) {String} original_data.Remark 备注
     * @apiSuccess (Success 200) {String} original_data.Date 开票日期
     * @apiSuccess (Success 200) {String} original_data.VCode 校验码
     * @apiSuccess (Success 200) {String} original_data.MachineNo 开票机编码
     * @apiSuccess (Success 200) {int} original_data.Status 发票状态：0-蓝票
     * 10-作废
     * 20-红票
     * 30-冲红（满足前提时支持）
     * @apiSuccess (Success 200) {json} original_data.Saler 销售方
     * @apiSuccess (Success 200) {json} original_data.Buyer 购买方
     * @apiSuccess (Success 200) {json} original_data.Items 发票明细
     * @apiSuccess (Success 200) {String} original_data.Buyer.Name 名称
     * @apiSuccess (Success 200) {String} original_data.Buyer.TaxCode 纳税人识别号
     * @apiSuccess (Success 200) {String} original_data.Buyer.AddressPhone 地址、电话
     * @apiSuccess (Success 200) {String} original_data.Buyer.AccountBank 开户行及账号
     * @apiSuccess (Success 200) {String} original_data.Saler.Name 名称
     * @apiSuccess (Success 200) {String} original_data.Saler.TaxCode 纳税人识别号
     * @apiSuccess (Success 200) {String} original_data.Saler.AddressPhone 地址、电话
     * @apiSuccess (Success 200) {String} original_data.Saler.AccountBank 开户行及账号
     * @apiSuccess (Success 200) {String} original_data.Items.Name 货物或应税劳务名称
     * @apiSuccess (Success 200) {String} original_data.Items.Specification 规格型号
     * @apiSuccess (Success 200) {String} original_data.Items.Unit 单位
     * @apiSuccess (Success 200) {int} original_data.Items.Quantity 数量
     * @apiSuccess (Success 200) {Double} original_data.Items.Price 单价
     * @apiSuccess (Success 200) {Double} original_data.Items.Amount 金额
     * @apiSuccess (Success 200) {Double} original_data.Items.TaxRate 税率
     * @apiSuccess (Success 200) {Double} original_data.Items.TaxAmount 税额
     * @apiSuccess (Success 200) {json} user_adjust_data 用户编辑字段
     * @apiSuccess (Success 200) {String} user_adjust_data.code 发票代码
     * @apiSuccess (Success 200) {String} user_adjust_data.no 发票号码
     * @apiSuccess (Success 200) {Double} user_adjust_data.amount 金额
     * @apiSuccess (Success 200) {String} user_adjust_data.currency 币种
     * @apiSuccess (Success 200) {String} user_adjust_data.date 开票日期
     * @apiSuccess (Success 200) {String} user_adjust_data.status 发票状态
     * @apiSuccess (Success 200) {String} user_adjust_data.saler_name 销售方名称
     * @apiSuccess (Success 200) {String} user_adjust_data.saler_taxcode 纳税人识别号
     * @apiSuccess (Success 200) {String} user_adjust_data.buyer_name 销售方名称
     * @apiSuccess (Success 200) {String} user_adjust_data.buyer_taxcode 纳税人识别号
     * @apiSuccess (Success 200) {String} user_adjust_data.user_name 姓名
     * @apiSuccess (Success 200) {String} user_adjust_data.departure time 开车时间
     * @apiSuccess (Success 200) {String} user_adjust_data.train_class 席位
     * @apiSuccess (Success 200) {String} user_adjust_data.destination_city 出发城市
     * @apiSuccess (Success 200) {String} user_adjust_data.destination_city_to 到达城市
     * @apiSuccess (Success 200) {String} user_adjust_data.start_time 上车时间
     * @apiSuccess (Success 200) {String} user_adjust_data.end_time 下车时间
     * @apiSuccess (Success 200) {String} user_adjust_data.location_from 出发地
     * @apiSuccess (Success 200) {String} user_adjust_data.location_to 到达地
     * @apiSuccess (Success 200) {String} user_adjust_data.ship_class 舱位
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * "list": [
     * {
     * "category": "01",
     * "status": "已验真",
     * "picture": "https://qawhx.s3.cn-north-1.amazonaws.com.cn/93eb478c-9001-4641-af3c-40e8552e5f2d.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20190213T081325Z&X-Amz-SignedHeaders=host&X-Amz-Expires=7200&X-Amz-Credential=%2F20190213%2Fcn-north-1%2Fs3%2Faws4_request&X-Amz-Signature=38312110f40c0f614b5906bbb595fdf4a85dc3188d879651708b12704733ac7b",
     * "picture_filename": "93eb478c-9001-4641-af3c-40e8552e5f2d.jpg",
     * "comment": "备注",
     * "source_type": "wechat",
     * "marks": "电子票据",
     * "submit_user": "**********",
     * "charge_user": "**********",
     * "create_date": "2019-01-01 11:00:00",
     * "last_update_date": "2019-01-01 11:00:00",
     * "original_data":
     * {
     * "No": "********",
     * "Area": "福建",
     * "Code": "**********",
     * "Date": "2019-01-31T00:00:00",
     * "Buyer":
     * {
     * "Name": "上海科华生物工程股份有限公司",
     * "TaxCode": "91310000132660318J",
     * "AccountBank": "交行漕河泾支行310066632010210021449",
     * "AddressPhone": "上海市钦州北路1189号021-********"
     * },
     * "Items": [
     * {
     * "Name": "*住宿服务*住宿費",
     * "Unit": "天",
     * "Price": 194.*************,
     * "Amount": 194.17,
     * "TaxRate": 3,
     * "Quantity": 1,
     * "TaxAmount": 5.83,
     * "Specification": ""
     * }],
     * "Saler":
     * {
     * "Name": "三明市三元区泊捷酒店有限公司",
     * "TaxCode": "91350403MA347DN73X",
     * "AccountBank": "福建三明市农村商业银行红杏支行 9030217040010000076462",
     * "AddressPhone": "福建省三明市三元区新市中路239号7幢1-3号 0598-8965888"
     * },
     * "VCode": "75924876033427560523",
     * "Amount": 194.17,
     * "Remark": "",
     * "Status": 0,
     * "Category": 20,
     * "MachineNo": "************",
     * "TaxAmount": 5.83,
     * "OriginalNo": "",
     * "OriginalCode": "",
     * "SummaryAmount": 200
     * },
     * "user_adjust_data":
     * {
     * "amount": 200
     * }
     * }]
     * }
     */
    @RequestMapping(value = "invoice/query", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject invoiceQuery(@RequestParam(value = "access_token") String accessToken,
                                   @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            logger.info("mapper start {}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("invoice_query");
            List<String> strings = openApiMapper.commonUpdate(fndQuery);
            logger.info("mapper end {}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
            JSONArray jsonArray = new JSONArray();
            for (String s : strings) {
                JSONObject jsonObject1 = JSONObject.parseObject(s);
                String file_name = jsonObject1.getString("picture_filename");
                if (file_name != null) {
                    String url = S3Util.getS3URL(file_name);
                    jsonObject1.put("picture", url);
                }
                jsonArray.add(jsonObject1);
            }
            logger.info("getFile end {}", DateUtil.format(new Date(),"yyyy-MM-dd HH:mm:ss.SSS"));
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn("list", jsonArray, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    /**
     * @apiDefine apiDocumentGroup 单据管理
     */

    /**
     * @api {POST} /api/v2/common/document?access_token=ACCESS_TOKEN 创建单据
     * @apiGroup apiDocumentGroup
     * @apiVersion 0.0.1
     * @apiDescription 创建新单据并按预先配置的审批流进行审批，字段是否必填需要按各公司配置
     * @apiParam (入参) {String} header_type_code 申请类型，申请类型编码，由顾问配置好后给出
     * @apiParam (入参) {String} created_by 单据创建人
     * @apiParam (入参) {String} lines 申请行
     * @apiParamExample {json} 请求样例：
     * {
     * "document": {
     * "header_type_code": "T01",
     * "lines": [{
     * "type_code": "T028"
     * }]
     * }
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * }
     */

    /**
     * 从输入流中获取字节数组
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] readInputStream(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        return bos.toByteArray();
    }

    public static void downLoadFromUrl(String urlStr, String fileName, String savePath) throws IOException {
        URL url = new URL(urlStr);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        //设置超时间为3秒
        conn.setConnectTimeout(10 * 1000);
        //防止屏蔽程序抓取而返回403错误
        conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

        //得到输入流
        InputStream inputStream = conn.getInputStream();
        //获取自己数组
        byte[] getData = readInputStream(inputStream);

        //文件保存位置
        File saveDir = new File(savePath);
        if (!saveDir.exists()) {
            saveDir.mkdir();
        }
        File file = new File(saveDir + File.separator + fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(getData);
        if (fos != null) {
            fos.close();
        }
        if (inputStream != null) {
            inputStream.close();
        }

        logger.info("info:" + url + " download success");
    }

    public JSONObject getSingleFile(String url, String savePath) throws Exception {
        JSONObject jsonObject = new JSONObject();
        String fileName = System.currentTimeMillis() + ".unknown";
        Pattern urlName = Pattern.compile(patt);
        Matcher m = urlName.matcher(url);
        if (m.find()) {
            fileName = m.group(0);
        } else {
            Pattern urlName2 = Pattern.compile(patt2);
            Matcher m2 = urlName2.matcher(url);
            if (m2.find()) {
                fileName = m2.group(0);
            }
        }
        String[] arr = fileName.split("\\.");
        String newFileName = UUID.randomUUID() + "." + arr[arr.length - 1];
        downLoadFromUrl(url, newFileName, savePath);
        logger.info("文件{}开始上传s3服务器", newFileName);
        S3Util.uploadToS3(savePath, newFileName);
        logger.info("文件{}结束上传s3服务器", newFileName);
        delSingleFile(savePath, newFileName);
        jsonObject.put("attachment_url", newFileName);
        jsonObject.put("file_name", fileName);
        return jsonObject;
    }

    /**
     * 删除单个文件
     * @param savePath
     * @param fileName
     */
    private void delSingleFile(String savePath, String fileName) {
        try {
            File file = new File(savePath + File.separator + fileName);
            if(file.isFile() && file.exists()) {
                file.delete();
                logger.info("删除单个文件{}成功", fileName);
            } else {
                logger.info("文件{}不存在", fileName);
            }
        } catch (Exception e) {
            logger.error("删除单个文件{}异常：{}", fileName, e.getMessage(), e);
        }
    }

    @RequestMapping(value = "document", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject documentUpdate(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        logger.info("单据创建接口入参为companyId={},json={}",companyId,json);
        return documentUtil(companyId, json);
    }

    @Autowired
    AsynService asynService;
    @RequestMapping(value = "documentAsyc", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject documentUpdateAsyc(@RequestParam(value = "access_token") String accessToken,
                                         @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        String forwardUrl = httpForwardUtil.getForwardUrl(companyId, "document.update");
        if(StringUtils.isEmpty(forwardUrl)) {
            logger.info("单据异步创建不支持转发处理: companyId={}", companyId);
            return openApiService.generateRtn(null, null, "不支持转发处理", 1001);
        } else {
            logger.info("单据异步创建执行转发请求: companyId={}", companyId);
            JSONObject response = httpForwardUtil.post(accessToken, forwardUrl, json);
            return response != null ? response : openApiService.generateRtn(null, null, "系统错误", 1001);
        }
    }

    public JSONObject documentUtil(int companyId, String json) throws Exception {
        JSONObject jsonObject;
        String errorMessage = "";
        int errorCode = 0;
        try {
            String objData = JSONObject.parseObject(json).getString("document");
            jsonObject = JSONObject.parseObject(objData);
        } catch (Exception e) {
            logger.error("输入JSON解析出错:{}", e.getMessage(),e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        {// 当前是为了获取行上面的附件信息 重新上传
            if (null == jsonObject) {
                return openApiService.generateRtn(null, null, "当前传入的数据结构是空", 1003);
            }
            JSONArray claim_line = jsonObject.getJSONArray("claim_line");
            if (null != claim_line) {
                for (Object obj : claim_line) {
                    JSONObject line = (JSONObject) obj;
                    if (null != line.getJSONArray("attachments")) {
                        JSONArray attaches = new JSONArray();
                        JSONArray attachments = line.getJSONArray("attachments");
                        for (Object o : attachments) {
                            try {
                                attaches.add(getSingleFile(o.toString(), savePath));
                            } catch (Exception e) {
                                logger.error("{}附件处理失败：{}", o.toString(), e.getMessage(), e);
                                errorMessage = errorMessage + "|" + o.toString() + "附件处理失败: " + e.getMessage();
                                errorCode = 1005;
                            }
                        }
                        if (attaches.size() > 0) {
                            line.put("attachments", attaches);
                        }
                    }

                }
            }
        }
        JSONArray attachments;
        try {
            JSONArray attaches = new JSONArray();
            attachments = jsonObject.getJSONArray("attachment_urls");
            if (attachments != null) {
                for (Object o : attachments) {
                    try {
                        attaches.add(getSingleFile(o.toString(), savePath));
                    } catch (Exception e) {
                        logger.error("{}附件处理失败：{}", o.toString(), e.getMessage(), e);
                        errorMessage = errorMessage + "|" + o.toString() + "附件处理失败: " + e.getMessage();
                        errorCode = 1005;
                    }
                }
                if (attaches.size() > 0) {
                    jsonObject.put("attachments", attaches);
                }
            }
        } catch (Exception e) {
            logger.error("没有附件{}", e.getMessage(), e);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("document_update");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, errorMessage, errorCode);
            } else {
                return openApiService.generateRtn(null, null, errorMessage + "|" + fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常{}", e.getMessage(), e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "newAttachment", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public JSONObject newAttachmentURL(@RequestParam(value = "access_token") String accessToken,
                                   @RequestBody String attachmentStr) throws Exception {

        int companyId = openApiService.authCompany(accessToken);
        if (attachmentStr.length() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        JSONObject attachments;

        try {
            attachments = JSONObject.parseObject(attachmentStr);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);

        }

        HashMap<String, String> stringHashMap;
        if (attachments.getJSONArray("attachment").size() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }

        try {
            stringHashMap =
                    openApiService.createAllAttachmentsURL(attachments.getJSONArray("attachment"), companyId);
        } catch (Exception e) {
            logger.error("数据库异常", e);
            return openApiService.generateRtn(null, null, "数据库异常", 1003);
        }
        String data = new org.json.JSONObject(stringHashMap).toString();
        return openApiService.generateRtn("result", JSONObject.parseObject(data), "ok", 0);

    }


    @RequestMapping(value = "updateBudget", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public JSONObject costCenterBudget(@RequestParam(value = "access_token") String accessToken,
                                       @RequestBody String budgetStr) throws Exception {

        int companyId = openApiService.authCompany(accessToken);
        JSONArray budgets;
        String errorMessage = "";
        int errorCode = 0;

        if (budgetStr.length() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            budgets = JSONObject.parseObject(budgetStr).getJSONArray("budgets");
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);

        }

        if (budgets.size() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }

        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(budgets.toJSONString());
            fndQuery.setType("cost_center_budget_1");
            String result1 = openApiMapper.commonUpdate(fndQuery).get(0);
            openApiService.asynchronousBudget(JSONObject.parseObject(JSONObject.toJSONString(fndQuery)));
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, "ok", errorCode);
            } else {
                return openApiService.generateRtn(null, null, errorMessage + "|" + fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }

    }


    @RequestMapping(value = "showBudgetDetails", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public JSONObject budgetDetails(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String conditionStr) throws Exception {

        int companyId = openApiService.authCompany(accessToken);
        JSONObject condition;
        String errorMessage = "";
        int errorCode = 0;

        if (conditionStr.length() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            condition = JSONObject.parseObject(conditionStr);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);

        }

        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(condition.toJSONString());
            fndQuery.setType("budget_details");
            List<String> result = openApiMapper.commonUpdate(fndQuery);
            logger.info(result.get(0));
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn("budget_details", JSONObject.parseArray(result.get(0)), "ok", errorCode);
            } else {
                return openApiService.generateRtn(null, null, errorMessage + "|" + fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "showBudgetDetailRollup", method = RequestMethod.POST, produces = {"application/json;charset=UTF-8"})
    public JSONObject showBudgetDetailRollup(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String conditionStr) throws Exception {

        int companyId = openApiService.authCompany(accessToken);
        JSONObject condition;
        String errorMessage = "";
        int errorCode = 0;
        if (conditionStr.length() == 0) {
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            condition = JSONObject.parseObject(conditionStr);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }

        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(condition.toJSONString());
            fndQuery.setType("budget_detail_rollup");
            List<String> result = openApiMapper.commonUpdate(fndQuery);
            logger.info(result.get(0));
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn("budget_detail_rollup", JSONObject.parseArray(result.get(0)), "ok", errorCode);
            } else {
                return openApiService.generateRtn(null, null, errorMessage + "|" + fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "wechat/update", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject wechatUpdate(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        JSONArray jsonArray;
        try {
            String arrayData = JSONObject.parseObject(json).getString("wechat_account");
            jsonArray = JSONObject.parseArray(arrayData);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("wechat_binding");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "account", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject accountingSubject(@RequestParam(value = "access_token") String accessToken,
                                   @RequestBody String json) throws Exception {

        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("accounts");
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }

        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("gl_account");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, "ok", 0);
            } else {
                return openApiService.generateRtn("error_details",
                        JSONObject.parseArray(fndQuery.getReturnMessage()),
                        "请检查输入数据", 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @apiDefine apiFndDataGroup 主数据更新
     */

    /**
     * @api {POST} /api/v2/common/lov/update?access_token=ACCESS_TOKEN 更新主数据
     * @apiGroup apiFndDataGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用主数据。每个主数据的type是固定的，由顾问配置好提供
     * @apiParam (入参) {String} type 主数据code
     * @apiParam (入参) {String} content 主数据内容
     * @apiParam (入参) {String} content.code 主数据内容代码，根据代码已有的会更新，没有的新建
     * @apiParam (入参) {String} content.value 主数据名称
     * @apiParam (入参) {String} content.enabled_flag 本条是否启用
     * @apiParamExample {json} 请求样例：
     * {
     * "type": "mainData",
     * "content": [
     * {
     * "code": "100396",
     * "value": "潜在客户",
     * "enabled_flag": "Y"
     * },
     * {
     * "code": "100397",
     * "value": "其他客户",
     * "enabled_flag": "Y"
     * }
     * ]
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * }
     */

    @RequestMapping(value = "lov/update", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject lovUpdate(@RequestParam(value = "access_token") String accessToken,
                                      @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("lov_update");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    /**
     * @apiDefine apiProjectGroup 项目管理
     */

    /**
     * @api {POST} /api/v2/common/project?access_token=ACCESS_TOKEN 创建项目
     * @apiGroup apiProjectGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用项目。以项目编码作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} project_code 项目编码
     * @apiParam (入参) {String} project_name 项目名称
     * @apiParam (入参) {String} [project_owner] 项目主管用户名
     * @apiParam (入参) {String} [project_department] 项目所属部门
     * @apiParam (入参) {String} [enabled_flag] Y-启用，N-禁用，默认Y
     * @apiParam (入参) {String} [description] 描述
     * @apiParam (入参) {String} [column1] 预留字段，企业自定义字段
     * @apiParamExample {json} 请求样例：
     * {
     * "projects": [
     * {
     * "project_code": "P001",
     * "project_name": "项目A",
     * "project_owner": "<EMAIL>",
     * "project_department": "D002",
     * "enabled_flag": "Y",
     * "column1":"上海分公司"
     * }
     * ]
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * }
     */

    @RequestMapping(value = "project", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateProject(@RequestParam(value = "access_token") String accessToken,
                                       @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("projects");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 项目同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("project");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }


    /**
     * @apiDefine apiSupplierGroup 供应商管理
     */

    /**
     * @api {POST} /api/v2/common/supplier?access_token=ACCESS_TOKEN 创建供应商
     * @apiGroup apiSupplierGroup
     * @apiVersion 0.0.1
     * @apiDescription 新建，更新或者禁用供应商。以供应商编码作为唯一凭据，支持批量更新
     * @apiParam (入参) {String} supplier_code 供应商编码
     * @apiParam (入参) {String} supplier_name 供应商名称
     * @apiParam (入参) {String} [contact_person] 联系人
     * @apiParam (入参) {String} [contact_phone] 联系电话
     * @apiParam (入参) {String} [contact_address] 联系地址
     * @apiParam (入参) {String} [branch_code] 分配给以下分公司，分公司编码数组，默认全公司
     * @apiParam (入参) {String} [bank_name] 银行名称
     * @apiParam (入参) {String} [bank_branch] 支行名称
     * @apiParam (入参) {String} [bank_code] 银行代码
     * @apiParam (入参) {String} [account_number] 银行卡号
     * @apiParam (入参) {String} [account_name] 账户名
     * @apiParam (入参) {String} [enabled_flag] Y-启用，N-禁用，默认Y
     * @apiParam (入参) {String} [column1] 预留字段，企业自定义字段
     * @apiParamExample {json} 请求样例：
     * {
     * "suppliers": [
     * {
     * "supplier_code": "S001",
     * "supplier_name": "供应商A",
     * "branch_code": ["D001","D002"],
     * "enabled_flag": "Y"
     * }
     * ]
     * }
     * @apiSuccess (Success 200) {int} errcode 返回码
     * @apiSuccess (Success 200) {String} errmsg 错误描述
     * @apiSuccessExample {json} 返回样例:
     * {
     * "errcode": 0,
     * "errmsg": "ok",
     * }
     */

    @RequestMapping(value = "supplier", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateSupplier(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("suppliers");
            if (CollectionUtils.isEmpty(jsonArray)) {
                logger.warn("companyId: {}, company schema: {}, 供应商同步数据为空", companyId, CustomerContextHolder.getCustomerType());
                return openApiService.generateRtn(null, null, null, 0);
            }
        } catch (Exception e) {
            logger.error("输入JSON解析出错", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("supplier");
            openApiMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

}
