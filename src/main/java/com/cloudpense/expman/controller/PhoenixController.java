package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.entity.phoenix.Response;
import com.cloudpense.expman.service.BaanService;
import com.cloudpense.expman.service.SAPService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.webService.phoenix.ActivityApplication;
import com.cloudpense.expman.webService.phoenix.ForPhoenixResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;


@RestController
@RequestMapping(value = "/phoenix")
public class PhoenixController {
    private static final Logger logger = LoggerFactory.getLogger(PhoenixController.class);
    private ActivityApplication activityApplication;
    private BaanService baanService;
    @Autowired
    public PhoenixController(ActivityApplication activityApplication, BaanService baanService){
        this.activityApplication = activityApplication;
        this.baanService = baanService;
    };
    @Autowired
    private SAPService sapService;
    @RequestMapping(value = "/activity",method = RequestMethod.POST,produces = CommonConstants.APPLICATION_JSON)
    public ForPhoenixResponse phoenixActivityApplication(@RequestBody String str,
                                                         @RequestHeader("username") String username,
                                                         @RequestHeader("password") String password){
        final String KEYNAME = "菲尼克斯activity===";
        logger.info("{}收到http请求入参={}, username={}", KEYNAME, username);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        try {
            String data = URLDecoder.decode(str,"UTF-8");
            JSONObject input = JSONObject.parseObject(data);
            CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
            return activityApplication.activityApplication(input,username,password);
        } catch (UnsupportedEncodingException e) {
            logger.error("{}出现异常={}", KEYNAME, e);
            ForPhoenixResponse response = new ForPhoenixResponse();
            response.RESPONSE_STATUS = "E";
            response.RESPONSE_MESSAGE = "错误的编码类型！";
            return response;
        }

    }

    @RequestMapping(value = "/baanRulesUpdate",method = RequestMethod.POST,produces = CommonConstants.APPLICATION_JSON)
    public void phoenixBAANUpdateRules() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        baanService.baanRulesUpdate();
    }

    @RequestMapping(value = "voucher", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public String phoenixPostDoc(@RequestBody JSONObject jsonObject) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        logger.info("jsonObject==>" + jsonObject);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(jsonObject.getInteger("companyId"));
        glBatchTransfer.setUserId(jsonObject.getInteger("userId"));
        glBatchTransfer.setInput(jsonObject.getString("input"));
        glBatchTransfer.setLanguage(jsonObject.getString("language"));
        logger.info("JSONObject.toJSONString(glBatchTransfer)==>" + JSONObject.toJSONString(glBatchTransfer));
        return baanService.postVoucher(glBatchTransfer);
    }

    @RequestMapping(value = "/reimbursement", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public Response phoenixReimbursement(@RequestBody String  json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        return baanService.phoenixReimbursement(json);
    }

}
