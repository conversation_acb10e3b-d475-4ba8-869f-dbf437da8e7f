package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.EvcardMapper;
import com.cloudpense.expman.mapper.OpenApiMapper;
import com.cloudpense.expman.service.EvcardService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.util.MD5Util;
import com.meiya.MeiyaUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

import static com.cloudpense.expman.controller.WanhuaController.Encrypt;
import static com.cloudpense.expman.controller.WanhuaController.oauthUrl;
import static com.cloudpense.expman.service.impl.EvcardServiceImpl.*;
import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON_UTF;
import static com.meiya.MeiyaUtils.HttpPostString;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/evcard")
public class EvcardController {
    private final OpenApiService openApiService;
    private final EvcardService evcardService;
    private final EvcardMapper evcardMapper;
    private final OpenApiMapper openApiMapper;
    private final OpenApiRestController openApiRestController;

    private static final Logger logger = LoggerFactory.getLogger(EvcardController.class);

    @Autowired
    public EvcardController(OpenApiService openApiService,
                            EvcardService evcardService,
                            EvcardMapper evcardMapper,
                            OpenApiMapper openApiMapper,
                            OpenApiRestController openApiRestController){
        this.openApiService = openApiService;
        this.evcardService = evcardService;
        this.evcardMapper = evcardMapper;
        this.openApiMapper = openApiMapper;
        this.openApiRestController = openApiRestController;
    }

    @RequestMapping(value = "createAccountingSubject", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject createAccountingSubject(@RequestParam(value = "access_token") String accessToken,
                                        @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray arrayData;
        try {
             arrayData = JSONObject.parseObject(json).getJSONArray("accountingSubject");
             return  evcardService.createAccountingSubject(arrayData,companyId);
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
    }

    @RequestMapping(value = "autoPostDocument", method = RequestMethod.GET)
    public void autoPostDocument() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        evcardService.postDocuments();
    }

    @RequestMapping(value = "autoPostPr", method = RequestMethod.GET)
    public void autoPostPr() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        try {
            evcardService.postPrSrm();
        } catch (Exception e) {
            logger.error("autoPostPr==>", e);

        }
        evcardService.postPrSap();
    }
    /**
     * @apiDefine 获取OA的Token在系统内实现跳转OA
//     * @param accessToken
     * @param json
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "checkToken/getTokenJump", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getTokenJump(@RequestBody String json) throws Exception {
        logger.info("evcard获取OA的Token在系统内实现跳转OA请求={}", json);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        try {
            JSONObject jsonObject = JSONObject.parseObject(json);
            String url = jsonObject.getJSONObject("input").getString("uri");
            if (url.indexOf("checkToken") != -1) {
               String uriPre = url.substring(0, url.indexOf("?"));
               String uriLast = url.substring(uriPre.length() + 1, url.length());
                String employee_number = jsonObject.getString("employeeNumber");
                String uri = uriLast;
                String result = evcardService.postGetTokenParameter(employee_number);
                JSONObject jsonObject1 = JSONObject.parseObject(result);
                String code = jsonObject1.getString("code");
                String message = jsonObject1.getString("message");
                if(code.equals("0")){
                    return tokenMessageReturn(10000, "成功获取token", "URL", message, null, uri);
                }else{
                    return tokenMessageReturn(500, "未获取到token", "URL", null, null, uri);
                }
            } else {
                JSONObject inputJson = jsonObject.getJSONObject("input");
                String uri = inputJson.getString("uri");
                JSONObject object = new JSONObject();
                object.put("contentType", "URL");
                object.put("url", uri);
                object.put("resCode", 10000);
                object.put("resMsg", "");
                object.put("data", new JSONObject());
                return object;
            }
        } catch (Exception e) {
            logger.error("Data processing error==>{}", e);
            return tokenMessageReturn(1003, "数据处理过程报错", null, null, null, null);
        }
    }
    public static void main(String[] args) {
        String uri="https://oapi.cloudpense.com/iExpenseSqftp/evcard/checkToken/getTokenJump?requestid=133301";
        String uriPre = uri.substring(0, uri.indexOf("?"));
        String uriLast = uri.substring(uriPre.length() + 1, uri.length());
        System.out.println(uriLast);
    }

    @RequestMapping(value = "voucher/back", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getVoucherBack(@RequestParam(value = "access_token") String accessToken,
                                       @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("vouchers");
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("vouchers");
            evcardMapper.evcardDataUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                openApiService.changeVoucherStatus(jsonArray,"","");
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常==>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "grabTheOrderUpdate", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getVoucherBack1(@RequestParam(value = "access_token") String accessToken,
                                     @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("grabTheOrderUpdate");
            evcardMapper.evcardDataUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常==>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "buying/type", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject buyingTypeUpdate(@RequestParam(value = "access_token") String accessToken,
                                     @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("content");
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("buying_type");
            evcardMapper.evcardDataUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常==>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "project", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateProject(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("projects");
        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            JSONArray type1 = new JSONArray(); ////统计性
            JSONObject type2 = new JSONObject();
            JSONArray content = new JSONArray();
            for (Object o : jsonArray) {
                    JSONObject obj = (JSONObject) o;
                    if ("X".equals(obj.getString("type"))) { //统计性
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("code", obj.getString("project_code"));
                        jsonObject.put("value", obj.getString("project_name"));
                        jsonObject.put("enabled_flag", obj.getString("enabled_flag"));
                        content.add(jsonObject);
                    } else {
                    type1.add(obj);
                }
            }
            type2.put("type", "project");
            type2.put("content", content);

            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(type1.toJSONString());
            fndQuery.setType("project");
            openApiMapper.commonUpdate(fndQuery);

            FndQuery fndQuery2 = new FndQuery();
            fndQuery2.setCompanyId(companyId);
            fndQuery2.setInput(type2.toJSONString());
            fndQuery2.setType("lov_update");
            openApiMapper.commonUpdate(fndQuery2);

            if (fndQuery.getReturnCode().equals("S") && fndQuery2.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage() + fndQuery2.getReturnMessage(), 1002);
            }

        } catch (Exception e) {
            logger.error("数据库执行异常==>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "document", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject updateDocument(@RequestParam(value = "access_token") String accessToken,
                                     @RequestBody String json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        JSONArray claimLines;
        try {
            claimLines = JSONObject.parseObject(json).getJSONObject("document").getJSONArray("claim_line");

        } catch (Exception e) {
            logger.error("输入JSON解析出错==>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        JSONArray resultLines = new JSONArray();
        BigDecimal totalAmount = new BigDecimal(0);
        try {
            for (Object o : claimLines) {
                JSONObject line = (JSONObject) o;
                String accountEvent = line.getString("drcr");
                String accountCode = line.getString("account");
                if (null == accountEvent || accountEvent.length() == 0 || null == accountCode || accountCode.length() == 0) {
                    throw new ValidationException("drcr或者account不能为空");
                }
                if ("H".equals(accountEvent)) {
                    line.put("accumulation_type_id", "-1");
                }

                String typeCode = evcardMapper.evcardGetTypeByAccount(evcardCompanyId, line.getString("account"));
                if (typeCode != null) {
                    line.put("type_id", typeCode);
                    totalAmount = totalAmount.add(line.getBigDecimal("receipt_amount"));
                    //todo
                    resultLines.add(line);
                    /*throw new ValidationException("未匹配到对应的费用类型");*/
                }
            }
        } catch (Exception e) {
            logger.error("document报错==>", e);
            return openApiService.generateRtn(null, null, e.getMessage(), 1001);
        }
        JSONObject document = JSONObject.parseObject(json).getJSONObject("document");
        String user = evcardMapper.evcardGetPositionRecever(evcardCompanyId, evcardReceivePosition);
        logger.info("user==>" + user);
        document.put("created_by", user);
        document.put("total_amount", totalAmount);
        document.put("claim_line", resultLines);
        JSONObject doc = new JSONObject();
        doc.put("document", document);
        logger.info("doc==>" + doc);
        return openApiRestController.documentUpdate(accessToken, doc.toJSONString());
    }

    @RequestMapping(value = "sso", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject ssoLogin(@RequestBody String json) throws Exception {
        JSONObject ret = new JSONObject();
        JSONObject codeObj = JSONObject.parseObject(json);
        String code = codeObj.getString("token");
        long timestamp = System.currentTimeMillis();
        String originalStr = ssoAppKey + timestamp + "token" + code + ssoAppSecret;
        String md5 = MD5Util.MD5Encode(originalStr).toUpperCase();
        Map<String, String> identity = new HashMap<>();
        identity.put("appKey", ssoAppKey);
        identity.put("timestamp", timestamp + "");
        identity.put("sign", md5);
        identity.put("Content-Type", "application/json");
        logger.info("md5==>" + md5);
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String ssoUrl = rb.getString("evcardSso");
        String result = HttpPostString(ssoUrl, identity, json);
        logger.info("result==>" + result);
        try {
            JSONObject jsonObject = JSONObject.parseObject(result);
            String employeeNumber = jsonObject.getJSONObject("data").getString("jobNum");
            String token = "";
            try {
                String encrypted = Encrypt(employeeNumber + "," + System.currentTimeMillis(), evcardKey);
                JSONObject obj = new JSONObject();
                obj.put("corp", evcardOAId);
                obj.put("code", encrypted);
                String results = HttpPostString(oauthUrl, null, obj.toJSONString());
                ret = JSONObject.parseObject(results);
                logger.info("ret1==>" + ret);
                if (ret.getString("token") != null) {
                    token = ret.getString("token");
                }
            } catch (Exception e) {
                logger.error("获取token失败==>", e);
                throw new ValidationException(e.getMessage());
            }
            if ("".equals(token)) {
                throw new ValidationException("登录失败");
            }
        } catch (Exception e) {
            logger.error("sso报错==>", e);
            e.printStackTrace();
            throw new ValidationException(e.getMessage());
        }
        return ret;
    }

    @RequestMapping(value = "getmyselfcookie", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getCookies(HttpServletRequest request) throws Exception {
        Cookie[] cookies = request.getCookies();
        JSONObject jsonObject = new JSONObject();
        for (Cookie cookie : cookies) {
            jsonObject.put(cookie.getName(), cookie.getValue());
        }
        return jsonObject;
    }

    @RequestMapping(value = "/evcardPushTask",method = RequestMethod.POST)
    public String evcardPushTask() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.EVC);
        return evcardService.evcardPushTask();
    }

    @RequestMapping(value = "sso/oa", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject oaSsoLogin(@RequestBody String json) throws Exception {
        JSONObject ret = new JSONObject();
        JSONObject codeObj = JSONObject.parseObject(json);
        String user = codeObj.getString("user");

        try {
            String token = "";
            try {
                String encrypted = Encrypt(user + "," + System.currentTimeMillis(), evcardKey);
                JSONObject obj = new JSONObject();
                obj.put("corp", evcardOAId);
                obj.put("code", encrypted);
                String results = HttpPostString(oauthUrl, null, obj.toJSONString());
                ret = JSONObject.parseObject(results);
                logger.info("ret2==>" + ret);
                if (ret.getString("token") != null) {
                    token = ret.getString("token");
                }
            } catch (Exception e) {
                logger.error("获取token失败==>", e);
                throw new ValidationException(e.getMessage());
            }
            if ("".equals(token)) {
                throw new ValidationException("登录失败");
            }
        } catch (Exception e) {
            logger.error("sso/oa报错==>", e);
            throw new ValidationException(e.getMessage());
        }
        return ret;
    }

    @RequestMapping(value = "sso/mobile", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject oaSsoLoginMobile(@RequestBody String json) throws Exception {
        JSONObject ret = new JSONObject();
        JSONObject codeObj = JSONObject.parseObject(json);
        String secrect = "ReYebD";
        String loginid = codeObj.getString("loginid");
        String stamp = codeObj.getString("stamp");
        String token1 = codeObj.getString("token");
        String token2  = MeiyaUtils.hexSHA1(secrect+loginid+stamp);
        logger.info("secrect==>" + secrect);
        logger.info("stamp==>" + stamp);
        logger.info("receivedtoken==>" + token1);
        logger.info("calculatedtoken==>" + token2);
        if (!token2.equals(token1)) {
            throw new ValidationException("token验证不通过");
        }
        try {
            String token = "";
            try {
                String encrypted = Encrypt(loginid + "," + System.currentTimeMillis(), evcardKey);
                JSONObject obj = new JSONObject();
                obj.put("corp", evcardOAMPId);
                obj.put("code", encrypted);
                String results = HttpPostString(oauthUrl, null, obj.toJSONString());
                ret = JSONObject.parseObject(results);
                logger.info("ret3==>" + ret);
                if (ret.getString("token") != null) {
                    token = ret.getString("token");
                }
            } catch (Exception e) {
                logger.error("获取token失败==>", e);
                throw new ValidationException(e.getMessage());
            }
            if ("".equals(token)) {
                throw new ValidationException("登录失败");
            }
        } catch (Exception e) {
            logger.error("sso/mobile报错==>", e);
            throw new ValidationException(e.getMessage());
        }
        return ret;

    }

    private JSONObject tokenMessageReturn(int resCode, String resMsg, String contentType, String token, Object data, String uri) throws Exception {
        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String jumpOaUrl = rb.getString("jumpOaUrl");
        JSONObject rtn = new JSONObject();
        rtn.put("resCode", resCode);
        rtn.put("resMsg", resMsg);
        rtn.put("contentType", contentType);
        rtn.put("data", data);
        if(resCode == 10000){
            rtn.put("url", jumpOaUrl + token + "&&" + uri);
        }else{
            rtn.put("url", uri);
        }
        logger.info("return web rtn==>" + rtn);
        return rtn;
    }
}
