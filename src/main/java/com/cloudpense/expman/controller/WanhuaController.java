package com.cloudpense.expman.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.XML;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.WanhuaMapper;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.util.S3.S3Util;
import com.cloudpense.expman.util.XStreamUtil;
import com.cloudpense.expman.vo.wanhua.GlBatchCreateApiVo;
import com.whchem.signature.SignatureClient;
import net.sf.json.JSON;
import net.sf.json.JSONSerializer;
import net.sf.json.JsonConfig;
import net.sf.json.processors.JsonValueProcessor;
import net.sf.json.xml.XMLSerializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON;
import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON_UTF;
import static com.cloudpense.expman.util.HttpUtil.doNormalJsonGet;
import static com.meiya.MeiyaUtils.HttpPostString;

@RestController
@RequestMapping(value = "api/v2/wanhua")
public class WanhuaController {
    private WanhuaMapper wanhuaMapper;
    private static final int positionId = 4322;
    private static final int wanhuaCompanyId = 15825;
    private static final String voucherUrl = "https://jp1.whchem.com:8093/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_ECS_P&receiverParty=&receiverService=&interface=SI_181_BPM_ExpenseClaimMakeDOC_Out&interfaceNamespace=http%3A%2F%2Fwhchem.com%2FECS%2FFICO";
    private static final String budgetUrl = "https://jp1.whchem.com:8093/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_ECS_P&receiverParty=&receiverService=&interface=SI_174_BPM_BudgetDataCheck_Out&interfaceNamespace=http%3A%2F%2Fwhchem.com%2FECS%2FFICO";
    private static final String indexPushUrl = "https://jp1.whchem.com:8093/XISOAPAdapter/MessageServlet?senderParty=&senderService=BC_OTAX_OCR&receiverParty=&receiverService=&interface=SI_202_OCR_OTAX_ScannedPictureLink_Out&interfaceNamespace=http%3A%2F%2Fwhchem.com%2FECS%2FFICO";
    private static final String invoicePostUrl = "https://jp1.whchem.com:8093/RESTAdapter/627/ExpenseInvoiceSynchronization";
    private static final String invoiceGetUrl = "https://jp1.whchem.com:8093/RESTAdapter/628/InvoiceSynchronizationResult";
    private static final String invoiceUserName = "user_te";
    private static final String invoicePassword = "PhiGg&v0O#2JlH";
    private static final String authHeader = "Basic dXNlcl90ZTpQaGlHZyZ2ME8jMkpsSA==";
    private static final String oaPushUrl = "https://jp1.whchem.com:8093/XISOAPAdapter/MessageServlet?senderParty=&senderService=BS_ECS_P&receiverParty=&receiverService=&interface=SI_118_ECS_CommonData_Out&interfaceNamespace=http%3A%2F%2Fwanhua.com%2FECS";
    private static final String wechatAuthUrl = "http://mplay.whchem.com/WebService/getWXUserInfo";
    String ak = "07aa6fb71dfa4132";
    String sk = "1fe24654627440e1950bb91562f4cbaa";
    public static final String whhxId = "c13de52307ef9968";
    public static final String whhxKey = "KB13B62d9D407e28";
    public static final String oauthUrl = "https://api.cloudpense.com/iExpenseManager/sso/login";

    private static final Logger logger = LoggerFactory.getLogger(WanhuaController.class);
    private OpenApiService openApiService;

    @Autowired
    public WanhuaController(WanhuaMapper wanhuaMapper,
                            OpenApiService openApiService) {
        this.wanhuaMapper = wanhuaMapper;
        this.openApiService = openApiService;
    }

    @RequestMapping(value = "budget/test", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public String postBudget(HttpServletRequest request) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        String documentNum = request.getParameter("document");
        if (null == documentNum) {
            throw new ValidationException("单据号为空");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("document_num", documentNum);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(wanhuaCompanyId);
        glBatchTransfer.setType("budget");
        glBatchTransfer.setInput(jsonObject.toJSONString());
        List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        String finalresult = "";
        for (String data : strings) {
            JSONObject json = JSONObject.parseObject(data);
            XMLSerializer xmlSerializer = new XMLSerializer();
            xmlSerializer.setElementName("item");
            xmlSerializer.setObjectName("fico:MT_174_BPM_BudgetDataCheck");
            logger.info("xml序列化=>" + xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString())));
            String content = xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString()));
            content = content
                    .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                    .replaceAll("<lines class=\"array\">", "<Data>")
                    .replaceAll("</lines>", "</Data>");
            String postData = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:fico=\"http://whchem.com/ECS/FICO\"><soapenv:Header/><soapenv:Body>\n"
                    + content
                    + "</soapenv:Body></soapenv:Envelope>";
            logger.info("budgetUrl=>" + postData);
            Map<String, String> identity = new HashMap<>();
            identity.put("Authorization", authHeader);
            identity.put("Content-Type", "application/xml");
            String result = HttpPostString(budgetUrl, identity, postData);
            finalresult += result;
        }
        return finalresult;
    }

    @RequestMapping(value = "budget", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public String docPostBudget(@RequestBody String jsonStr) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        JSONObject claimInput = JSONObject.parseObject(jsonStr);
        String documentNum = claimInput.getString("documentNum");
        JSONObject ret = new JSONObject();
        boolean error = false;
        String errorResult = "";
        if (null == documentNum) {
            throw new ValidationException("单据号为空");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("document_num", documentNum);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(wanhuaCompanyId);
        glBatchTransfer.setType("budget");
        glBatchTransfer.setInput(jsonObject.toJSONString());
        List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);

        for (String data : strings) {
            if (null == data || "null".equals(data)) {
                continue;
            }
            JSONObject json = JSONObject.parseObject(data);
            XMLSerializer xmlSerializer = new XMLSerializer();
            xmlSerializer.setElementName("item");
            xmlSerializer.setObjectName("fico:MT_174_BPM_BudgetDataCheck");
            logger.info("xml序列化=>" + xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString())));
            String content = xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString()));
            content = content
                    .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                    .replaceAll("<lines class=\"array\">", "<Data>")
                    .replaceAll("</lines>", "</Data>");
            String postData = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:fico=\"http://whchem.com/ECS/FICO\"><soapenv:Header/><soapenv:Body>\n"
                    + content
                    + "</soapenv:Body></soapenv:Envelope>";
            logger.info("budgetUrl发送的数据=>" + postData);
            Map<String, String> identity = new HashMap<>();
            identity.put("Authorization", authHeader);
            identity.put("Content-Type", "application/xml");

            try {
                String result = HttpPostString(budgetUrl, identity, postData);
                logger.info("budgetUrl接口返回结果=>" + result);
                XMLSerializer xmlSerializer2json = new XMLSerializer();
                net.sf.json.JSON resultJson = xmlSerializer2json.read(result);
                logger.info("xml序列化结果=>" + resultJson);
                JSONObject jsonObject1 = JSONObject.parseObject(resultJson.toString());
                JSONObject soapBody = jsonObject1.getJSONObject("SOAP:Body").getJSONObject("ns0:MT_174_BPM_BudgetDataCheck_Resp");
                if ("Y".equals(soapBody.getString("flag"))) {

                } else {
                    error = true;
                    String detailStr = "";
                    try {
                        Object obj = soapBody.get("Data");
                        if (obj instanceof JSONArray) {
                            JSONArray jsonArray = (JSONArray)obj;
                            for (Object j : jsonArray) {
                                JSONObject jsonObject2 = (JSONObject) j;
                                detailStr += jsonObject2.getString("field1") + ",预算余额：" + jsonObject2.getString("budgetFigure") + "元\n";
                            }
                        } else {
                            JSONObject jsonObject2 = ((JSONObject)obj).getJSONObject("item");
                            detailStr += jsonObject2.getString("field1") + ",预算余额：" + jsonObject2.getString("budgetFigure") + "元\n";
                        }
                    } catch (ClassCastException e) {
                        logger.error("类型强转出错=>", e);
                    } catch (Exception e) {
                        logger.error("其他错误=>", e);
                    }
                    errorResult += soapBody.getString("errorMessage") + detailStr;

                }
            } catch (Exception e) {
                logger.error("万华预算接口报错", e);
                error = true;
                errorResult += "万华预算接口报错";
            }
        }
        logger.info("万华预算校验结果: {}", errorResult);
        if (error) {
            ret.put("exception_level", 99);
            ret.put("message", errorResult);
        } else {
            ret.put("exception_level", 0);
            ret.put("message", "OK");
        }
        return ret.toJSONString();
    }

    @RequestMapping(value = "budgetauto", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public void doAutoPostBudget() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        List<Map<String, String>> docs = wanhuaMapper.getBudgetTodo(wanhuaCompanyId);
        for (Map<String, String> doc : docs) {
            logger.info("doAutoPostBudget getBudgetTodo is {}",JSONUtil.toJsonStr(docs));

            String documentNum =  doc.get("document_num");
            if (null == documentNum) {
                continue;
            }

            if(doc.containsKey("type_code") && Arrays.asList("FYC004","FYC005").contains(doc.get("type_code"))){
                wanhuaMapper.setBudgetPosted("unlocked", "OK", documentNum, wanhuaCompanyId);
                continue;
            }
            if(doc.containsKey("internal_type") && Arrays.asList("request","request_travel").contains(doc.get("internal_type"))){
                wanhuaMapper.setBudgetPosted("unlocked", "OK", documentNum, wanhuaCompanyId);
                continue;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("document_num", documentNum);
            GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
            glBatchTransfer.setCompanyId(wanhuaCompanyId);
            glBatchTransfer.setType("budget");
            glBatchTransfer.setInput(jsonObject.toJSONString());
            List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
            String mailContent = "";
            boolean error = false;
            String sentFlag = "POST_ERROR:";
            String errorResult = "";
            for (String data : strings) {
                if (null == data || "null".equals(data)) {
                    continue;
                }
                JSONObject json = JSONObject.parseObject(data);
                XMLSerializer xmlSerializer = new XMLSerializer();
                xmlSerializer.setElementName("item");
                xmlSerializer.setObjectName("fico:MT_174_BPM_BudgetDataCheck");
                System.out.println(xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString())));
                String content = xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString()));
                content = content
                        .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "")
                        .replaceAll("<lines class=\"array\">", "<Data>")
                        .replaceAll("</lines>", "</Data>");
                String postData = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:fico=\"http://whchem.com/ECS/FICO\"><soapenv:Header/><soapenv:Body>\n"
                        + content
                        + "</soapenv:Body></soapenv:Envelope>";
                Map<String, String> identity = new HashMap<>();
                identity.put("Authorization", authHeader);
                identity.put("Content-Type", "application/xml");
                logger.info("接口请求结果=>" + postData);
                String result = HttpPostString(budgetUrl, identity, postData);
                logger.info("接口返回结果=>" + result);
                XMLSerializer xmlSerializer2json = new XMLSerializer();
                net.sf.json.JSON resultJson = xmlSerializer2json.read(result);
                logger.info("接口返回结果序列化=>" + resultJson);
                try {
                    JSONObject jsonObject1 = JSONObject.parseObject(resultJson.toString());
                    JSONObject soapBody = jsonObject1.getJSONObject("SOAP:Body").getJSONObject("ns0:MT_174_BPM_BudgetDataCheck_Resp");
                    if ("S".equals(soapBody.getString("executeStatus"))) {  //预算释放（撤回、审批拒绝）、预算消耗（审批通过），是通过ZSTATUS来判断的，S成功、F失败

                    } else {
                        error = true;
                        mailContent += "出错请求数据：" + postData + ",出错返回数据：" + result + "------\n";
                        String detailStr = soapBody.getString("errorMessage") + ":";
                        try {
                            JSONArray jsonArray = soapBody.getJSONArray("Data");
                            for (Object j : jsonArray) {
                                JSONObject jsonObject2 = (JSONObject) j;
                                detailStr += jsonObject2.getString("field1") + ",预算余额：" + jsonObject2.getString("budgetFigure") + "元\n";
                            }
                        } catch (ClassCastException e) {
                            logger.error("预算结果出错=>", e);
                            JSONObject jsonObject2 = soapBody.getJSONObject("Data").getJSONObject("item");
                            detailStr +=jsonObject2.getString("field1") + ",预算余额：" + jsonObject2.getString("budgetFigure") + "元\n";
                        } catch (Exception e) {
                            logger.error("其他错误=>", e);
                        }
                        errorResult += sentFlag + detailStr;
                    }
                } catch (Exception e) {
                    logger.error("网络错误=>", e);
                    error = true;
                    mailContent += "网络错误：" + postData + "------\n";
                    errorResult += sentFlag + "接口报错，请稍后重试或联系系统管理员";
                }
            }
            if (error) {
                wanhuaMapper.setBudgetPosted("locked", errorResult, documentNum, wanhuaCompanyId);
                String userName = "<EMAIL>";
                String password = "Cloud123";
                String host = "smtp.exmail.qq.com";
                String protocal = "smtp";
                Properties props = new Properties();
                props.put("mail.smtp.auth", "true");
                Session session = Session.getDefaultInstance(props);
                MimeMessage message = new MimeMessage(session);
                try {
                    message.setRecipients(Message.RecipientType.TO, "<EMAIL>");
                    message.setFrom(new InternetAddress(userName));
                    message.setSubject("简约费控调用预算接口报错");
                    message.setText(mailContent);
                    Transport transport = session.getTransport(protocal);
                    transport.connect(host, userName, password);
                    transport.sendMessage(message, message.getAllRecipients());
                } catch (MessagingException e) {
                    logger.error("简约费控调用预算接口报错=>", e);
                }
            } else {
                wanhuaMapper.setBudgetPosted("unlocked", "OK", documentNum, wanhuaCompanyId);
            }
        }
    }

    @RequestMapping(value = "voucher", method = RequestMethod.POST, produces = APPLICATION_JSON)
    @Scheduled(fixedDelay = 300000)
    public void postVoucher() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        List<Integer> headerIds = wanhuaMapper.scanWanhuaVoucherPost(positionId, wanhuaCompanyId);
        for (Integer headerId : headerIds) {
            if (headerId == 0) {
                break;
            }
            GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
            glBatchTransfer.setCompanyId(wanhuaCompanyId);
            glBatchTransfer.setType("api");
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("header_id", headerId);
            glBatchTransfer.setInput(jsonObject.toJSONString());
            List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
            for (String data : strings) {
                JSONObject json = JSONObject.parseObject(data);
                boolean hasValue = false;
                try {
                    JSONArray lines = json.getJSONArray("lines");
                    for (int i = lines.size() - 1; i >= 0; i--) {
                        JSONObject line = (JSONObject) lines.get(i);
                        if (line.getFloatValue("freeFigure") == 0) {
                            lines.remove(i);
                        }
                    }
                    for (Object object : lines) {
                        JSONObject line = (JSONObject) object;
                        if (line.getFloatValue("freeFigure") > 0) {
                            hasValue = true;
                            break;
                        }
                    }
                } catch (Exception e) {
                    logger.error("postVoucher出错=>", e);
                }
                if (!hasValue) {
                    try {
                        JSONObject body = new JSONObject();
                        body.put("headerId", headerId);
                        body.put("voucherNum", "X");
                        body.put("excuteStatus", "S");
                        body.put("errorMessage", "金额为0，无需制证");
                        FndQuery fndQuery = new FndQuery();
                        fndQuery.setCompanyId(wanhuaCompanyId);
                        fndQuery.setInput(body.toJSONString());
                        fndQuery.setType("document_workflow");
                        wanhuaMapper.commonUpdate(fndQuery);
                    } catch (Exception e) {
                        logger.error("万华document_workflow更新失败=>", e);
                    } finally {
                        continue;
                    }
                }
//                XMLSerializer xmlSerializer = new XMLSerializer();
//                xmlSerializer.setElementName("item");
//                xmlSerializer.setObjectName("fico:MT_181_BPM_ExpenseClaimMakeDOC");
                GlBatchCreateApiVo glBatchCreateApiVo = JSONUtil.toBean(JSONUtil.toJsonStr(json), GlBatchCreateApiVo.class);
                glBatchCreateApiVo.setLines(new GlBatchCreateApiVo.LinesVo(JSONUtil.toList(JSONUtil.toJsonStr(json.getJSONArray("lines")), GlBatchCreateApiVo.LinesDTO.class)));
                String content = XStreamUtil.marshal(glBatchCreateApiVo, GlBatchCreateApiVo.class);
//                String content = xmlSerializer.write(JSONSerializer.toJSON(json.toJSONString()));
                logger.info("181接口返回值序列化结果=>" + content);
                content = content
                        .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"\\?>","")
                        .replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "");
//                        .replaceAll("<lines class=\"array\">", "<Data>")
//                        .replaceAll("</lines>", "</Data>");
                String postData = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:fico=\"http://whchem.com/ECS/FICO\"><soapenv:Header/><soapenv:Body>\n"
                        + content
                        + "</soapenv:Body></soapenv:Envelope>";
                logger.info("181接口发送数据=>" + postData);
                Map<String, String> identity = new HashMap<>();
                identity.put("Authorization", authHeader);
                identity.put("Content-Type", "application/xml");
                String result = HttpPostString(voucherUrl, identity, postData);
                logger.info("181接口调用返回结果=>" + result);
                XMLSerializer xmlSerializer2json = new XMLSerializer();
                net.sf.json.JSON resultJson = xmlSerializer2json.read(result);
                logger.info("181接口返回结果xml序列化=>" + resultJson);
                try {
                    JSONObject jsonObject1 = JSONObject.parseObject(resultJson.toString());
                    JSONObject soapBody = jsonObject1.getJSONObject("SOAP:Body").getJSONObject("ns0:MT_181_BPM_ExpenseClaimMakeDOC_Resp");
                    soapBody.put("headerId", headerId);
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setCompanyId(wanhuaCompanyId);
                    fndQuery.setInput(soapBody.toJSONString());
                    fndQuery.setType("document_workflow");
                    wanhuaMapper.commonUpdate(fndQuery);

                } catch (Exception e) {
                    logger.error("单据头id为" + headerId + "的document_workflow更新失败=>", e);
                }
            }
        }
    }

    @RequestMapping(value = "status", method = RequestMethod.POST, produces = APPLICATION_JSON)
    public JSONObject updateVoucherStatus(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        json = "{\"document_status\":" + json + "}";
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            String arrayData = JSONObject.parseObject(json).getString("document_status");
            jsonArray = JSONArray.parseArray(arrayData);
        } catch (Exception e) {
            logger.error("json解析失败=>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("document_status");
            wanhuaMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("document_status数据库执行异常=>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "payment", method = RequestMethod.POST, produces = APPLICATION_JSON)
    public JSONObject updatePaymentStatus(@RequestParam(value = "access_token") String accessToken,
                                          @RequestBody String json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        int companyId = openApiService.authCompany(accessToken);
        JSONObject jsonObject;
        try {
            String arrayData = JSONObject.parseObject(json).getString("payment_status");
            jsonObject = JSONObject.parseObject(arrayData);
        } catch (Exception e) {
            logger.error("输入JSON解析出错=>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            fndQuery.setType("payment_status");
            wanhuaMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常=>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "invoice/post", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public String postInvoice() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(wanhuaCompanyId);
        glBatchTransfer.setType("invoice");
        glBatchTransfer.setInput(new JSONObject().toJSONString());
        List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        logger.info("invoicePost目标=>{}", strings);
        String resultStr = "S";
//        Map<String, String> identity = SignatureClient.hmacSignature(ak, sk, "");
        String auth = DatatypeConverter.printBase64Binary((invoiceUserName+":"+invoicePassword).getBytes("UTF-8"));
        Map<String, String> identity=new HashMap<>();
        identity.put("Authorization", "Basic " + auth);
        JSONArray jsonArray = new JSONArray();
        JSONArray feedBacks = new JSONArray();
        for (String data : strings) {
            try {
                JSONObject json = JSONObject.parseObject(data);
                String rawData = json.getString("invoiceList");
                JSONArray lists = JSONArray.parseArray(rawData);
                JSONArray temp = new JSONArray();
                for (Object list : lists) {
                    JSONObject obj = (JSONObject) list;
                    if ("disabled".equals(obj.getString("disabled"))) {
                        continue;
                    }
                    JSONObject invoiceDetail;
                    try {
                        invoiceDetail = obj.getJSONObject("originmessage").getJSONObject("data");
                    } catch (Exception e) {
                        invoiceDetail = obj;
                        invoiceDetail.put("invoiceCode", invoiceDetail.getString("Code"));
                        invoiceDetail.put("invoiceNo", invoiceDetail.getString("No"));
                        invoiceDetail.put("invoiceTotalPrice", invoiceDetail.getDoubleValue("Amount"));
                        invoiceDetail.put("invoiceTotalTax", invoiceDetail.getDoubleValue("TaxAmount"));
                        invoiceDetail.put("invoiceStatus", 0);
                        invoiceDetail.put("invoiceTypeCode", obj.getString("invoiceType"));
                        invoiceDetail.put("invoiceDate", formatDate(invoiceDetail.getString("Date"), "-"));
                        JSONObject buyer = invoiceDetail.getJSONObject("Buyer");
                        if(buyer != null) {
                            invoiceDetail.put("buyerTaxNo", buyer.getString("TaxCode"));
                            invoiceDetail.put("buyerName", buyer.getString("Name"));
                        } else {
                            invoiceDetail.put("buyerTaxNo", "");
                            invoiceDetail.put("buyerName", "");
                        }
                        JSONObject saler = invoiceDetail.getJSONObject("Saler");
                        if(saler != null) {
                            invoiceDetail.put("sellerTaxNo", saler.getString("TaxCode"));
                            invoiceDetail.put("sellerName", saler.getString("Name"));
                        } else {
                            invoiceDetail.put("sellerTaxNo", "");
                            invoiceDetail.put("sellerName", "");
                        }
                        invoiceDetail.put("checkCode", invoiceDetail.getString("VCode"));
                        invoiceDetail.put("invoiceTotalPriceTax", invoiceDetail.getString("SummaryAmount"));
                        JSONArray jsonArray1 = new JSONArray();
                        JSONArray invoiceDetailList = invoiceDetail.getJSONArray("Items");
                        if(invoiceDetailList != null && invoiceDetailList.size() > 0) {
                            for (Object o : invoiceDetailList) {
                                JSONObject jsonObject = (JSONObject) o;
                                JSONObject d = new JSONObject();
                                d.put("goodsName", jsonObject.getString("Name"));
                                d.put("goodsPrice", jsonObject.getString("Price"));
                                d.put("goodsQuantity", jsonObject.getString("Quantity"));
                                d.put("goodsTotalPrice", jsonObject.getString("Amount"));
                                d.put("goodsTaxRate", "");
                                d.put("goodsTotalTax", "");
                                jsonArray1.add(d);
                            }
                        }
                        invoiceDetail.put("detailList", jsonArray1);
                    }
                    invoiceDetail.put("invoiceId", obj.getString("invoiceId"));
                    invoiceDetail.put("lanType", 1);
                    temp.add(invoiceDetail);
                }
                json.put("invoiceList", temp);
                jsonArray.add(json);
                JSONObject feedBack = new JSONObject();
                feedBack.put("echId", json.getString("echId"));
                feedBacks.add(feedBack);
            } catch (Exception e) {
                logger.error("postInvoice出错=>data={}", data, e);
                resultStr += e.getMessage();
            }
        }
        logger.info("invoicePostUrl接口参数=>" + jsonArray.toString());
        String result = HttpPostString(invoicePostUrl, identity, jsonArray.toJSONString());
        resultStr += result;
        logger.info("invoicePostUrl接口返回=>" + result);
        glBatchTransfer.setType("setInvoiceStatus");
        glBatchTransfer.setInput(feedBacks.toJSONString());
        List<String> str = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        return resultStr;
    }

    @RequestMapping(value = "invoice/get", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public String getInvoice() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("systemDate", DateUtil.format(DateTime.of(new Date()).setTimeZone(TimeZone.getTimeZone("GMT+8")), "yyyy-MM-dd"));

        String auth = DatatypeConverter.printBase64Binary((invoiceUserName+":"+invoicePassword).getBytes("UTF-8"));

//        Map<String, String> identity = SignatureClient.hmacSignature(ak, sk, "");
        Map<String, String> identity=new HashMap<>();
        identity.put("Authorization", "Basic " + auth);
        String result = HttpPostString(invoiceGetUrl, identity, jsonObject.toJSONString());
        FndQuery fndQuery = new FndQuery();
        fndQuery.setCompanyId(wanhuaCompanyId);
        fndQuery.setInput(result);
        fndQuery.setType("invoiceUpdate");
        wanhuaMapper.commonUpdate(fndQuery);
        return "S";
    }

    @RequestMapping(value = "indexnum", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public String getIndexNum() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(wanhuaCompanyId);
        glBatchTransfer.setType("indexNum");
        glBatchTransfer.setInput(new JSONObject().toJSONString());
        List<String> strings = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        logger.info("202接口，本次待处理数{}条", strings == null ? 0 : strings.size());
        Map<String, String> identity = new HashMap<>();
        identity.put("Authorization", authHeader);
        identity.put("Content-Type", "text/xml;charset=UTF-8");
        for (String s : strings) {
            logger.info("202接口，开始处理单据=" + s);
            String postData;
            String documentNum = null;
            try {
                documentNum = s.split(",")[0];
                postData = indexConcat(s);
            } catch (Exception e) {
                logger.error("202接口，单据{}数据封装失败：error={}", documentNum, e.getMessage(), e);
                continue;
            }
            try {
                logger.error("202接口，单据{}调用外部接口请求：url={}, request={}", documentNum, indexPushUrl, postData);
                String receive = HttpPostString(indexPushUrl, identity, postData);
                logger.error("202接口，单据{}调用外部接口响应：url={}, response={}", documentNum, indexPushUrl, receive);
                XMLSerializer xmlSerializer2json = new XMLSerializer();
                net.sf.json.JSON resultJson = xmlSerializer2json.read(receive);
                JSONObject jsonObject1 = JSONObject.parseObject(resultJson.toString());
                JSONObject soapBody = jsonObject1.getJSONObject("SOAP:Body").getJSONObject("ns0:MT_202_OCR_OTAX_ScannedPictureLink_Resp");
                if ("F".equals(soapBody.getString("ZSTATUS"))) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("error_message", soapBody.getString("MESSAGE"));
                    jsonObject.put("document_num", s.split(",")[0]);
                    jsonObject.put("zstatus", "F");
                    GlBatchTransfer glBatchTransfer1 = new GlBatchTransfer();
                    glBatchTransfer1.setCompanyId(wanhuaCompanyId);
                    glBatchTransfer1.setType("indexPostError");
                    glBatchTransfer1.setInput(jsonObject.toJSONString());
                    List<String> str = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer1);
                    // 发送通知邮件
                    sendMail("<EMAIL>", "简约费控调用索引号接口报错", jsonObject.toJSONString());
                } else {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("error_message", soapBody.getString("Field1"));
                    jsonObject.put("document_num", s.split(",")[0]);
                    jsonObject.put("zstatus", "S");
                    GlBatchTransfer glBatchTransfer1 = new GlBatchTransfer();
                    glBatchTransfer1.setCompanyId(wanhuaCompanyId);
                    glBatchTransfer1.setType("indexPostError");
                    glBatchTransfer1.setInput(jsonObject.toJSONString());
                    List<String> str = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer1);
                }
            } catch (Exception e) {
                logger.error("202接口，单据{}接口调用出错：error={}", documentNum, e.getMessage(), e);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("error_message", "接口调用出错");
                jsonObject.put("document_num", s.split(",")[0]);
                GlBatchTransfer glBatchTransfer1 = new GlBatchTransfer();
                glBatchTransfer1.setCompanyId(wanhuaCompanyId);
                glBatchTransfer1.setType("indexPostError");
                glBatchTransfer1.setInput(jsonObject.toJSONString());
                List<String> str = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer1);
                // 发送通知邮件
                sendMail("<EMAIL>", "简约费控调用索引号接口报错", jsonObject.toJSONString());
            }
        }
        return "S";
    }

    public String indexConcat(String s) throws ParseException {
        String[] lists = s.split(",");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat scanDateformat = new SimpleDateFormat("yyyy-MM-dd");
        String scanDateStr = null;
        Date scanDate = format.parse(lists[4]);
        scanDateStr = scanDateformat.format(scanDate);
        return  "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:fico=\"http://whchem.com/ECS/FICO\"><soapenv:Header/><soapenv:Body><fico:MT_202_OCR_OTAX_ScannedPictureLink><ZSQD>"
                + lists[0]
                + "</ZSQD><ZSMSY>"
                + lists[1]
                + "</ZSMSY><ZSTATUS>?</ZSTATUS><MESSAGE>?</MESSAGE><SPRAS>?</SPRAS><ZFLAG>2</ZFLAG><Field1>?</Field1><Field2>?</Field2><barcodeNum>"
                +lists[2]
                +"</barcodeNum><realFlag>"
                +(("是".equals(lists[3]))?"Y":"N")
                +"</realFlag><scanDate>"
                +(scanDateStr == null?"":scanDateStr)
                +"</scanDate><Data><item><DOCID>?</DOCID><Field1>?</Field1><Field2>?</Field2></item></Data></fico:MT_202_OCR_OTAX_ScannedPictureLink></soapenv:Body></soapenv:Envelope>";

    }

    /**
     * 发送邮件
     * @param email
     * @param subject
     * @param text
     */
    private void sendMail(String email, String subject, String text) {
        try {
            String userName = "<EMAIL>";
            String password = "Cloud123";
            String host = "smtp.exmail.qq.com";
            String protocal = "smtp";
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            Session session = Session.getInstance(props);
            MimeMessage message = new MimeMessage(session);
            message.setRecipients(Message.RecipientType.TO, email);
            message.setFrom(new InternetAddress(userName));
            message.setSubject(subject);
            message.setText(text);
            Transport transport = session.getTransport(protocal);
            transport.connect(host, userName, password);
            transport.sendMessage(message, message.getAllRecipients());
        } catch (Exception e) {
            logger.error("发送邮件失败：email={}, subject={}, text={}, error={}", email, subject, text, e.getMessage(), e);
        }
    }


    @RequestMapping(value = "user", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject wanhuaUpdateUser(HttpServletRequest request, @RequestBody String json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        String auth = request.getHeader("Authorization");
        logger.info("Authorization =>" + auth);
        if (!"Basic SV93YW5odWE6ZGxmMjMqRFk=".equals(auth)) {
            throw new ValidationException("身份验证失败");
        }
        int companyId = wanhuaCompanyId;
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("users");
        } catch (Exception e) {
            logger.error("用户更新失败=>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }

        jsonArray.forEach(e->{
            if(Arrays.asList("0","1","2").contains(((JSONObject)e).getString("enabledflag"))){
                ((JSONObject) e).put("inactive_date",DateUtil.format(new Date(),"yyyy-MM-dd"));
            }else{
                ((JSONObject) e).put("inactive_date",null);
            }
        });

        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("employee");
            wanhuaMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("数据库执行异常=>", e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    private String todoConcat(int id, String array) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:SS");
        return "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:int=\"http://interfaceServer.portal.wanhua.com/\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <int:MESSAGE>\n" +
                "         <!--Optional:-->\n" +
                "           <HEADER>\n" +
                "        <SOURCEID>TE</SOURCEID>\n" +
                "        <DESTINATIONID>PORTAL</DESTINATIONID>\n" +
                "        <ACTION>CREATE</ACTION>\n" +
                "        <SIZE>" + id +
                "</SIZE>\n" +
                "        <DATE>" +
                LocalDateTime.now().format(formatter) +
                "</DATE>\n" +
                "        <BO>TASK</BO>\n" +
                "    </HEADER>\n" +
                "    <REQUEST>" +
                array +
                "</REQUEST>\n" +
                "      </int:MESSAGE>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";
    }

    @RequestMapping(value = "pushauto", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
    public String pushOaTasks() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        List<String> tasks = wanhuaMapper.getOaPushTask(wanhuaCompanyId);
        logger.info("万华待办推送oa获取目标数据{}条", tasks != null ? tasks.size() : 0);
        String arrayResult = "";
        int size = 0;
        ArrayList<String> documentNums = new ArrayList<>();
        ArrayList<Integer> headerIds = new ArrayList<>();
        for (String s : tasks) {
            try {
                JSONObject obj = JSONObject.parseObject(s);
                String orderId = obj.getString("OrderId");
                String orderTaskId = obj.getString("OrderTaskId");
                String orderTaskStatus = obj.getString("OrderTaskStatus");
                logger.info("单据{}推送目标：{}", orderId, s);
                if(s.contains("RejectedFlag")){
                    cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(s);
                    headerIds.add(Integer.valueOf(jsonObject.getStr("OrderTaskId").replaceAll("CPworkflow", "")));
                }
                XMLSerializer xmlSerializer = new XMLSerializer();
                xmlSerializer.setTypeHintsEnabled(false);
                xmlSerializer.setObjectName("DATAROW");
                JsonConfig jsonConfig = new JsonConfig();
                jsonConfig.setExcludes(new String[]{""});
                String data = xmlSerializer.write(JSONSerializer.toJSON(s));
                data = data.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "").replaceAll("null=\"true\"","");
                arrayResult += data;
                documentNums.add(orderId);
                size ++;

            } catch (Exception e) {
                logger.error("pushOaTasks出错=>{}", e.getMessage(),e);
            }
        }
        if (size == 0) {
            return "当前没有待办任务";
        }
        String result = todoConcat(size, arrayResult);
        logger.info("调用oaPushUrl参数=>" + result);
        Map<String, String> identity = new HashMap<>();
        identity.put("Authorization", authHeader);
        identity.put("Content-Type", "text/xml;charset=UTF-8");
        String receive = null;
        boolean isSuccess = false;
        try {
            receive = HttpPostString(oaPushUrl, identity, result);
            isSuccess = true;
            if(headerIds.size()>0)
            wanhuaMapper.deletePortalLog(headerIds);
        } catch (Exception e) {
            logger.error("wanhua OA 推送失败{}", e.getMessage(),e);
        }
        logger.info("调用oaPushUrl返回结果=>" + receive);
        wanhuaMapper.updateOaPushStatusByDocumentNums(documentNums, isSuccess ? "success" : "fail");
        String message = "";
        message = message + "本次发送数据：\n" + result + "\n本次收到数据：" + receive;
        return message;
    }

    @RequestMapping(value = "pushwechat", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject pushWechatTasks() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.WHX);
        List<String> tasks = wanhuaMapper.getWechatPushTask(wanhuaCompanyId);
        JSONArray jsonArray = new JSONArray();
        for (String s : tasks) {
            JSONObject jsonObject = JSONObject.parseObject(s);
            jsonArray.add(jsonObject);
        }
        JSONObject postData = new JSONObject();
        postData.put("data", jsonArray);
        return postData;
    }

    @RequestMapping(value = "getArchiveDocument", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getArchiveDocument(@RequestParam(value = "access_token") String accessToken) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(companyId);
        glBatchTransfer.setType("getArchiveDocument");
        glBatchTransfer.setInput("{}");
        List<String> tasks = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        JSONObject result = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (String s : tasks) {
            try {
                String processedFileName = "";
                JSONArray processedAttachment = new JSONArray();
                JSONArray trainAttachment = new JSONArray();
                JSONObject jsonObject = JSONObject.parseObject(s);
                String documentNum = jsonObject.getString("document_num");
                try {
                    String attaches = jsonObject.getString("attachment");
                    if (StringUtils.isNotEmpty(attaches)) {
                        String[] lists = attaches.split(",");
                        for (String list : lists) {
                            if (StringUtils.isEmpty(list)) {
                                continue;
                            }
                            if (processedFileName.indexOf(list) > -1) {
                                continue;
                            }
                            String url = S3Util.getS3URL(list);
                            processedAttachment.add(url);
                            processedFileName += "|" + list;
                        }
                    }
                } catch (Exception e) {
                    logger.error("get " +  documentNum + " ArchiveDocument=>", e);
                }

                try {
                    String invoiceAttaches = jsonObject.getString("invoice_attachment");
                    if (StringUtils.isNotEmpty(invoiceAttaches)) {
                        JSONArray invoiceAttachments = JSONArray.parseArray(invoiceAttaches);
                        for (Object o : invoiceAttachments) {
                            String att = ((JSONObject) o).getString("attachment_url");
                            if (StringUtils.isNotEmpty(att)) {
                                if (processedFileName.indexOf(att) > -1) {
                                    continue;
                                }
                                String url = S3Util.getS3URL(att);
                                processedAttachment.add(url);
                                processedFileName += "|" + att;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("get " + documentNum + " invoice_attachment=>", e);
                }
                jsonObject.put("attachment", processedAttachment);
                try {
                    String attaches = jsonObject.getString("train_type_attachment");
                    if (StringUtils.isNotEmpty(attaches)) {
                        String[] lists = attaches.split(",");
                        for (String list : lists) {
                            if (list.length() > 0) {
                                if (processedFileName.indexOf(list) > -1) {
                                    continue;
                                }
                                String url = S3Util.getS3URL(list);
                                trainAttachment.add(url);
                                processedFileName += "|" + list;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("get " + documentNum + " train_type_attachment=>", e);
                }
                try {
                    String invoiceAttaches = jsonObject.getString("train_invoice_attachment");
                    if (StringUtils.isNotEmpty(invoiceAttaches)) {
                        JSONArray invoiceAttachments = JSONArray.parseArray(invoiceAttaches);
                        for (Object o : invoiceAttachments) {
                            String att = ((JSONObject) o).getString("attachment_url");
                            if (null != att) {
                                if (processedFileName.indexOf(att) > -1) {
                                    continue;
                                }
                                String url = S3Util.getS3URL(att);
                                trainAttachment.add(url);
                                processedFileName += "|" + att;
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("get " + documentNum + " train_invoice_attachment=>", e);
                }
                jsonObject.put("trainAttachment", trainAttachment);
                jsonArray.add(jsonObject);
            } catch (Exception e) {
                logger.error("getArchiveDocument=>", e);
            }
        }
        result.put("data", jsonArray);
        logger.info("getArchiveDocument返回数据=>" + jsonArray);
        return result;
    }

    @RequestMapping(value = "getDeletedFiles", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject getDeletedFiles(@RequestParam(value = "access_token") String accessToken,
                                      @RequestBody JSONObject time) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(companyId);
        glBatchTransfer.setType("getDeletedFiles");
        glBatchTransfer.setInput(time.toJSONString());
        List<String> tasks = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        JSONObject result = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (String s : tasks) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(s);
                String attach = jsonObject.getString("attachment");
                String url = S3Util.getS3URL(attach);
                jsonObject.put("attachment", url);
                jsonArray.add(jsonObject);
            } catch (Exception e) {
                logger.error("getArchiveDocument=>", e);
            }
        }
        result.put("data", jsonArray);
        logger.info("getDeletedFiles=>" + jsonArray);
        return result;
    }


    //    {
//        "data": [{
//        "document_num": "FYC0000000001"
//    }]
//    }
    @RequestMapping(value = "setArchiveDocument", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject setArchiveDocument(@RequestParam(value = "access_token") String accessToken,
                                         @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            String arrayData = JSONObject.parseObject(json).getString("data");
            jsonArray = JSONObject.parseArray(arrayData);
        } catch (Exception e) {
            logger.error("setArchiveDocument=>", e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(companyId);
        glBatchTransfer.setType("setArchiveDocument");
        glBatchTransfer.setInput(jsonArray.toJSONString());
        List<String> tasks = wanhuaMapper.glBatchCreateWanhua(glBatchTransfer);
        return new JSONObject();
    }

    @RequestMapping(value = "wechatAuth", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public JSONObject wanhuaWechatAuth(@RequestBody JSONObject json) throws Exception {
        JSONObject ret = new JSONObject();
        String code = json.getString("code");
        String authUrl = wechatAuthUrl + "?code=" + code;
        String result = doNormalJsonGet(authUrl);
        logger.info("doNormalJsonGet返回结果=>" + result);
        result = result.replace("null(", "");
        result = result.substring(0, result.length() - 1);
        JSONObject resultObject = JSONObject.parseObject(result);
        if (0 == resultObject.getInteger("result_code")) {
            String userName;
            try {
                userName = resultObject.getJSONObject("result").getString("userName");
            } catch (Exception e) {
                logger.error("wanhuaWechatAuth=>", e);
                throw new ValidationException(resultObject.toJSONString());
            }
            String token = "";
            try {
                String encrypted = Encrypt(userName + "@whchem.com," + System.currentTimeMillis(), whhxKey);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("corp", whhxId);
                jsonObject.put("code", encrypted);
                String results = HttpPostString(oauthUrl, null, jsonObject.toJSONString());
                ret = JSONObject.parseObject(results);
                logger.info("wanhuaWechatAuth=>" + ret);
                if (ret.getString("token") != null) {
                    token = ret.getString("token");
                }
            } catch (Exception e) {
                logger.error("wanhuaWechatAuth=>", e);
                throw new ValidationException(e.getMessage());
            }
            if ("".equals(token)) {
                throw new ValidationException("登录失败");
            }
        } else {
            throw new ValidationException(resultObject.getString("result_msg"));
        }

        return ret;
    }

    // 加密
    public static String Encrypt(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            logger.info("Key为空null");
            return null;
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            logger.info("Key长度不是16位");
            return null;
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));

        return new org.apache.commons.codec.binary.Base64().encodeToString(encrypted);//此处使用BASE64做转码功能，同时能起到2次加密的作用。
    }

    /**
     * 格式化日期，指定分割符号
     * @param date
     * @param split
     * @return
     */
    public static String formatDate(String date, String split) {
        if(date == null || date.length() < 8) {
            return "";
        }
        String dateString = date.replaceAll("-", "").replaceAll("/", "");
        return dateString.substring(0, 4) + split + dateString.substring(4, 6) + split + dateString.substring(6,8);
    }

    public static void main(String[] args) {
        String split = "-";
        System.out.println(formatDate("2022/08/29", split));
        System.out.println(formatDate("20220829", split));
        System.out.println(formatDate("2022-08-29", split));
        System.out.println(formatDate("2022-08", split));
    }
}
