package com.cloudpense.expman.controller;


import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.service.HSFService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static java.nio.charset.StandardCharsets.ISO_8859_1;

@RestController
@RequestMapping(value = "/hsf")
public class HSFController {

    private Logger logger = LoggerFactory.getLogger(HSFController.class);

    private HSFService hsfService;

    @Autowired
    public HSFController(HSFService hsfService) {
        this.hsfService = hsfService;
    }

    @RequestMapping(value = "/info/update")
    public void updateData() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        //为避免父级单位晚于子级单位被插入导致子级单位中字段缺失,对子公司和部门进行二次同步
        /*try {
            logger.info("HSF同步公司信息开始...");
            hsfService.updateCompany(true);
            logger.info("HSF同步公司信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update company error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步公司信息开始...");
            hsfService.updateCompany(true);
            logger.info("HSF同步公司信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update company error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步部门信息开始...");
            hsfService.updateDept(true);
            logger.info("HSF同步部门信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update department error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步部门信息开始...");
            hsfService.updateDept(true);
            logger.info("HSF同步部门信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update department error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步职位信息开始...");
            hsfService.updatePosition(true);
            logger.info("HSF同步职位信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update position error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步职位信息开始...");
            hsfService.updatePosition(true);
            logger.info("HSF同步职位信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update position error: ", ignored);
        }*/
        /*try {
            logger.info("HSF同步用户信息开始...");
            hsfService.updateUser(true);
            logger.info("HSF同步用户信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update user error: ", ignored);
        }*/
        try {
            logger.info("HSF同步合同信息开始...");
            hsfService.updateNCContract(false);
            logger.info("HSF同步合同信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF update user error: ", ignored);
        }
        /*try {
            logger.info("HSF更新outdate信息开始...");
            hsfService.disableOutdatedData();
            logger.info("HSF更新outdate信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF disable outdated data error: ", ignored);
        }*/
        try {
            logger.info("HSF同步银行账户信息开始...");
            hsfService.updateBankAccount(true);
            logger.info("HSF同步银行账户信息结束！");
        } catch (Exception ignored) {
            logger.error("HSF disable outdated data error: ", ignored);
        }
    }

    @RequestMapping(value = "/info/dailyUpdate")
    public void updateAllData() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        try {
            hsfService.updateCompany(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update company error: ", ignored);
        }
        try {
            hsfService.updateCompany(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update company error: ", ignored);
        }
        try {
            hsfService.updateDept(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update department error: ", ignored);
        }
        try {
            hsfService.updateDept(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update department error: ", ignored);
        }
        try {
            hsfService.updatePosition(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update position error: ", ignored);
        }
        try {
            hsfService.updatePosition(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update position error: ", ignored);
        }
        try {
            hsfService.updateUser(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update user error: ", ignored);
        }
        try {
            hsfService.updateNCContract(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update NCContract error: ", ignored);
        }
        try {
            hsfService.updateBankAccount(false);
        } catch (Exception ignored) {
            logger.error("HSF daily update bank account error: ", ignored);
        }
    }

    @RequestMapping(value = "/claim/push")
    public void claimPush() {
        logger.info("hsf单据推送NC系统开始执行");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.claimPush();
        logger.info("hsf单据推送NC系统结束执行");
    }

    @RequestMapping(value = "/claim/repush")
    public void claimRepush(@RequestBody List<String> documentNums) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        if (documentNums != null && documentNums.size() > 0) {
            hsfService.claimRepush(documentNums);
        }
    }

    //    @Scheduled(cron = "0 0 5 * * ?")
    @RequestMapping(value = "/company/dailyUpdate")
    public void updateCompany() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateCompany(false);
    }

    @RequestMapping(value = "/company/update")
    public void updateAllCompany() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateCompany(true);
    }

    //    @Scheduled(cron = "0 15 5 * * ?")
    @RequestMapping(value = "/dept/dailyUpdate")
    public void updateDept() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateDept(false);
    }

    @RequestMapping(value = "/dept/update")
    public void updateAllDept() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateDept(true);
    }

    @RequestMapping(value = "/position/dailyUpdate")
    public void updatePosition() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updatePosition(false);
    }

    @RequestMapping(value = "/position/update")
    public void updateAllPosition() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updatePosition(true);
    }

    //    @Scheduled(cron = "0 30 5 * * ?")
    @RequestMapping(value = "/user/dailyUpdate")
    public void updateUser() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateUser(false);
    }

    @RequestMapping(value = "/user/update")
    public void updateAllUser() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateUser(true);
    }

    @RequestMapping(value = "/user/dailyUpdateBA")
    public void updateUserAcc() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateBankAccount(false);
    }

    @RequestMapping(value = "/user/updateBA")
    public void updateAllUserAcc() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateBankAccount(true);
    }

    @RequestMapping(value = "/ntf/push")
    public void ntfPush() {
        logger.info("hsf待办推送OA系统开始执行");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.ntfPush();
        logger.info("hsf待办推送OA系统结束执行");
    }

    @RequestMapping(value = "/OAToken")
    public void getOAToken() {
        logger.info("hsf获取OA系统token开始执行");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateOAToken();
        logger.info("hsf获取OA系统token结束执行");
    }

    @RequestMapping(value = "/reNtf")
    public void refreshNtf() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.refreshNtf();
    }

    @ResponseBody
    @RequestMapping(value = "/oa/token")
    public String oaGetToken(@RequestParam(value = "id") String id) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        return hsfService.encrypt(id);
    }

    @ResponseBody
    @RequestMapping(value = "/contract", produces = "application/json;charset=UTF-8", method = RequestMethod.POST)
    public String saveContract(@RequestBody String str) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        try {
            if (str.equals(new String(str.getBytes(ISO_8859_1), ISO_8859_1))) {
                str = new String(str.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
            }
        } catch (Exception ignored) {
            logger.error("saveContract出现异常：", ignored);
        }
        logger.info("saveContract接口入参：" + str);
        JSONObject result = hsfService.saveOAContract(str);
        return result.toJSONString();
    }


    @RequestMapping(value = "/con/update")
    public void updateContract() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateNCContract(true);
    }

    @RequestMapping(value = "/con/dailyUpdate")
    public void updateContractDaily() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        hsfService.updateNCContract(false);
    }

    /**
     * 获取推送错误的单据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     */
    @RequestMapping(value = "/errorPushedClaims")
    public JSONObject getErrorPushedClaims(@RequestParam String startDate, @RequestParam String endDate) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.F);
        JSONObject result = hsfService.getErrorPushedClaims(startDate, endDate);
        return result;
    }

}
