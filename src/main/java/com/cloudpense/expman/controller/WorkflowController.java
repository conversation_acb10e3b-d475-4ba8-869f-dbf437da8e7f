package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.service.CheryJaguarLandRoverToDoService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.service.WorkflowService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping(value = "api/v3/workflow")
public class WorkflowController {

    @Autowired
    private OpenApiService openApiService;
    @Autowired
    WorkflowService workflowService;
    @Autowired
    private CheryJaguarLandRoverToDoService cheryJaguarLandRoverToDoService;

    @RequestMapping(value = "ToDo/getWorkflowCount", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public JSONObject getToDoWorkflowCount(@RequestParam(value = "access_token") String accessToken, @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        String PERNR;
        try {
            JSONObject request = JSONObject.parseObject(json);
            PERNR = request.getString("PERNR");
        }catch (Exception ex){
            ex.printStackTrace();
            return cheryJaguarLandRoverToDoService.generateRtn1(null, null, "输入JSON解析出错", "输入JSON解析出错", 1001);
        }
        JSONObject ret = new JSONObject();
        int cnt = workflowService.getToDoWorkflowCount(companyId,PERNR);
        ret.put("PERNR",PERNR);
        ret.put("NUMBER",cnt);
        return  ret;
    }
}
