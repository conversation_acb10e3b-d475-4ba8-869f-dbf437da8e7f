package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.CustomUserDetails;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.service.ShangFaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2019/11/20 15:09
 */
@RestController
@RequestMapping(value = "/shangFa")
public class ShangFaController {

    private ShangFaService shangFaService;

    @Autowired
    public ShangFaController(ShangFaService shangFaService) {
        this.shangFaService = shangFaService;
    }

    @RequestMapping(value = "/sapUserStatus",method = RequestMethod.POST)
    public void sapUserStatus() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.SSF);
        shangFaService.sapUserStatus();
    }

    @RequestMapping(value = "/shangFaVoucherTest",method = RequestMethod.POST)
    public String shangFaVoucherTest(@RequestParam String documentNum) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.SSF);
        return  shangFaService.shangfaVoucherTest(documentNum,"云简技术支持3",new StringBuilder()).getPostData();
    }


    @RequestMapping(value = "/shangFaVoucher",method = RequestMethod.POST)
    public String shangFaVoucher(@RequestBody JSONObject jsonObject) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.SSF);

        return  shangFaService.shangFaVoucher(jsonObject);
    }


    @RequestMapping(value = "/shangfaBuTie",method = RequestMethod.POST)
    public String shangfaBuTie(String stringInput, ExpClaimHeader ech, CustomUserDetails activeUser, String locale) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.SSF);

        return  shangFaService.shangfaBuTie(stringInput,ech,activeUser,locale);
    }

}
