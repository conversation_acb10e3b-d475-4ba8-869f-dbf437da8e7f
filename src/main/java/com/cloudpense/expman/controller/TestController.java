package com.cloudpense.expman.controller;

import com.cloudpense.expman.util.MQProducer;
import com.cloudpense.expman.util.SnowFlakeUtil;
import com.cloudpense.message.strategy.common.dto.DocumentMQStrategyDTO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

/**
 * 类功能描述
 *
 * <AUTHOR>
 * @Date 2024/4/2
 */
@Controller
@RequestMapping("/test")
public class TestController {

    @Resource
    MQProducer mqProducer;

    @RequestMapping("/send2Message")
    @ResponseBody
    public void mqProducer() {
        DocumentMQStrategyDTO d = new DocumentMQStrategyDTO();
        d.setPathId(1L);
        d.setDocumentNum("EXP0000291118");
        d.setHeaderId(2072416L);
        d.setStrategyCode("document_approved");
        d.setStrategyId(SnowFlakeUtil.getId());
        d.setStatus("approved");
        mqProducer.send2MessageCenter(d, 15220L, 0L);
    }

}
