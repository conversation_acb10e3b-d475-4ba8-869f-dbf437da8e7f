package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.LianDianRequestParameter;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.service.WorkflowService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.util.LianDianRequestUtil;
import com.cloudpense.expman.util.LianDianResponseUtil;
import com.cloudpense.expman.util.LruMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ResourceBundle;


@RestController
@RequestMapping(value = "api/v3/liandian")
public class LianDianController {
    private static final Logger logger = LoggerFactory.getLogger(LianDianController.class);

    @Autowired
    private OpenApiService openApiService;

    private static ResourceBundle rb = ResourceBundle.getBundle("urisetting");
    private LruMap<String, JSONObject> cache = new LruMap<>(capacity);
    private static final long expiredTime = Long.parseLong(rb.getString("liandian.todoCount.expireTime"));
    private static final int capacity = Integer.parseInt(rb.getString("liandian.todoCount.capacity"));
    @Autowired
    private WorkflowService workflowService;

    /**
     * @apiDefine apiToDo  联电获取待办数量
     * @param accessToken
     * @param lianDianRequestParameter
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "ToDo/getWorkflowCount", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public JSONObject getToDoCount(@RequestParam(value = "access_token") String accessToken, @RequestBody LianDianRequestParameter lianDianRequestParameter) throws Exception {
        String getRequestTime = lianDianRequestParameter.getEsbInfo().getRequestTime(); //获取传来的请求时间（可为空）
        String instId = lianDianRequestParameter.getEsbInfo().getInstId();//获取传来的流水号（可为空）
        //获取传来的attr1、attr2、attr3
        String attr1 = lianDianRequestParameter.getEsbInfo().getAttr1();
        String attr2 = lianDianRequestParameter.getEsbInfo().getAttr2();
        String attr3 = lianDianRequestParameter.getEsbInfo().getAttr3();
        //获取传来的员工号（不可为空）
        if(lianDianRequestParameter.getRequestInfo().getEmployeeId() == null){
            return LianDianResponseUtil.dataProcessing(getRequestTime,"W", null, null, instId, attr1, attr2, attr3);
        }
        String employeeNumber = lianDianRequestParameter.getRequestInfo().getEmployeeId();
        //调用接口获取员工待办数量
        try{
            JSONObject middleInputJson = LianDianRequestUtil.ReturnObject(employeeNumber);
            logger.info("middleInputJson==>" + middleInputJson);

            int companyId = openApiService.authCompany(accessToken);
            String PERNR;
            try {
                JSONObject request = JSONObject.parseObject(middleInputJson.toJSONString());
                PERNR = request.getString("PERNR");
            } catch (Exception ex) {
                logger.error("JSON 解析错误==>", ex);
                return LianDianResponseUtil.dataProcessing(getRequestTime,"E", employeeNumber, null, instId, attr1, attr2, attr3);
            }

            JSONObject cacheItem = cache.get(PERNR);

            long timeDiff = 0L;
            if (cacheItem != null) {
                long currentTime = System.currentTimeMillis();
                long fistCacheTime = (long)cacheItem.get("timestamp");
                timeDiff = currentTime - fistCacheTime;
                logger.info("{} time diff : {}ms", PERNR, timeDiff);
            }

            if (cacheItem == null || timeDiff > expiredTime) {
                cacheItem = new JSONObject();
                int cnt = workflowService.getToDoWorkflowCount(companyId, PERNR);
                JSONObject ret = new JSONObject();
                ret.put("PERNR", PERNR);
                ret.put("NUMBER", cnt);
                cacheItem.put("timestamp", System.currentTimeMillis());
                cacheItem.put("ret", ret);
                cache.put(PERNR, cacheItem);
                logger.info("cache size : {}", cache.size());
            }

            JSONObject resultInfo = (JSONObject)cacheItem.get("ret");
            logger.info("resultInfo==>" + resultInfo);
            return LianDianResponseUtil.dataProcessing(getRequestTime, "S", employeeNumber, resultInfo, instId, attr1, attr2, attr3);

        }catch (Exception e) {
            logger.error("过程处理出错==>", e);
            return LianDianResponseUtil.dataProcessing(getRequestTime,"E", employeeNumber, null, instId, attr1, attr2, attr3);
        }
    }
}
