package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.TokenVo;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.exception.ValidationException;
import com.cloudpense.expman.mapper.OauthMapper;
import com.cloudpense.expman.util.JWTUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.TimeZone;
import java.util.concurrent.ConcurrentHashMap;

import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON;

@RestController
@RequestMapping(value = "")
public class OpenApiOauthRestController {
    private final OauthMapper oauthMapper;

    //过期时间2小时
    private static final long EXPIRE_TIME = 1000 * 60 * 60 * 2;
    private static ResourceBundle RB = ResourceBundle.getBundle("token");
    private static String secret = RB.getString("token_Secret");
    private static Map<String, String> cache = new ConcurrentHashMap<>();

    @Autowired
    public OpenApiOauthRestController(final OauthMapper oauthMapper) {
        this.oauthMapper = oauthMapper;
    }

    private static final Logger logger = LoggerFactory.getLogger(OpenApiOauthRestController.class);
    /**
     * @apiDefine apiTokenGroup 授权
     */

    /**
     * @api {POST} /open/oauth/token 获取access_token
     * @apiGroup apiTokenGroup
     * @apiVersion 0.0.1
     * @apiDescription 获取access_token是调用简约费控API接口的第一步，相当于创建了一个登录凭证，其它的业务API接口，都需要依赖于access_token来鉴权调用者身份。
     * @apiParam (入参) {String} client_id 平台ID
     * @apiParam (入参) {String} client_secret 平台密钥
     * @apiParam (入参) {String} grant_type 授权类型
     * @apiSuccess (Success 200) {String} access_token 获取到的凭证
     * @apiSuccess (Success 200) {String} expires_in 凭证的有效时间（秒）
     * @apiSuccess (Success 200) {String} token_type 凭证类型
     * @apiSuccess (Success 200) {String} scope 权限类型
     * @apiSuccessExample {json} 返回样例:
     * {
     * "access_token": "54e9246a-1e54-4685-9b9c-017600efe9f5",
     * "token_type": "bearer",
     * "expires_in": 604596,
     * "scope": "read write"
     * }
     */
    @RequestMapping(value = "open/oauth/token", method = {RequestMethod.POST, RequestMethod.GET}, produces = APPLICATION_JSON)
    public JSONObject uaesToken(@RequestParam(value = "grant_type") String grantType,
                                @RequestParam(value = "client_id") String clientId,
                                @RequestParam(value = "client_secret") String clientSecret) throws Exception {

        if (!"client_credentials".equals(grantType)) {
            //grantType类型不符
            throw new ValidationException("Bad credentials");
        }

        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        String key = clientId;

        String companyId = cache.get(key);
        if (StringUtils.isBlank(companyId)) {
            TokenVo tokenVo = oauthMapper.getTokenVo(clientId, clientSecret);
            if (tokenVo == null) {
                throw new ValidationException("Bad credentials");
            }
            companyId = tokenVo.getCompanyId().toString();
            cache.put(key, companyId);
        }

        //JWT生成token并设置有效时间为2h
        String token = JWTUtil.sign(key, secret, EXPIRE_TIME);

        JSONObject object = new JSONObject();
        object.put("access_token", token);
        object.put("token_type", "bearer");
        object.put("expires_in", 60 * 60 * 2);
        object.put("scope", "write");
        logger.info("获取JWT token == company_id: {}, token: {}", companyId, token);
        return object;

    }
}
