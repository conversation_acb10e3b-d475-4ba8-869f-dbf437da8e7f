package com.cloudpense.expman.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.mapper.HeraeusMapper;
import com.cloudpense.expman.mapper.WanhuaMapper;
import com.cloudpense.expman.service.BaanService;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.service.SAPService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.util.PropertiesUtil;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jca.context.SpringContextResourceAdapter;
import org.springframework.scheduling.annotation.Scheduled;

import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.ResourceBundle;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@RestController
@RequestMapping(value = "")

public class SAPController {
    private SAPService sapService;
    private BaanService baanService;
    private OpenApiService openApiService;
    private WanhuaMapper wanhuaMapper;
    @Autowired
    private HeraeusMapper heraeusMapper;



    private static Logger logger = LoggerFactory.getLogger(SAPController.class);


    @Autowired
    public SAPController(SAPService sapService,
                         BaanService baanService,
                         OpenApiService openApiService,
                         WanhuaMapper wanhuaMapper) {
        this.sapService = sapService;
        this.baanService = baanService;
        this.openApiService = openApiService;
        this.wanhuaMapper = wanhuaMapper;
    }

    @RequestMapping(value = "/sap/status", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    public JSONObject test() throws Exception{
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("status", "on");
        return jsonObject;
    }

    @RequestMapping(value = "/sap/scan", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
//    @Scheduled(fixedDelay = 100)
    public void scan(HttpServletRequest request) throws Exception{
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        String type = request.getParameter("type");
        if (type.equals("FMI01")) {
            sapService.receiveSapData("FMI01");
        }
        if (type.equals("FMI02")) {
            sapService.receiveSapData("FMI02");
        }
        if (type.equals("FMI03")) {
            sapService.receiveSapData("FMI03");
        }
        if (type.equals("FMI04")) {
            sapService.receiveSapData("FMI04");
        }
        if (type.equals("FMI06")) {
            sapService.receiveSapData("FMI06");
        }
        if (type.equals("SVI01")) {
            sapService.receiveSapData("SVI01");
        }
        if (type.equals("CTI01")) {
            sapService.scanClaimPositionId(3591, "CTI01");
        }
        if (type.equals("PRI01")) {
            sapService.scanClaimPositionId(3453, "PRI01");
        }
        if (type.equals("POI01")) {
            sapService.scanClaimPositionId(3454, "POI01");
        }
        if (type.equals("GRI01")) {
            sapService.scanClaimPositionId(3455, "GRI01");
        }
        if (type.equals("PRI02")) {
            sapService.receiveSapData("PRI02");
        }
        if (type.equals("POI02")) {
            sapService.receiveSapData("POI02");
        }
        if (type.equals("GRI02")) {
            sapService.receiveSapData("GRI02");
        }
        if (type.equals("FMI10")) {
            sapService.receiveSapData("FMI10");
        }
        if (type.equals("CTI02")) {
            sapService.receiveSapData("CTI02");
        }
        if (type.equals("FMI09")) {
            sapService.scanClaimPositionId(3592, "FMI09");
        }
        if (type.equals("VDI01")) {
            sapService.scanClaimPositionId(0, "VDI01");
        }
        if (type.equals("FMI07")) {
            sapService.receiveSapData("FMI07");
        }
//        sapService.receiveSapData("POI02");
//        sapService.receiveSapData("GRI02");
//        sapService.receiveSapData("FMI10");
//        sapService.scanClaimPositionId(3365, "PRI01");
//        sapService.scanClaimPositionId(3366, "POI01");
//        sapService.scanClaimPositionId(3367, "GRI01");
    }

    @RequestMapping(value = "/sap/basic_info", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 40 16 * * ?")
    public void basic() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.receiveSapData("FMI04");
        sapService.receiveSapData("FMI02");
        sapService.receiveSapData("FMI03");
        sapService.receiveSapData("FMI06");
    }

    @RequestMapping(value = "/sap/supplier", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 0 * * * ?")
    public void supplierInfo() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.receiveSapData("SVI01");
        
    }

    @RequestMapping(value = "/sap/budget", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 30 17 * * ?")
    public void budget() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.receiveSapData("FMI07");
        
    }

    @RequestMapping(value = "/sap/cti01", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 10 * * * ?")
    public void CTI01() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.scanClaimPositionId(3591, "CTI01");
        
    }

    @RequestMapping(value = "/sap/pri01", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 20 * * * ?")
    public void PRI01() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.scanClaimPositionId(3453, "PRI01");
        
    }

    @RequestMapping(value = "/sap/poi01", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 30 * * * ?")
    public void POI01() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.scanClaimPositionId(3454, "POI01");
        
    }

    @RequestMapping(value = "/sap/gri01", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 40 * * * ?")
    public void GRI01() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.scanClaimPositionId(3455, "GRI01");
        
    }

//    @RequestMapping(value = "fmi09", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
//    public String FMI09(HttpServletRequest request) throws Exception {
//        sapService.scanClaimPositionId(3592, "FMI09");
//        
//    }

    @RequestMapping(value = "/sap/user", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 10 * * * ?")
    public void User() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        sapService.scanClaimPositionId(0, "VDI01");
        
    }

    @RequestMapping(value = "/sap/receive", method = RequestMethod.GET, produces = CommonConstants.APPLICATION_JSON_UTF)
    //@Scheduled(cron = "0 50 * * * ?")
    public void Receive() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        try {
            sapService.receiveSapData("PRI02");
        } catch (Exception e) {
            logger.error("Receive ERROR:",e);
        }
        try {
            sapService.receiveSapData("POI02");
        } catch (Exception e) {
            logger.error("Receive ERROR:",e);
        }
        try {
            sapService.receiveSapData("GRI02");
        } catch (Exception e) {
            logger.error("Receive ERROR:",e);
        }
        try {
            sapService.receiveSapData("FMI10");
        } catch (Exception e) {
            logger.error("Receive ERROR:",e);
        }
        try {
            sapService.receiveSapData("CTI02");
        } catch (Exception e) {
            logger.error("Receive ERROR:",e);
        }

        
    }

//
//    @RequestMapping(value = "/sap/send_by_document_num", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
//    public String sendDocumentNum(HttpServletRequest request) throws Exception {
//        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
//        String type = request.getParameter("type");
//        String documentNum = request.getParameter("document");
//        sapService.sendByDocumentNum(documentNum, type);
//        return "success";
//    }
//
//    @RequestMapping(value = "/sap/send_by_type", method = RequestMethod.GET, produces = APPLICATION_JSON_UTF)
//    public String sendDocumentHeaderType(HttpServletRequest request) throws Exception {
//        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
//        String type = request.getParameter("type");
//        int headerTypeId = Integer.parseInt(request.getParameter("header_type_id"));
//        sapService.sendByHeaderTypeId(headerTypeId, type);
//        return "success";
//    }


    @RequestMapping(value = "phoenix/ibmpapprove", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixIBMPApprove3(@RequestBody String str) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iBMPApprove();
    }
    @RequestMapping(value = "phoenix/ibmppost", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixIBMPPost(@RequestBody String str) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iBMPExpensePost();
    }
    @RequestMapping(value = "phoenix/iportalapprove", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixIPortalApprove(@RequestBody String str) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iPortalApprove();
    }
    @RequestMapping(value = "phoenix/iportalpost", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixIPortalPost(@RequestBody String str) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iPortalTravelPost();
    }
    @RequestMapping(value = "phoenix/iportalpush", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixIPortalPush(@RequestBody String str) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iPortalPush();
    }

    @RequestMapping(value = "phoenix/syncall", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
//    @Scheduled(cron = "0 0 18 * * ?")
    public void PhoenixSync() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.phoenixSync();
    }

    @RequestMapping(value = "phoenix/syncsupplier", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
//    @Scheduled(cron = "0 0 18 * * ?")
    public void PhoenixSyncSupplier() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.phoenixSupplier();
    }

    @RequestMapping(value = "phoenix/hrpost", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
//    @Scheduled(cron = "0 0 18 * * ?")
    public void PhoenixHrPost() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.phoenixHrPost();
    }

    @RequestMapping(value = "phoenix/iportaldelete", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
//    @Scheduled(cron = "0 0 18 * * ?")
    public void PhoenixIportalDelete() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        try {
            sapService.iPortalDeleteGenerate();
        } catch (Exception e) {
            logger.error("PhoenixIportalDelete ERROR:",e);
        }
        try {
            sapService.iPortalPush();
        } catch (Exception e) {
            logger.error("PhoenixIportalDelete ERROR:",e);
        }
    }

    @RequestMapping(value = "phoenix/iportaldeletereset", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
//    @Scheduled(cron = "0 0 18 * * ?")
    public void PhoenixIportalDeleteReset(@RequestBody JSONObject jsonObject) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        sapService.iPortalDeleteReset(jsonObject);
    }

    @RequestMapping(value = "phoenix/iportalpostall", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON)
    public void PhoenixPostAll() throws Exception {
        final String LOG_KEY = "phoenix iportalpostall===";
        logger.info("{}开始执行", LOG_KEY);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
//        try {
//            sapService.iPortalDeleteGenerate();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            logger.info("{}iBMPApprove开始执行", LOG_KEY);
            sapService.iBMPApprove();
            logger.info("{}iBMPApprove完成执行", LOG_KEY);
        } catch (Exception e) {
            logger.error("{}iBMPApprove执行出现未知异常:",e);
        }
        try {
            logger.info("{}iBMPExpensePost开始执行", LOG_KEY);
            sapService.iBMPExpensePost();
            logger.info("{}iBMPExpensePost完成执行", LOG_KEY);
        } catch (Exception e) {
            logger.error("{}iBMPExpensePost执行出现未知异常:",e);
        }
        try {
            logger.info("{}iPortalApprove开始执行", LOG_KEY);
            sapService.iPortalApprove();
            logger.info("{}iPortalApprove完成执行", LOG_KEY);
        } catch (Exception e) {
            logger.error("{}iPortalApprove执行出现未知异常:",e);
        }
        try {
            logger.info("{}iPortalTravelPost开始执行", LOG_KEY);
            sapService.iPortalTravelPost();
            logger.info("{}iPortalTravelPost完成执行", LOG_KEY);
        } catch (Exception e) {
            logger.error("{}iPortalTravelPost执行出现未知异常:",e);
        }
        try {
            logger.info("{}phoenixHrPost开始执行", LOG_KEY);
            sapService.phoenixHrPost();
            logger.info("{}phoenixHrPost完成执行", LOG_KEY);
        } catch (Exception e) {
            logger.error("{}phoenixHrPost执行出现未知异常:",e);
        }
//        try {
//            sapService.iPortalPush();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @RequestMapping(value = "phoenix/biget", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public void PhoenixFetchBi() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        try {
            /*BI凭证回传*/
            baanService.voucherPassBack();
        } catch (Exception e){
            logger.error("PhoenixFetchBi ERROR:",e);
        }
    }
    @RequestMapping(value = "phoenix/bisupplier", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public void PhoenixFetchBiSupplier() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        try {
            /*BI员工主数据回传*/
            sapService.phoenixSupplier();
        } catch (Exception e){
            logger.error("PhoenixFetchBiSupplier ERROR:",e);
        }
    }

    @RequestMapping(value = "phoenix/hrcheck", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public String PhoenixHrCheck(@RequestBody String json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.E);
        JSONObject jsonObject = JSONObject.parseObject(json);
        return sapService.phoenixHrCheck(jsonObject);
    }

    @RequestMapping(value = "meiya/doc", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public String MeiyaDoc(@RequestBody JSONObject jsonObject) throws Exception {
        String KEYWORD = "美亚凭证推送===";
        logger.info("{}收到请求={}", KEYWORD, JSON.toJSONString(jsonObject));
        //
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(jsonObject.getInteger("companyId"));
        glBatchTransfer.setUserId(jsonObject.getInteger("userId"));
        glBatchTransfer.setInput(jsonObject.getString("input"));
        glBatchTransfer.setLanguage(jsonObject.getString("language"));
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);

        return sapService.meiyaPostDocument(glBatchTransfer);
    }

    @RequestMapping(value = "heraeus/doc1", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public String heraeusDoc1(@RequestBody JSONObject jsonObject) throws Exception {
        String KEYWORD = "贺利氏凭证推送===";
        logger.info("{}收到请求={}", KEYWORD, JSON.toJSONString(jsonObject));
        if (22180 == jsonObject.getInteger("companyId")) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
        } else if(17239 == jsonObject.getInteger("companyId") || 3954 == jsonObject.getInteger("companyId")) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        } else {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        }

        ResourceBundle rb = ResourceBundle.getBundle("urisetting");
        String industrialCode = rb.getString("heraeus.industrialCode");
        Integer companyId = jsonObject.getInteger("companyId");
        JSONObject inputObject = JSONObject.parseObject(jsonObject.getString("input"));
        if (companyId == 17239) {
            try {
                inputObject.put("glDate", inputObject.getString("date"));
                jsonObject.put("input", inputObject.toJSONString());
                logger.info("日期:{}",inputObject.getString("date"));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


        JSONArray jsonArray = inputObject.getJSONArray("document_id");
        List<Integer> documentIds = new ArrayList();
        for (Object o : jsonArray) {
            Integer docId = (Integer) o;
            List<String> departmentCodes = heraeusMapper.getDepartmentCodeByCompanyIdAndDocumentId(companyId, docId);
            logger.info("{}departmentCodes: {}", KEYWORD, departmentCodes);
            String documentNum = heraeusMapper.getDocumentNumByDocumentId(docId);
            if (departmentCodes != null && departmentCodes.size() > 0 && departmentCodes.get(0).equals(industrialCode)) {

                //保存凭证日期  因为对方不会回传


                inputObject.put("ExpenseReportNumber", documentNum);
                inputObject.put("DocumentNumber", docId);
                logger.info("{}inputObject: {}", KEYWORD, inputObject.toJSONString());
                sapService.generateDocument(companyId, inputObject);
//                continue;
            } else {
                documentIds.add(docId);
            }
            if (companyId == 17239) {
//                updateGlDate(KEYWORD, companyId, inputObject, documentNum);
                saveGlDate(inputObject,jsonObject.getInteger("companyId"),documentNum);
            }

        }
        inputObject.put("document_id", documentIds);

        if (documentIds.size() == 0) {
            JSONObject ret = new JSONObject();
            ret.put("exception_level", 0);
            ret.put("message", "OK");
            return ret.toJSONString();
        }

        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(jsonObject.getInteger("companyId"));
        glBatchTransfer.setUserId(jsonObject.getInteger("userId"));
        glBatchTransfer.setInput(jsonObject.getString("input"));
        glBatchTransfer.setLanguage(jsonObject.getString("language"));
        if (22180 == jsonObject.getInteger("companyId")) {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
        } else {
            CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        }
        logger.info("{}GlBatchTransfer={}", KEYWORD, JSONObject.toJSONString(glBatchTransfer));
        return sapService.heraeusPostDocument(glBatchTransfer);
    }

    private void updateGlDate(String KEYWORD, Integer companyId, JSONObject inputObject, String documentNum) {
        inputObject.put("ExpenseReportNumber", documentNum);
//        inputObject.put("DocumentNumber", UUID.randomUUID().toString());
        inputObject.put("glDate", inputObject.getString("date"));
        logger.info("{}inputObject: {}", KEYWORD, inputObject.toJSONString());
        sapService.generateDocument(companyId, inputObject);
    }

    Lock lock = new ReentrantLock();
    private void saveGlDate(JSONObject inputObject, Integer companyId, String documentNum) {

        if (companyId == 17239) {
            try {
                lock.lock();
                ResourceBundle rb = ResourceBundle.getBundle("urisetting");
                String tomcatPath = rb.getString("helishi.tomcatPath");
                String pathname = tomcatPath + File.separator + "gldate.json";
                File file = new File(pathname);
                logger.info("单据:{},tomcat路径:{},{}",documentNum,tomcatPath, pathname);
                if (!file.exists()&&file.createNewFile()) {
                    logger.info("单据:{},文件不存在且创建成功:{}",documentNum,true);
                    JSONObject gldates = new JSONObject();
                    gldates.put(documentNum, inputObject.getString("date"));
                    FileUtils.writeStringToFile(file,gldates.toJSONString(),"UTF-8");
                }else {
                    JSONObject gldates = JSON.parseObject(FileUtils.readFileToString(file, "UTF-8"));
                    gldates.put(documentNum, inputObject.getString("date"));
                    logger.info("单据:{},文件存在且写入成功:{}",documentNum,gldates.toJSONString());
                    FileUtils.writeStringToFile(file,gldates.toJSONString(),"UTF-8");
                }
//                if (FileUtil.exist("gldate.json")) {
//                    String s = FileUtil.readString("gldate.json", "UTF-8");
//                    JSONObject gldates = JSON.parseObject(s);
//                    gldates.put(documentNum, inputObject.getString("date"));
//                    FileUtil.writeBytes(gldates.toJSONString().getBytes(StandardCharsets.UTF_8), "gldate.json");
//                } else {
//                    FileWriter fileWriter = new FileWriter("gldate.json");
//                    JSONObject gldates = new JSONObject();
//                    gldates.put(documentNum, inputObject.getString("date"));
//                    fileWriter.write(gldates.toJSONString());
//                }

            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                lock.unlock();
            }
        }

    }



    @RequestMapping(value = "meiya/voucher/receive", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public void MeiyaVoucherReceive() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        sapService.meiyaVoucherReceive();
    }

    @RequestMapping(value = "wanhua/user", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public JSONObject wanhuaUpdateUser(@RequestParam(value = "access_token") String accessToken,
                                       @RequestBody String json) throws Exception {
        int companyId = openApiService.authCompany(accessToken);
        JSONArray jsonArray;
        try {
            jsonArray = JSONObject.parseObject(json).getJSONArray("users");
        } catch (Exception e) {
            logger.error("wanhuaUpdateUser ERROR:",e);
            return openApiService.generateRtn(null, null, "输入JSON解析出错", 1001);
        }
        try {
            FndQuery fndQuery = new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonArray.toJSONString());
            fndQuery.setType("employee");
            wanhuaMapper.commonUpdate(fndQuery);
            if (fndQuery.getReturnCode().equals("S")) {
                return openApiService.generateRtn(null, null, null, 0);
            } else {
                return openApiService.generateRtn(null, null, fndQuery.getReturnMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("wanhuaUpdateUser ERROR:",e);
            return openApiService.generateRtn(null, null, "数据库执行异常", 1003);
        }
    }

    @RequestMapping(value = "cjlr/doc", method = RequestMethod.POST, produces = CommonConstants.APPLICATION_JSON_UTF)
    public String cjlrDoc(@RequestBody JSONObject jsonObject) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CJLR);
        System.out.println(jsonObject);
        GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
        glBatchTransfer.setCompanyId(jsonObject.getInteger("companyId"));
        glBatchTransfer.setUserId(jsonObject.getInteger("userId"));
        glBatchTransfer.setInput(jsonObject.getString("input"));
        glBatchTransfer.setLanguage(jsonObject.getString("language"));
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CJLR);
        System.out.println("glBatchTransfer==>"+JSONObject.toJSONString(glBatchTransfer));
        return sapService.cjlrPostDocument(glBatchTransfer);
    }

}
