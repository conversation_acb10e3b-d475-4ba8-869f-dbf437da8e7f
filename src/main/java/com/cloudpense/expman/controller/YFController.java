package com.cloudpense.expman.controller;

import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.service.YFService;
import com.cloudpense.expman.util.Constants.CommonConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/yanfeng")
public class YFController {
    @Autowired
    private YFService yfService;

    @RequestMapping(value = "/voucherOutput",method = RequestMethod.POST,produces = CommonConstants.APPLICATION_JSON)
    public void yfPaperGenerating(){
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        yfService.yfPaperGenerating();
    }

    @RequestMapping(value = "/mainDataGet",method = RequestMethod.POST,produces = CommonConstants.APPLICATION_JSON)
    public void personMainData(){
        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
        yfService.personMainData();
    }
}
