package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.service.MeiYaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "api/v2/meiya")
public class MeiYaController {

    private MeiYaService meiYaService;

    @Autowired
    public MeiYaController(MeiYaService meiYaService){
        this.meiYaService = meiYaService;
    }

    @RequestMapping(value = "dingtalk/sync/user", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    @Scheduled(cron = "0 0 01 * * ?")
    public void syncMeiYaUserInfoByDingDing() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        meiYaService.syncMeiYaUserInfoByDingDing();
    }

    @RequestMapping(value = "dingtalk/push/tasks", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    @Scheduled(cron = "0 0/2 * * * ?")
    public void pushDingTalkTasks() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        meiYaService.pushDingTalkTasks();
    }

//    @RequestMapping(value = "dingtalk/update/taskstatus", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
//    @Scheduled(cron = "0 0/2 * * * ?")
//    public void updateDingTalkTaskStatus() throws Exception {
//        CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
//        meiYaService.updateDingTalkTaskStatus();
//    }



    @RequestMapping(value = "dingTalkAuth", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public JSONObject meiyaDingTalkAuth(@RequestBody JSONObject json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        return meiYaService.meiyaDingTalkAuth(json);
    }



}
