package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.mapper.HeraeusMapper;
import com.cloudpense.expman.mapper.MeiyaMapper;
import com.cloudpense.expman.mapper.PhoenixMapper;
import com.cloudpense.expman.mapper.SapMapper;
import com.cloudpense.expman.service.SAPService;
import com.cloudpense.expman.webService.phoenix.ForPhoenixResponse;
import net.sf.json.xml.XMLSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;


@RestController
@RequestMapping(value = "/heraeus")
public class HeraeusController {

    public static int heraeus1CompanyId = 17239;
    public static int heraeus2CompanyId = 22180;
    public static String HeraeusAuth = "Basic VV9TQVA6XEBtaGwsYSV0WD9CO2VKPTErPzcuKkBxVQ==";
    private HeraeusMapper heraeusMapper;
    private SapMapper sapMapper;
    private static Logger logger = LoggerFactory.getLogger(HeraeusController.class);
    @Autowired
    private SAPService sapService;

    @Autowired
    public HeraeusController(HeraeusMapper heraeusMapper,
                             SapMapper sapMapper){
        this.heraeusMapper = heraeusMapper;
        this.sapMapper = sapMapper;
    }

    public String generateReturn(String code, String message, String nsKey) throws Exception {
        if (null == code) {
            code = "Y";
        }
        if (message == null) {
            message = "";
        }
        return "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\"><soap:Body><ns2:" + nsKey + " xmlns:ns2=\"http://heraeus.webService.expman.cloudpense.com/\"><return><RESPONSE_STATUS>"
                + code
                + "</RESPONSE_STATUS><RESPONSE_MESSAGE>"
                + message
                + "</RESPONSE_MESSAGE></return></ns2:" + nsKey + "></soap:Body></soap:Envelope>";
    }

    @RequestMapping(value = "/employeeInfo1",method = RequestMethod.POST, produces = "application/xml")
    public String heraeusEmployeeUpdate(@RequestBody String str,
                                             @RequestHeader("Authorization") String auth) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        JSONObject received = new JSONObject();
        received.put("data", str);
        JSONObject heraeusAuth = new JSONObject();
        heraeusAuth.put("HeraeusAuthorization", auth);
        sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeusOri", heraeusAuth.toJSONString());
        sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeusOri", received.toJSONString());
        if (!HeraeusAuth.equals(auth)) {
            return generateReturn("N", "Error Password", "setEmployeeInfoResponse");
        }
        try {
            XMLSerializer xmlSerializer2json = new XMLSerializer();
            net.sf.json.JSON resultJson = xmlSerializer2json.read(str);
            JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
            System.out.println(jsonObject);
            sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeus", jsonObject.toJSONString());
            JSONArray jsonArray = new JSONArray();
            try {
                jsonArray = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setEmployeeInfo").getJSONArray("EmployeeGroup");
            } catch (Exception e) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setEmployeeInfo").getJSONObject("EmployeeGroup").getJSONObject("Employee");
                jsonArray.add(jsonObject1);
            }
            String finalReturnCode = "Y";
            String finalReturnMessage = "|";
            for (Object obj : jsonArray) {
                JSONObject jsonObject1 = (JSONObject) obj;
                try {
                    FndQuery fndQuery = new FndQuery();
                    fndQuery.setType("employee");
                    fndQuery.setInput(jsonObject1.toJSONString());
                    fndQuery.setCompanyId(heraeus1CompanyId);
                    heraeusMapper.updateHeraeusEmployee(fndQuery);
                    if (fndQuery.getReturnCode().equals("E")) {
                        finalReturnCode = "N";
                        finalReturnMessage += fndQuery.getReturnMessage() + "|";
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    finalReturnCode = "N";
                    finalReturnMessage += "Item: " + jsonObject1.toJSONString() + ":Database Exception|";
                }
            }
            return generateReturn(finalReturnCode, finalReturnMessage, "setEmployeeInfoResponse");
        } catch (Exception e) {
            e.printStackTrace();
            return generateReturn("N", "Internal Error: " + e.getMessage(), "setEmployeeInfoResponse");
        }
    }

    @RequestMapping(value = "/payment1",method = RequestMethod.POST, produces = "application/xml")
    public String heraeusPaymentUpdate(@RequestBody String str,
                                       @RequestHeader("Authorization") String auth) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        JSONObject received = new JSONObject();
        received.put("data", str);
        sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeusOri", received.toJSONString());
        if (!HeraeusAuth.equals(auth)) {
            return generateReturn("N", "Error Password", "setSAPPaymentStatusResponse");
        }
        try {
            XMLSerializer xmlSerializer2json = new XMLSerializer();
            net.sf.json.JSON resultJson = xmlSerializer2json.read(str);
            JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
            System.out.println(jsonObject);
            sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeus", jsonObject.toJSONString());
            JSONArray jsonArray = new JSONArray();
            try {
                jsonArray = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPPaymentStatus").getJSONArray("PaymentStatusGroup");
            } catch (Exception e) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPPaymentStatus").getJSONObject("PaymentStatusGroup").getJSONObject("PaymentStatus");
                jsonArray.add(jsonObject1);
            }
            String finalReturnCode = "Y";
            String finalReturnMessage = "|";
            for (Object obj : jsonArray) {
                JSONObject jsonObject1 = (JSONObject) obj;
                try {
                    GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
                    glBatchTransfer.setCompanyId(heraeus1CompanyId);
                    glBatchTransfer.setInput(jsonObject1.toJSONString());
                    glBatchTransfer.setLanguage("zh_CN");
                    glBatchTransfer.setType("statusupdate");
                    List<String> strings = heraeusMapper.glBatchCreateHeraeus1(glBatchTransfer);
                } catch (Exception e) {
                    e.printStackTrace();
                    finalReturnCode = "N";
                    finalReturnMessage += "Item: " + jsonObject1.toJSONString() + ":Database Exception|";
                }
            }
            return generateReturn(finalReturnCode, finalReturnMessage, "setSAPPaymentStatusResponse");
        } catch (Exception e) {
            e.printStackTrace();
            return generateReturn("N", "Internal Error: " + e.getMessage(), "setSAPPaymentStatusResponse");
        }
    }

    @RequestMapping(value = "/voucher1",method = RequestMethod.POST, produces = "application/xml")
    public String heraeusVoucherUpdate(@RequestBody String str,
                                       @RequestHeader("Authorization") String auth) throws Exception {
        logger.info("heraeusVoucherUpdate===凭证更新入参:{},{}",str,auth);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        JSONObject received = new JSONObject();
        received.put("data", str);
        sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeusOri", received.toJSONString());
        if (!HeraeusAuth.equals(auth)) {
            return generateReturn("N", "Error Password", "setSAPDocumentNoResponse");
        }
        try {
            XMLSerializer xmlSerializer2json = new XMLSerializer();
            net.sf.json.JSON resultJson = xmlSerializer2json.read(str);
            JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
            System.out.println(jsonObject);
            sapMapper.normalInsertLog(heraeus1CompanyId, "newHeraeus", jsonObject.toJSONString());
            JSONArray jsonArray = new JSONArray();
            try {
                jsonArray = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPDocumentNo").getJSONArray("DocumentNoGroup");
            } catch (Exception e) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPDocumentNo").getJSONObject("DocumentNoGroup").getJSONObject("DocumentNo");
                jsonArray.add(jsonObject1);
            }
            String finalReturnCode = "Y";
            String finalReturnMessage = "|";
            for (Object obj : jsonArray) {
                JSONObject jsonObject1 = (JSONObject) obj;
                try {
                    sapService.generateDocument(heraeus1CompanyId,jsonObject1);
                } catch (Exception e) {
                    e.printStackTrace();
                    finalReturnCode = "N";
                    finalReturnMessage += "Item: " + jsonObject1.toJSONString() + ":Database Exception|";
                }
            }
            return generateReturn(finalReturnCode, finalReturnMessage, "setSAPDocumentNoResponse");
        } catch (Exception e) {
            e.printStackTrace();
            return generateReturn("N", "Internal Error: " + e.getMessage(), "setSAPDocumentNoResponse");
        }
    }

    /**
     * 贺利式台湾-凭证回写
     * @param str
     * @param auth
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/voucher2",method = RequestMethod.POST, produces = "application/xml")
    public String heraeusTwVoucherUpdate(@RequestBody String str,
                                         @RequestHeader("Authorization") String auth) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
        JSONObject received = new JSONObject();
        received.put("data", str);
        sapMapper.normalInsertLog(heraeus2CompanyId, "heraeus2", received.toJSONString());
        if (!HeraeusAuth.equals(auth)) {
            return generateReturn("N", "Error Password", "setSAPDocumentNoResponse");
        }
        try {
            XMLSerializer xmlSerializer2json = new XMLSerializer();
            net.sf.json.JSON resultJson = xmlSerializer2json.read(str);
            JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
            System.out.println(jsonObject);
            sapMapper.normalInsertLog(heraeus2CompanyId, "heraeus2", jsonObject.toJSONString());
            JSONArray jsonArray = new JSONArray();
            try {
                jsonArray = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPDocumentNo").getJSONArray("DocumentNoGroup");
            } catch (Exception e) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPDocumentNo").getJSONObject("DocumentNoGroup").getJSONObject("DocumentNo");
                jsonArray.add(jsonObject1);
            }
            String finalReturnCode = "Y";
            String finalReturnMessage = "|";
            for (Object obj : jsonArray) {
                JSONObject jsonObject1 = (JSONObject) obj;
                try {
                    sapService.generateDocument(heraeus2CompanyId,jsonObject1);
                } catch (Exception e) {
                    e.printStackTrace();
                    finalReturnCode = "N";
                    finalReturnMessage += "Item: " + jsonObject1.toJSONString() + ":Database Exception|";
                }
            }
            return generateReturn(finalReturnCode, finalReturnMessage, "setSAPDocumentNoResponse");
        } catch (Exception e) {
            e.printStackTrace();
            return generateReturn("N", "Internal Error: " + e.getMessage(), "setSAPDocumentNoResponse");
        }
    }

    /**
     * 贺利式台湾-支付回写
     * @param str
     * @param auth
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/payment2",method = RequestMethod.POST, produces = "application/xml")
    public String heraeusTwPaymentUpdate(@RequestBody String str,
                                         @RequestHeader("Authorization") String auth) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPHRSTW);
        JSONObject received = new JSONObject();
        received.put("data", str);
        sapMapper.normalInsertLog(heraeus2CompanyId, "heraeus2", received.toJSONString());
        if (!HeraeusAuth.equals(auth)) {
            return generateReturn("N", "Error Password", "setSAPPaymentStatusResponse");
        }
        try {
            XMLSerializer xmlSerializer2json = new XMLSerializer();
            net.sf.json.JSON resultJson = xmlSerializer2json.read(str);
            JSONObject jsonObject = JSONObject.parseObject(resultJson.toString());
            System.out.println(jsonObject);
            sapMapper.normalInsertLog(heraeus2CompanyId, "heraeus2", jsonObject.toJSONString());
            JSONArray jsonArray = new JSONArray();
            try {
                jsonArray = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPPaymentStatus").getJSONArray("PaymentStatusGroup");
            } catch (Exception e) {
                JSONObject jsonObject1 = jsonObject.getJSONObject("SOAP:Body").getJSONObject("n0:setSAPPaymentStatus").getJSONObject("PaymentStatusGroup").getJSONObject("PaymentStatus");
                jsonArray.add(jsonObject1);
            }
            String finalReturnCode = "Y";
            String finalReturnMessage = "|";
            for (Object obj : jsonArray) {
                JSONObject jsonObject1 = (JSONObject) obj;
                try {
                    GlBatchTransfer glBatchTransfer = new GlBatchTransfer();
                    glBatchTransfer.setCompanyId(heraeus2CompanyId);
                    glBatchTransfer.setInput(jsonObject1.toJSONString());
                    glBatchTransfer.setLanguage("zh_CN");
                    glBatchTransfer.setType("statusupdate");
                    List<String> strings = heraeusMapper.glBatchCreateHeraeus2(glBatchTransfer);
                } catch (Exception e) {
                    e.printStackTrace();
                    finalReturnCode = "N";
                    finalReturnMessage += "Item: " + jsonObject1.toJSONString() + ":Database Exception|";
                }
            }
            return generateReturn(finalReturnCode, finalReturnMessage, "setSAPPaymentStatusResponse");
        } catch (Exception e) {
            e.printStackTrace();
            return generateReturn("N", "Internal Error: " + e.getMessage(), "setSAPPaymentStatusResponse");
        }
    }


}
