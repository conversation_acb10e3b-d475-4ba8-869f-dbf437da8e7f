package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.WshhSapReturnReq;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.service.OpenApiService;
import com.cloudpense.expman.service.WshhService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.cloudpense.expman.util.Constants.CommonConstants.APPLICATION_JSON_UTF;

@RestController
@RequestMapping(value = "/wshh")
public class WshhController {
    private WshhService wshhService;
    private OpenApiService openApiService;
    @Autowired
    public WshhController(WshhService wshhService,OpenApiService openApiService) {
        this.wshhService = wshhService;
        this.openApiService = openApiService;
    }

    @RequestMapping(value = "/claimPush", method = RequestMethod.GET)
    public void claimPush() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        wshhService.claimPush();
    }

    /**
     *  推送单据到sap
     */
    @RequestMapping(value = "/sapPush", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public String sapPush(@RequestBody String json) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(json);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String date = (String) jsonObject.get("pushDate");
        String dateTo = (String) jsonObject.get("pushDateTo");
        Date pushDate = sdf.parse(date);
        Date pushDateTo = sdf.parse(dateTo);
        return wshhService.sapPush(pushDate, pushDateTo);
    }

    /**
     *  SAP传输结果
     */
    @RequestMapping(value = "/sapReturn", method = RequestMethod.POST, produces = APPLICATION_JSON_UTF)
    public String sapReturn(@RequestBody WshhSapReturnReq wshhSapReturnReq) throws Exception {
        int companyId = 16097;
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPPRD03);
        wshhSapReturnReq.setCompanyId(companyId);
        return wshhService.sapReturn(wshhSapReturnReq);
    }
}

