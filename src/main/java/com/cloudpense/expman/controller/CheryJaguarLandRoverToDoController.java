package com.cloudpense.expman.controller;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.mapper.CheryJaguarLandRoverToDoMapper;
import com.cloudpense.expman.service.CheryJaguarLandRoverToDoService;
import com.cloudpense.expman.service.DocumentService;
import com.cloudpense.expman.service.OpenApiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.TimeZone;

@RestController
@RequestMapping(value = "api/v2/cjlr")
public class CheryJaguarLandRoverToDoController {
    private OpenApiService openApiService;
    private CheryJaguarLandRoverToDoMapper cheryJaguarLandRoverToDoMapper;
    private CheryJaguarLandRoverToDoService cheryJaguarLandRoverToDoService;
    private DocumentService documentService;
    @Autowired
    private OpenApiRestController openApiRestController;
    private static final Logger logger = LoggerFactory.getLogger(CheryJaguarLandRoverToDoController.class);
    @Autowired
    public CheryJaguarLandRoverToDoController(OpenApiService openApiService,
                                              CheryJaguarLandRoverToDoService cheryJaguarLandRoverToDoService,
                                              CheryJaguarLandRoverToDoMapper cheryJaguarLandRoverToDoMapper,
                                              DocumentService documentService){
            this.openApiService = openApiService;
            this.cheryJaguarLandRoverToDoService = cheryJaguarLandRoverToDoService;
            this.cheryJaguarLandRoverToDoMapper = cheryJaguarLandRoverToDoMapper;
            this.documentService = documentService;
    }

    /**
     * @apiDefine apiToDo 捷豹路虎待办
     */
    /**
     * @api{get} api/v3/common1/ToDo/query?access_token=ACCESS_TOKEN 待办
     * @apiGroup apiToDoGroup
     * @apiVersion 0.0.1
     * @apiDescription  依据传入的员工编号获取该员工所有的待办事项，从SharePoint获取所待办，sharePoint从接入系统获取所有待办
     * @apiParam(入参) {String} Source 每个系统接入时申请的App key
     * @apiParam(入参) {list} AppkeyList App Key列表（SharePoint从接入系统获取所有待办为空集合）
     * @apiParam(入参) {String} AppKey App Key
     * @apiParam(入参) {string} TaskOwnerAdAccount AD账号（不用）
     * @apiParam(入参) {string} TaskOwnerWorkerNo 工号（必填）
     * @apiParam(入参) {string} OperatedBy 操作者
     * @apiParam(入参) {datatime} OperatedTime 操作时间
     * @apiParamExample {json} 请求样例：
     *  {
     *      "Source":"OA",
     *      "OperatedBy":"lucy.li"
     *      "OperatedTime":"2019-04-10 16:00",
     *      "AppKeyList":
     *      [
     *      {"AppKey":"OA"},
     *      {"AppKey":"T&E"}
     *      ]
     *      "TaskOwnerAdAccount":"Lucy.Li",
     *      "TaskOwnerWorkerNo":"********"
     *  }
     * @apiSuccess(Success 200) {String} Code 接口返回编码；200-成功；其他参数待定义
     * @apiSuccess(Success 200) {List} Data 返回列表
     * @apiSuccess(Success 200) {Int} TotalCount 流程待办总数
     * @apiSuccess(Success 200) {List} TodoList 流程待办集合
     * @apiSuccess(Success 200) {String} Source 每个系统接入时申请的App Key
     * @apiSuccess(Success 200) {String} SourceName 每个系统接入时申请的App名称
     * @apiSuccess(Success 200) {String} ApplicationID 申请单主键
     * @apiSuccess(Success 200) {String} ApplicationNumber 申请单号
     * @apiSuccess(Success 200) {String} WorkflowID 流程ID，属于哪一种类型流程ID
     * @apiSuccess(Success 200) {String} WorkflowName 流程名称，属于哪一种类型流程名称
     * @apiSuccess(Success 200) {String} WorkflowInstanceID 流程实例ID
     * @apiSuccess(Success 200) {String} TaskInstanceID 流程任务ID
     * @apiSuccess(Success 200) {String} TaskName 流程任务名称
     * @apiSuccess(Success 200) {String} Operator 当前处理人账号
     * @apiSuccess(Success 200) {String} OperatorName 当前处理人姓名
     * @apiSuccess(Success 200) {String} CreatedBy 创建者
     * @apiSuccess(Success 200) {Datetime} CreatedTime 创建日期
     * @apiSuccess(Success 200) {String} OperationLink 该任务所在系统中的链接，点击后直接在对应的系统上打开该任务的详情
     * @apiSuccess(Success 200) {String} ErrorDetail 接口返回值非200时，该参数有效，如果返回值为200，则该参数为null
     * @apiSuccess(Success 200) {String} Message 错误消息描述
     * @apiSuccess(Success 200) {String} Detail 错误消息详细描述
     * @apiSuccessExample {json} 返回样例：
     * {
     *     "Code":200,
     *     "Data":
     *     {
     *         "TatalCount": 101,
     *         TodoList:
     *         [
     *         {
     *             "Source":"OA",
     *             "SourceName":"OA System",
     *             "ApplicationID":"00001",
     *             "ApplicationNumber":"ER-000010",
     *             "WorkflowID":"WF-0020",
     *             "WorkflowName":"T&E Expens",
     *             "WorkflowInstanceID":"E81EEB97-D093-41B0-948E-44809231206C",
     *             "TaskInstanceID":"0ED62556-CC68-4CCB-A504-4FB69EC48845",
     *             "TaskName":"关于XXX 出差报销审批",
     *             "Operator":"san.zhang",
     *             "OperatorName":"张三",
     *             "CreatedBy":"System Account",
     *             "CreatedTime":"2019-04-10 16:00",
     *             "OperationLink":"http://oa.cjlr.com/xx/xxxxxx.aspx"
     *         }
     *         ]
     *     }
     *     "ErrorDetail":null
     * }
     */
    @RequestMapping(value = "ToDo/query", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public JSONObject ToDoQuery(@RequestParam(value = "access_token") String accessToken,
                                @RequestBody String json
    ) throws Exception{
        final String LOG_KEY = "捷豹路虎待办==";
        logger.info("{}开始处理，入参={}", LOG_KEY, json);
        int companyId = openApiService.authCompany(accessToken);
        JSONObject jsonObject;
        try {
            jsonObject = JSONObject.parseObject(json);
        } catch(Exception e){
            logger.error("{}输入JSON解析出错:", LOG_KEY, e);
            return cheryJaguarLandRoverToDoService.generateRtn1(null, null, "输入JSON解析出错", "输入JSON解析出错", 1001);
        }
        try{
            FndQuery fndQuery=new FndQuery();
            fndQuery.setCompanyId(companyId);
            fndQuery.setInput(jsonObject.toJSONString());
            List<String> strings = cheryJaguarLandRoverToDoMapper.commonUpdate1(fndQuery);
            logger.info("{}获取待办数据结果fndQuery={}", LOG_KEY, fndQuery);
            JSONArray jsonArray1 = new JSONArray();
            JSONObject jsonObject3 = new JSONObject();
            int i=0,j=0;
            for (String s : strings) {
                i++;
                logger.info("{}开始转换第{}行数据输入={}", LOG_KEY, i, s);
                if(i>=j)
                {
                    j=i;
                }
                JSONObject jsonObject1 = JSONObject.parseObject(s);
                String Source = jsonObject1.getString("Source");
                String ApplicationID = jsonObject1.getString("ApplicationID");
                String ApplicationNumber = jsonObject1.getString("ApplicationNumber");
                String WorkflowID = jsonObject1.getString("WorkflowID");
                String WorkflowName = jsonObject1.getString("WorkflowName");
                String WorkflowInstanceID = jsonObject1.getString("WorkflowInstanceID");
                String TaskInstanceID = jsonObject1.getString("TaskInstanceID");
                String TaskName = jsonObject1.getString("TaskName");
                String Operator = jsonObject1.getString("Operator");
                String OperatorName = jsonObject1.getString("OperatorName");
                String CreatedBy = jsonObject1.getString("CreatedBy");
                // 转换utc时间为东八区时间
                String CreatedTimeUTC = jsonObject1.getString("CreatedTime");// utc时间
                String CreatedTime = dateTransfer(CreatedTimeUTC, "yyyy-MM-dd HH:mm:ss",
                        TimeZone.getTimeZone("GMT+0"), TimeZone.getTimeZone("GMT+8"));// 东八区时间
                String OperationLink = jsonObject1.getString("OperationLink");

                JSONObject jsonObject2 = new JSONObject();
                jsonObject2.put("Source", Source);
                jsonObject2.put("SourceName", "T&E");
                jsonObject2.put("ApplicationID", ApplicationID);
                jsonObject2.put("ApplicationNumber", ApplicationNumber);
                jsonObject2.put("WorkflowID", WorkflowID);
                jsonObject2.put("WorkflowName", WorkflowName);
                jsonObject2.put("WorkflowInstanceID", WorkflowInstanceID);
                jsonObject2.put("TaskInstanceID", TaskInstanceID);
                jsonObject2.put("TaskName", TaskName);
                jsonObject2.put("Operator", Operator);
                jsonObject2.put("OperatorName", OperatorName);
                jsonObject2.put("CreatedBy", CreatedBy);
                jsonObject2.put("CreatedTime", CreatedTime);
                jsonObject2.put("OperationLink", OperationLink);
                jsonObject2.put("TaskType", "1");

                jsonArray1.add(jsonObject2);
                jsonObject3.put("TotalCount", j);
                jsonObject3.put("TodoList", jsonArray1);
                logger.info("{}完成转换第{}行数据输出={}", LOG_KEY, i, jsonObject3);
            }
            if (fndQuery.getCode().equals("S")) {
                return cheryJaguarLandRoverToDoService.generateRtn1(jsonObject3, null, null, null, 200);
            } else {
                return cheryJaguarLandRoverToDoService.generateRtn1(null, null, fndQuery.getMessage(), fndQuery.getMessage(), 1002);
            }
        } catch (Exception e) {
            logger.error("{}出现未知异常：{}", LOG_KEY, e.getMessage(), e);
            String message = "系统错误：" + e.getMessage();
            return cheryJaguarLandRoverToDoService.generateRtn1(null, null, message, message, 1004);
        }
    }

    /**
     * 修改单据状态
     * @param accessToken
     * @param json
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "documentClose", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public JSONObject documentClose(@RequestParam(value = "access_token") String accessToken,
                                    @RequestBody String json) throws Exception {
        //验证token
        int companyId = openApiService.authCompany(accessToken);
        //修改状态
        documentService.updateDocument(json);
        return openApiRestController.documentUpdate(accessToken, json);
    }


    /**
     * 时区转换工具
     * @param fromDate
     * @param pattern
     * @param fromTz
     * @param toTz
     * @return
     * @throws ParseException
     */
    private String dateTransfer(String fromDate, String pattern,
                                      TimeZone fromTz, TimeZone toTz) throws ParseException {
        if(fromDate == null)
            return null;
        SimpleDateFormat sdf1 = new SimpleDateFormat(pattern);
        sdf1.setTimeZone(fromTz);
        SimpleDateFormat sdf2 = new SimpleDateFormat(pattern);
        sdf2.setTimeZone(toTz);

        return sdf2.format(sdf1.parse(fromDate));
    }
}
