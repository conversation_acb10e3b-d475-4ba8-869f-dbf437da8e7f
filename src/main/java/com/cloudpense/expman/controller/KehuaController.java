package com.cloudpense.expman.controller;

import com.Kehua.KehuaFtp;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.KhSapVoucherInfo;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.mapper.KehuaMapper;
import com.cloudpense.expman.service.KehuaService;
import com.cloudpense.expman.util.Constants.KehuaConstants;
import com.cloudpense.expman.util.LogCtx;
import com.cloudpense.expman.util.SendUtil;
import com.google.common.base.Strings;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.text.ParseException;
import java.util.*;

/**
 * <AUTHOR>
 * 科华生物
 * create_date:2019-01-07
 */
@RestController
@RequestMapping(value = "/kehua")
public class KehuaController {

    private Logger logger = LoggerFactory.getLogger(KehuaController.class);

    //k3推凭证列表
    final List<String> voucherTypeCodeList= Arrays.asList("QDT001","QD889","QD005","QD003","QD1000","QD1002","QD1001","QD1004");

    @Autowired
    KehuaService kehuaService;

    @Autowired
    private KehuaMapper mapper;

    @Autowired
    private SendUtil sendUtil;

    private ResourceBundle rb = ResourceBundle.getBundle("urisetting");

    @RequestMapping(value = "/orderScheduled",method = RequestMethod.POST)
    public void orderScheduled(){
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        kehuaService.orderScheduled("","","");
    }

    @RequestMapping(value = "/orderScheduledSection",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public void orderScheduledSection(@RequestBody JSONObject jsonObject){
        String beginTime = jsonObject.getString("begin_time");
        String endTime = jsonObject.getString("end_time");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        kehuaService.orderScheduled(beginTime,endTime,"true");
    }

    @RequestMapping(value = "/sapVoucherPosted",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public String sapVoucherPosted(@RequestBody JSONObject jsonObject) throws Exception {
        StringBuilder message= new StringBuilder();
        JSONObject ret = new JSONObject();
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        org.json.JSONObject input =new  org.json.JSONObject(jsonObject.getString("input"));
        String type = jsonObject.getString("type");
        String glDate = input.getString("date").replaceAll("-", "");
        if ("api".equals(type)) {
            List list = input.getJSONArray("document_id").toList();
            int exceptionLevel =0;
            for (Object o : list) {
                int documentId = (int) o;
                logger.info("documentId: " + documentId);
                JSONObject postResult=new JSONObject();
                //查询单据头信息
                KhSapVoucherInfo header = mapper.selectExpClaimHeader(documentId);
                if (voucherTypeCodeList.contains(header.getTypeCode())) {
                    JSONObject paramJson = new JSONObject();
                    paramJson.put("documentId", documentId);
                    paramJson.put("date", input.getString("date"));
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type","application/json");
                    logger.info("k3接口入参:headers={},param={}",JSON.toJSONString(headers),paramJson);

                    String result="";
                    try {
                        // 科华金蝶凭证生成接口
                        result = sendUtil.httpPostString("kehuaK3VoucherPush", rb.getString("kehua.k3VoucherPush"),headers, paramJson.toString());
                        logger.info("k3接口出参:result={}", result);
                    }catch (Exception e){
                        logger.info("科华金蝶凭证接口调用失败:{}",e);
                    }

                    if(result.indexOf("推送凭证成功")!=-1){
                        postResult.put("exception_level",0);
                        postResult.put("message",result.split(",")[0]+"kehua金蝶凭证生成成功");
                        postResult.put("voucher_code",result.split(",")[2]);
                    }else{
                        postResult.put("exception_level",99);
                        postResult.put("message",result);
                    }

                }else{
                    postResult = JSONObject.parseObject(kehuaService.sapVoucherPosted(documentId, glDate));
                }
                exceptionLevel+= postResult.getInteger("exception_level");
                if(postResult.getInteger("exception_level")>1){
                    message.append(postResult.getString("message")).append('\n');
                }
            }
            ret.put("exception_level",exceptionLevel);
            ret.put("message",message);
        }else {
            List list = input.getJSONArray("document_id").toList();
            JSONArray jsonArray = new JSONArray();
            for (Object o : list) {
                int documentId = (int) o;
                logger.info("documentId: " + documentId);
                JSONArray jsonArrayResult=new JSONArray();
                //查询单据头信息
                KhSapVoucherInfo header = mapper.selectExpClaimHeader(documentId);
                if (voucherTypeCodeList.contains(header.getTypeCode())) {
                    JSONObject paramJson = new JSONObject();
                    paramJson.put("documentId", documentId);
                    paramJson.put("date", input.getString("date"));
                    Map<String, String> headers = new HashMap<>();
                    headers.put("Content-Type", "application/json");
                    logger.info("k3凭证预览接口入参:headers={},param={}", JSON.toJSONString(headers), paramJson);

                    String result = "";
                    try {
                        // 科华金蝶凭证生成接口
                        result = sendUtil.httpPostString("kehuaK3VoucherPreview", rb.getString("kehua.k3VoucherPreview"), headers, paramJson.toString());
                        jsonArrayResult = JSON.parseArray(result);
                        logger.info("k3凭证预览接口出参:result={}", jsonArrayResult.toString());
                    } catch (Exception e) {
                        logger.info("科华金蝶凭证预览接口调用失败:{}", e);
                    }
                }else{
                    jsonArrayResult = kehuaService.sapVoucherPreview(documentId, glDate);
                }
                jsonArray.addAll(jsonArrayResult);
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
            }
            JSONArray jsonArray1 = new JSONArray();
            jsonArray1.add(jsonArray);
            message.append(jsonArray1);
            ret.put("excl",JSONArray.parseArray(message.toString()));
        }
        return ret.toString();
    }

    /**
     * 科华合同接口，FTP接口，定时任务型
     */
    @RequestMapping(value = "/khContract",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public void khContract(@RequestBody(required = false) String jsonStr)  {
        LogCtx.setLogKey("科华合同");
        logger.info("{}处理开始，入参={}", LogCtx.getLogKey(), jsonStr);
        // 设置切库
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        try {
            // 文件日期
            String date;
            if(!Strings.isNullOrEmpty(jsonStr)) {
                date = JSON.parseObject(jsonStr).getString("date");
            } else {
                date = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
            }
            LogCtx.setLogKey("科华合同"+date);
            // 删除缓存文件 其中的拼接生成的路径是文件下载后保存的路径
            KehuaFtp.deleteFiles(KehuaConstants.XML_FILE);
            KehuaFtp.deleteFiles(KehuaConstants.ATTACHMENT_FILE, "\\S*?.*");
            // 执行合同下载处理
            kehuaService.khContract(date);
        } catch (Exception e) {
            logger.error("{}出现未知异常: {}", LogCtx.getLogKey(), e.getMessage(), e);
        }
        logger.info("{}处理结束", LogCtx.getLogKey());
    }

    /**
     * 科华合同接口，FTP接口，入参型 示例: 2021-02-05
     */
    @RequestMapping(value = "/khContractSingleDay",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public void khContractSingleDay(@RequestBody JSONObject jsonObject)  {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        String date = jsonObject.getString("date");
        KehuaFtp.deleteFiles(KehuaConstants.XML_FILE);
        try {
            kehuaService.khContract(date);
        } catch (IOException e) {
            logger.error("科华合同接口同步失败: ", e);
        }

    }

    /**
     * 测试方法：批量读取,指定开始日期和结束日期
     */
    @Deprecated
    @RequestMapping(value = "/khContractTest",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public void khContractTest(@RequestBody JSONObject jsonObject)  {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CPKHB);
        String beginDate = jsonObject.getString("begin_date");
        String endDate = jsonObject.getString("end_date");
        KehuaFtp.deleteFiles(KehuaConstants.XML_FILE);
        try {
            List<String> list = KehuaConstants.findDates(beginDate,endDate);
            if(list.size()>0){
                for(Object date:list){
                    logger.info("khContractTest === date：" + date);
                    kehuaService.khContract((String)date);
                }
            }
        } catch (ParseException|IOException e) {
            e.printStackTrace();
        }

    }
}
