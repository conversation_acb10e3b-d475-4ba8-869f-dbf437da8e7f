package com.cloudpense.expman.controller;

import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.service.NipponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequestMapping(value = "/nippon")
public class NipponController {
    private NipponService nipponService;

    @Autowired
    public NipponController(NipponService nipponService) {
        this.nipponService = nipponService;
    }

    @RequestMapping(value = "/auth")
    public JSONObject nipponOauth(@RequestBody JSONObject json) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        return nipponService.getToken(json);
    }

    @RequestMapping(value = "/ntf/push")
    public void ntfPush() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.ntfPush();
    }

    @RequestMapping(value = "/synccall")
    public void dailySync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
//        try {
//            nipponService.updateCompany();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateCostCenter();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            nipponService.putDoaTargetEnableFlagN();
        } catch (Exception e) {
            e.printStackTrace();
        }
//        try {
//            nipponService.updateData("position");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateData("position");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateData("employee");// 恢复立邦调整组织架构20210705
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateEmployeeWhoLeft();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateData("partjob");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateData("costcentermanager");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateData("deptlevel");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateDataDeptleader();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updatePhoneLimit();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            nipponService.updateFrameOrder(false);
        } catch (Exception e) {
            e.printStackTrace();
        }
//        try {
//            nipponService.updateWbs(false);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateTraining(false);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateOutWork();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateCredit();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            nipponService.updateInterOrder();
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            nipponService.updateRate();
        } catch (Exception e) {
            e.printStackTrace();
        }
//        #72144迁移到新定制
//        try {
//            nipponService.freshStatus();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        try {
            nipponService.updateBudget();
        } catch (Exception e) {
            e.printStackTrace();
        }
//        try {
//            nipponService.updateSupplier();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        try {
//            nipponService.updateCustomer();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @RequestMapping(value = "/synccallf")
    public void fullSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateCompany();
        nipponService.updateCostCenter();
        nipponService.updatePhoneLimit();
        nipponService.updateFrameOrder(true);
        nipponService.freshStatus();
    }

    @RequestMapping(value = "/reimpush")
    public void reimPush() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.reimPush(false);
    }

    @RequestMapping(value = "/reimpushf")
    public void reimPushF() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.reimPush(true);
    }

    @RequestMapping(value = "/synccall/{type}")
    public void dataSync(@PathVariable(value = "type") String type) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        if ("employee".equals(type) || "partjob".equals(type) || "position".equals(type) || "costcentermanager".equals(type)) {
            nipponService.updateData(type);
            nipponService.updateData(type);
        }
    }

    @RequestMapping(value = "/aplsynccall")
    public void approvalLevelSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
//        nipponService.updateData("deptlevel");
//        nipponService.updateData("deptleader");
//        nipponService.updateDataDeptleader();
    }

    @RequestMapping(value = "/outsynccall")
    public void outSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateOutWork();
    }

    @RequestMapping(value = "/creditsynccall")
    public void creditSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateCredit();
    }

    @RequestMapping(value = "/comsynccall")
    public void comSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateCompany();
    }

    @RequestMapping(value = "/ccsynccall")
    public void ccSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateCostCenter();
        nipponService.updateData("costcentermanager");
    }

    @RequestMapping(value = "/pelsynccall")
    public void pelSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updatePhoneLimit();
    }

    @RequestMapping(value = "/framesynccall")
    public void frameSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateFrameOrder(false);
    }

    @RequestMapping(value = "/framesynccallf")
    public void frameSyncf() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateFrameOrder(true);
    }

    @RequestMapping(value = "/suppliersynccall")
    public void supplierSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateSupplier();
    }

    @RequestMapping(value = "/customersynccall")
    public void customerSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateCustomer();
    }

    @RequestMapping(value = "/lovsynccall")
    public void lovSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateSupplier();
        nipponService.updateCustomer();
    }

    @RequestMapping(value = "/freshStatus")
    public void freshStatus() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.freshStatus();
    }

    @RequestMapping(value = "/ratesynccall")
    public void rateSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateRate();
    }

    @RequestMapping(value = "/wbssynccall")
    public void wbsSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateWbs(false);
    }

    @RequestMapping(value = "/wbssynccallf")
    public void wbsSyncF() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateWbs(true);
    }

    @RequestMapping(value = "/meetsynccall")
    public void meetSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateData("meetings");
    }

    @RequestMapping(value = "/intosynccall")
    public void intOrderSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateInterOrder();
    }

    @RequestMapping(value = "/trainsynccallf")
    public void trianSyncF() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateTraining(true);
    }

    @RequestMapping(value = "/trainsynccall")
    public void trianSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateTraining(false);
    }

    @RequestMapping(value = "/travelpushcall")
    public void travelPush() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.travelPush();
    }

    @RequestMapping(value = "/invoicepushcall")
    public void invoicePush() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.invoicePush();
    }
    @RequestMapping(value = "/getSAPNo/{id}")
    @ResponseBody
    public String getSAPNo(@PathVariable(value = "id") String id) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        return nipponService.getSAPNo(Integer.parseInt(id));
    }

    @RequestMapping(value = "/budgetsynccall")
    @ResponseBody
    public void budgetSync() {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        nipponService.updateBudget();
    }

    @RequestMapping(value = "/abs")
    public String switchNipponTravelPush(String status) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        return nipponService.toggleTravelPush(status);
    }

    @RequestMapping(value = "/absent")
    public String switchNipponTravelPushByGroup(String status, String group) {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.NPP);
        return nipponService.toggleTravelPush(status, group);
    }

    @RequestMapping(value = "/pushClaimWithJson")
    public void pushClaimWithJson(@RequestBody String json, HttpServletResponse response) throws IOException {
        response.setHeader("Content-Type","text/plain;charset=UTF-8");
        String s = nipponService.pushClaimWithJson(json);
        response.getWriter().write(s);
    }
}

