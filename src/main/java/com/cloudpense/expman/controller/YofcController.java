package com.cloudpense.expman.controller;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.Request.YofcPaymentVoucherReq;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.changfei.ClaimCopyInfo;
import com.cloudpense.expman.entity.changfei.ClaimHeader;
import com.cloudpense.expman.mapper.LovMapper;
import com.cloudpense.expman.mapper.YofcMapper;
import com.cloudpense.expman.service.YofcService;
import com.cloudpense.expman.util.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import com.cloudpense.expman.util.LogCtx;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/yofc")
public class YofcController {


    private static ResourceBundle RB = ResourceBundle.getBundle("changfeiurl");
    private static final String  OWNERUSERNAME = RB.getString("owner_user_name");
    private static final String  OWNERCLAIMINFO = RB.getString("owner_claim_info");
    private static final Integer COMPANY_ID = 16895;
    private static final String  CLAIM_COPY_LOCK = "ClaimCopyLock";

    private YofcService yofcService;

    private static Logger logger = LoggerFactory.getLogger(YofcController.class);

    private YofcMapper yofcMapper;

    private LovMapper lovMapper;

    @Autowired
    public YofcController(YofcService yofcService,YofcMapper yofcMapper, LovMapper lovMapper) {
        this.yofcService = yofcService;
        this.yofcMapper = yofcMapper;
        this.lovMapper = lovMapper;
    }

    @RequestMapping(value = "/repeater", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public String repeater(@RequestBody String str) throws Exception {
        return "";
    }

    @RequestMapping(value = "/cbsPay", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public String cbsPay(@RequestBody JSONObject jsonObject) throws Exception {
        logger.info("cbsPay =============== cbsPay接口入参：" + jsonObject);
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        JSONObject inputJson = JSONObject.parseObject(jsonObject.getString("input"));
        int userId = jsonObject.getInteger("userId");
        return yofcService.cbsPayStart(inputJson, userId).toJSONString();
    }

    @RequestMapping(value = "/cbsPayDate", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public void cbsPayDate() {// 刷新已支付但未入账单据的支付时间
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        try {
            yofcService.cbsPayDate();
        }catch (Exception e){
            logger.error("长飞支付日期查询，出现未知异常：{}", e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/cbsPayStatusPublic",method = RequestMethod.POST)
    public void cbsPayStatusPublic() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        try {
            yofcService.cbsPayStatusPublic();
        } catch (Exception e) {
            logger.error("长飞对公支付状态查询，出现未知异常：{}", e.getMessage(), e);
        }
    }

    @RequestMapping(value = "/cbsPayStatusPrivate",method = RequestMethod.POST)
    public void cbsPayStatusPrivate() throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        try {
            yofcService.cbsPayStatusPrivate();
        } catch (Exception e) {
            logger.error("长飞对私支付状态查询，出现未知异常：{}", e.getMessage(), e);
        }
    }

    /**
     * 长飞凭证接口
     * @param jsonObject
     * @return
     */
    @RequestMapping(value = "/yofcVoucherPosted",method = RequestMethod.POST,produces = "application/json;charset=utf-8")
    public String yofcVoucherPosted(@RequestBody JSONObject jsonObject) {
        logger.info("长飞财务凭证开始执行，入参={}", jsonObject);
        MdcUtil.setMapValue("UUID", "id", IdUtil.simpleUUID());
        LogHolder.setLogKey("长飞财务凭证==");
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        JSONObject ret = new JSONObject();
        StringBuilder message = new StringBuilder();
        org.json.JSONObject input = new org.json.JSONObject(jsonObject.getString("input"));
        String type = jsonObject.getString("type");
        String date = input.getString("date");
        Integer seriousNum = input.getInt("num");
        String userId = jsonObject.getString("userId");
        if ("api".equals(type)) {
            List list = input.getJSONArray("document_id").toList();
            int exceptionLevel = 0;
            //用于区分发票凭证（对公）和老接口（对私）的数据
            List<String> publicList = new ArrayList<>();
            List<String> privateList = new ArrayList<>();
            for (Object o : list) {
                String documentId = o.toString();
                ClaimHeader claimHeader = yofcMapper.yofcGetClaimHeaderByDocumentId(documentId);
                String groupNum = Optional.of(claimHeader.getGroupNum()).orElse("");
                if ("40".equals(groupNum) || "30".equals(groupNum)) {
                    publicList.add(documentId);
                }else {
                    privateList.add(documentId);
                }
            }
            if (publicList.size() > 0 && privateList.size() > 0){
                ret.put("exception_level", 99);
                ret.put("message", "不能同时勾选对公和其他单据");
                return  ret.toJSONString();
            }
            logger.info("{}本次处理目标，对公单据：{}，对私单据：{}", LogHolder.getLogKey(), publicList, privateList);
            // 对公
            if(publicList.size() > 0){
                for (String documentId : publicList) {
                    String errorMessage = null;
                    try {
                        if(UniLock.lock(documentId, 30)) {
                            errorMessage = yofcService.yofcFaPiaoPosted(documentId, date, userId);
                        }
                    } catch (Exception e){
                        logger.error("{}对公凭证{}推送异常：{}", LogCtx.getLogKey(), documentId, e.getMessage(), e);
                        message.append("未知异常：" + e.getMessage());
                        break;
                    } finally {
                        UniLock.unlock(documentId);
                        if(!StringUtils.isEmpty(errorMessage)) {
                            yofcService.updateGlMessage(Integer.valueOf(documentId), "error", errorMessage);
                        }
                    }
                    if (null != errorMessage){
                        message.append(errorMessage).append('\n');
                    }
                }
            }
            if (message.length() > 0){
                ret.put("exception_level", 99);
                ret.put("message", message);
                LogCtx.clearLogKey();
                return  ret.toJSONString();
            }
            // 对私
            if(privateList.size() > 0){// 对私
                List<String> documentList = new ArrayList<>();
                try {
                    for (String documentId : privateList) {
                        if(UniLock.lock(documentId, 30)) {
                            documentList.add(documentId);
                        }
                    }
                    JSONObject inputData = new JSONObject();
                    inputData.put("date",date);
                    inputData.put("documentId", JSON.toJSONString(documentList));
                    inputData.put("seriousNum",seriousNum);
                    inputData.put("userId",userId);
                    JSONObject postResult = JSONObject.parseObject(yofcService.yofcSapPosted(inputData));
                    exceptionLevel += postResult.getInteger("exception_level");
                    if (postResult.getInteger("exception_level") > 1) {
                        String errorMessage = postResult.getString("message");
                        message.append(errorMessage).append('\n');
                    }
                } catch (Exception e) {
                    logger.error("{}对私凭证推送异常：{}",LogCtx.getLogKey(), e.getMessage(), e);
                    message.append("未知异常：" + e.getMessage());
                } finally {
                    for(Object obj : documentList) {
                        UniLock.unlock(obj);
                    }
                }
            }

            ret.put("exception_level", exceptionLevel);
            ret.put("message", message);

            logger.info("{}结束执行，返回={}", LogHolder.getLogKey(), ret.toJSONString());
        } else {
            List list = input.getJSONArray("document_id").toList();
            JSONArray jsonArray = new JSONArray();
            for (Object o : list) {
                int documentId = (int) o;
                logger.info("documentId: " + documentId);
                JSONArray jsonArrayResult = yofcService.sapVoucherPreview(documentId,date);
                logger.info(jsonArrayResult.toJSONString());
                jsonArray.addAll(jsonArrayResult);
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
                jsonArray.add(JSONArray.parseArray("[\"\"]"));
            }
            JSONArray jsonArray1 = new JSONArray();
            jsonArray1.add(jsonArray);
            message.append(jsonArray1);
            ret.put("excl",JSONArray.parseArray(message.toString()));
        }
        LogCtx.clearLogKey();
        MDC.clear();
        return ret.toJSONString();
    }


    @RequestMapping(value = "/paymentVouchers", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    public String getPaymentVouchers(@RequestParam("headerIds") List<Integer> headerIds, @RequestParam("date") String date,
                                     @RequestParam("number") int number, @RequestParam("userId") int userId) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        try {
            List<Map<String, Object>> vouchers = yofcService.getPaymentVouchers(headerIds, userId, date, number);
            return JSONObject.toJSONString(vouchers);
        } catch (Exception e) {
            logger.error("获取支付凭证失败", e);
            return "[]";
        }
    }

    /**
     * 生成支付凭证
     * @param req
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/paymentVouchers", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    public String postPaymentVouchers(@RequestBody YofcPaymentVoucherReq req) throws Exception {
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        LogHolder.setLogKey("支付凭证==");
        logger.info("{}开始处理，入参={}", LogHolder.getLogKey(), JSONUtil.toJsonStr(req));
        String result = null;
        List<Integer> headerIds = req.getHeaderIds();
        // 对私
        List<Integer>   priList = new ArrayList<>();
        // 对公
        List<Integer>   pubList = new ArrayList<>();
        StringBuffer errorMessage = new StringBuffer();
        for (Integer headerId : headerIds) {
            try {
                if(!UniLock.lock(headerId, 30)) {
                    logger.error("单据{}出现重复请求，忽略", headerId);
                    continue;
                }
                ClaimHeader claimHeader = yofcMapper.yofcTypeCode(headerId);
                if(claimHeader == null) {
                    errorMessage.append("单据"+headerId+"不满足推送条件\n");
                    continue;
                }
                String typeCode = claimHeader.getHeaderTypeCode();
                Integer documentId = claimHeader.getDocumentId();
                if ("PAY003".equals(typeCode)) {
                    pubList.add(documentId);
                }else {
                    priList.add(documentId);
                }
            } catch (Exception e) {
                logger.error("{}单据{}处理出现系统异常: {}", LogHolder.getLogKey(), headerId, e.getMessage(), e);
                errorMessage.append("单据"+headerId+"处理出现系统异常\n");
            } finally {
                UniLock.unlock(headerId);
            }
        }
        if(errorMessage.length() > 0) {
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("exception_level",999);
            resMap.put("message", errorMessage.toString());
            logger.error("{}处理失败，返回={}", LogHolder.getLogKey(), resMap);
            return JSON.toJSONString(resMap);
        }
        logger.info("{}获取单据结果，对私={}，对公={}", LogHolder.getLogKey(), priList, pubList);
        //不能同时存在
        if (priList.size() > 0 && pubList.size() > 0){
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("exception_level",999);
            resMap.put("message","AP支付单和其他支付单不能同时勾选");
            logger.error("{}处理失败，返回={}", LogHolder.getLogKey(), resMap);
            return JSON.toJSONString(resMap);
        }
        //不能都不存在
        if (priList.size() == 0 && pubList.size() == 0){
            Map<String, Object> resMap = new HashMap<>();
            resMap.put("exception_level",999);
            resMap.put("message","不满足推送条件");
            logger.error("{}处理失败，返回={}", LogHolder.getLogKey(), resMap);
            return JSON.toJSONString(resMap);
        }
        // 对公
        if (pubList.size()>0){
            try {
                result = yofcService.postPaymentVouchersPay003(pubList, req.getUserId(), req.getDate(), req.getNumber());
            } catch (Exception e) {
                logger.error("对公支付凭证处理出现未知异常：{}", e.getMessage(), e);
                errorMessage.append("对公支付凭证处理出现未知异常:" + e.getMessage());
                Map<String, Object> resMap = new HashMap<>();
                resMap.put("exception_level", 999);
                resMap.put("message", errorMessage.toString());
                logger.error("{}处理失败，返回={}", LogHolder.getLogKey(), resMap);
                return JSON.toJSONString(resMap);
            }
        }
        // 对私
        if (priList.size()>0){
            try {
                result = yofcService.postPaymentVouchers(priList, req.getUserId(), req.getDate(), req.getNumber());
            } catch (Exception e) {
                logger.error("对私支付凭证处理出现未知异常：{}", e.getMessage(), e);
                errorMessage.append("对私支付凭证处理出现未知异常:"+e.getMessage());
                Map<String, Object> resMap = new HashMap<>();
                resMap.put("exception_level", 999);
                resMap.put("message", errorMessage.toString());
                logger.error("{}处理失败，返回={}", LogHolder.getLogKey(), resMap);
                return JSON.toJSONString(resMap);
            }
        }

        logger.error("{}完成处理，返回={}", LogHolder.getLogKey(), result);
        LogCtx.clearLogKey();
        return result;
    }

    /**
     * 单据复制
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/copyClaim", method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    public String copyClaim() {
        LogHolder.setLogKey("单据复制==");
        String res;
        // 获取待处理数据
        CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
        List<ClaimCopyInfo> headerIds = yofcService.getCopyClaim();
        if(CollectionUtils.isEmpty(headerIds)) {
            logger.info("{}待处理单据0条", LogHolder.getLogKey());
            res = "无待处理单据";
            return res;
        }
        logger.info("{}待处理单据{}条", LogHolder.getLogKey(), headerIds.size());
        // 获取任务执行锁
        Integer lovId = lockTask(COMPANY_ID, CLAIM_COPY_LOCK);
        if(lovId == null) {
            logger.info("{}获取任务锁失败", LogHolder.getLogKey());
            res = "获取任务锁失败";
            return res;
        }
        try {
            for (ClaimCopyInfo header : headerIds) {
                logger.info("{}单据{}开始处理：{}", LogHolder.getLogKey(), header.getHeaderId(), header);
                CustomerContextHolder.setCustomerType(CustomerContextHolder.A);
                Map<String, Object> data = new HashMap<>();

                data.put("company_id", COMPANY_ID);
                data.put("user_id", Integer.valueOf(header.getHeaderId()));
                data.put("user_name", OWNERUSERNAME);
                data.put("doc_MD5", getMD5());
                String claimResult;
                try {
                    claimResult = HttpHelper.doCommonPost(OWNERCLAIMINFO+header.getHeaderId()+"?locale=zh_CN", null, JSONObject.toJSONString(data));
                } catch (Exception e) {
                    logger.error("{}单据{}获取主工程单据信息异常：{}", LogHolder.getLogKey(), header.getHeaderId(), e.getMessage(), e);
                    continue;
                }
                if (null == claimResult){
                    logger.error("{}单据{}获取主工程单据信息失败", LogHolder.getLogKey(), header.getHeaderId());
                    continue;
                }
                JSONObject jsonObject = JSONObject.parseObject(claimResult);
                jsonObject.put("link_header_id",header.getHeaderId());
                jsonObject.put("header_type_id",header.getNewTypeId());
                jsonObject.put("column14",header.getDocumentNum());
                jsonObject.put("created_by",header.getUserId());
                jsonObject.put("status","submitted");
                jsonObject.put("receipts",null);
                try{
                    JSONArray lineArray = jsonObject.getJSONArray("claim_line");
                    for (Object o : lineArray) {
                        JSONObject line =  (JSONObject)o;
                        JSONArray receipts = line.getJSONArray("receipts");
                        if (null != receipts){
                            JSONArray attachments = line.getJSONArray("attachments");
                            if (null == attachments){
                                attachments = new JSONArray();
                            }
                            for (Object receipt : receipts) {
                                JSONObject receiptObject =  (JSONObject)receipt;
                                JSONArray invoiceAttachments = receiptObject.getJSONArray("invoice_attachments");
                                if (null != invoiceAttachments){
                                    for (Object invoiceAttachment : invoiceAttachments) {
                                        JSONObject invoiceAttachmentObject =  (JSONObject)invoiceAttachment;
                                        Map<String, Object> newAttachment = new HashMap<>();
                                        newAttachment.put("file_name",invoiceAttachmentObject.get("file_name"));
                                        newAttachment.put("attachment_url",invoiceAttachmentObject.get("attachment_url"));
                                        attachments.add(newAttachment);
                                    }
                                }
                            }
                            line.put("attachments",attachments);
                        }

                        line.put("created_by",header.getUserId());
                        line.put("expenses",null);
                        line.put("expense_id",null);
                        line.put("receipts",null);
                        line.put("column40",line.get("line_id"));
                    }
                }catch (Exception e) {
                    logger.error("{}单据{}行信息处理异常：{}", LogHolder.getLogKey(), header.getHeaderId(), e.getMessage(), e);
                }

                String agent_id = null != jsonObject.get("agent_id") ? jsonObject.get("agent_id").toString() : "0";
                String inputJson = JSON.toJSONString(jsonObject);
                CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);

                ExpClaimHeader result = yofcService.copyClaim(agent_id, inputJson, header.getUserId(), header, jsonObject);

            }
            res = "处理成功";
        } catch (Exception e) {
            logger.error("{}处理异常：{}", LogHolder.getLogKey(), e.getMessage(), e);
            res = "处理异常：" + e.getMessage();
        } finally {
            unlockTask(lovId, CLAIM_COPY_LOCK);
        }
        LogCtx.clearLogKey();
        return res;
    }

    /**
     * 增加任务锁
     * @return
     */
    synchronized Integer lockTask(Integer companyId, String code) {
        Integer lovId;
        try {
            // 加锁，防止任务调度冲突 fixme: 不适用于分布式环境
            lovId = lovMapper.getLovId("System Param", companyId);
            String flag = lovMapper.getValueEnableFlag(lovId, code);
            if(flag == null) {
                lovMapper.insertValue(lovId, companyId, code, "Y");
            } else if("Y".equals(flag)) {
                logger.error("{}已存在处理中任务，忽略处理", LogHolder.getLogKey());
                return null;
            } else {
                lovMapper.setValueEnableFlag(lovId, code, "Y");
            }
            logger.info("{}获取任务锁成功", LogHolder.getLogKey());
            return lovId;
        } catch (Exception e) {
            logger.error("{}获取任务锁失败：{}", LogHolder.getLogKey(), e.getMessage(), e);
            return null;
        }
    }


    /**
     * 释放任务锁
     * @param lovId
     */
    synchronized void unlockTask(Integer lovId, String code) {
        if(lovId != null) {
            logger.info("{}释放任务锁开始lovId:{},code:{}",LogHolder.getLogKey(),lovId,code);
            lovMapper.setValueEnableFlag(lovId, code, "N");
            String valueEnableFlag = lovMapper.getValueEnableFlag(lovId, code);
            logger.info("{}释放任务锁结束,valueEnableFlag is {}", LogHolder.getLogKey(),valueEnableFlag);
        }
    }

    /**
     * 获取MD5加密串
     * @return
     */
    private String getMD5() {
        //MD5加密
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (Exception e) {
            logger.error("{}获取MD5加密串异常:{}", LogHolder.getLogKey(), e.getMessage(), e);
            return "";
        }
        md.update((COMPANY_ID + OWNERUSERNAME).getBytes(StandardCharsets.UTF_8));
        return MD5Util.byteArrayToHexString(md.digest());
    }


}



