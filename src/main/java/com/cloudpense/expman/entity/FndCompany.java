package com.cloudpense.expman.entity;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;

/**
 * Created by Spooky on 2015/1/8.
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "company_id")
public class FndCompany implements Serializable {

    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "accounts")
    private String accounts;

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "company_code")
    private String companyCode;

    @JsonProperty(value = "company_name")
    private String companyName;

    @JsonProperty(value = "bank_accounts")
    private Collection<FndCompanyAccount> fndCompanyAccount;
    
    @JsonProperty(value = "email_address")
    private String emailAddress;

    @JsonProperty(value = "address")
    private String address;

    @JsonProperty(value = "company_currency")
    private String companyCurrency;

    @JsonProperty(value = "rate_method")
    private String rateMethod;

    @JsonProperty(value = "rate_interface")
    private String rateInterface;

    @JsonProperty(value = "language")
    private String language;

    @JsonProperty(value = "business_license")
    private String businessLicense;

    @JsonProperty(value = "legal_person")
    private String legalPerson;

    @JsonProperty(value = "phone")
    private String phone;

    @JsonProperty(value = "attachment_url")
    private String attachmentUrl;

    @JsonProperty(value = "attachment_name")
    private String attachmentName;

    @JsonIgnore
    private String returnCode;

    @JsonIgnore
    private String returnMessage;

    @JsonProperty(value = "cost_center_enable")
    private String costCenterEnable;
    
    @JsonProperty(value = "inactive_date")
    private Date inactiveDate;
    
    @JsonProperty(value = "claim_header")
    private Collection<ExpClaimHeader> echCollection;

    @JsonProperty(value="default_language")
    private String defaultLanguage;

    @JsonProperty(value = "language_list")
    private Collection<String> languageList;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "tax_number")
    private String taxNumber;

    @JsonProperty(value = "tax_address")
    private String taxAddress;

    @JsonProperty(value = "bank_name")
    private String bankName;

    @JsonProperty(value = "account_number")
    private String accountNumber;

    @JsonProperty(value = "approver_flag")
    private String approverFlag;

    @JsonProperty(value = "ctrip_flag")
    private String ctripFlag;

    @JsonProperty(value = "huazhu_flag")
    private String huazhuFlag;

    @JsonProperty(value = "reject_note_flag")
    private String rejectNoteFlag;

    @JsonProperty(value = "ctrip_approval")
    private String ctripApproval;

    @JsonProperty(value = "payment_flag")
    private String paymentFlag;

    @JsonProperty(value = "order_flag")
    private String orderFlag;

    @JsonIgnore
    private String companyFlagStr;

    @JsonProperty(value = "company_flag")
    private JSONObject companyFlag;

    @JsonProperty(value = "order_setup")
    private String orderSetup;

    @JsonProperty(value = "delivery_setup_str")
    private String deliverySetupStr;

    @JsonProperty(value = "delivery_setup")
    private JSONObject deliverySetup;

    @JsonProperty(value = "company_setup_str")
    private String companySetupStr;

    @JsonProperty(value = "company_setup")
    private JSONObject companySetup;

    @JsonProperty(value = "invoice_setup_str")
    private String invoiceSetupStr;

    @JsonProperty(value = "invoice_setup")
    private JSONObject invoiceSetup;

    @JsonProperty(value = "config_setup_str")
    private String configSetupStr;

    @JsonProperty(value = "config_setup")
    private JSONObject configSetup;

    @JsonIgnore
    private String functionSetupStr;

    @JsonProperty(value = "function_setup")
    private JSONObject functionSetup;

    @JsonProperty(value = "function_url")
    private String functionUrl;


    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getAccounts() {
        return accounts;
    }

    public void setAccounts(String accounts) {
        this.accounts = accounts;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Collection<FndCompanyAccount> getFndCompanyAccount() {
        return fndCompanyAccount;
    }

    public void setFndCompanyAccount(Collection<FndCompanyAccount> fndCompanyAccount) {
        this.fndCompanyAccount = fndCompanyAccount;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getCompanyCurrency() {
        return companyCurrency;
    }

    public void setCompanyCurrency(String companyCurrency) {
        this.companyCurrency = companyCurrency;
    }

    public Date getInactiveDate() {
        return inactiveDate;
    }

    public void setInactiveDate(Date inactiveDate) {
        this.inactiveDate = inactiveDate;
    }

    public Collection<ExpClaimHeader> getEchCollection() {
        return echCollection;
    }

    public void setEchCollection(Collection<ExpClaimHeader> echCollection) {
        this.echCollection = echCollection;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getRateMethod() {
        return rateMethod;
    }

    public void setRateMethod(String rateMethod) {
        this.rateMethod = rateMethod;
    }

    public String getRateInterface() {
        return rateInterface;
    }

    public void setRateInterface(String rateInterface) {
        this.rateInterface = rateInterface;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage= returnMessage;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(String attachmentName) {
        this.attachmentName= attachmentName;
    }

    public String getCostCenterEnable() {
        return costCenterEnable;
    }

    public void setCostCenterEnable(String costCenterEnable) {
        this.costCenterEnable = costCenterEnable;
    }

    public String getDefaultLanguage() {
        return defaultLanguage;
    }

    public void setDefaultLanguage(String defaultLanguage) {
        this.defaultLanguage = defaultLanguage;
    }

    public Collection<String> getLanguageList() {
        return languageList;
    }

    public void setLanguageList(Collection<String> languageList) {
        this.languageList = languageList;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getTaxAddress() {
        return taxAddress;
    }

    public void setTaxAddress(String taxAddress) {
        this.taxAddress = taxAddress;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getApproverFlag() {
        return approverFlag;
    }

    public void setApproverFlag(String approverFlag) {
        this.approverFlag = approverFlag;
    }

    public String getCtripFlag() {
        return ctripFlag;
    }

    public void setCtripFlag(String ctripFlag) {
        this.ctripFlag = ctripFlag;
    }

    public String getHuazhuFlag() {
        return huazhuFlag;
    }

    public void setHuazhuFlag(String huazhuFlag) {
        this.huazhuFlag = huazhuFlag;
    }

    public String getRejectNoteFlag() {
        return rejectNoteFlag;
    }

    public void setRejectNoteFlag(String rejectNoteFlag) {
        this.rejectNoteFlag = rejectNoteFlag;
    }

    public String getCtripApproval() {
        return ctripApproval;
    }

    public void setCtripApproval(String ctripApproval) {
        this.ctripApproval = ctripApproval;
    }

    public String getPaymentFlag() {
        return paymentFlag;
    }

    public void setPaymentFlag(String paymentFlag) {
        this.paymentFlag = paymentFlag;
    }

    public String getOrderFlag() {
        return orderFlag;
    }

    public void setOrderFlag(String orderFlag) {
        this.orderFlag = orderFlag;
    }

    public String getCompanyFlagStr() {
        return companyFlagStr;
    }

    public void setCompanyFlagStr(String companyFlagStr) {
        this.companyFlagStr = companyFlagStr;
    }

    public JSONObject getCompanyFlag() {
        return companyFlag;
    }

    public void setCompanyFlag(JSONObject companyFlag) {
        this.companyFlag = companyFlag;
    }

    public String getOrderSetup() {
        return orderSetup;
    }

    public void setOrderSetup(String orderSetup) {
        this.orderSetup = orderSetup;
    }

    public String getDeliverySetupStr() {
        return deliverySetupStr;
    }

    public void setDeliverySetupStr(String deliverySetupStr) {
        this.deliverySetupStr = deliverySetupStr;
    }

    public JSONObject getDeliverySetup() {
        return deliverySetup;
    }

    public void setDeliverySetup(JSONObject deliverySetup) {
        this.deliverySetup = deliverySetup;
    }

    public String getCompanySetupStr() {
        return companySetupStr;
    }

    public void setCompanySetupStr(String companySetupStr) {
        this.companySetupStr = companySetupStr;
    }

    public JSONObject getCompanySetup() {
        return companySetup;
    }

    public void setCompanySetup(JSONObject companySetup) {
        this.companySetup = companySetup;
    }

    public String getInvoiceSetupStr() {
        return invoiceSetupStr;
    }

    public void setInvoiceSetupStr(String invoiceSetupStr) {
        this.invoiceSetupStr = invoiceSetupStr;
    }

    public JSONObject getInvoiceSetup() {
        return invoiceSetup;
    }

    public void setInvoiceSetup(JSONObject invoiceSetup) {
        this.invoiceSetup = invoiceSetup;
    }

    public String getConfigSetupStr() {
        return configSetupStr;
    }

    public void setConfigSetupStr(String configSetupStr) {
        this.configSetupStr = configSetupStr;
    }

    public JSONObject getConfigSetup() {
        return configSetup;
    }

    public void setConfigSetup(JSONObject configSetup) {
        this.configSetup = configSetup;
    }

    public String getFunctionSetupStr() {
        return functionSetupStr;
    }

    public void setFunctionSetupStr(String functionSetupStr) {
        this.functionSetupStr = functionSetupStr;
    }

    public JSONObject getFunctionSetup() {
        return functionSetup;
    }

    public void setFunctionSetup(JSONObject functionSetup) {
        this.functionSetup = functionSetup;
    }

    public String getFunctionUrl() {
        return functionUrl;
    }

    public void setFunctionUrl(String functionUrl) {
        this.functionUrl = functionUrl;
    }
}
