package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Created by Spooky on 2015/1/23.
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "cost_element_id")
public class FndCostElement implements Serializable {

    @NotNull(message = "should have cost_element_id field.")
    
    @JsonProperty(value = "cost_element_id")
    private int costElementId;

    
    @JsonProperty(value = "company_id")
    private int companyId;

    
    @JsonProperty(value = "cost_element_name")
    private String costElementName;

    
    @JsonProperty(value = "description")
    private String description;

    
    @JsonProperty(value = "account_code")
    private String accountCode;

    @JsonProperty(value = "enabled")
    private String enabled;

    
    @JsonProperty(value = "value")
    private double value;

    @JsonProperty(value = "pay_value")
    private double payValue;

    @JsonProperty(value = "row_id")
    private int rowId;

    @JsonIgnore
    private int userId;

    @JsonIgnore
    private String returnCode;

    @JsonIgnore
    private String returnMessage;

    @JsonIgnore
    private int headerId;

    public int getCostElementId() {
        return costElementId;
    }

    public void setCostElementId(int costElementId) {
        this.costElementId = costElementId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getCostElementName() {
        return costElementName;
    }

    public void setCostElementName(String costElementName) {
        this.costElementName = costElementName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    public double getPayValue() {
        return payValue;
    }

    public void setPayValue(double payValue) {
        this.payValue = payValue;
    }

    public int getRowId() {
        return rowId;
    }

    public void setRowId(int rowId) {
        this.rowId = rowId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public int getHeaderId() {
        return headerId;
    }

    public void setHeaderId(int headerId) {
        this.headerId = headerId;
    }
}

