package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;

import java.io.Serializable;
import java.util.Collection;

/**
 * Created by Spooky on 2015/1/8.
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "type_id")
public class ExpType implements Serializable {

    @JsonProperty(value = "type_id")
    private int typeId;

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "type_code")
    private String typeCode;

    @JsonProperty(value = "enabled")
    private String enabled;

    @JsonProperty(value = "priority")
    private Integer priority;

    @JsonProperty(value = "category")
    private String category;

    @JsonProperty(value = "description")
    private String description;

    @JsonProperty(value = "attachment_url")
    private String attachmentUrl;

    @JsonProperty(value = "print_url")
    private String printUrl;

    @JsonProperty(value = "print_template")
    private String printTemplate;

    @JsonProperty(value = "budget_category")
    private String budgetCategory;

    @JsonProperty(value = "internal_type")
    private String internalType;

    @JsonProperty(value = "request_required_flag")
    private String requestRequiredFlag;

    @JsonProperty(value = "request_link_num")
    private Integer requestLinkNum;

    @JsonProperty(value = "claim_link_num")
    private Integer claimLinkNum;

    @JsonProperty(value = "period_type")
    private String periodType;

    @JsonProperty(value = "advance_date")
    private String advanceDate;

    @JsonProperty(value = "period_day")
    private String periodDay;

    @JsonProperty(value = "reminder_day")
    private String reminderDay;

    private String language;

    @JsonProperty(value = "staff_flag")
    private String staffFlag;

    @JsonProperty(value = "line_block")
    private String lineBlock;

    @JsonProperty(value = "return_block")
    private String returnBlock;

    @JsonProperty(value = "advance_block")
    private String advanceBlock;

    @JsonProperty(value = "group_num")
    private Integer groupNum;

    @JsonProperty(value = "group_name")
    private String groupName;

    @JsonProperty(value = "revision_flag")
    private String revisionFlag;

    @JsonProperty(value = "revision_name")
    private String revisionName;

    @JsonProperty(value = "share_flag")
    private String shareFlag;

    public int getTypeId() {
        return typeId;
    }

    public void setTypeId(int typeId) {
        this.typeId = typeId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public void setTypeCode(String typeCode) {
        this.typeCode = typeCode;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getPrintUrl() {
        return printUrl;
    }

    public void setPrintUrl(String printUrl) {
        this.printUrl = printUrl;
    }

    public String getPrintTemplate() {
        return printTemplate;
    }

    public void setPrintTemplate(String printTemplate) {
        this.printTemplate = printTemplate;
    }

    public String getBudgetCategory() {
        return budgetCategory;
    }

    public void setBudgetCategory(String budgetCategory) {
        this.budgetCategory = budgetCategory;
    }

    public String getInternalType() {
        return internalType;
    }

    public void setInternalType(String internalType) {
        this.internalType = internalType;
    }

    public String getRequestRequiredFlag() {
        return requestRequiredFlag;
    }

    public void setRequestRequiredFlag(String requestRequiredFlag) {
        this.requestRequiredFlag = requestRequiredFlag;
    }

    public Integer getRequestLinkNum() {
        return requestLinkNum;
    }

    public void setRequestLinkNum(Integer requestLinkNum) {
        this.requestLinkNum = requestLinkNum;
    }

    public Integer getClaimLinkNum() {
        return claimLinkNum;
    }

    public void setClaimLinkNum(Integer claimLinkNum) {
        this.claimLinkNum = claimLinkNum;
    }

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public String getAdvanceDate() {
        return advanceDate;
    }

    public void setAdvanceDate(String advanceDate) {
        this.advanceDate = advanceDate;
    }

    public String getPeriodDay() {
        return periodDay;
    }

    public void setPeriodDay(String periodDay) {
        this.periodDay = periodDay;
    }

    public String getReminderDay() {
        return reminderDay;
    }

    public void setReminderDay(String reminderDay) {
        this.reminderDay = reminderDay;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getStaffFlag() {
        return staffFlag;
    }

    public void setStaffFlag(String staffFlag) {
        this.staffFlag = staffFlag;
    }

    public String getLineBlock() {
        return lineBlock;
    }

    public void setLineBlock(String lineBlock) {
        this.lineBlock = lineBlock;
    }

    public String getReturnBlock() {
        return returnBlock;
    }

    public void setReturnBlock(String returnBlock) {
        this.returnBlock = returnBlock;
    }

    public String getAdvanceBlock() {
        return advanceBlock;
    }

    public void setAdvanceBlock(String advanceBlock) {
        this.advanceBlock = advanceBlock;
    }

    public Integer getGroupNum() {
        return groupNum;
    }

    public void setGroupNum(Integer groupNum) {
        this.groupNum = groupNum;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getRevisionFlag() {
        return revisionFlag;
    }

    public void setRevisionFlag(String revisionFlag) {
        this.revisionFlag = revisionFlag;
    }

    public String getRevisionName() {
        return revisionName;
    }

    public void setRevisionName(String revisionName) {
        this.revisionName = revisionName;
    }

    public String getShareFlag() {
        return shareFlag;
    }

    public void setShareFlag(String shareFlag) {
        this.shareFlag = shareFlag;
    }
}

