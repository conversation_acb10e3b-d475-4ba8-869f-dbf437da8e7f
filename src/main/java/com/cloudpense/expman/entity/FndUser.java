package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.*;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by Spooky on 2015/1/8.
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "user_id")
public class FndUser implements Serializable {

    
    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "company_id")
    private int companyId;

    
    @JsonProperty(value = "user_name")
    private String userName;

    
    @JsonProperty(value = "full_name")
    private String fullName;

    @JsonIgnore
    private String password;

    
    @JsonProperty(value = "email_address")
    private String emailAddress;

    
    @JsonProperty(value = "mobile")
    private String mobile;

    
    @JsonProperty(value = "description")
    private String description;

    
    @JsonProperty(value = "wechat")
    private String wechat;

    
    @JsonProperty(value = "qq")
    private String qq;

    
    @JsonProperty(value = "sub_company")
    private String subCompany;

    
    @JsonProperty(value = "inactive_date")
    private Date inactiveDate;

    @JsonIgnore
    private String verifyRequired;

    
    @JsonProperty(value = "user_type")
    private String userType;

    
    @JsonProperty(value = "gender")
    private String gender;

    
    @JsonProperty(value = "base_city")
    private int baseCity;

    @JsonProperty(value = "equipment_id")
    private String equipmentId;

    @JsonProperty(value = "equipment_type")
    private String equipmentType;

    
    @JsonProperty(value = "printer_name")
    private String printerName;

    
    @JsonProperty(value = "printer_address")
    private String printerAddress;

    @JsonProperty(value="bank_name")
    private String userBankName;

    @JsonProperty(value = "attachment_url")
    private String attachmentURL;

    @JsonProperty(value = "account_number")
    private String accountNumber;

    @JsonProperty(value = "department_id")
    private int departmentId;

    @JsonProperty(value = "agent_id")
    private int agentId;

    @JsonProperty(value = "supervisor_id")
    private int supervisorId;

    @JsonIgnore
    private String passwordReset;

    @JsonIgnore
    private String returnCode;
    @JsonIgnore
    private String returnMessage;

    @JsonProperty(value = "company")
    private FndCompany fndCompany;

    public FndCompany getFndCompany() {
        return fndCompany;
    }

    public FndUser setFndCompany(FndCompany fndCompany) {
        this.fndCompany = fndCompany;
        return this;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getInactiveDate() {
        return inactiveDate;
    }

    public void setInactiveDate(Date inactiveDate) {
        this.inactiveDate = inactiveDate;
    }

    public String getVerifyRequired() {
        return verifyRequired;
    }

    public void setVerifyRequired(String verifyRequired) {
        this.verifyRequired = verifyRequired;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getQq() {
        return qq;
    }

    public void setQq(String qq) {
        this.qq = qq;
    }

    public String getSubCompany() {
        return subCompany;
    }

    public void setSubCompany(String subCompany) {
        this.subCompany = subCompany;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public int getBaseCity() {
        return baseCity;
    }

    public void setBaseCity(int baseCity) {
        this.baseCity = baseCity;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getAttachmentURL() {
        return attachmentURL;
    }

    public void setAttachmentURL(String attachmentURL) {
        this.attachmentURL = attachmentURL;
    }

    public String getPrinterAddress() {
        return printerAddress;
    }

    public void setPrinterAddress(String printerAddress) {
        this.printerAddress = printerAddress;
    }

    public String getPrinterName() {
        return printerName;
    }

    public void setPrinterName(String printerName) {
        this.printerName = printerName;
    }

    public String getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(String equipmentId) {
        this.equipmentId = equipmentId;
    }

    public String getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public int getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(int departmentId) {
        this.departmentId = departmentId;
    }

    public String getUserBankName() {
        return userBankName;
    }

    public void setUserBankName(String userBankName) {
        this.userBankName = userBankName;
    }

    public String getPasswordReset() {
        return passwordReset;
    }

    public void setPasswordReset(String passwordReset) {
        this.passwordReset = passwordReset;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public int getSupervisorId() {
        return supervisorId;
    }

    public void setSupervisorId(int supervisorId) {
        this.supervisorId = supervisorId;
    }
}