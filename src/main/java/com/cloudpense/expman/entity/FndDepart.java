package com.cloudpense.expman.entity;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by futurefall on 2015/7/23.
 */
public class FndDepart {
    @JsonProperty(value = "department_id")
    private Integer departmentId;
    @JsonProperty(value = "department_node")
    private String departmentNode;

    @JsonProperty(value = "departments")
    private String departments;

    @JsonProperty(value = "department_list")
    private JSONArray departmentList;

    @JsonProperty(value = "department_name")
    private String departmentName;

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "supervisor_id")
    private Integer supervisorId;

    @JsonProperty(value = "sequence_num")
    private int sequenceNum;

    @JsonProperty(value = "enabled_flag")
    private String enabledFlag;

    @JsonProperty(value = "owner_id")
    private Integer ownerId;

    @JsonProperty(value = "owner_name")
    private String ownerName;

    private String key;

    @JsonProperty(value = "approval_level")
    private int approvalLevel;

    @JsonProperty(value = "ledger")
    private String ledger;

    private String type;

    @JsonProperty(value = "department_code")
    private String departmentCode;

    private String description;

    private String address;

    private String phone;

    private String input;

    @JsonProperty(value = "email_address")
    private String emailAddress;

    @JsonProperty(value = "tax_number")
    private String taxNumber;

    @JsonProperty(value = "tax_address")
    private String taxAddress;

    @JsonProperty(value = "bank_name")
    private String bankName;

    @JsonProperty(value = "account_number")
    private String accountNumber;

    @JsonProperty(value = "currency_code")
    private String currencyCode;

    @JsonProperty(value = "cost_center_flag")
    private String costCenterFlag;

    @JsonProperty(value = "branch_company_id")
    private int branchCompanyId;

    private JSONArray accounts;

    @JsonIgnore
    private String accountsT;

    private String language;
    @JsonIgnore
    private String returnCode;
    @JsonIgnore
    private String returnMessage;

    @JsonProperty(value = "company_name")
    private String companyName;

    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "column1")
    private String column1;

    @JsonProperty(value = "column2")
    private String column2;

    @JsonProperty(value = "column3")
    private String column3;

    @JsonProperty(value = "column4")
    private String column4;

    @JsonProperty(value = "column5")
    private String column5;

    @JsonProperty(value = "column6")
    private String column6;

    @JsonProperty(value = "column7")
    private String column7;

    @JsonProperty(value = "column8")
    private String column8;

    @JsonProperty(value = "column9")
    private String column9;

    @JsonProperty(value = "column10")
    private String column10;

    public Integer getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentNode() {
        return departmentNode;
    }

    public void setDepartmentNode(String departmentNode) {
        this.departmentNode = departmentNode;
    }

    public String getDepartments() {
        return departments;
    }

    public void setDepartments(String departments) {
        this.departments = departments;
    }

    public JSONArray getDepartmentList() {
        return departmentList;
    }

    public void setDepartmentList(JSONArray departmentList) {
        this.departmentList = departmentList;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getLanguage() {
        return language;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public Integer getSupervisorId() {
        return supervisorId;
    }

    public void setSupervisorId(Integer supervisorId) {
        this.supervisorId = supervisorId;
    }

    public int getSequenceNum() {
        return sequenceNum;
    }

    public void setSequenceNum(int sequenceNum) {
        this.sequenceNum = sequenceNum;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }


    public int getApprovalLevel() {
        return approvalLevel;
    }

    public void setApprovalLevel(int approvalLevel) {
        this.approvalLevel = approvalLevel;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public static void main(String[] arg) {
        FndDepart f = new FndDepart();
        int d = 1;
        f.setDepartmentId(d);
        System.out.print(f.getDepartmentId());
    }


    public String getLedger() {
        return ledger;
    }

    public void setLedger(String ledger) {
        this.ledger = ledger;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getTaxAddress() {
        return taxAddress;
    }

    public void setTaxAddress(String taxAddress) {
        this.taxAddress = taxAddress;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getCostCenterFlag() {
        return costCenterFlag;
    }

    public void setCostCenterFlag(String costCenterFlag) {
        this.costCenterFlag = costCenterFlag;
    }

    public int getBranchCompanyId() {
        return branchCompanyId;
    }

    public void setBranchCompanyId(int branchCompanyId) {
        this.branchCompanyId = branchCompanyId;
    }

    public JSONArray getAccounts() {
        return accounts;
    }

    public void setAccounts(JSONArray accounts) {
        this.accounts = accounts;
    }

    public String getAccountsT() {
        return accountsT;
    }

    public void setAccountsT(String accountsT) {
        this.accountsT = accountsT;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }
}
