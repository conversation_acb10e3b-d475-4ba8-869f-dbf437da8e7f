package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Collection;
import java.util.Date;

/**
 * Created by Huiyi on 2015/7/8.
 */
public class GlBatchTransfer {

    @JsonProperty(value = "batch_id")
    private int batchId;

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "gl_date")
    private String glDate;

    @JsonProperty(value = "journal_num")
    private long journalNum;

    @JsonProperty(value = "attachment_url")
    private String attachmentUrl;

    @JsonProperty(value = "payment_attachment")
    private String paymentAttachment;

    @JsonIgnore
    private int userId;

    @JsonProperty(value = "document_list")
    private String documentList;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "input")
    private String input;

    @JsonProperty(value = "language")
    private String language;

    @JsonIgnore
    private String returnCode;

    @JsonIgnore
    private String returnMessage;

    public int getBatchId() {
        return batchId;
    }

    public void setBatchId(int batchId) {
        this.batchId = batchId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getGlDate() {
        return glDate;
    }

    public void setGlDate(String glDate) {
        this.glDate = glDate;
    }

    public long getJournalNum() {
        return journalNum;
    }

    public void setJournalNum(long journalNum) {
        this.journalNum = journalNum;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public String getPaymentAttachment() {
        return paymentAttachment;
    }

    public void setPaymentAttachment(String paymentAttachment) {
        this.paymentAttachment = paymentAttachment;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getDocumentList() {
        return documentList;
    }

    public void setDocumentList(String documentList) {
        this.documentList = documentList;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}

