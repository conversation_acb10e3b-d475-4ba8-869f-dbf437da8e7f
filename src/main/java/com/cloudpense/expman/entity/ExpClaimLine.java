package com.cloudpense.expman.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;

/**
 * Created by Spooky on 2015/1/8.
 */
public class ExpClaimLine implements Serializable {

    @Valid
    
    @JsonProperty(value = "attachment")
    private Collection<ExpClaimAttachment> attachments;

    @Valid
    
    @JsonProperty(value = "attachments")
    private Collection<ExpClaimAttachment> allAttachments;

    
    @JsonProperty(value = "link_advance")
    private HashMap trvRequestAdvance;

    @JsonProperty(value = "line_id")
    private int lineId;

    //@JsonProperty(value = "header_id")
    //@JsonIdentityReference(alwaysAsId = true)
    @JsonIgnore
    private ExpClaimHeader expClaimHeader;

    
    @JsonProperty(value = "company_id")
    private int companyId;

    @NotNull(message = "should have type_id field.")
    
    @JsonProperty(value = "type_id")
    @JsonIdentityReference(alwaysAsId = true)
    private ExpType expType;

    //    @NotNull(message = "should have receiptDate field.")
    
    @JsonProperty(value = "receipt_date")
    private Date receiptDate;

    @Range(message = "receipt_amount should be more than 0.")
    
    @JsonProperty(value = "receipt_amount")
    private double receiptAmount;

    @Length(max = 16, message = "receipt_currency length should be less than or equal to 2.")
    
    @JsonProperty(value = "receipt_currency")
    private String receiptCurrency;

    @Range(message = "claim_amount should be more than 0.")
    
    @JsonProperty(value = "claim_amount")
    private double claimAmount;

    @Length(max = 16, message = "claim_currency length should be less than or equal to 16.")
    
    @JsonProperty(value = "claim_currency")
    private String claimCurrency;

    @Range(message = "exchange_rate should be more than 0.")
    
    @JsonProperty(value = "exchange_rate")
    private double exchangeRate;

    
    @JsonProperty(value = "return_amount")
    private double returnAmount;

    @Length(max = 255, message = "comments length should be less than or equal to 255.")
    
    @JsonProperty(value = "comments")
    private String comments;

    @Length(max = 255, message = "receipt_location length should be less than or equal to 255.")
    
    @JsonProperty(value = "receipt_location")
    private String receiptLocation;

    @Length(max = 255, message = "location_from length should be less than or equal to 255.")
    
    @JsonProperty(value = "location_from")
    private String locationFrom;

    @Length(max = 255, message = "location_to length should be less than or equal to 255.")
    
    @JsonProperty(value = "location_to")
    private String locationTo;

    @Length(max = 255, message = "shop_name length should be less than or equal to 255.")
    
    @JsonProperty(value = "shop_name")
    private String shopName;

    @Length(max = 255, message = "shop_location length should be less than or equal to 255.")
    @JsonProperty(value = "shop_location")
    private String shopLocation;

    //    @NotNull(message = "should have start_datetime field.")
    
    @JsonProperty(value = "start_datetime")
    private Date startDatetime;

    //    @NotNull(message = "should have end_datetime field.")
    
    @JsonProperty(value = "end_datetime")
    private Date endDatetime;

    @Length(max = 255, message = "attendee_list length should be less than or equal to 255.")
    
    @JsonProperty(value = "attendee_list")
    private String attendeeList;

    @JsonProperty(value = "column1")
    private String column1;

    
    @JsonProperty(value = "column2")
    private String column2;

    
    @JsonProperty(value = "column3")
    private String column3;

    
    @JsonProperty(value = "column4")
    private String column4;

    
    @JsonProperty(value = "column5")
    private String column5;

    
    @JsonProperty(value = "column6")
    private String column6;

    
    @JsonProperty(value = "column7")
    private String column7;


    
    @JsonProperty(value = "column8")
    private String column8;

    
    @JsonProperty(value = "column9")
    private String column9;

    
    @JsonProperty(value = "column10")
    private String column10;

    
    @JsonProperty(value = "column11")
    private String column11;

    
    @JsonProperty(value = "column12")
    private String column12;

    
    @JsonProperty(value = "column13")
    private String column13;

    
    @JsonProperty(value = "column14")
    private String column14;

    
    @JsonProperty(value = "column15")
    private String column15;

    
    @JsonProperty(value = "column16")
    private String column16;

    
    @JsonProperty(value = "column17")
    private String column17;

    
    @JsonProperty(value = "column18")
    private String column18;

    
    @JsonProperty(value = "column19")
    private String column19;

    
    @JsonProperty(value = "column20")
    private String column20;

    
    @JsonProperty(value = "column21")
    private String column21;

    
    @JsonProperty(value = "column22")
    private String column22;

    
    @JsonProperty(value = "column23")
    private String column23;

    
    @JsonProperty(value = "column24")
    private String column24;

    
    @JsonProperty(value = "column25")
    private String column25;

    
    @JsonProperty(value = "column26")
    private String column26;

    
    @JsonProperty(value = "column27")
    private String column27;

    
    @JsonProperty(value = "column28")
    private String column28;

    
    @JsonProperty(value = "column29")
    private String column29;

    
    @JsonProperty(value = "column30")
    private String column30;

    
    @JsonProperty(value = "column31")
    private String column31;

    
    @JsonProperty(value = "column32")
    private String column32;

    
    @JsonProperty(value = "column33")
    private String column33;

    
    @JsonProperty(value = "column34")
    private String column34;

    
    @JsonProperty(value = "column35")
    private String column35;

    
    @JsonProperty(value = "column36")
    private String column36;

    
    @JsonProperty(value = "column37")
    private String column37;

    
    @JsonProperty(value = "column38")
    private String column38;

    
    @JsonProperty(value = "column39")
    private String column39;

    
    @JsonProperty(value = "column40")
    private String column40;

    
    @JsonProperty(value = "column41")
    private String column41;

    
    @JsonProperty(value = "column42")
    private String column42;

    
    @JsonProperty(value = "column43")
    private String column43;

    
    @JsonProperty(value = "column44")
    private String column44;

    
    @JsonProperty(value = "column45")
    private String column45;

    
    @JsonProperty(value = "column46")
    private String column46;

    
    @JsonProperty(value = "column47")
    private String column47;

    
    @JsonProperty(value = "column48")
    private String column48;

    
    @JsonProperty(value = "column49")
    private String column49;

    
    @JsonProperty(value = "column50")
    private String column50;

    
    @JsonProperty(value = "link_header_id")
    private int linkHeaderId;

    
    @JsonProperty(value = "invoice_serial")
    private String invoiceSerial;
    
    @JsonProperty(value = "invoice_num")
    private String invoiceNum;
    
    @JsonProperty(value = "invoice_code")
    private String invoiceCode;
    
    @JsonProperty(value = "source")
    private String source;
    
    @JsonProperty(value = "product_name")
    private String productName;

    
    @JsonProperty(value = "destination_city")
    private int destinationCity;

    
    @JsonProperty(value = "destination_city_to")
    private int destinationCityTo;

    @JsonIgnore
    private String toCitiesT;

    @JsonIgnore
    private String fromCitiesT;

    
    @JsonProperty(value = "created_by")
    private Integer createdBy;

    
    @JsonProperty(value = "creation_date")
    private Date creationDate;

    
    @JsonProperty(value = "last_updated_by")
    private Integer lastUpdatedBy;

    
    @JsonProperty(value = "last_update_date")
    private Date lastUpdateDate;

    
    @JsonProperty(value = "flight_number")
    private String flightNumber;

    
    @JsonProperty(value = "mileage")
    private int mileage;

    
    @JsonProperty(value = "expense_id")
    private int expenseId;

    
    @JsonProperty(value = "mileage_rate")
    private double mileageRate;

    
    @JsonProperty(value = "attendee_number")
    private int attendeeNumber;

    
    @JsonProperty(value = "business_purpose")
    private String businessPurpose;

    
    @JsonProperty(value = "mobile_number")
    private String mobileNumber;

    
    @JsonProperty(value = "duration")
    private String duration;

    
    @JsonProperty(value = "telephone_number")
    private String telephoneNumber;

    
    @JsonProperty(value = "equipment_id")
    private String equipmentId;

    
    @JsonProperty(value = "tax_code_id")
    private int taxCodeId;

    
    @JsonProperty(value = "pay_amount")
    private double payAmount;

    
    @JsonProperty(value = "tax_amount")
    private double taxAmount;

    
    @JsonProperty(value = "fin_tax_amount")
    private double finTaxAmount;

    
    @JsonProperty(value = "fin_tax_code_id")
    private int finTaxCodeId;

    
    @JsonProperty(value = "fin_claim_amount")
    private double finClaimAmount;

    
    @JsonProperty(value = "fin_net_amount")
    private double finNetAmount;

    
    @JsonProperty(value = "finance_comments")
    private String financeComments;

    
    @JsonProperty(value = "validation_messages")
    private Collection<String> validationMessages;

    @JsonIgnore
    private String returnCode;

    @JsonIgnore
    private String returnMessage;

    
    @JsonProperty(value = "attachment_count")
    private int attachmentCount;

    
    @JsonProperty(value = "project_name")
    private String projectName;

    @JsonProperty(value = "customer_name")
    private String customerName;

    
    @JsonProperty(value = "supplier_id")
    private int supplierId;

    
    @JsonProperty(value = "supplier_name")
    private String supplierName;

    @JsonProperty(value = "dr_account_id")
    private int drAccountId;

    @JsonProperty(value = "cr_account_id")
    private int crAccountId;

    @JsonProperty(value = "tax_account_id")
    private int taxAccountId;

    
    private String recharge;

    @JsonIgnore
    private String detail;

    @JsonIgnore
    private String var1;

    @JsonIgnore
    private String var2;

    
    @JsonProperty(value = "internal_type")
    private String internalType;

    
    @JsonProperty(value = "trip_type")
    private int tripType;

    
    @JsonProperty(value = "flight_type")
    private int flightType;

    
    @JsonProperty(value = "train_type")
    private int trainType;

    
    @JsonProperty(value = "time_length")
    private double timeLength;

    
    @JsonProperty(value = "depart_begin_datetime")
    private  Date departBeginDatetime;

    
    @JsonProperty(value = "depart_end_datetime")
    private Date departEndDatetime;

    
    @JsonProperty(value = "depart_type")
    private int departType;

    
    @JsonProperty(value = "return_begin_datetime")
    private Date returnBeginDatetime;

    
    @JsonProperty(value = "return_end_datetime")
    private Date returnEndDatetime;

    
    @JsonProperty(value = "return_type")
    private int returnType;

    
    @JsonProperty(value = "price")
    private double price;

    
    @JsonProperty(value = "quantity")
    private double quantity;

    
    @JsonProperty(value = "flight_class")
    private String flightClass;

    
    @JsonProperty(value = "train_class")
    private String trainClass;

    
    @JsonProperty(value = "passenger_list")
    private String passengerList;

//    @JsonIgnore
//    private String passengerListTransfer;

    
    @JsonProperty(value = "approval_number")
    private String approvalNumber;

    
    @JsonProperty(value = "standard_id")
    private int standardId;

    
    @JsonProperty(value = "link_line_id")
    private int linkLineId;

    
    @JsonProperty(value = "linked_line")
    private Collection<HashMap> linkedLines;

    
    @JsonProperty(value = "original_amount")
    private double originalAmount;

    
    @JsonProperty(value = "net_amount")
    private double netAmount;

    
    @JsonProperty(value = "pay_claim_amount")
    private double payClaimAmount;

    
    @JsonProperty(value = "offset_detail")
    private String offsetDetail;

    
    @JsonProperty(value = "fin_receipt_amount")
    private double finReceiptAmount;

    
    @JsonProperty(value = "fin_exchange_rate")
    private double finExchangeRate;

    
    @JsonProperty(value = "fin_pay_amount")
    private double finPayAmount;

    
    @JsonProperty(value = "fin_pay_claim_amount")
    private double finPayClaimAmount;

    
    @JsonProperty(value = "invoice_type")
    private String invoiceType;

    
    @JsonProperty(value = "einvoice_flag")
    private String einvoiceFlag;

    
    @JsonProperty(value = "invoice_true")
    private String invoiceTrue;

    
    @JsonProperty(value = "invoice_attachments")
    private JSONArray invoiceAttachments;

    
    @JsonProperty(value = "invoice_detail")
    private JSONObject invoiceDetail;

    
    @JsonProperty(value = "com_exchange_rate")
    private double comExchangeRate;

    
    @JsonProperty(value = "cost_center_id")
    private int costCenterId;

    
    @JsonProperty(value = "budget_id")
    private int budgetId;

    @JsonProperty(value = "budget_period")
    private int budgetPeriod;

    @JsonProperty(value = "pay_method_id")
    private int payMethodId;
    
    @JsonProperty(value = "address")
    private String address;

    @JsonProperty(value = "exp_type_id")
    private Integer expLineTypeId;

    public Integer getExpLineTypeId() {
        return expLineTypeId;
    }

    public ExpClaimLine setExpLineTypeId(Integer expLineTypeId) {
        this.expLineTypeId = expLineTypeId;
        return this;
    }

    public int getPayMethodId() {
        return payMethodId;
    }

    public void setPayMethodId(int payMethodId) {
        this.payMethodId = payMethodId;
    }

    public int getBudgetPeriod() {
        return budgetPeriod;
    }

    public void setBudgetPeriod(int budgetPeriod) {
        this.budgetPeriod = budgetPeriod;
    }

    public Collection<ExpClaimAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(Collection<ExpClaimAttachment> attachments) {
        this.attachments = attachments;
    }

    public Collection<ExpClaimAttachment> getAllAttachments() {
        return allAttachments;
    }

    public void setAllAttachments(Collection<ExpClaimAttachment> allAttachments) {
        this.allAttachments = allAttachments;
    }

    public HashMap getTrvRequestAdvance() {
        return trvRequestAdvance;
    }

    public void setTrvRequestAdvance(HashMap trvRequestAdvance) {
        this.trvRequestAdvance = trvRequestAdvance;
    }

    public int getLineId() {
        return lineId;
    }

    public void setLineId(int lineId) {
        this.lineId = lineId;
    }

    public ExpClaimHeader getExpClaimHeader() {
        return expClaimHeader;
    }

    public void setExpClaimHeader(ExpClaimHeader expClaimHeader) {
        this.expClaimHeader = expClaimHeader;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public ExpType getExpType() {
        return expType;
    }

    public void setExpType(ExpType expType) {
        this.expType = expType;
    }

    public Date getReceiptDate() {
        return receiptDate;
    }

    public void setReceiptDate(Date receiptDate) {
        this.receiptDate = receiptDate;
    }

    public double getReceiptAmount() {
        return receiptAmount;
    }

    public void setReceiptAmount(double receiptAmount) {
        this.receiptAmount = receiptAmount;
    }

    public String getReceiptCurrency() {
        return receiptCurrency;
    }

    public void setReceiptCurrency(String receiptCurrency) {
        this.receiptCurrency = receiptCurrency;
    }

    public double getClaimAmount() {
        return claimAmount;
    }

    public void setClaimAmount(double claimAmount) {
        this.claimAmount = claimAmount;
    }

    public String getClaimCurrency() {
        return claimCurrency;
    }

    public void setClaimCurrency(String claimCurrency) {
        this.claimCurrency = claimCurrency;
    }

    public double getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(double exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public double getReturnAmount() {
        return returnAmount;
    }

    public void setReturnAmount(double returnAmount) {
        this.returnAmount = returnAmount;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public String getReceiptLocation() {
        return receiptLocation;
    }

    public void setReceiptLocation(String receiptLocation) {
        this.receiptLocation = receiptLocation;
    }

    public String getLocationFrom() {
        return locationFrom;
    }

    public void setLocationFrom(String locationFrom) {
        this.locationFrom = locationFrom;
    }

    public String getLocationTo() {
        return locationTo;
    }

    public void setLocationTo(String locationTo) {
        this.locationTo = locationTo;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopLocation() {
        return shopLocation;
    }

    public void setShopLocation(String shopLocation) {
        this.shopLocation = shopLocation;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getAttendeeList() {
        return attendeeList;
    }

    public void setAttendeeList(String attendeeList) {
        this.attendeeList = attendeeList;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getColumn11() {
        return column11;
    }

    public void setColumn11(String column11) {
        this.column11 = column11;
    }

    public String getColumn12() {
        return column12;
    }

    public void setColumn12(String column12) {
        this.column12 = column12;
    }

    public String getColumn13() {
        return column13;
    }

    public void setColumn13(String column13) {
        this.column13 = column13;
    }

    public String getColumn14() {
        return column14;
    }

    public void setColumn14(String column14) {
        this.column14 = column14;
    }

    public String getColumn15() {
        return column15;
    }

    public void setColumn15(String column15) {
        this.column15 = column15;
    }

    public String getColumn16() {
        return column16;
    }

    public void setColumn16(String column16) {
        this.column16 = column16;
    }

    public String getColumn17() {
        return column17;
    }

    public void setColumn17(String column17) {
        this.column17 = column17;
    }

    public String getColumn18() {
        return column18;
    }

    public void setColumn18(String column18) {
        this.column18 = column18;
    }

    public String getColumn19() {
        return column19;
    }

    public void setColumn19(String column19) {
        this.column19 = column19;
    }

    public String getColumn20() {
        return column20;
    }

    public void setColumn20(String column20) {
        this.column20 = column20;
    }

    public String getColumn21() {
        return column21;
    }

    public void setColumn21(String column21) {
        this.column21 = column21;
    }

    public String getColumn22() {
        return column22;
    }

    public void setColumn22(String column22) {
        this.column22 = column22;
    }

    public String getColumn23() {
        return column23;
    }

    public void setColumn23(String column23) {
        this.column23 = column23;
    }

    public String getColumn24() {
        return column24;
    }

    public void setColumn24(String column24) {
        this.column24 = column24;
    }

    public String getColumn25() {
        return column25;
    }

    public void setColumn25(String column25) {
        this.column25 = column25;
    }

    public String getColumn26() {
        return column26;
    }

    public void setColumn26(String column26) {
        this.column26 = column26;
    }

    public String getColumn27() {
        return column27;
    }

    public void setColumn27(String column27) {
        this.column27 = column27;
    }

    public String getColumn28() {
        return column28;
    }

    public void setColumn28(String column28) {
        this.column28 = column28;
    }

    public String getColumn29() {
        return column29;
    }

    public void setColumn29(String column29) {
        this.column29 = column29;
    }

    public String getColumn30() {
        return column30;
    }

    public void setColumn30(String column30) {
        this.column30 = column30;
    }

    public String getColumn31() {
        return column31;
    }

    public void setColumn31(String column31) {
        this.column31 = column31;
    }

    public String getColumn32() {
        return column32;
    }

    public void setColumn32(String column32) {
        this.column32 = column32;
    }

    public String getColumn33() {
        return column33;
    }

    public void setColumn33(String column33) {
        this.column33 = column33;
    }

    public String getColumn34() {
        return column34;
    }

    public void setColumn34(String column34) {
        this.column34 = column34;
    }

    public String getColumn35() {
        return column35;
    }

    public void setColumn35(String column35) {
        this.column35 = column35;
    }

    public String getColumn36() {
        return column36;
    }

    public void setColumn36(String column36) {
        this.column36 = column36;
    }

    public String getColumn37() {
        return column37;
    }

    public void setColumn37(String column37) {
        this.column37 = column37;
    }

    public String getColumn38() {
        return column38;
    }

    public void setColumn38(String column38) {
        this.column38 = column38;
    }

    public String getColumn39() {
        return column39;
    }

    public void setColumn39(String column39) {
        this.column39 = column39;
    }

    public String getColumn40() {
        return column40;
    }

    public void setColumn40(String column40) {
        this.column40 = column40;
    }

    public String getColumn41() {
        return column41;
    }

    public void setColumn41(String column41) {
        this.column41 = column41;
    }

    public String getColumn42() {
        return column42;
    }

    public void setColumn42(String column42) {
        this.column42 = column42;
    }

    public String getColumn43() {
        return column43;
    }

    public void setColumn43(String column43) {
        this.column43 = column43;
    }

    public String getColumn44() {
        return column44;
    }

    public void setColumn44(String column44) {
        this.column44 = column44;
    }

    public String getColumn45() {
        return column45;
    }

    public void setColumn45(String column45) {
        this.column45 = column45;
    }

    public String getColumn46() {
        return column46;
    }

    public void setColumn46(String column46) {
        this.column46 = column46;
    }

    public String getColumn47() {
        return column47;
    }

    public void setColumn47(String column47) {
        this.column47 = column47;
    }

    public String getColumn48() {
        return column48;
    }

    public void setColumn48(String column48) {
        this.column48 = column48;
    }

    public String getColumn49() {
        return column49;
    }

    public void setColumn49(String column49) {
        this.column49 = column49;
    }

    public String getColumn50() {
        return column50;
    }

    public void setColumn50(String column50) {
        this.column50 = column50;
    }

    public int getLinkHeaderId() {
        return linkHeaderId;
    }

    public void setLinkHeaderId(int linkHeaderId) {
        this.linkHeaderId = linkHeaderId;
    }

    public String getInvoiceSerial() {
        return invoiceSerial;
    }

    public void setInvoiceSerial(String invoiceSerial) {
        this.invoiceSerial = invoiceSerial;
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public int getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(int destinationCity) {
        this.destinationCity = destinationCity;
    }

    public int getDestinationCityTo() {
        return destinationCityTo;
    }

    public void setDestinationCityTo(int destinationCityTo) {
        this.destinationCityTo = destinationCityTo;
    }

    public String getToCitiesT() {
        return toCitiesT;
    }

    public void setToCitiesT(String toCitiesT) {
        this.toCitiesT = toCitiesT;
    }

    public String getFromCitiesT() {
        return fromCitiesT;
    }

    public void setFromCitiesT(String fromCitiesT) {
        this.fromCitiesT = fromCitiesT;
    }

    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getFlightNumber() {
        return flightNumber;
    }

    public void setFlightNumber(String flightNumber) {
        this.flightNumber = flightNumber;
    }

    public int getMileage() {
        return mileage;
    }

    public void setMileage(int mileage) {
        this.mileage = mileage;
    }

    public int getExpenseId() {
        return expenseId;
    }

    public void setExpenseId(int expenseId) {
        this.expenseId = expenseId;
    }

    public double getMileageRate() {
        return mileageRate;
    }

    public void setMileageRate(double mileageRate) {
        this.mileageRate = mileageRate;
    }

    public int getAttendeeNumber() {
        return attendeeNumber;
    }

    public void setAttendeeNumber(int attendeeNumber) {
        this.attendeeNumber = attendeeNumber;
    }

    public String getBusinessPurpose() {
        return businessPurpose;
    }

    public void setBusinessPurpose(String businessPurpose) {
        this.businessPurpose = businessPurpose;
    }

    public String getMobileNumber() {
        return mobileNumber;
    }

    public void setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public String getTelephoneNumber() {
        return telephoneNumber;
    }

    public void setTelephoneNumber(String telephoneNumber) {
        this.telephoneNumber = telephoneNumber;
    }

    public String getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(String equipmentId) {
        this.equipmentId = equipmentId;
    }

    public int getTaxCodeId() {
        return taxCodeId;
    }

    public void setTaxCodeId(int taxCodeId) {
        this.taxCodeId = taxCodeId;
    }

    public double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(double payAmount) {
        this.payAmount = payAmount;
    }

    public double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public double getFinTaxAmount() {
        return finTaxAmount;
    }

    public void setFinTaxAmount(double finTaxAmount) {
        this.finTaxAmount = finTaxAmount;
    }

    public int getFinTaxCodeId() {
        return finTaxCodeId;
    }

    public void setFinTaxCodeId(int finTaxCodeId) {
        this.finTaxCodeId = finTaxCodeId;
    }

    public double getFinClaimAmount() {
        return finClaimAmount;
    }

    public void setFinClaimAmount(double finClaimAmount) {
        this.finClaimAmount = finClaimAmount;
    }

    public double getFinNetAmount() {
        return finNetAmount;
    }

    public void setFinNetAmount(double finNetAmount) {
        this.finNetAmount = finNetAmount;
    }

    public String getFinanceComments() {
        return financeComments;
    }

    public void setFinanceComments(String financeComments) {
        this.financeComments = financeComments;
    }

    public Collection<String> getValidationMessages() {
        return validationMessages;
    }

    public void setValidationMessages(Collection<String> validationMessages) {
        this.validationMessages = validationMessages;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public int getAttachmentCount() {
        return attachmentCount;
    }

    public void setAttachmentCount(int attachmentCount) {
        this.attachmentCount = attachmentCount;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public int getDrAccountId() {
        return drAccountId;
    }

    public void setDrAccountId(int drAccountId) {
        this.drAccountId = drAccountId;
    }

    public int getCrAccountId() {
        return crAccountId;
    }

    public void setCrAccountId(int crAccountId) {
        this.crAccountId = crAccountId;
    }

    public int getTaxAccountId() {
        return taxAccountId;
    }

    public void setTaxAccountId(int taxAccountId) {
        this.taxAccountId = taxAccountId;
    }

    public String getRecharge() {
        return recharge;
    }

    public void setRecharge(String recharge) {
        this.recharge = recharge;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getVar1() {
        return var1;
    }

    public void setVar1(String var1) {
        this.var1 = var1;
    }

    public String getVar2() {
        return var2;
    }

    public void setVar2(String var2) {
        this.var2 = var2;
    }

    public String getInternalType() {
        return internalType;
    }

    public void setInternalType(String internalType) {
        this.internalType = internalType;
    }

    public int getTripType() {
        return tripType;
    }

    public void setTripType(int tripType) {
        this.tripType = tripType;
    }

    public int getFlightType() {
        return flightType;
    }

    public void setFlightType(int flightType) {
        this.flightType = flightType;
    }

    public int getTrainType() {
        return trainType;
    }

    public void setTrainType(int trainType) {
        this.trainType = trainType;
    }

    public double getTimeLength() {
        return timeLength;
    }

    public void setTimeLength(double timeLength) {
        this.timeLength = timeLength;
    }

    public Date getDepartBeginDatetime() {
        return departBeginDatetime;
    }

    public void setDepartBeginDatetime(Date departBeginDatetime) {
        this.departBeginDatetime = departBeginDatetime;
    }

    public Date getDepartEndDatetime() {
        return departEndDatetime;
    }

    public void setDepartEndDatetime(Date departEndDatetime) {
        this.departEndDatetime = departEndDatetime;
    }

    public int getDepartType() {
        return departType;
    }

    public void setDepartType(int departType) {
        this.departType = departType;
    }

    public Date getReturnBeginDatetime() {
        return returnBeginDatetime;
    }

    public void setReturnBeginDatetime(Date returnBeginDatetime) {
        this.returnBeginDatetime = returnBeginDatetime;
    }

    public Date getReturnEndDatetime() {
        return returnEndDatetime;
    }

    public void setReturnEndDatetime(Date returnEndDatetime) {
        this.returnEndDatetime = returnEndDatetime;
    }

    public int getReturnType() {
        return returnType;
    }

    public void setReturnType(int returnType) {
        this.returnType = returnType;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public String getFlightClass() {
        return flightClass;
    }

    public void setFlightClass(String flightClass) {
        this.flightClass = flightClass;
    }

    public String getTrainClass() {
        return trainClass;
    }

    public void setTrainClass(String trainClass) {
        this.trainClass = trainClass;
    }

    public String getPassengerList() {
        return passengerList;
    }

    public void setPassengerList(String passengerList) {
        this.passengerList = passengerList;
    }

    public String getApprovalNumber() {
        return approvalNumber;
    }

    public void setApprovalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
    }

    public int getStandardId() {
        return standardId;
    }

    public void setStandardId(int standardId) {
        this.standardId = standardId;
    }

    public int getLinkLineId() {
        return linkLineId;
    }

    public void setLinkLineId(int linkLineId) {
        this.linkLineId = linkLineId;
    }

    public Collection<HashMap> getLinkedLines() {
        return linkedLines;
    }

    public void setLinkedLines(Collection<HashMap> linkedLines) {
        this.linkedLines = linkedLines;
    }

    public double getOriginalAmount() {
        return originalAmount;
    }

    public void setOriginalAmount(double originalAmount) {
        this.originalAmount = originalAmount;
    }

    public double getNetAmount() {
        return netAmount;
    }

    public void setNetAmount(double netAmount) {
        this.netAmount = netAmount;
    }

    public double getPayClaimAmount() {
        return payClaimAmount;
    }

    public void setPayClaimAmount(double payClaimAmount) {
        this.payClaimAmount = payClaimAmount;
    }

    public String getOffsetDetail() {
        return offsetDetail;
    }

    public void setOffsetDetail(String offsetDetail) {
        this.offsetDetail = offsetDetail;
    }

    public double getFinReceiptAmount() {
        return finReceiptAmount;
    }

    public void setFinReceiptAmount(double finReceiptAmount) {
        this.finReceiptAmount = finReceiptAmount;
    }

    public double getFinExchangeRate() {
        return finExchangeRate;
    }

    public void setFinExchangeRate(double finExchangeRate) {
        this.finExchangeRate = finExchangeRate;
    }

    public double getFinPayAmount() {
        return finPayAmount;
    }

    public void setFinPayAmount(double finPayAmount) {
        this.finPayAmount = finPayAmount;
    }

    public double getFinPayClaimAmount() {
        return finPayClaimAmount;
    }

    public void setFinPayClaimAmount(double finPayClaimAmount) {
        this.finPayClaimAmount = finPayClaimAmount;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getEinvoiceFlag() {
        return einvoiceFlag;
    }

    public void setEinvoiceFlag(String einvoiceFlag) {
        this.einvoiceFlag = einvoiceFlag;
    }

    public String getInvoiceTrue() {
        return invoiceTrue;
    }

    public void setInvoiceTrue(String invoiceTrue) {
        this.invoiceTrue = invoiceTrue;
    }

    public JSONArray getInvoiceAttachments() {
        return invoiceAttachments;
    }

    public void setInvoiceAttachments(JSONArray invoiceAttachments) {
        this.invoiceAttachments = invoiceAttachments;
    }

    public JSONObject getInvoiceDetail() {
        return invoiceDetail;
    }

    public void setInvoiceDetail(JSONObject invoiceDetail) {
        this.invoiceDetail = invoiceDetail;
    }

    public double getComExchangeRate() {
        return comExchangeRate;
    }

    public void setComExchangeRate(double comExchangeRate) {
        this.comExchangeRate = comExchangeRate;
    }

    public int getCostCenterId() {
        return costCenterId;
    }

    public void setCostCenterId(int costCenterId) {
        this.costCenterId = costCenterId;
    }

    public int getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(int budgetId) {
        this.budgetId = budgetId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}

