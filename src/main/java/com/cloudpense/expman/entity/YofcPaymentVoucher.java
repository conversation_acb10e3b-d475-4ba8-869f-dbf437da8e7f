package com.cloudpense.expman.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class YofcPaymentVoucher {
    /**
     * 单据头ID
     */
    private int headerId;
    /**
     * 费控系统单据号(流水号)
     */
    private String documentNum;
    /**
     * 所属公司的ERP代码
     */
    private String orgId;

    private String fdColumn4;

    private String fdColumn5;

    /**
     * 公司代码
     */
    private String branchCode;

    /**
     * 付款时间
     */
    private Date paymentDate;
    /**
     * 差旅报销/费用报销
     */
    private String typeCode;
    /**
     * 科目组合
     */
    private String accountCode;
    /**
     * 金额
     */
    private BigDecimal lineAmount;
    /**
     * 银行业务流水号
     */
    private String pipelineNumber;
    /**
     * 出差人/申请人
     */
    private String chargeUserName;
    /**
     * 出差开始时间
     */
    private Date startDate;
    /**
     * 出差结束时间
     */
    private Date endDate;
    /**
     * 出差城市
     */
    private String cityName;

}
