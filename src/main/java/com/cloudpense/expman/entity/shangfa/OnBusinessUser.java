package com.cloudpense.expman.entity.shangfa;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/20 15:49
 */
public class OnBusinessUser {

    private String employeeNumber;

    private Date startDatetime;

    private String typeCode;

    private Date endDatetime;

    private String documentNum;

    private Integer headerId;

    private String externalStatus;

    private String externalMessage;

    private Integer lineId;

    public Integer getLineId() {
        return lineId;
    }

    public OnBusinessUser setLineId(Integer lineId) {
        this.lineId = lineId;
        return this;
    }

    public String getExternalMessage() {
        return externalMessage;
    }

    public OnBusinessUser setExternalMessage(String externalMessage) {
        this.externalMessage = externalMessage;
        return this;
    }

    public String getExternalStatus() {
        return externalStatus;
    }

    public OnBusinessUser setExternalStatus(String externalStatus) {
        this.externalStatus = externalStatus;
        return this;
    }

    public Integer getHeaderId() {
        return headerId;
    }

    public OnBusinessUser setHeaderId(Integer headerId) {
        this.headerId = headerId;
        return this;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public OnBusinessUser setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
        return this;
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public OnBusinessUser setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
        return this;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public OnBusinessUser setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
        return this;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public OnBusinessUser setTypeCode(String typeCode) {
        this.typeCode = typeCode;
        return this;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public OnBusinessUser setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
        return this;
    }
}
