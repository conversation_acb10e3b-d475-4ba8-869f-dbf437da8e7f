package com.cloudpense.expman.entity.shangfa;

/**
 * <AUTHOR>
 * @date 2019/11/21 15:24
 */
public class VoucherHeader {

    private Integer headerId;

    private String documentNum;

    private String typeCode;

    private String description;

    private String column12;

    private String column3;

    private String column35;

    private String currencyCode;

    private String departmentCode;

    private String projectCode;

    private String projectId;

    public String getProjectCode() {
        return projectCode;
    }

    public VoucherHeader setProjectCode(String projectCode) {
        this.projectCode = projectCode;
        return this;
    }

    public String getProjectId() {
        return projectId;
    }

    public VoucherHeader setProjectId(String projectId) {
        this.projectId = projectId;
        return this;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public VoucherHeader setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
        return this;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public VoucherHeader setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
        return this;
    }

    public Integer getHeaderId() {
        return headerId;
    }

    public VoucherHeader setHeaderId(Integer headerId) {
        this.headerId = headerId;
        return this;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public VoucherHeader setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
        return this;
    }

    public String getTypeCode() {
        return typeCode;
    }

    public VoucherHeader setTypeCode(String typeCode) {
        this.typeCode = typeCode;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public VoucherHeader setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getColumn12() {
        return column12;
    }

    public VoucherHeader setColumn12(String column12) {
        this.column12 = column12;
        return this;
    }

    public String getColumn3() {
        return column3;
    }

    public VoucherHeader setColumn3(String column3) {
        this.column3 = column3;
        return this;
    }

    public String getColumn35() {
        return column35;
    }

    public VoucherHeader setColumn35(String column35) {
        this.column35 = column35;
        return this;
    }
}
