package com.cloudpense.expman.entity.shangfa;

/**
 * <AUTHOR>
 * @date 2019/12/9 16:20
 */
public class HandleEntity {

    private String postData;

    private Boolean isPostFlag;

    private StringBuilder errorMsg;

    public String getPostData() {
        return postData;
    }

    public HandleEntity setPostData(String postData) {
        this.postData = postData;
        return this;
    }

    public Boolean getPostFlag() {
        return isPostFlag;
    }

    public HandleEntity setPostFlag(Boolean postFlag) {
        isPostFlag = postFlag;
        return this;
    }

    public StringBuilder getErrorMsg() {
        return errorMsg;
    }

    public HandleEntity setErrorMsg(StringBuilder errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }
}
