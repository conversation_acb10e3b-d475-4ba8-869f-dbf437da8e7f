package com.cloudpense.expman.entity.shangfa;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/11/21 17:00
 */
public class VoucherLine {

    private BigDecimal finNetAmount;

    private BigDecimal finClaimAmount;

    private BigDecimal finTaxAmount;

    private String suppplierCode;

    private String drAccountCode;

    private String crAccountCode;

    private String taxAccountCode;

    private BigDecimal originalAmount;

    private Date startDateTime;

    private Date  endDateTime;

    private String receiptCurrency;

    private String departmentCode;

    private String projectName;

    private String projectCode;

    private String projectId;

    private String column41;


    public String getColumn41() {
        return column41;
    }

    public VoucherLine setColumn41(String column41) {
        this.column41 = column41;
        return this;
    }

    public String getProjectId() {
        return projectId;
    }

    public VoucherLine setProjectId(String projectId) {
        this.projectId = projectId;
        return this;
    }

    public String getProjectCode() {
        return projectCode;
    }

    public VoucherLine setProjectCode(String projectCode) {
        this.projectCode = projectCode;
        return this;
    }

    public BigDecimal getFinNetAmount() {
        return finNetAmount;
    }

    public VoucherLine setFinNetAmount(BigDecimal finNetAmount) {
        this.finNetAmount = finNetAmount;
        return this;
    }

    public BigDecimal getFinClaimAmount() {
        return finClaimAmount;
    }

    public VoucherLine setFinClaimAmount(BigDecimal finClaimAmount) {
        this.finClaimAmount = finClaimAmount;
        return this;
    }

    public BigDecimal getFinTaxAmount() {
        return finTaxAmount;
    }

    public VoucherLine setFinTaxAmount(BigDecimal finTaxAmount) {
        this.finTaxAmount = finTaxAmount;
        return this;
    }

    public String getSuppplierCode() {
        return suppplierCode;
    }

    public VoucherLine setSuppplierCode(String suppplierCode) {
        this.suppplierCode = suppplierCode;
        return this;
    }

    public String getDrAccountCode() {
        return drAccountCode;
    }

    public VoucherLine setDrAccountCode(String drAccountCode) {
        this.drAccountCode = drAccountCode;
        return this;
    }

    public String getCrAccountCode() {
        return crAccountCode;
    }

    public VoucherLine setCrAccountCode(String crAccountCode) {
        this.crAccountCode = crAccountCode;
        return this;
    }

    public String getTaxAccountCode() {
        return taxAccountCode;
    }

    public VoucherLine setTaxAccountCode(String taxAccountCode) {
        this.taxAccountCode = taxAccountCode;
        return this;
    }

    public BigDecimal getOriginalAmount() {
        return originalAmount;
    }

    public VoucherLine setOriginalAmount(BigDecimal originalAmount) {
        this.originalAmount = originalAmount;
        return this;
    }

    public Date getStartDateTime() {
        return startDateTime;
    }

    public VoucherLine setStartDateTime(Date startDateTime) {
        this.startDateTime = startDateTime;
        return this;
    }

    public Date getEndDateTime() {
        return endDateTime;
    }

    public VoucherLine setEndDateTime(Date endDateTime) {
        this.endDateTime = endDateTime;
        return this;
    }

    public String getReceiptCurrency() {
        return receiptCurrency;
    }

    public VoucherLine setReceiptCurrency(String receiptCurrency) {
        this.receiptCurrency = receiptCurrency;
        return this;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public VoucherLine setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
        return this;
    }

    public String getProjectName() {
        return projectName;
    }

    public VoucherLine setProjectName(String projectName) {
        this.projectName = projectName;
        return this;
    }
}
