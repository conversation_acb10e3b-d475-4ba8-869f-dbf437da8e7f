package com.cloudpense.expman.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.util.HttpUtil;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class DingTalk {

    public static String getToken(String appKey, String appSecret) throws Exception {

        String tokenURL = "https://oapi.dingtalk.com/gettoken?appkey="+appKey+"&appsecret="+appSecret;
        JSONObject result = HttpUtil.httpGet(tokenURL);
        String accessToken = null;
        if(result != null){
            accessToken = result.getString("access_token");
        }
        return accessToken;
    }

    public static JSONObject asyncSendMsg(String token,int agentId,String userIds,JSONObject msg) throws Exception{
        String asyncSendMsgURL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token="+token;
        JSONObject param = new JSONObject();
        param.put("agent_id",agentId);
        param.put("userid_list",userIds);
        param.put("msg",msg);
        return HttpUtil.httpPost(asyncSendMsgURL,param);
    }

    public static JSONObject getUserIdByCode(String accessToken, String code) throws Exception {
        String userIdByCodeURL = "https://oapi.dingtalk.com/user/getuserinfo?access_token="+accessToken+"&code="+code;
        return HttpUtil.httpGet(userIdByCodeURL);
    }

    public static JSONObject getDepartmentList(String accessToken, String locale) throws Exception{
        locale = "zh_CN";
        String departmentURL = "https://oapi.dingtalk.com/department/list?access_token="+accessToken+"&lang="+locale+"&fetch_child=true&id=1";
        return HttpUtil.httpGet(departmentURL);
    }

    public static JSONObject addTask(String token, JSONObject param) throws Exception{
        String addTaskURL = "https://oapi.dingtalk.com/topapi/workrecord/add?access_token="+token;
        return HttpUtil.httpPost(addTaskURL,param);
    }

    public static JSONObject updateTask(String token, String userId,String recordId) throws Exception{
        String updateTaskURL = "https://oapi.dingtalk.com/topapi/workrecord/update?access_token="+token;
        JSONObject param = new JSONObject();
        param.put("userid",userId);
        param.put("record_id",recordId);
        return HttpUtil.httpPost(updateTaskURL,param);
    }

    public static JSONArray getDeptMember(String accessToken, String deptId) throws Exception{
        String deptMemberURL = "https://oapi.dingtalk.com/user/getDeptMember?access_token="+accessToken+"&deptId="+deptId;
        JSONObject result = HttpUtil.httpGet(deptMemberURL);
        JSONArray userIds = null;
        if(result != null){
            userIds = result.getJSONArray("userIds");
        }
        return userIds;
    }

    public static JSONObject userListByPage(String accessToken, String deptId, long offset,int size, String order) throws Exception{
        String listByPage = "https://oapi.dingtalk.com/user/listbypage?access_token="+accessToken+"&department_id="+deptId+"&offset="+offset+"&size="+size+"&order="+order;
        return HttpUtil.httpGet(listByPage);
    }

    public static JSONObject getUserInfoByCode(String signature,String timestamp,String accessKey,String code) throws Exception{
        String userInfoURL = "https://oapi.dingtalk.com/sns/getuserinfo_bycode?signature="+signature+"&timestamp="+timestamp+"&accessKey="+accessKey;
        JSONObject param = new JSONObject();
        param.put("tmp_auth_code",code);
        return HttpUtil.httpPost(userInfoURL,param);
    }

    public static JSONObject getUserInfoById(String token, String userId) throws Exception{
        String userInfoURL = "https://oapi.dingtalk.com/user/get?access_token="+token+"&userid="+userId;
        return HttpUtil.httpGet(userInfoURL);
    }

    public static String getSignature(String timestamp, String appSecret) throws Exception{
        // 根据timestamp, appSecret计算签名值
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(appSecret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signatureBytes = mac.doFinal(timestamp.getBytes("UTF-8"));
        String signature = new String(Base64.encodeBase64(signatureBytes));
        System.out.println(signature);
        return urlEncode(signature,"UTF-8");
    }

    public static JSONObject getUserIdByUnionId(String token,String unionId) throws Exception{
        String userIdByUnionIdURL = "https://oapi.dingtalk.com/user/getUseridByUnionid?access_token="+token+"&unionid="+unionId;
        return HttpUtil.httpGet(userIdByUnionIdURL);
    }

    // encoding参数使用utf-8
    public static String urlEncode(String value, String encoding) throws Exception{
        if (value == null) {
            return "";
        }
        try {
            String encoded = URLEncoder.encode(value, encoding);
            return encoded.replace("+", "%20").replace("*", "%2A")
                    .replace("~", "%7E").replace("/", "%2F");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException("FailedToEncodeUri", e);
        }
    }

}
