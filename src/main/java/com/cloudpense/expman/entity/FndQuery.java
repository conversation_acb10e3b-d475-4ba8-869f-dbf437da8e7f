package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * Created by futurefall on 2016/3/3.
 */
public class FndQuery {
    private String type;
    private int id;
    private String input;
    private int companyId;
    private int userId;
    private int agentId;
    private String language;
    private String returnCode;
    private String returnMessage;
    private String p1;
    private String p2;
    private String p3;
    private String p4;
    private String p5;
    private int lovId ;
    private String Code;
    private String ErrorDetail;
    private String Message;
    private String Detail;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }


    public String getReturnMessage() {
        return returnMessage;
    }

    public String getCode() {
        return Code;
    }

    public void setCode(String Code) {
        this.Code = Code;
    }

    public String getErrorDetail() {
        return ErrorDetail;
    }

    public void setErrorDetail(String ErrorDetail) {
        this.ErrorDetail = ErrorDetail;
    }

    public String getMessage() {
        return Message;
    }

    public void setMessage(String Message) {
        this.Message = Message;
    }


    public String getDetail() {
        return Detail;
    }

    public void setDetail(String Detail) {
        this.Detail = Detail;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getP1() {
        return p1;
    }

    public void setP1(String p1) {
        this.p1 = p1;
    }

    public String getP2() {
        return p2;
    }

    public void setP2(String p2) {
        this.p2 = p2;
    }

    public String getP3() {
        return p3;
    }

    public void setP3(String p3) {
        this.p3 = p3;
    }

    public String getP4() {
        return p4;
    }

    public void setP4(String p4) {
        this.p4 = p4;
    }

    public String getP5() {
        return p5;
    }

    public void setP5(String p5) {
        this.p5 = p5;
    }

    public int getLovId() {
        return lovId;
    }

    public void setLovId(int lovId) {
        this.lovId = lovId;
    }
}
