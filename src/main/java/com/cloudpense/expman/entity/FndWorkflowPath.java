package com.cloudpense.expman.entity;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.*;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Spooky on 2015/1/8.
 */
@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "workflow_id")
public class FndWorkflowPath implements Serializable {

    
    @JsonProperty(value = "path_id")
    private int pathId;

    
    @JsonProperty(value = "workflow_id")
    private int workflowId;

    
    @JsonProperty(value = "workflow_type")
    private String workflowType;

    
    @JsonProperty(value = "sequence_num")
    private int sequenceNum;

    
    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "source_id")
    private int sourceId;

    
    @JsonProperty(value = "position_id")
    private int positionId;

    
    @JsonProperty(value = "position_name")
    private String positionName;

    
    @JsonProperty(value = "user_name")
    private String userName;

    
    @JsonProperty(value = "note")
    private String note;

    
    @JsonProperty(value = "open_date")
    private Date openDate;

    
    @JsonProperty(value = "end_date")
    private Date endDate;

    
    @JsonProperty(value = "user_id")
    private int userId;

    
    @JsonProperty(value = "position_user")
    private JSONArray positionUsers;

    
    @JsonProperty(value = "position")
    private String position;

    
    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "user_position_name")
    private String userPositionName;

    @JsonProperty(value = "user_position_id")
    private int userPositionId;

    
    private String language;

    
    @JsonProperty(value = "parent_id")
    private int parentId;

    
    @JsonProperty(value = "question")
    private String question;

    
    @JsonProperty(value = "agent_id")
    private int agentId;

    
    @JsonProperty(value = "function")
    private String function;

    
    @JsonProperty(value = "value")
    private String value;

    
    @JsonProperty(value = "value_json")
    private String valueJson;

    
    @JsonProperty(value = "function_name")
    private String functionName;

    
    @JsonProperty(value = "created_by")
    private int createdBy;


    public int getWorkflowId() {
        return workflowId;
    }

    public void setWorkflowId(int workflowId) {
        this.workflowId = workflowId;
    }

    public String getWorkflowType() {
        return workflowType;
    }

    public void setWorkflowType(String workflowType) {
        this.workflowType = workflowType;
    }

    public int getSequenceNum() {
        return sequenceNum;
    }

    public void setSequenceNum(int sequenceNum) {
        this.sequenceNum = sequenceNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getSourceId() {
        return sourceId;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getOpenDate() {
        return openDate;
    }

    public void setOpenDate(Date openDate) {
        this.openDate = openDate;
    }

    public int getPathId() {
        return pathId;
    }

    public void setPathId(int pathId) {
        this.pathId = pathId;
    }

    public static Map<Object,Object> reflect(FndWorkflowPath fndWorkflowPath) throws Exception{
        Class cls = fndWorkflowPath.getClass();
        Field[] fields = cls.getDeclaredFields();
        Map<Object,Object> ref = new HashMap<Object,Object>();
        for(int i=0; i<fields.length; i++){
            Field f = fields[i];
            f.setAccessible(true);
            ref.put(f.getName(), f.get(fndWorkflowPath));
        }
        return ref;
    }


    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public JSONArray getPositionUsers() {
        return positionUsers;
    }

    public void setPositionUsers(JSONArray positionUsers) {
        this.positionUsers = positionUsers;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public int getPositionId() {
        return positionId;
    }

    public void setPositionId(int positionId) {
        this.positionId = positionId;
    }

    public String getUserPositionName() {
        return userPositionName;
    }

    public void setUserPositionName(String userPositionName) {
        this.userPositionName = userPositionName;
    }

    public int getUserPositionId() {
        return userPositionId;
    }

    public void setUserPositionId(int userPositionId) {
        this.userPositionId = userPositionId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getValueJson() {
        return valueJson;
    }

    public void setValueJson(String valueJson) {
        this.valueJson = valueJson;
    }

    public String getFunctionName() {
        return functionName;
    }

    public void setFunctionName(String functionName) {
        this.functionName = functionName;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }
}
