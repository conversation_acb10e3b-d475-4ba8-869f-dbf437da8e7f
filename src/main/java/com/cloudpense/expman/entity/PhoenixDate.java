package com.cloudpense.expman.entity;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;

public class PhoenixDate {
    private LocalDate date;
    private String time;

    private final String morning = "08:00:00";
    private final String noon = "12:00:00";
    private final String night = "17:00:00";

    private DateTimeFormatter fomatter1 = DateTimeFormatter
            .ofPattern("yyyy-MM-dd");

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        if (null == time) {
            time = morning;
        }
        this.time = time;
    }

    public String getFormattedDate() {
        return this.date.format(fomatter1) + " " + this.time;
    }

    public PhoenixDate plusDays(Double day) {
        if (day == 0) {
            return this;
        }
        double baseDay = 0;
        if (this.time.equals(noon)) {
            baseDay = 0.5;
        }
        if (this.time.equals(night)) {
            baseDay = 1.0;
        }
        day += baseDay;
        double wholeDay = Math.floor(day);
        double halfDay = day - Math.floor(day);
        if (halfDay == 0) {
            halfDay = 1.0;
            wholeDay -= 1;
        }
        PhoenixDate phoenixDate = new PhoenixDate();
        phoenixDate.setDate(this.date.plusDays((long) wholeDay));
        if (halfDay == 1) {
            phoenixDate.setTime(night);
        } else if (halfDay == 0.5) {
            phoenixDate.setTime(noon);
        } else {
            phoenixDate.setTime(morning);
        }
        return phoenixDate;
    }

    private double getDayDb(String s) {
        if (s.equals(morning)) {
            return 0.0;
        } else if (s.equals(noon)) {
            return 0.5;
        } else if (s.equals(night)) {
            return 1;
        } else {
            return 0.0;
        }
    }

    public double minus(PhoenixDate phoenixDate) {
        LocalDate anotherDate = phoenixDate.getDate();
        String anotherTime = phoenixDate.getTime();
        long daysDiff = ChronoUnit.DAYS.between(anotherDate, this.date);
        double db1 = getDayDb(this.time);
        double db2 = getDayDb(anotherTime);
        return daysDiff + db1 - db2;
    }

    public static void main(String[] arg) throws Exception {
        PhoenixDate phoenixDate = new PhoenixDate();
        phoenixDate.setDate(LocalDate.of(2018, 12, 25));
        phoenixDate.setTime("08:00:00");
        PhoenixDate out = phoenixDate.plusDays(-6.5);
        System.out.println(out.getDate());
        System.out.println(out.getTime());
        System.out.println(out.minus(phoenixDate));
    }
}
