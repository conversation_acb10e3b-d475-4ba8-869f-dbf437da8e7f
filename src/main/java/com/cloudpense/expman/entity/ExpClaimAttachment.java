package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by Spooky on 2015/1/23.
 */
//@JsonSerialize(include = JsonSerialize.Inclusion.NON_DEFAULT)
public class ExpClaimAttachment implements Serializable {

    
    @JsonProperty("attachment_id")
    private int attachmentId;

    @JsonIgnore
    private ExpClaimHeader expClaimHeader;

    @JsonIgnore
    private ExpClaimLine expClaimLine;

    
    @JsonProperty(value = "attachment_name")
    private String attachmentURL;

    
    @JsonProperty(value = "attachment_url")
    private String attachmentUrl;

    
    @JsonProperty(value = "file_name")
    private String fileName;

    
    private String description;

    
    @JsonProperty(value = "created_by")
    private int createdBy;

    
    @JsonProperty(value = "full_name")
    private String fullName;

    
    @JsonProperty(value = "last_updated_by")
    private int lastUpdatedBy;

    
    @JsonProperty(value = "last_update_date")
    private Date lastUpdateDate;

    
    @JsonProperty(value = "creation_date")
    private Date creationDate;

    @JsonIgnore
    private String returnCode;

    @JsonIgnore
    private String returnMessage;

    public ExpClaimLine getExpClaimLine() {
        return expClaimLine;
    }

    public void setExpClaimLine(ExpClaimLine expClaimLine) {
        this.expClaimLine = expClaimLine;
    }

    public String getAttachmentURL() {
        return attachmentURL;
    }

    public void setAttachmentURL(String attachmentURL) {
        this.attachmentURL = attachmentURL;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

    public ExpClaimHeader getExpClaimHeader() {
        return expClaimHeader;
    }

    public void setExpClaimHeader(ExpClaimHeader expClaimHeader) {
        this.expClaimHeader = expClaimHeader;
    }

    public int getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(int attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public int getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(int lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }
}

