package com.cloudpense.expman.entity;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Arrays;
import java.util.Collection;

/**
 * Created by Spooky on 2015/1/21.
 */
public class CustomUserDetails implements UserDetails {

    private static final String ROLE_CLIENT = "ROLE_CLIENT";
    private static final GrantedAuthority DEFAULT_CLIENT_ROLE = new SimpleGrantedAuthority(ROLE_CLIENT);
    // todo: serial version
    private static final long serialVersionUID = 4237185205905481854L;

    private int userId;
    private int companyId;
    private String username;
    private String password;
    private String fullName;
    private int agentId;
    private boolean enabled;
    private boolean credentialExpired;
    private boolean accountLocked;
    private boolean accountExpired;

    /*
    private FndUser user;
    */

    public CustomUserDetails(FndUser user) {
        this.userId = user.getUserId();
        this.companyId = user.getFndCompany().getCompanyId();
        this.username = user.getUserName();
        this.password = user.getPassword();
        this.fullName = user.getFullName();
        this.agentId = user.getAgentId();
        this.enabled = true;
        this.credentialExpired = false;
        this.accountLocked = false;
        this.accountExpired = false;
    }

    /**
     * Returns the authorities granted to the user. Cannot return <code>null</code>.
     *
     * @return the authorities, sorted by natural key (never <code>null</code>)
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Arrays.asList(DEFAULT_CLIENT_ROLE);
    }

    /**
     * Returns the password used to authenticate the user.
     *
     * @return the password
     */
    @Override
    public String getPassword() {
        return password;
    }

    /**
     * Returns the username used to authenticate the user. Cannot return <code>null</code>.
     *
     * @return the username (never <code>null</code>)
     */
    @Override
    public String getUsername() {
        return username;
    }

    /**
     * Indicates whether the user's account has expired. An expired account cannot be authenticated.
     *
     * @return <code>true</code> if the user's account is valid (ie non-expired), <code>false</code> if no longer valid
     * (ie expired)
     */
    @Override
    public boolean isAccountNonExpired() {
        return !accountExpired;
    }

    /**
     * Indicates whether the user is locked or unlocked. A locked user cannot be authenticated.
     *
     * @return <code>true</code> if the user is not locked, <code>false</code> otherwise
     */
    @Override
    public boolean isAccountNonLocked() {
        return !accountLocked;
    }

    /**
     * Indicates whether the user's credentials (password) has expired. Expired credentials prevent
     * authentication.
     *
     * @return <code>true</code> if the user's credentials are valid (ie non-expired), <code>false</code> if no longer
     * valid (ie expired)
     */
    @Override
    public boolean isCredentialsNonExpired() {
        return !credentialExpired;
    }

    /**
     * Indicates whether the user is enabled or disabled. A disabled user cannot be authenticated.
     *
     * @return <code>true</code> if the user is enabled, <code>false</code> otherwise
     */
    @Override
    public boolean isEnabled() {
        return enabled;
    }

    public int getUserId() {
        return userId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public int getAgentId() {
        return agentId;
    }

    public String getFullName() {
        return fullName;
    }
}
