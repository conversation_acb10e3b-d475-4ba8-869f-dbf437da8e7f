
package com.cloudpense.expman.entity.hsf.claim.withholding;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;


/**
 * <p>billheadType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="billheadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pk_accrued_bill" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_tradetypeid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="billstatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="apprstatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="effectstatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="predict_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="reason" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="attach_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator_dept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="approver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="approvetime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="printer" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="printdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="creator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="creationtime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="modifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="modifiedtime" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="auditman" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="hasntbcheck" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="warningmsg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="accrued_detail" type="{}accrued_detailType"/>
 *         &lt;element name="accrued_verify" type="{}accrued_verifyType"/>
 *         &lt;element name="redflag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="imag_status" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="isneedimag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="isexpedited" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="red_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "billheadType", propOrder = {
        "pkAccruedBill",
        "pkGroup",
        "pkOrg",
        "pkBilltype",
        "pkTradetype",
        "pkTradetypeid",
        "billno",
        "billdate",
        "pkCurrtype",
        "billstatus",
        "apprstatus",
        "effectstatus",
        "orgCurrinfo",
        "groupCurrinfo",
        "globalCurrinfo",
        "amount",
        "orgAmount",
        "groupAmount",
        "globalAmount",
        "verifyAmount",
        "orgVerifyAmount",
        "groupVerifyAmount",
        "globalVerifyAmount",
        "predictRestAmount",
        "restAmount",
        "orgRestAmount",
        "groupRestAmount",
        "globalRestAmount",
        "reason",
        "attachAmount",
        "operatorOrg",
        "operatorDept",
        "operator",
        "approver",
        "approvetime",
        "printer",
        "printdate",
        "creator",
        "creationtime",
        "modifier",
        "modifiedtime",
        "auditman",
        "hasntbcheck",
        "warningmsg",
        "accruedDetail",
//        "accruedVerify",
        "redflag",
        "imagStatus",
        "isneedimag",
        "isexpedited",
        "redAmount"
})
public class BillheadType {

    @XmlElement(name = "pk_accrued_bill", required = true)
    protected String pkAccruedBill;
    @XmlElement(name = "pk_group", required = true)
    protected String pkGroup;
    @XmlElement(name = "pk_org", required = true)
    protected String pkOrg;
    @XmlElement(name = "pk_billtype", required = true)
    protected String pkBilltype;
    @XmlElement(name = "pk_tradetype", required = true)
    protected String pkTradetype;
    @XmlElement(name = "pk_tradetypeid", required = true)
    protected String pkTradetypeid;
    @XmlElement(required = true)
    protected String billno;
    @XmlElement(required = true)
    protected String billdate;
    @XmlElement(name = "pk_currtype", required = true)
    protected String pkCurrtype;
    @XmlElement(required = true)
    protected String billstatus;
    @XmlElement(required = true)
    protected String apprstatus;
    @XmlElement(required = true)
    protected String effectstatus;
    @XmlElement(name = "org_currinfo", required = true)
    protected String orgCurrinfo;
    @XmlElement(name = "group_currinfo", required = true)
    protected String groupCurrinfo;
    @XmlElement(name = "global_currinfo", required = true)
    protected String globalCurrinfo;
    @XmlElement(required = true)
    protected String amount;
    @XmlElement(name = "org_amount", required = true)
    protected String orgAmount;
    @XmlElement(name = "group_amount", required = true)
    protected String groupAmount;
    @XmlElement(name = "global_amount", required = true)
    protected String globalAmount;
    @XmlElement(name = "verify_amount", required = true)
    protected String verifyAmount;
    @XmlElement(name = "org_verify_amount", required = true)
    protected String orgVerifyAmount;
    @XmlElement(name = "group_verify_amount", required = true)
    protected String groupVerifyAmount;
    @XmlElement(name = "global_verify_amount", required = true)
    protected String globalVerifyAmount;
    @XmlElement(name = "predict_rest_amount", required = true)
    protected String predictRestAmount;
    @XmlElement(name = "rest_amount", required = true)
    protected String restAmount;
    @XmlElement(name = "org_rest_amount", required = true)
    protected String orgRestAmount;
    @XmlElement(name = "group_rest_amount", required = true)
    protected String groupRestAmount;
    @XmlElement(name = "global_rest_amount", required = true)
    protected String globalRestAmount;
    @XmlElement(required = true)
    protected String reason;
    @XmlElement(name = "attach_amount", required = true)
    protected String attachAmount;
    @XmlElement(name = "operator_org", required = true)
    protected String operatorOrg;
    @XmlElement(name = "operator_dept", required = true)
    protected String operatorDept;
    @XmlElement(required = true)
    protected String operator;
    @XmlElement(required = true)
    protected String approver;
    @XmlElement(required = true)
    protected String approvetime;
    @XmlElement(required = true)
    protected String printer;
    @XmlElement(required = true)
    protected String printdate;
    @XmlElement(required = true)
    protected String creator;
    @XmlElement(required = true)
    protected String creationtime;
    @XmlElement(required = true)
    protected String modifier;
    @XmlElement(required = true)
    protected String modifiedtime;
    @XmlElement(required = true)
    protected String auditman;
    @XmlElement(required = true)
    protected String hasntbcheck;
    @XmlElement(required = true)
    protected String warningmsg;
    @XmlElementWrapper(name = "accrued_detail", required = true)
    @XmlElement(name = "item")
    protected ArrayList<ItemType> accruedDetail;

    @XmlElement(required = true)
    protected String redflag;
    @XmlElement(name = "imag_status", required = true)
    protected String imagStatus;
    @XmlElement(required = true)
    protected String isneedimag;
    @XmlElement(required = true)
    protected String isexpedited;
    @XmlElement(name = "red_amount", required = true)
    protected String redAmount;

    /**
     * 获取pkAccruedBill属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkAccruedBill() {
        return pkAccruedBill;
    }

    /**
     * 设置pkAccruedBill属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkAccruedBill(String value) {
        this.pkAccruedBill = value;
    }

    /**
     * 获取pkGroup属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkGroup() {
        return pkGroup;
    }

    /**
     * 设置pkGroup属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkGroup(String value) {
        this.pkGroup = value;
    }

    /**
     * 获取pkOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrg() {
        return pkOrg;
    }

    /**
     * 设置pkOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrg(String value) {
        this.pkOrg = value;
    }

    /**
     * 获取pkBilltype属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkBilltype() {
        return pkBilltype;
    }

    /**
     * 设置pkBilltype属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkBilltype(String value) {
        this.pkBilltype = value;
    }

    /**
     * 获取pkTradetype属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkTradetype() {
        return pkTradetype;
    }

    /**
     * 设置pkTradetype属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkTradetype(String value) {
        this.pkTradetype = value;
    }

    /**
     * 获取pkTradetypeid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkTradetypeid() {
        return pkTradetypeid;
    }

    /**
     * 设置pkTradetypeid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkTradetypeid(String value) {
        this.pkTradetypeid = value;
    }

    /**
     * 获取billno属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBillno() {
        return billno;
    }

    /**
     * 设置billno属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBillno(String value) {
        this.billno = value;
    }

    /**
     * 获取billdate属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBilldate() {
        return billdate;
    }

    /**
     * 设置billdate属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBilldate(String value) {
        this.billdate = value;
    }

    /**
     * 获取pkCurrtype属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkCurrtype() {
        return pkCurrtype;
    }

    /**
     * 设置pkCurrtype属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkCurrtype(String value) {
        this.pkCurrtype = value;
    }

    /**
     * 获取billstatus属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBillstatus() {
        return billstatus;
    }

    /**
     * 设置billstatus属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBillstatus(String value) {
        this.billstatus = value;
    }

    /**
     * 获取apprstatus属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getApprstatus() {
        return apprstatus;
    }

    /**
     * 设置apprstatus属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setApprstatus(String value) {
        this.apprstatus = value;
    }

    /**
     * 获取effectstatus属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getEffectstatus() {
        return effectstatus;
    }

    /**
     * 设置effectstatus属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setEffectstatus(String value) {
        this.effectstatus = value;
    }

    /**
     * 获取orgCurrinfo属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrgCurrinfo() {
        return orgCurrinfo;
    }

    /**
     * 设置orgCurrinfo属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrgCurrinfo(String value) {
        this.orgCurrinfo = value;
    }

    /**
     * 获取groupCurrinfo属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGroupCurrinfo() {
        return groupCurrinfo;
    }

    /**
     * 设置groupCurrinfo属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGroupCurrinfo(String value) {
        this.groupCurrinfo = value;
    }

    /**
     * 获取globalCurrinfo属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGlobalCurrinfo() {
        return globalCurrinfo;
    }

    /**
     * 设置globalCurrinfo属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGlobalCurrinfo(String value) {
        this.globalCurrinfo = value;
    }

    /**
     * 获取amount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getAmount() {
        return amount;
    }

    /**
     * 设置amount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAmount(String value) {
        this.amount = value;
    }

    /**
     * 获取orgAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrgAmount() {
        return orgAmount;
    }

    /**
     * 设置orgAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrgAmount(String value) {
        this.orgAmount = value;
    }

    /**
     * 获取groupAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGroupAmount() {
        return groupAmount;
    }

    /**
     * 设置groupAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGroupAmount(String value) {
        this.groupAmount = value;
    }

    /**
     * 获取globalAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGlobalAmount() {
        return globalAmount;
    }

    /**
     * 设置globalAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGlobalAmount(String value) {
        this.globalAmount = value;
    }

    /**
     * 获取verifyAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getVerifyAmount() {
        return verifyAmount;
    }

    /**
     * 设置verifyAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setVerifyAmount(String value) {
        this.verifyAmount = value;
    }

    /**
     * 获取orgVerifyAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrgVerifyAmount() {
        return orgVerifyAmount;
    }

    /**
     * 设置orgVerifyAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrgVerifyAmount(String value) {
        this.orgVerifyAmount = value;
    }

    /**
     * 获取groupVerifyAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGroupVerifyAmount() {
        return groupVerifyAmount;
    }

    /**
     * 设置groupVerifyAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGroupVerifyAmount(String value) {
        this.groupVerifyAmount = value;
    }

    /**
     * 获取globalVerifyAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGlobalVerifyAmount() {
        return globalVerifyAmount;
    }

    /**
     * 设置globalVerifyAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGlobalVerifyAmount(String value) {
        this.globalVerifyAmount = value;
    }

    /**
     * 获取predictRestAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPredictRestAmount() {
        return predictRestAmount;
    }

    /**
     * 设置predictRestAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPredictRestAmount(String value) {
        this.predictRestAmount = value;
    }

    /**
     * 获取restAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRestAmount() {
        return restAmount;
    }

    /**
     * 设置restAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRestAmount(String value) {
        this.restAmount = value;
    }

    /**
     * 获取orgRestAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrgRestAmount() {
        return orgRestAmount;
    }

    /**
     * 设置orgRestAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrgRestAmount(String value) {
        this.orgRestAmount = value;
    }

    /**
     * 获取groupRestAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGroupRestAmount() {
        return groupRestAmount;
    }

    /**
     * 设置groupRestAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGroupRestAmount(String value) {
        this.groupRestAmount = value;
    }

    /**
     * 获取globalRestAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGlobalRestAmount() {
        return globalRestAmount;
    }

    /**
     * 设置globalRestAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGlobalRestAmount(String value) {
        this.globalRestAmount = value;
    }

    /**
     * 获取reason属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getReason() {
        return reason;
    }

    /**
     * 设置reason属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReason(String value) {
        this.reason = value;
    }

    /**
     * 获取attachAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getAttachAmount() {
        return attachAmount;
    }

    /**
     * 设置attachAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAttachAmount(String value) {
        this.attachAmount = value;
    }

    /**
     * 获取operatorOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperatorOrg() {
        return operatorOrg;
    }

    /**
     * 设置operatorOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperatorOrg(String value) {
        this.operatorOrg = value;
    }

    /**
     * 获取operatorDept属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperatorDept() {
        return operatorDept;
    }

    /**
     * 设置operatorDept属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperatorDept(String value) {
        this.operatorDept = value;
    }

    /**
     * 获取operator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperator(String value) {
        this.operator = value;
    }

    /**
     * 获取approver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getApprover() {
        return approver;
    }

    /**
     * 设置approver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setApprover(String value) {
        this.approver = value;
    }

    /**
     * 获取approvetime属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getApprovetime() {
        return approvetime;
    }

    /**
     * 设置approvetime属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setApprovetime(String value) {
        this.approvetime = value;
    }

    /**
     * 获取printer属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPrinter() {
        return printer;
    }

    /**
     * 设置printer属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPrinter(String value) {
        this.printer = value;
    }

    /**
     * 获取printdate属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPrintdate() {
        return printdate;
    }

    /**
     * 设置printdate属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPrintdate(String value) {
        this.printdate = value;
    }

    /**
     * 获取creator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCreator() {
        return creator;
    }

    /**
     * 设置creator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreator(String value) {
        this.creator = value;
    }

    /**
     * 获取creationtime属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCreationtime() {
        return creationtime;
    }

    /**
     * 设置creationtime属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreationtime(String value) {
        this.creationtime = value;
    }

    /**
     * 获取modifier属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * 设置modifier属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setModifier(String value) {
        this.modifier = value;
    }

    /**
     * 获取modifiedtime属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getModifiedtime() {
        return modifiedtime;
    }

    /**
     * 设置modifiedtime属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setModifiedtime(String value) {
        this.modifiedtime = value;
    }

    /**
     * 获取auditman属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getAuditman() {
        return auditman;
    }

    /**
     * 设置auditman属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAuditman(String value) {
        this.auditman = value;
    }

    /**
     * 获取hasntbcheck属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getHasntbcheck() {
        return hasntbcheck;
    }

    /**
     * 设置hasntbcheck属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHasntbcheck(String value) {
        this.hasntbcheck = value;
    }

    /**
     * 获取warningmsg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getWarningmsg() {
        return warningmsg;
    }

    /**
     * 设置warningmsg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setWarningmsg(String value) {
        this.warningmsg = value;
    }

    /**
     * 获取accruedDetail属性的值。
     *
     * @return possible object is
     * {@link AccruedDetailType }
     */
    public ArrayList<ItemType> getAccruedDetail() {
        return accruedDetail;
    }

    /**
     * 设置accruedDetail属性的值。
     *
     * @param value allowed object is
     *              {@link AccruedDetailType }
     */
    public void setAccruedDetail(ArrayList<ItemType> value) {
        this.accruedDetail = value;
    }

//    /**
//     * 获取accruedVerify属性的值。
//     *
//     * @return possible object is
//     * {@link AccruedVerifyType }
//     */
//    public AccruedVerifyType getAccruedVerify() {
//        return accruedVerify;
//    }
//
//    /**
//     * 设置accruedVerify属性的值。
//     *
//     * @param value allowed object is
//     *              {@link AccruedVerifyType }
//     */
//    public void setAccruedVerify(AccruedVerifyType value) {
//        this.accruedVerify = value;
//    }

    /**
     * 获取redflag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRedflag() {
        return redflag;
    }

    /**
     * 设置redflag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRedflag(String value) {
        this.redflag = value;
    }

    /**
     * 获取imagStatus属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getImagStatus() {
        return imagStatus;
    }

    /**
     * 设置imagStatus属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setImagStatus(String value) {
        this.imagStatus = value;
    }

    /**
     * 获取isneedimag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getIsneedimag() {
        return isneedimag;
    }

    /**
     * 设置isneedimag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIsneedimag(String value) {
        this.isneedimag = value;
    }

    /**
     * 获取isexpedited属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getIsexpedited() {
        return isexpedited;
    }

    /**
     * 设置isexpedited属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIsexpedited(String value) {
        this.isexpedited = value;
    }

    /**
     * 获取redAmount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRedAmount() {
        return redAmount;
    }

    /**
     * 设置redAmount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRedAmount(String value) {
        this.redAmount = value;
    }

}
