
package com.cloudpense.expman.entity.hsf.claim.reimburse;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>itemType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="itemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="pk_reimtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkbxr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="receiver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="skyhzh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="tablecode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_busitem">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value=""/>
 *               &lt;enumeration value="1001ZZ10000000003SLM"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="pk_jkbx" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rowno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ybje">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="400.00000000"/>
 *               &lt;enumeration value="1000.00000000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="bbje">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="400.00000000"/>
 *               &lt;enumeration value="1000.00000000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="hkybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="hkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="zfybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="zfbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cjkybje">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value="1000.00000000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="cjkbbje">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value="1000.00000000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="globalbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="globalbbye" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="globalhkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="globalzfbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="globalcjkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="groupbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="groupbbye" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="grouphkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="groupzfbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="groupcjkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="99100000"/>
 *               &lt;enumeration value="99101000"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="pk_bxcontrast" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_bxd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_jkd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbhl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fyybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fybbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cxnd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cxqj" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sxrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djlxbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cxrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_pc" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bxdjbh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkdjbh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sxbz" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="groupfybbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="globalfybbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_accrued_verify" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_accrued_bill" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_accrued_detail" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_verify_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_man" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_date" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="effectstatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="effectdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="accrued_billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bxd_billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_iobsclass" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "itemType", propOrder = {
        "pkReimtypeOrDwbmOrDeptid"
})
public class ItemType {

    @XmlElementRefs({
            @XmlElementRef(name = "effectstatus", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "org_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_jkbx", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalcjkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "deptid", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "hkybje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "cjkybje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "grouphkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "rowno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "djlxbm", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_iobsclass", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "groupbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_accrued_verify", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "skyhzh", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalfybbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_group", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "group_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "ybje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "groupcjkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "global_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_busitem", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "groupzfbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_date", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "cxrq", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalbbye", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "dwbm", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "bxdjbh", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "sxrq", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "jkbxr", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "bbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_pc", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "receiver", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "cxqj", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_bxcontrast", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "bxd_billno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "cxnd", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "tablecode", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalhkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_accrued_detail", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "fyybje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_bxd", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "jkdjbh", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_jkd", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "zfbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "sxbz", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "effectdate", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_reimtype", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "bbhl", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "fybbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_accrued_bill", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "hkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "groupbbye", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_man", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "zfybje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "accrued_billno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "globalzfbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "cjkbbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_org", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "groupfybbje", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem12", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem13", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem14", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem15", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem16", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem17", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem18", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem19", type = JAXBElement.class, required = false)
    })
    protected List<JAXBElement<String>> pkReimtypeOrDwbmOrDeptid;

    /**
     * Gets the value of the pkReimtypeOrDwbmOrDeptid property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the pkReimtypeOrDwbmOrDeptid property.
     *
     * <p>
     * For example, to add a new jkBusitem, do as follows:
     * <pre>
     *    getPkReimtypeOrDwbmOrDeptid().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public List<JAXBElement<String>> getPkReimtypeOrDwbmOrDeptid() {
        if (pkReimtypeOrDwbmOrDeptid == null) {
            pkReimtypeOrDwbmOrDeptid = new ArrayList<JAXBElement<String>>();
        }
        return this.pkReimtypeOrDwbmOrDeptid;
    }

}
