
package com.cloudpense.expman.entity.hsf.claim.withholding;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlElementRefs;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>itemType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="itemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice maxOccurs="unbounded" minOccurs="0">
 *         &lt;element name="pk_accrued_detail" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rowno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="assume_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="assume_dept" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_iobsclass">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="001"/>
 *               &lt;enumeration value=""/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="pk_pcorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_resacostcenter" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_checkele" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_project" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_wbs" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_supplier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_customer" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_proline" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_brand" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_currinfo" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_amount">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value=""/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="org_verify_amount">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value=""/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="group_verify_amount">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value=""/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="global_verify_amount">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="0.00000000"/>
 *               &lt;enumeration value=""/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="predict_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="org_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="group_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="global_rest_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="src_accruedpk" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="srctype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="src_detailpk" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="red_amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_accrued_verify" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_bxd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_man" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="verify_date" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="effectstatus" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="effectdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="accrued_billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bxd_billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "itemType", propOrder = {
        "pkAccruedDetailOrRownoOrAssumeOrg"
})
public class ItemType {

    @XmlElementRefs({
            @XmlElementRef(name = "pk_checkele", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "group_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "global_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "assume_org", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "org_currinfo", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_project", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "group_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "group_currinfo", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "effectstatus", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "accrued_billno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_accrued_detail", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "group_rest_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "global_currinfo", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "red_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_accrued_verify", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_proline", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_pcorg", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_org", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "org_verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_iobsclass", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "org_rest_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "effectdate", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "org_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "global_rest_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_bxd", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "srctype", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "bxd_billno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_resacostcenter", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "rest_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_customer", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "assume_dept", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_date", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_supplier", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "global_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_man", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_wbs", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "src_accruedpk", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "verify_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "rowno", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_brand", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "src_detailpk", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "pk_group", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "predict_rest_amount", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem12", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem13", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem14", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem15", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem16", type = JAXBElement.class, required = false),
            @XmlElementRef(name = "defitem17", type = JAXBElement.class, required = false)
    })
    protected List<JAXBElement<String>> pkAccruedDetailOrRownoOrAssumeOrg;

    /**
     * Gets the value of the pkAccruedDetailOrRownoOrAssumeOrg property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the pkAccruedDetailOrRownoOrAssumeOrg property.
     *
     * <p>
     * For example, to add a new jkBusitem, do as follows:
     * <pre>
     *    getPkAccruedDetailOrRownoOrAssumeOrg().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     * {@link JAXBElement }{@code <}{@link String }{@code >}
     */
    public List<JAXBElement<String>> getPkAccruedDetailOrRownoOrAssumeOrg() {
        if (pkAccruedDetailOrRownoOrAssumeOrg == null) {
            pkAccruedDetailOrRownoOrAssumeOrg = new ArrayList<JAXBElement<String>>();
        }
        return this.pkAccruedDetailOrRownoOrAssumeOrg;
    }

}
