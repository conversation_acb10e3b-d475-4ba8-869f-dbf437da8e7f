
package com.cloudpense.expman.entity.hsf.claim.repayment;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>itemType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="itemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkbxr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_jkd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_busitem">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="1001ZZ10000000003LMS1"/>
 *               &lt;enumeration value="1001ZZ10000000003LMT1"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *         &lt;element name="cjkybje">
 *           &lt;simpleType>
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *               &lt;enumeration value="1"/>
 *               &lt;enumeration value="2"/>
 *             &lt;/restriction>
 *           &lt;/simpleType>
 *         &lt;/element>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "itemType", propOrder = {
        "pkOrg",
        "jkbxr",
        "pkJkd",
        "pkBusitem",
        "cjkybje",
        "defitem12",
        "defitem13",
        "defitem14",
        "defitem15",
        "defitem16",
        "defitem17"
})
public class ItemType {

    @XmlElement(name = "pk_org", required = true)
    protected String pkOrg;
    @XmlElement(required = true)
    protected String jkbxr;
    @XmlElement(name = "pk_jkd", required = true)
    protected String pkJkd;
    @XmlElement(name = "pk_busitem", required = true)
    protected String pkBusitem;
    @XmlElement(required = true)
    protected String cjkybje;
    @XmlElement(required = true)
    protected String defitem12;
    @XmlElement(required = true)
    protected String defitem13;
    @XmlElement(required = true)
    protected String defitem14;
    @XmlElement(required = true)
    protected String defitem15;
    @XmlElement(required = true)
    protected String defitem16;
    @XmlElement(required = true)
    protected String defitem17;

    public String getDefitem12() {
        return defitem12;
    }

    public void setDefitem12(String defitem12) {
        this.defitem12 = defitem12;
    }

    public String getDefitem13() {
        return defitem13;
    }

    public void setDefitem13(String defitem13) {
        this.defitem13 = defitem13;
    }

    public String getDefitem14() {
        return defitem14;
    }

    public void setDefitem14(String defitem14) {
        this.defitem14 = defitem14;
    }

    public String getDefitem15() {
        return defitem15;
    }

    public void setDefitem15(String defitem15) {
        this.defitem15 = defitem15;
    }

    public String getDefitem16() {
        return defitem16;
    }

    public void setDefitem16(String defitem16) {
        this.defitem16 = defitem16;
    }

    public String getDefitem17() {
        return defitem17;
    }

    public void setDefitem17(String defitem17) {
        this.defitem17 = defitem17;
    }

    /**
     * 获取pkOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrg() {
        return pkOrg;
    }

    /**
     * 设置pkOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrg(String value) {
        this.pkOrg = value;
    }

    /**
     * 获取jkbxr属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJkbxr() {
        return jkbxr;
    }

    /**
     * 设置jkbxr属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJkbxr(String value) {
        this.jkbxr = value;
    }

    /**
     * 获取pkJkd属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkJkd() {
        return pkJkd;
    }

    /**
     * 设置pkJkd属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkJkd(String value) {
        this.pkJkd = value;
    }

    /**
     * 获取pkBusitem属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkBusitem() {
        return pkBusitem;
    }

    /**
     * 设置pkBusitem属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkBusitem(String value) {
        this.pkBusitem = value;
    }

    /**
     * 获取cjkybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCjkybje() {
        return cjkybje;
    }

    /**
     * 设置cjkybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCjkybje(String value) {
        this.cjkybje = value;
    }

}
