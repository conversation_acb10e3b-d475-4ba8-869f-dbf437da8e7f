
package com.cloudpense.expman.entity.hsf.claim.loan;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>itemType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="itemType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "itemType", propOrder = {
        "defitem20",
        "pk_jkh",
        "amount",
        "yjye",
        "defitem12",
        "defitem13",
        "defitem14",
        "defitem15",
        "defitem16",
        "defitem17"
})
public class ItemType {

    @XmlElement(required = true)
    protected String amount;
    @XmlElement(required = true)
    protected String yjye;
    @XmlElement(required = true)
    protected String pk_jkh;
    @XmlElement(required = true)
    protected String defitem20;
    @XmlElement(required = true)
    protected String defitem12;
    @XmlElement(required = true)
    protected String defitem13;
    @XmlElement(required = true)
    protected String defitem14;
    @XmlElement(required = true)
    protected String defitem15;
    @XmlElement(required = true)
    protected String defitem16;
    @XmlElement(required = true)
    protected String defitem17;

    public String getYjye() {
        return yjye;
    }

    public void setYjye(String yjye) {
        this.yjye = yjye;
    }

    public String getDefitem12() {
        return defitem12;
    }

    public void setDefitem12(String defitem12) {
        this.defitem12 = defitem12;
    }

    public String getDefitem13() {
        return defitem13;
    }

    public void setDefitem13(String defitem13) {
        this.defitem13 = defitem13;
    }

    public String getDefitem14() {
        return defitem14;
    }

    public void setDefitem14(String defitem14) {
        this.defitem14 = defitem14;
    }

    public String getDefitem15() {
        return defitem15;
    }

    public void setDefitem15(String defitem15) {
        this.defitem15 = defitem15;
    }

    public String getDefitem16() {
        return defitem16;
    }

    public void setDefitem16(String defitem16) {
        this.defitem16 = defitem16;
    }

    public String getDefitem17() {
        return defitem17;
    }

    public void setDefitem17(String defitem17) {
        this.defitem17 = defitem17;
    }

    public String getDefitem20() {
        return defitem20;
    }

    public void setDefitem20(String defitem20) {
        this.defitem20 = defitem20;
    }

    public String getPk_jkh() {
        return pk_jkh;
    }

    public void setPk_jkh(String pk_jkh) {
        this.pk_jkh = pk_jkh;
    }

    /**
     * 获取amount属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getAmount() {
        return amount;
    }

    /**
     * 设置amount属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAmount(String value) {
        this.amount = value;
    }

}
