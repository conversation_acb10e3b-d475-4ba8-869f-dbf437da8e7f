
package com.cloudpense.expman.entity.hsf.claim.reimburse;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.cloudpense.expman.entity.hsf.claim.reimburse package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Ufinterface_QNAME = new QName("", "ufinterface");
    private final static QName _ItemTypePkGroup_QNAME = new QName("", "pk_group");
    private final static QName _ItemTypeGlobalcjkbbje_QNAME = new QName("", "globalcjkbbje");
    private final static QName _ItemTypeSkyhzh_QNAME = new QName("", "skyhzh");
    private final static QName _ItemTypePkOrg_QNAME = new QName("", "pk_org");
    private final static QName _ItemTypeVerifyAmount_QNAME = new QName("", "verify_amount");
    private final static QName _ItemTypeHkybje_QNAME = new QName("", "hkybje");
    private final static QName _ItemTypeGlobalhkbbje_QNAME = new QName("", "globalhkbbje");
    private final static QName _ItemTypeCjkbbje_QNAME = new QName("", "cjkbbje");
    private final static QName _ItemTypeReceiver_QNAME = new QName("", "receiver");
    private final static QName _ItemTypePkBxd_QNAME = new QName("", "pk_bxd");
    private final static QName _ItemTypePkJkbx_QNAME = new QName("", "pk_jkbx");
    private final static QName _ItemTypeBxdjbh_QNAME = new QName("", "bxdjbh");
    private final static QName _ItemTypeFybbje_QNAME = new QName("", "fybbje");
    private final static QName _ItemTypeGrouphkbbje_QNAME = new QName("", "grouphkbbje");
    private final static QName _ItemTypePkReimtype_QNAME = new QName("", "pk_reimtype");
    private final static QName _ItemTypeDwbm_QNAME = new QName("", "dwbm");
    private final static QName _ItemTypeZfybje_QNAME = new QName("", "zfybje");
    private final static QName _ItemTypeFyybje_QNAME = new QName("", "fyybje");
    private final static QName _ItemTypeGlobalbbye_QNAME = new QName("", "globalbbye");
    private final static QName _ItemTypeTablecode_QNAME = new QName("", "tablecode");
    private final static QName _ItemTypeYbje_QNAME = new QName("", "ybje");
    private final static QName _ItemTypeOrgVerifyAmount_QNAME = new QName("", "org_verify_amount");
    private final static QName _ItemTypeCxnd_QNAME = new QName("", "cxnd");
    private final static QName _ItemTypeDeptid_QNAME = new QName("", "deptid");
    private final static QName _ItemTypeGlobalbbje_QNAME = new QName("", "globalbbje");
    private final static QName _ItemTypePkBxcontrast_QNAME = new QName("", "pk_bxcontrast");
    private final static QName _ItemTypeSxbz_QNAME = new QName("", "sxbz");
    private final static QName _ItemTypeAccruedBillno_QNAME = new QName("", "accrued_billno");
    private final static QName _ItemTypePkJkd_QNAME = new QName("", "pk_jkd");
    private final static QName _ItemTypeSxrq_QNAME = new QName("", "sxrq");
    private final static QName _ItemTypePkAccruedVerify_QNAME = new QName("", "pk_accrued_verify");
    private final static QName _ItemTypePkBusitem_QNAME = new QName("", "pk_busitem");
    private final static QName _ItemTypePkIobsclass_QNAME = new QName("", "pk_iobsclass");
    private final static QName _ItemTypeBbje_QNAME = new QName("", "bbje");
    private final static QName _ItemTypeBxdBillno_QNAME = new QName("", "bxd_billno");
    private final static QName _ItemTypePkAccruedBill_QNAME = new QName("", "pk_accrued_bill");
    private final static QName _ItemTypeCxrq_QNAME = new QName("", "cxrq");
    private final static QName _ItemTypeGroupbbje_QNAME = new QName("", "groupbbje");
    private final static QName _ItemTypeZfbbje_QNAME = new QName("", "zfbbje");
    private final static QName _ItemTypeCxqj_QNAME = new QName("", "cxqj");
    private final static QName _ItemTypeGroupfybbje_QNAME = new QName("", "groupfybbje");
    private final static QName _ItemTypeRowno_QNAME = new QName("", "rowno");
    private final static QName _ItemTypeAmount_QNAME = new QName("", "amount");
    private final static QName _ItemTypeGroupzfbbje_QNAME = new QName("", "groupzfbbje");
    private final static QName _ItemTypePkAccruedDetail_QNAME = new QName("", "pk_accrued_detail");
    private final static QName _ItemTypeGlobalfybbje_QNAME = new QName("", "globalfybbje");
    private final static QName _ItemTypeVerifyMan_QNAME = new QName("", "verify_man");
    private final static QName _ItemTypeGlobalzfbbje_QNAME = new QName("", "globalzfbbje");
    private final static QName _ItemTypeGroupbbye_QNAME = new QName("", "groupbbye");
    private final static QName _ItemTypeBbhl_QNAME = new QName("", "bbhl");
    private final static QName _ItemTypePkPc_QNAME = new QName("", "pk_pc");
    private final static QName _ItemTypeVerifyDate_QNAME = new QName("", "verify_date");
    private final static QName _ItemTypeCjkybje_QNAME = new QName("", "cjkybje");
    private final static QName _ItemTypeGroupVerifyAmount_QNAME = new QName("", "group_verify_amount");
    private final static QName _ItemTypeEffectstatus_QNAME = new QName("", "effectstatus");
    private final static QName _ItemTypeGroupcjkbbje_QNAME = new QName("", "groupcjkbbje");
    private final static QName _ItemTypeDjlxbm_QNAME = new QName("", "djlxbm");
    private final static QName _ItemTypeEffectdate_QNAME = new QName("", "effectdate");
    private final static QName _ItemTypeHkbbje_QNAME = new QName("", "hkbbje");
    private final static QName _ItemTypeJkdjbh_QNAME = new QName("", "jkdjbh");
    private final static QName _ItemTypeGlobalVerifyAmount_QNAME = new QName("", "global_verify_amount");
    private final static QName _ItemTypeJkbxr_QNAME = new QName("", "jkbxr");
    private final static QName _ItemTypeDefitem12_QNAME = new QName("", "defitem12");
    private final static QName _ItemTypeDefitem13_QNAME = new QName("", "defitem13");
    private final static QName _ItemTypeDefitem14_QNAME = new QName("", "defitem14");
    private final static QName _ItemTypeDefitem15_QNAME = new QName("", "defitem15");
    private final static QName _ItemTypeDefitem16_QNAME = new QName("", "defitem16");
    private final static QName _ItemTypeDefitem17_QNAME = new QName("", "defitem17");
    private final static QName _ItemTypeDefitem18_QNAME = new QName("", "defitem18");
    private final static QName _ItemTypeDefitem19_QNAME = new QName("", "defitem19");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cloudpense.expman.entity.hsf.claim.reimburse
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link UfinterfaceType }
     */
    public UfinterfaceType createUfinterfaceType() {
        return new UfinterfaceType();
    }

    /**
     * Create an instance of {@link ItemType }
     */
    public ItemType createItemType() {
        return new ItemType();
    }

    /**
     * Create an instance of {@link BillType }
     */
    public BillType createBillType() {
        return new BillType();
    }

    /**
     * Create an instance of {@link BillheadType }
     */
    public BillheadType createBillheadType() {
        return new BillheadType();
    }

    /**
     * Create an instance of {@link ErBusitemType }
     */
    public ErBusitemType createErBusitemType() {
        return new ErBusitemType();
    }

    /**
     * Create an instance of {@link ErBxcontrastType }
     */
    public ErBxcontrastType createErBxcontrastType() {
        return new ErBxcontrastType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UfinterfaceType }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "ufinterface")
    public JAXBElement<UfinterfaceType> createUfinterface(UfinterfaceType value) {
        return new JAXBElement<UfinterfaceType>(_Ufinterface_QNAME, UfinterfaceType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_group", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkGroup(String value) {
        return new JAXBElement<String>(_ItemTypePkGroup_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalcjkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalcjkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalcjkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "skyhzh", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSkyhzh(String value) {
        return new JAXBElement<String>(_ItemTypeSkyhzh_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_org", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkOrg(String value) {
        return new JAXBElement<String>(_ItemTypePkOrg_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "hkybje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeHkybje(String value) {
        return new JAXBElement<String>(_ItemTypeHkybje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalhkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalhkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalhkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "cjkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeCjkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeCjkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "receiver", scope = ItemType.class)
    public JAXBElement<String> createItemTypeReceiver(String value) {
        return new JAXBElement<String>(_ItemTypeReceiver_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_bxd", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkBxd(String value) {
        return new JAXBElement<String>(_ItemTypePkBxd_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_jkbx", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkJkbx(String value) {
        return new JAXBElement<String>(_ItemTypePkJkbx_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bxdjbh", scope = ItemType.class)
    public JAXBElement<String> createItemTypeBxdjbh(String value) {
        return new JAXBElement<String>(_ItemTypeBxdjbh_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "fybbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeFybbje(String value) {
        return new JAXBElement<String>(_ItemTypeFybbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "grouphkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGrouphkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGrouphkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_reimtype", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkReimtype(String value) {
        return new JAXBElement<String>(_ItemTypePkReimtype_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "dwbm", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDwbm(String value) {
        return new JAXBElement<String>(_ItemTypeDwbm_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "zfybje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeZfybje(String value) {
        return new JAXBElement<String>(_ItemTypeZfybje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "fyybje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeFyybje(String value) {
        return new JAXBElement<String>(_ItemTypeFyybje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalbbye", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalbbye(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalbbye_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "tablecode", scope = ItemType.class)
    public JAXBElement<String> createItemTypeTablecode(String value) {
        return new JAXBElement<String>(_ItemTypeTablecode_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "ybje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeYbje(String value) {
        return new JAXBElement<String>(_ItemTypeYbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "org_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeOrgVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeOrgVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "cxnd", scope = ItemType.class)
    public JAXBElement<String> createItemTypeCxnd(String value) {
        return new JAXBElement<String>(_ItemTypeCxnd_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "deptid", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDeptid(String value) {
        return new JAXBElement<String>(_ItemTypeDeptid_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_bxcontrast", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkBxcontrast(String value) {
        return new JAXBElement<String>(_ItemTypePkBxcontrast_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "sxbz", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSxbz(String value) {
        return new JAXBElement<String>(_ItemTypeSxbz_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "accrued_billno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAccruedBillno(String value) {
        return new JAXBElement<String>(_ItemTypeAccruedBillno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_jkd", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkJkd(String value) {
        return new JAXBElement<String>(_ItemTypePkJkd_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "sxrq", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSxrq(String value) {
        return new JAXBElement<String>(_ItemTypeSxrq_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_accrued_verify", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkAccruedVerify(String value) {
        return new JAXBElement<String>(_ItemTypePkAccruedVerify_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_busitem", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkBusitem(String value) {
        return new JAXBElement<String>(_ItemTypePkBusitem_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_iobsclass", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkIobsclass(String value) {
        return new JAXBElement<String>(_ItemTypePkIobsclass_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeBbje(String value) {
        return new JAXBElement<String>(_ItemTypeBbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bxd_billno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeBxdBillno(String value) {
        return new JAXBElement<String>(_ItemTypeBxdBillno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_accrued_bill", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkAccruedBill(String value) {
        return new JAXBElement<String>(_ItemTypePkAccruedBill_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "cxrq", scope = ItemType.class)
    public JAXBElement<String> createItemTypeCxrq(String value) {
        return new JAXBElement<String>(_ItemTypeCxrq_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGroupbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "zfbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeZfbbje(String value) {
        return new JAXBElement<String>(_ItemTypeZfbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "cxqj", scope = ItemType.class)
    public JAXBElement<String> createItemTypeCxqj(String value) {
        return new JAXBElement<String>(_ItemTypeCxqj_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupfybbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupfybbje(String value) {
        return new JAXBElement<String>(_ItemTypeGroupfybbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "rowno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeRowno(String value) {
        return new JAXBElement<String>(_ItemTypeRowno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAmount(String value) {
        return new JAXBElement<String>(_ItemTypeAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupzfbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupzfbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGroupzfbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_accrued_detail", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkAccruedDetail(String value) {
        return new JAXBElement<String>(_ItemTypePkAccruedDetail_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalfybbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalfybbje(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalfybbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_man", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyMan(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyMan_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "globalzfbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalzfbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalzfbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupbbye", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupbbye(String value) {
        return new JAXBElement<String>(_ItemTypeGroupbbye_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bbhl", scope = ItemType.class)
    public JAXBElement<String> createItemTypeBbhl(String value) {
        return new JAXBElement<String>(_ItemTypeBbhl_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_pc", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkPc(String value) {
        return new JAXBElement<String>(_ItemTypePkPc_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_date", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyDate(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyDate_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "cjkybje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeCjkybje(String value) {
        return new JAXBElement<String>(_ItemTypeCjkybje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "group_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGroupVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "effectstatus", scope = ItemType.class)
    public JAXBElement<String> createItemTypeEffectstatus(String value) {
        return new JAXBElement<String>(_ItemTypeEffectstatus_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupcjkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupcjkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeGroupcjkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "djlxbm", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDjlxbm(String value) {
        return new JAXBElement<String>(_ItemTypeDjlxbm_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "effectdate", scope = ItemType.class)
    public JAXBElement<String> createItemTypeEffectdate(String value) {
        return new JAXBElement<String>(_ItemTypeEffectdate_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "hkbbje", scope = ItemType.class)
    public JAXBElement<String> createItemTypeHkbbje(String value) {
        return new JAXBElement<String>(_ItemTypeHkbbje_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "jkdjbh", scope = ItemType.class)
    public JAXBElement<String> createItemTypeJkdjbh(String value) {
        return new JAXBElement<String>(_ItemTypeJkdjbh_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "global_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "jkbxr", scope = ItemType.class)
    public JAXBElement<String> createItemTypeJkbxr(String value) {
        return new JAXBElement<String>(_ItemTypeJkbxr_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem12", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem12(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem12_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem13", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem13(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem13_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem14", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem14(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem14_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem15", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem15(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem15_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem16", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem16(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem16_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem17", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem17(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem17_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem18", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem18(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem18_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem19", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem19(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem19_QNAME, String.class, ItemType.class, value);
    }
}
