
package com.cloudpense.expman.entity.hsf.claim.withholding;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.cloudpense.expman.entity.hsf.claim.withholding package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Ufinterface_QNAME = new QName("", "ufinterface");
    private final static QName _ItemTypeSrctype_QNAME = new QName("", "srctype");
    private final static QName _ItemTypePkGroup_QNAME = new QName("", "pk_group");
    private final static QName _ItemTypePkProline_QNAME = new QName("", "pk_proline");
    private final static QName _ItemTypeOrgVerifyAmount_QNAME = new QName("", "org_verify_amount");
    private final static QName _ItemTypeOrgRestAmount_QNAME = new QName("", "org_rest_amount");
    private final static QName _ItemTypeGroupAmount_QNAME = new QName("", "group_amount");
    private final static QName _ItemTypeOrgCurrinfo_QNAME = new QName("", "org_currinfo");
    private final static QName _ItemTypeOrgAmount_QNAME = new QName("", "org_amount");
    private final static QName _ItemTypePkProject_QNAME = new QName("", "pk_project");
    private final static QName _ItemTypeAccruedBillno_QNAME = new QName("", "accrued_billno");
    private final static QName _ItemTypePkOrg_QNAME = new QName("", "pk_org");
    private final static QName _ItemTypeVerifyAmount_QNAME = new QName("", "verify_amount");
    private final static QName _ItemTypeGlobalRestAmount_QNAME = new QName("", "global_rest_amount");
    private final static QName _ItemTypePkSupplier_QNAME = new QName("", "pk_supplier");
    private final static QName _ItemTypePkAccruedVerify_QNAME = new QName("", "pk_accrued_verify");
    private final static QName _ItemTypePkIobsclass_QNAME = new QName("", "pk_iobsclass");
    private final static QName _ItemTypeGroupCurrinfo_QNAME = new QName("", "group_currinfo");
    private final static QName _ItemTypeRedAmount_QNAME = new QName("", "red_amount");
    private final static QName _ItemTypeBxdBillno_QNAME = new QName("", "bxd_billno");
    private final static QName _ItemTypePkCheckele_QNAME = new QName("", "pk_checkele");
    private final static QName _ItemTypeSrcAccruedpk_QNAME = new QName("", "src_accruedpk");
    private final static QName _ItemTypeRowno_QNAME = new QName("", "rowno");
    private final static QName _ItemTypeAmount_QNAME = new QName("", "amount");
    private final static QName _ItemTypeRestAmount_QNAME = new QName("", "rest_amount");
    private final static QName _ItemTypePkAccruedDetail_QNAME = new QName("", "pk_accrued_detail");
    private final static QName _ItemTypePkCustomer_QNAME = new QName("", "pk_customer");
    private final static QName _ItemTypePkBxd_QNAME = new QName("", "pk_bxd");
    private final static QName _ItemTypeVerifyMan_QNAME = new QName("", "verify_man");
    private final static QName _ItemTypePkWbs_QNAME = new QName("", "pk_wbs");
    private final static QName _ItemTypePredictRestAmount_QNAME = new QName("", "predict_rest_amount");
    private final static QName _ItemTypeVerifyDate_QNAME = new QName("", "verify_date");
    private final static QName _ItemTypeGroupRestAmount_QNAME = new QName("", "group_rest_amount");
    private final static QName _ItemTypeGroupVerifyAmount_QNAME = new QName("", "group_verify_amount");
    private final static QName _ItemTypeEffectstatus_QNAME = new QName("", "effectstatus");
    private final static QName _ItemTypeAssumeDept_QNAME = new QName("", "assume_dept");
    private final static QName _ItemTypeSrcDetailpk_QNAME = new QName("", "src_detailpk");
    private final static QName _ItemTypeGlobalAmount_QNAME = new QName("", "global_amount");
    private final static QName _ItemTypeEffectdate_QNAME = new QName("", "effectdate");
    private final static QName _ItemTypePkBrand_QNAME = new QName("", "pk_brand");
    private final static QName _ItemTypePkResacostcenter_QNAME = new QName("", "pk_resacostcenter");
    private final static QName _ItemTypeGlobalVerifyAmount_QNAME = new QName("", "global_verify_amount");
    private final static QName _ItemTypeGlobalCurrinfo_QNAME = new QName("", "global_currinfo");
    private final static QName _ItemTypePkPcorg_QNAME = new QName("", "pk_pcorg");
    private final static QName _ItemTypeAssumeOrg_QNAME = new QName("", "assume_org");
    private final static QName _ItemTypeDefitem12_QNAME = new QName("", "defitem12");
    private final static QName _ItemTypeDefitem13_QNAME = new QName("", "defitem13");
    private final static QName _ItemTypeDefitem14_QNAME = new QName("", "defitem14");
    private final static QName _ItemTypeDefitem15_QNAME = new QName("", "defitem15");
    private final static QName _ItemTypeDefitem16_QNAME = new QName("", "defitem16");
    private final static QName _ItemTypeDefitem17_QNAME = new QName("", "defitem17");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cloudpense.expman.entity.hsf.claim.withholding
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link UfinterfaceType }
     */
    public UfinterfaceType createUfinterfaceType() {
        return new UfinterfaceType();
    }

    /**
     * Create an instance of {@link ItemType }
     */
    public ItemType createItemType() {
        return new ItemType();
    }

    /**
     * Create an instance of {@link AccruedDetailType }
     */
    public AccruedDetailType createAccruedDetailType() {
        return new AccruedDetailType();
    }

    /**
     * Create an instance of {@link BillType }
     */
    public BillType createBillType() {
        return new BillType();
    }

    /**
     * Create an instance of {@link BillheadType }
     */
    public BillheadType createBillheadType() {
        return new BillheadType();
    }

    /**
     * Create an instance of {@link AccruedVerifyType }
     */
    public AccruedVerifyType createAccruedVerifyType() {
        return new AccruedVerifyType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UfinterfaceType }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "ufinterface")
    public JAXBElement<UfinterfaceType> createUfinterface(UfinterfaceType value) {
        return new JAXBElement<UfinterfaceType>(_Ufinterface_QNAME, UfinterfaceType.class, null, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "srctype", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSrctype(String value) {
        return new JAXBElement<String>(_ItemTypeSrctype_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_group", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkGroup(String value) {
        return new JAXBElement<String>(_ItemTypePkGroup_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_proline", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkProline(String value) {
        return new JAXBElement<String>(_ItemTypePkProline_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "org_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeOrgVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeOrgVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "org_rest_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeOrgRestAmount(String value) {
        return new JAXBElement<String>(_ItemTypeOrgRestAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "group_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGroupAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "org_currinfo", scope = ItemType.class)
    public JAXBElement<String> createItemTypeOrgCurrinfo(String value) {
        return new JAXBElement<String>(_ItemTypeOrgCurrinfo_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "org_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeOrgAmount(String value) {
        return new JAXBElement<String>(_ItemTypeOrgAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_project", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkProject(String value) {
        return new JAXBElement<String>(_ItemTypePkProject_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "accrued_billno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAccruedBillno(String value) {
        return new JAXBElement<String>(_ItemTypeAccruedBillno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_org", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkOrg(String value) {
        return new JAXBElement<String>(_ItemTypePkOrg_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "global_rest_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalRestAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalRestAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_supplier", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkSupplier(String value) {
        return new JAXBElement<String>(_ItemTypePkSupplier_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_accrued_verify", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkAccruedVerify(String value) {
        return new JAXBElement<String>(_ItemTypePkAccruedVerify_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_iobsclass", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkIobsclass(String value) {
        return new JAXBElement<String>(_ItemTypePkIobsclass_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "group_currinfo", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupCurrinfo(String value) {
        return new JAXBElement<String>(_ItemTypeGroupCurrinfo_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "red_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeRedAmount(String value) {
        return new JAXBElement<String>(_ItemTypeRedAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bxd_billno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeBxdBillno(String value) {
        return new JAXBElement<String>(_ItemTypeBxdBillno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_checkele", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkCheckele(String value) {
        return new JAXBElement<String>(_ItemTypePkCheckele_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_accruedpk", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSrcAccruedpk(String value) {
        return new JAXBElement<String>(_ItemTypeSrcAccruedpk_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "rowno", scope = ItemType.class)
    public JAXBElement<String> createItemTypeRowno(String value) {
        return new JAXBElement<String>(_ItemTypeRowno_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAmount(String value) {
        return new JAXBElement<String>(_ItemTypeAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "rest_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeRestAmount(String value) {
        return new JAXBElement<String>(_ItemTypeRestAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_accrued_detail", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkAccruedDetail(String value) {
        return new JAXBElement<String>(_ItemTypePkAccruedDetail_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_customer", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkCustomer(String value) {
        return new JAXBElement<String>(_ItemTypePkCustomer_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_bxd", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkBxd(String value) {
        return new JAXBElement<String>(_ItemTypePkBxd_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_man", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyMan(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyMan_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_wbs", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkWbs(String value) {
        return new JAXBElement<String>(_ItemTypePkWbs_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "predict_rest_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypePredictRestAmount(String value) {
        return new JAXBElement<String>(_ItemTypePredictRestAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "verify_date", scope = ItemType.class)
    public JAXBElement<String> createItemTypeVerifyDate(String value) {
        return new JAXBElement<String>(_ItemTypeVerifyDate_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "group_rest_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupRestAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGroupRestAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "group_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGroupVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGroupVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "effectstatus", scope = ItemType.class)
    public JAXBElement<String> createItemTypeEffectstatus(String value) {
        return new JAXBElement<String>(_ItemTypeEffectstatus_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "assume_dept", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAssumeDept(String value) {
        return new JAXBElement<String>(_ItemTypeAssumeDept_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_detailpk", scope = ItemType.class)
    public JAXBElement<String> createItemTypeSrcDetailpk(String value) {
        return new JAXBElement<String>(_ItemTypeSrcDetailpk_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "global_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "effectdate", scope = ItemType.class)
    public JAXBElement<String> createItemTypeEffectdate(String value) {
        return new JAXBElement<String>(_ItemTypeEffectdate_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_brand", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkBrand(String value) {
        return new JAXBElement<String>(_ItemTypePkBrand_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_resacostcenter", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkResacostcenter(String value) {
        return new JAXBElement<String>(_ItemTypePkResacostcenter_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "global_verify_amount", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalVerifyAmount(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalVerifyAmount_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "global_currinfo", scope = ItemType.class)
    public JAXBElement<String> createItemTypeGlobalCurrinfo(String value) {
        return new JAXBElement<String>(_ItemTypeGlobalCurrinfo_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_pcorg", scope = ItemType.class)
    public JAXBElement<String> createItemTypePkPcorg(String value) {
        return new JAXBElement<String>(_ItemTypePkPcorg_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "assume_org", scope = ItemType.class)
    public JAXBElement<String> createItemTypeAssumeOrg(String value) {
        return new JAXBElement<String>(_ItemTypeAssumeOrg_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem12", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem12(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem12_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem13", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem13(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem13_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem14", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem14(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem14_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem15", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem15(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem15_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem16", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem16(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem16_QNAME, String.class, ItemType.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "defitem17", scope = ItemType.class)
    public JAXBElement<String> createItemTypeDefitem17(String value) {
        return new JAXBElement<String>(_ItemTypeDefitem17_QNAME, String.class, ItemType.class, value);
    }
}
