
package com.cloudpense.expman.entity.hsf.claim.loan;

import javax.xml.bind.annotation.*;
import java.util.List;


/**
 * <p>billheadType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="billheadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="total" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="payflag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djdl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djlxbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkbxr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydeptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bzbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbhl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djzt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ischeck" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbye" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ybye" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="spzt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qcbz" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="kjnd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="kjqj" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qzzt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="zhrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="loantype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="sxbz" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="creator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkBusitem" type="{}jk_busitemType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "billheadType", propOrder = {
        "pkFiorg",
        "pkOrgV",
        "pkGroup",
        "pkOrg",
        "total",
        "yjye",
        "payflag",
        "fydwbm",
        "dwbm",
        "djdl",
        "djlxbm",
        "djrq",
        "deptid",
        "jkbxr",
        "receiver",
        "operator",
        "fydeptid",
        "bzbm",
        "bbhl",
        "ybje",
        "bbje",
        "djzt",
        "ischeck",
        "bbye",
        "ybye",
        "spzt",
        "qcbz",
        "kjnd",
        "kjqj",
        "qzzt",
        "zhrq",
        "loantype",
        "sxbz",
        "creator",
        "zyx20",
        "jkBusitem"
})
public class BillheadType {

    @XmlElement(name = "pk_fiorg", required = true)
    protected String pkFiorg;
    @XmlElement(name = "pk_org_v", required = true)
    protected String pkOrgV;
    @XmlElement(name = "pk_group", required = true)
    protected String pkGroup;
    @XmlElement(name = "pk_org", required = true)
    protected String pkOrg;
    @XmlElement(required = true)
    protected String total;
    @XmlElement(required = true)
    protected String yjye;
    @XmlElement(required = true)
    protected String payflag;
    @XmlElement(required = true)
    protected String fydwbm;
    @XmlElement(required = true)
    protected String dwbm;
    @XmlElement(required = true)
    protected String djdl;
    @XmlElement(required = true)
    protected String djlxbm;
    @XmlElement(required = true)
    protected String djrq;
    @XmlElement(required = true)
    protected String deptid;
    @XmlElement(required = true)
    protected String jkbxr;
    @XmlElement(required = true)
    protected String receiver;
    @XmlElement(required = true)
    protected String operator;
    @XmlElement(required = true)
    protected String fydeptid;
    @XmlElement(required = true)
    protected String bzbm;
    @XmlElement(required = true)
    protected String bbhl;
    @XmlElement(required = true)
    protected String ybje;
    @XmlElement(required = true)
    protected String bbje;
    @XmlElement(required = true)
    protected String djzt;
    @XmlElement(required = true)
    protected String ischeck;
    @XmlElement(required = true)
    protected String bbye;
    @XmlElement(required = true)
    protected String ybye;
    @XmlElement(required = true)
    protected String spzt;
    @XmlElement(required = true)
    protected String qcbz;
    @XmlElement(required = true)
    protected String kjnd;
    @XmlElement(required = true)
    protected String kjqj;
    @XmlElement(required = true)
    protected String qzzt;
    @XmlElement(required = true)
    protected String zhrq;
    @XmlElement(required = true)
    protected String loantype;
    @XmlElement(required = true)
    protected String sxbz;
    @XmlElement(required = true)
    protected String creator;
    @XmlElement(required = true)
    protected String zyx20;
    @XmlElementWrapper(name = "jk_busitem", required = true)
    @XmlElement(name = "item")
    protected List<ItemType> jkBusitem;

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getYjye() {
        return yjye;
    }

    public void setYjye(String yjye) {
        this.yjye = yjye;
    }

    public String getZyx20() {
        return zyx20;
    }

    public void setZyx20(String zyx20) {
        this.zyx20 = zyx20;
    }

    /**
     * 获取item属性的值。
     *
     * @return possible object is
     * {@link List<ItemType> }
     */
    public List<ItemType> getJkBusitem() {
        return jkBusitem;
    }

    /**
     * 设置item属性的值。
     *
     * @param value allowed object is
     *              {@link List<ItemType> }
     */
    public void setJkBusitem(List<ItemType> value) {
        this.jkBusitem = value;
    }


    /**
     * 获取pkFiorg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkFiorg() {
        return pkFiorg;
    }

    /**
     * 设置pkFiorg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkFiorg(String value) {
        this.pkFiorg = value;
    }

    /**
     * 获取pkOrgV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrgV() {
        return pkOrgV;
    }

    /**
     * 设置pkOrgV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrgV(String value) {
        this.pkOrgV = value;
    }

    /**
     * 获取pkGroup属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkGroup() {
        return pkGroup;
    }

    /**
     * 设置pkGroup属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkGroup(String value) {
        this.pkGroup = value;
    }

    /**
     * 获取pkOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrg() {
        return pkOrg;
    }

    /**
     * 设置pkOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrg(String value) {
        this.pkOrg = value;
    }

    /**
     * 获取total属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getTotal() {
        return total;
    }

    /**
     * 设置total属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTotal(String value) {
        this.total = value;
    }

    /**
     * 获取payflag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPayflag() {
        return payflag;
    }

    /**
     * 设置payflag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPayflag(String value) {
        this.payflag = value;
    }

    /**
     * 获取fydwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydwbm() {
        return fydwbm;
    }

    /**
     * 设置fydwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydwbm(String value) {
        this.fydwbm = value;
    }

    /**
     * 获取dwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDwbm() {
        return dwbm;
    }

    /**
     * 设置dwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDwbm(String value) {
        this.dwbm = value;
    }

    /**
     * 获取djdl属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjdl() {
        return djdl;
    }

    /**
     * 设置djdl属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjdl(String value) {
        this.djdl = value;
    }

    /**
     * 获取djlxbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjlxbm() {
        return djlxbm;
    }

    /**
     * 设置djlxbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjlxbm(String value) {
        this.djlxbm = value;
    }

    /**
     * 获取djrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjrq() {
        return djrq;
    }

    /**
     * 设置djrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjrq(String value) {
        this.djrq = value;
    }

    /**
     * 获取deptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDeptid() {
        return deptid;
    }

    /**
     * 设置deptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDeptid(String value) {
        this.deptid = value;
    }

    /**
     * 获取jkbxr属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJkbxr() {
        return jkbxr;
    }

    /**
     * 设置jkbxr属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJkbxr(String value) {
        this.jkbxr = value;
    }

    /**
     * 获取operator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperator(String value) {
        this.operator = value;
    }

    /**
     * 获取fydeptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydeptid() {
        return fydeptid;
    }

    /**
     * 设置fydeptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydeptid(String value) {
        this.fydeptid = value;
    }

    /**
     * 获取bzbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBzbm() {
        return bzbm;
    }

    /**
     * 设置bzbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBzbm(String value) {
        this.bzbm = value;
    }

    /**
     * 获取bbhl属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBbhl() {
        return bbhl;
    }

    /**
     * 设置bbhl属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBbhl(String value) {
        this.bbhl = value;
    }

    /**
     * 获取ybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getYbje() {
        return ybje;
    }

    /**
     * 设置ybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setYbje(String value) {
        this.ybje = value;
    }

    /**
     * 获取bbje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBbje() {
        return bbje;
    }

    /**
     * 设置bbje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBbje(String value) {
        this.bbje = value;
    }

    /**
     * 获取djzt属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjzt() {
        return djzt;
    }

    /**
     * 设置djzt属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjzt(String value) {
        this.djzt = value;
    }

    /**
     * 获取ischeck属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getIscheck() {
        return ischeck;
    }

    /**
     * 设置ischeck属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIscheck(String value) {
        this.ischeck = value;
    }

    /**
     * 获取bbye属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBbye() {
        return bbye;
    }

    /**
     * 设置bbye属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBbye(String value) {
        this.bbye = value;
    }

    /**
     * 获取ybye属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getYbye() {
        return ybye;
    }

    /**
     * 设置ybye属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setYbye(String value) {
        this.ybye = value;
    }

    /**
     * 获取spzt属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSpzt() {
        return spzt;
    }

    /**
     * 设置spzt属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSpzt(String value) {
        this.spzt = value;
    }

    /**
     * 获取qcbz属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getQcbz() {
        return qcbz;
    }

    /**
     * 设置qcbz属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setQcbz(String value) {
        this.qcbz = value;
    }

    /**
     * 获取kjnd属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getKjnd() {
        return kjnd;
    }

    /**
     * 设置kjnd属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setKjnd(String value) {
        this.kjnd = value;
    }

    /**
     * 获取kjqj属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getKjqj() {
        return kjqj;
    }

    /**
     * 设置kjqj属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setKjqj(String value) {
        this.kjqj = value;
    }

    /**
     * 获取qzzt属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getQzzt() {
        return qzzt;
    }

    /**
     * 设置qzzt属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setQzzt(String value) {
        this.qzzt = value;
    }

    /**
     * 获取zhrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getZhrq() {
        return zhrq;
    }

    /**
     * 设置zhrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setZhrq(String value) {
        this.zhrq = value;
    }

    /**
     * 获取loantype属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getLoantype() {
        return loantype;
    }

    /**
     * 设置loantype属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setLoantype(String value) {
        this.loantype = value;
    }

    /**
     * 获取sxbz属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSxbz() {
        return sxbz;
    }

    /**
     * 设置sxbz属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSxbz(String value) {
        this.sxbz = value;
    }

    /**
     * 获取creator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCreator() {
        return creator;
    }

    /**
     * 设置creator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCreator(String value) {
        this.creator = value;
    }


}
