
package com.cloudpense.expman.entity.hsf.claim.reimburse;

import javax.xml.bind.annotation.*;
import java.util.List;


/**
 * <p>billheadType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="billheadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_org_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="total" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydwbm_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydeptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydeptid_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dwbm_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deptid_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_payorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_payorg_v" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_jkbx" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djdl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djlxbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djbh" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="shrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jsrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="payman" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="paydate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="payflag" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="receiver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkbxr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="approver" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="modifier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cjkybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="cjkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="hkybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="hkbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="zfybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="zfbbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bzbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbhl" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bbje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djzt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ischeck" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="spzt" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="qcbz" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="kjqj" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="kjnd" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jsr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="er_busitem" type="{}er_busitemType"/>
 *         &lt;element name="er_bxcontrast" type="{}er_bxcontrastType"/>
 *         &lt;element name="accrued_verify" type="{}accrued_verifyType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "billheadType", propOrder = {
        "pkGroup",
        "pkOrg",
        "pkOrgV",
        "total",
        "fydwbm",
        "fydwbmV",
        "fydeptid",
        "fydeptidV",
        "dwbm",
        "dwbmV",
        "deptid",
        "deptidV",
        "pkPayorg",
        "pkPayorgV",
        "pkJkbx",
        "djdl",
        "djlxbm",
        "djbh",
        "djrq",
        "shrq",
        "jsrq",
        "payman",
        "paydate",
        "payflag",
        "receiver",
        "jkbxr",
        "zyx30",
        "operator",
        "approver",
        "modifier",
        "cjkybje",
        "cjkbbje",
        "hkybje",
        "hkbbje",
        "zfybje",
        "zfbbje",
        "bzbm",
        "bbhl",
        "ybje",
        "bbje",
        "djzt",
        "ischeck",
        "spzt",
        "qcbz",
        "kjqj",
        "kjnd",
        "jsr",
        "zy",
        "erBusitem",
        "erBxcontrast"
})
public class BillheadType {

    @XmlElement(name = "pk_group", required = true)
    protected String pkGroup;
    @XmlElement(name = "pk_org", required = true)
    protected String pkOrg;
    @XmlElement(name = "pk_org_v", required = true)
    protected String pkOrgV;
    @XmlElement(required = true)
    protected String total;
    @XmlElement(required = true)
    protected String fydwbm;
    @XmlElement(name = "fydwbm_v", required = true)
    protected String fydwbmV;
    @XmlElement(required = true)
    protected String fydeptid;
    @XmlElement(name = "fydeptid_v", required = true)
    protected String fydeptidV;
    @XmlElement(required = true)
    protected String dwbm;
    @XmlElement(name = "dwbm_v", required = true)
    protected String dwbmV;
    @XmlElement(required = true)
    protected String deptid;
    @XmlElement(name = "deptid_v", required = true)
    protected String deptidV;
    @XmlElement(name = "pk_payorg", required = true)
    protected String pkPayorg;
    @XmlElement(name = "pk_payorg_v", required = true)
    protected String pkPayorgV;
    @XmlElement(name = "pk_jkbx", required = true)
    protected String pkJkbx;
    @XmlElement(required = true)
    protected String djdl;
    @XmlElement(required = true)
    protected String djlxbm;
    @XmlElement(required = true)
    protected String djbh;
    @XmlElement(required = true)
    protected String djrq;
    @XmlElement(required = true)
    protected String shrq;
    @XmlElement(required = true)
    protected String jsrq;
    @XmlElement(required = true)
    protected String payman;
    @XmlElement(required = true)
    protected String paydate;
    @XmlElement(required = true)
    protected String payflag;
    @XmlElement(required = true)
    protected String receiver;
    @XmlElement(required = true)
    protected String jkbxr;
    @XmlElement(required = true)
    protected String operator;
    @XmlElement(required = true)
    protected String approver;
    @XmlElement(required = true)
    protected String modifier;
    @XmlElement(required = true)
    protected String cjkybje;
    @XmlElement(required = true)
    protected String cjkbbje;
    @XmlElement(required = true)
    protected String hkybje;
    @XmlElement(required = true)
    protected String hkbbje;
    @XmlElement(required = true)
    protected String zfybje;
    @XmlElement(required = true)
    protected String zfbbje;
    @XmlElement(required = true)
    protected String bzbm;
    @XmlElement(required = true)
    protected String bbhl;
    @XmlElement(required = true)
    protected String ybje;
    @XmlElement(required = true)
    protected String bbje;
    @XmlElement(required = true)
    protected String djzt;
    @XmlElement(required = true)
    protected String zy;
    @XmlElement(required = true)
    private String zyx30;
    @XmlElement(required = true)
    protected String ischeck;
    @XmlElement(required = true)
    protected String spzt;
    @XmlElement(required = true)
    protected String qcbz;
    @XmlElement(required = true)
    protected String kjqj;
    @XmlElement(required = true)
    protected String kjnd;
    @XmlElement(required = true)
    protected String jsr;
    @XmlElementWrapper(name = "er_busitem", required = true)
    @XmlElement(name = "item")
    protected List<ItemType> erBusitem;
    @XmlElementWrapper(name = "er_bxcontrast", required = true)
    @XmlElement(name = "item")
    protected List<ItemType> erBxcontrast;


    public String getZyx30() {
        return zyx30;
    }

    public void setZyx30(String zyx30) {
        this.zyx30 = zyx30;
    }

    /**
     * 获取pkGroup属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkGroup() {
        return pkGroup;
    }

    /**
     * 设置pkGroup属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkGroup(String value) {
        this.pkGroup = value;
    }

    /**
     * 获取pkOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrg() {
        return pkOrg;
    }

    /**
     * 设置pkOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrg(String value) {
        this.pkOrg = value;
    }

    public String getZy() {
        return zy;
    }

    public void setZy(String zy) {
        this.zy = zy;
    }

    /**
     * 获取pkOrgV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrgV() {
        return pkOrgV;
    }

    /**
     * 设置pkOrgV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrgV(String value) {
        this.pkOrgV = value;
    }

    /**
     * 获取total属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getTotal() {
        return total;
    }

    /**
     * 设置total属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setTotal(String value) {
        this.total = value;
    }

    /**
     * 获取fydwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydwbm() {
        return fydwbm;
    }

    /**
     * 设置fydwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydwbm(String value) {
        this.fydwbm = value;
    }

    /**
     * 获取fydwbmV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydwbmV() {
        return fydwbmV;
    }

    /**
     * 设置fydwbmV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydwbmV(String value) {
        this.fydwbmV = value;
    }

    /**
     * 获取fydeptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydeptid() {
        return fydeptid;
    }

    /**
     * 设置fydeptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydeptid(String value) {
        this.fydeptid = value;
    }

    /**
     * 获取fydeptidV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydeptidV() {
        return fydeptidV;
    }

    /**
     * 设置fydeptidV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydeptidV(String value) {
        this.fydeptidV = value;
    }

    /**
     * 获取dwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDwbm() {
        return dwbm;
    }

    /**
     * 设置dwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDwbm(String value) {
        this.dwbm = value;
    }

    /**
     * 获取dwbmV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDwbmV() {
        return dwbmV;
    }

    /**
     * 设置dwbmV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDwbmV(String value) {
        this.dwbmV = value;
    }

    /**
     * 获取deptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDeptid() {
        return deptid;
    }

    /**
     * 设置deptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDeptid(String value) {
        this.deptid = value;
    }

    /**
     * 获取deptidV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDeptidV() {
        return deptidV;
    }

    /**
     * 设置deptidV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDeptidV(String value) {
        this.deptidV = value;
    }

    /**
     * 获取pkPayorg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkPayorg() {
        return pkPayorg;
    }

    /**
     * 设置pkPayorg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkPayorg(String value) {
        this.pkPayorg = value;
    }

    /**
     * 获取pkPayorgV属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkPayorgV() {
        return pkPayorgV;
    }

    /**
     * 设置pkPayorgV属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkPayorgV(String value) {
        this.pkPayorgV = value;
    }

    /**
     * 获取pkJkbx属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkJkbx() {
        return pkJkbx;
    }

    /**
     * 设置pkJkbx属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkJkbx(String value) {
        this.pkJkbx = value;
    }

    /**
     * 获取djdl属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjdl() {
        return djdl;
    }

    /**
     * 设置djdl属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjdl(String value) {
        this.djdl = value;
    }

    /**
     * 获取djlxbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjlxbm() {
        return djlxbm;
    }

    /**
     * 设置djlxbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjlxbm(String value) {
        this.djlxbm = value;
    }

    /**
     * 获取djbh属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjbh() {
        return djbh;
    }

    /**
     * 设置djbh属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjbh(String value) {
        this.djbh = value;
    }

    /**
     * 获取djrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjrq() {
        return djrq;
    }

    /**
     * 设置djrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjrq(String value) {
        this.djrq = value;
    }

    /**
     * 获取shrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getShrq() {
        return shrq;
    }

    /**
     * 设置shrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setShrq(String value) {
        this.shrq = value;
    }

    /**
     * 获取jsrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJsrq() {
        return jsrq;
    }

    /**
     * 设置jsrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJsrq(String value) {
        this.jsrq = value;
    }

    /**
     * 获取payman属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPayman() {
        return payman;
    }

    /**
     * 设置payman属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPayman(String value) {
        this.payman = value;
    }

    /**
     * 获取paydate属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPaydate() {
        return paydate;
    }

    /**
     * 设置paydate属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPaydate(String value) {
        this.paydate = value;
    }

    /**
     * 获取payflag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPayflag() {
        return payflag;
    }

    /**
     * 设置payflag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPayflag(String value) {
        this.payflag = value;
    }

    /**
     * 获取receiver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 设置receiver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReceiver(String value) {
        this.receiver = value;
    }

    /**
     * 获取jkbxr属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJkbxr() {
        return jkbxr;
    }

    /**
     * 设置jkbxr属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJkbxr(String value) {
        this.jkbxr = value;
    }

    /**
     * 获取operator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperator(String value) {
        this.operator = value;
    }

    /**
     * 获取approver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getApprover() {
        return approver;
    }

    /**
     * 设置approver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setApprover(String value) {
        this.approver = value;
    }

    /**
     * 获取modifier属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * 设置modifier属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setModifier(String value) {
        this.modifier = value;
    }

    /**
     * 获取cjkybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCjkybje() {
        return cjkybje;
    }

    /**
     * 设置cjkybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCjkybje(String value) {
        this.cjkybje = value;
    }

    /**
     * 获取cjkbbje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getCjkbbje() {
        return cjkbbje;
    }

    /**
     * 设置cjkbbje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setCjkbbje(String value) {
        this.cjkbbje = value;
    }

    /**
     * 获取hkybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getHkybje() {
        return hkybje;
    }

    /**
     * 设置hkybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHkybje(String value) {
        this.hkybje = value;
    }

    /**
     * 获取hkbbje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getHkbbje() {
        return hkbbje;
    }

    /**
     * 设置hkbbje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHkbbje(String value) {
        this.hkbbje = value;
    }

    /**
     * 获取zfybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getZfybje() {
        return zfybje;
    }

    /**
     * 设置zfybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setZfybje(String value) {
        this.zfybje = value;
    }

    /**
     * 获取zfbbje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getZfbbje() {
        return zfbbje;
    }

    /**
     * 设置zfbbje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setZfbbje(String value) {
        this.zfbbje = value;
    }

    /**
     * 获取bzbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBzbm() {
        return bzbm;
    }

    /**
     * 设置bzbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBzbm(String value) {
        this.bzbm = value;
    }

    /**
     * 获取bbhl属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBbhl() {
        return bbhl;
    }

    /**
     * 设置bbhl属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBbhl(String value) {
        this.bbhl = value;
    }

    /**
     * 获取ybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getYbje() {
        return ybje;
    }

    /**
     * 设置ybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setYbje(String value) {
        this.ybje = value;
    }

    /**
     * 获取bbje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBbje() {
        return bbje;
    }

    /**
     * 设置bbje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBbje(String value) {
        this.bbje = value;
    }

    /**
     * 获取djzt属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjzt() {
        return djzt;
    }

    /**
     * 设置djzt属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjzt(String value) {
        this.djzt = value;
    }

    /**
     * 获取ischeck属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getIscheck() {
        return ischeck;
    }

    /**
     * 设置ischeck属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIscheck(String value) {
        this.ischeck = value;
    }

    /**
     * 获取spzt属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSpzt() {
        return spzt;
    }

    /**
     * 设置spzt属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSpzt(String value) {
        this.spzt = value;
    }

    /**
     * 获取qcbz属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getQcbz() {
        return qcbz;
    }

    /**
     * 设置qcbz属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setQcbz(String value) {
        this.qcbz = value;
    }

    /**
     * 获取kjqj属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getKjqj() {
        return kjqj;
    }

    /**
     * 设置kjqj属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setKjqj(String value) {
        this.kjqj = value;
    }

    /**
     * 获取kjnd属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getKjnd() {
        return kjnd;
    }

    /**
     * 设置kjnd属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setKjnd(String value) {
        this.kjnd = value;
    }

    /**
     * 获取jsr属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJsr() {
        return jsr;
    }

    /**
     * 设置jsr属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJsr(String value) {
        this.jsr = value;
    }

    /**
     * 获取erBusitem属性的值。
     *
     * @return possible object is
     * {@link ErBusitemType }
     */
    public List<ItemType> getErBusitem() {
        return erBusitem;
    }

    /**
     * 设置erBusitem属性的值。
     *
     * @param value allowed object is
     *              {@link ErBusitemType }
     */
    public void setErBusitem(List<ItemType> value) {
        this.erBusitem = value;
    }

    /**
     * 获取erBxcontrast属性的值。
     *
     * @return possible object is
     * {@link ErBxcontrastType }
     */
    public List<ItemType> getErBxcontrast() {
        return erBxcontrast;
    }

    /**
     * 设置erBxcontrast属性的值。
     *
     * @param value allowed object is
     *              {@link ErBxcontrastType }
     */
    public void setErBxcontrast(List<ItemType> value) {
        this.erBxcontrast = value;
    }


}
