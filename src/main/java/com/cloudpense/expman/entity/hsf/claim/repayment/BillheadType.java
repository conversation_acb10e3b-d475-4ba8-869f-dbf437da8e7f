
package com.cloudpense.expman.entity.hsf.claim.repayment;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>billheadType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="billheadType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="pk_org" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="fydeptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="dwbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="deptid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="bzbm" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="pk_payorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="djrq" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="jkbxr" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="operator" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="hkybje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="er_bxcontrast" type="{}er_bxcontrastType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "billheadType", propOrder = {
        "pkOrg",
        "fydwbm",
        "fydeptid",
        "dwbm",
        "deptid",
        "bzbm",
        "pkPayorg",
        "djrq",
        "jkbxr",
        "operator",
        "hkybje",
        "erBxcontrast"
})
public class BillheadType {

    @XmlElement(name = "pk_org", required = true)
    protected String pkOrg;
    @XmlElement(required = true)
    protected String fydwbm;
    @XmlElement(required = true)
    protected String fydeptid;
    @XmlElement(required = true)
    protected String dwbm;
    @XmlElement(required = true)
    protected String deptid;
    @XmlElement(required = true)
    protected String bzbm;
    @XmlElement(name = "pk_payorg", required = true)
    protected String pkPayorg;
    @XmlElement(required = true)
    protected String djrq;
    @XmlElement(required = true)
    protected String jkbxr;
    @XmlElement(required = true)
    protected String operator;
    @XmlElement(required = true)
    protected String hkybje;
    @XmlElementWrapper(name = "er_bxcontrast", required = true)
    @XmlElement(name = "item")
    protected List<ItemType> erBxcontrast;

    /**
     * Gets the value of the jkBusitem property.
     *
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the jkBusitem property.
     *
     * <p>
     * For example, to add a new jkBusitem, do as follows:
     * <pre>
     *    getJkBusitem().add(newItem);
     * </pre>
     *
     *
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link ItemType }
     */
    public List<ItemType> getErBxcontrast() {
        if (erBxcontrast == null) {
            erBxcontrast = new ArrayList<ItemType>();
        }
        return this.erBxcontrast;
    }

    public void setErBxcontrast(List<ItemType> erBxcontrast) {
        this.erBxcontrast = erBxcontrast;
    }

    /**
     * 获取pkOrg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkOrg() {
        return pkOrg;
    }

    /**
     * 设置pkOrg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkOrg(String value) {
        this.pkOrg = value;
    }

    /**
     * 获取fydwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydwbm() {
        return fydwbm;
    }

    /**
     * 设置fydwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydwbm(String value) {
        this.fydwbm = value;
    }

    /**
     * 获取fydeptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFydeptid() {
        return fydeptid;
    }

    /**
     * 设置fydeptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFydeptid(String value) {
        this.fydeptid = value;
    }

    /**
     * 获取dwbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDwbm() {
        return dwbm;
    }

    /**
     * 设置dwbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDwbm(String value) {
        this.dwbm = value;
    }

    /**
     * 获取deptid属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDeptid() {
        return deptid;
    }

    /**
     * 设置deptid属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDeptid(String value) {
        this.deptid = value;
    }

    /**
     * 获取bzbm属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBzbm() {
        return bzbm;
    }

    /**
     * 设置bzbm属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBzbm(String value) {
        this.bzbm = value;
    }

    /**
     * 获取pkPayorg属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getPkPayorg() {
        return pkPayorg;
    }

    /**
     * 设置pkPayorg属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setPkPayorg(String value) {
        this.pkPayorg = value;
    }

    /**
     * 获取djrq属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getDjrq() {
        return djrq;
    }

    /**
     * 设置djrq属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setDjrq(String value) {
        this.djrq = value;
    }

    /**
     * 获取jkbxr属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getJkbxr() {
        return jkbxr;
    }

    /**
     * 设置jkbxr属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setJkbxr(String value) {
        this.jkbxr = value;
    }

    /**
     * 获取operator属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOperator() {
        return operator;
    }

    /**
     * 设置operator属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOperator(String value) {
        this.operator = value;
    }

    /**
     * 获取hkybje属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getHkybje() {
        return hkybje;
    }

    /**
     * 设置hkybje属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setHkybje(String value) {
        this.hkybje = value;
    }


}
