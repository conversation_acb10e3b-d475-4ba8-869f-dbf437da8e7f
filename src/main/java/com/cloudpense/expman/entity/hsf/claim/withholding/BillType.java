
package com.cloudpense.expman.entity.hsf.claim.withholding;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>billType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="billType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="billhead" type="{}billheadType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "billType", propOrder = {
        "billhead"
})
public class BillType {

    @XmlElement(required = true)
    protected BillheadType billhead;

    /**
     * 获取billhead属性的值。
     *
     * @return possible object is
     * {@link BillheadType }
     */
    public BillheadType getBillhead() {
        return billhead;
    }

    /**
     * 设置billhead属性的值。
     *
     * @param value allowed object is
     *              {@link BillheadType }
     */
    public void setBillhead(BillheadType value) {
        this.billhead = value;
    }

}
