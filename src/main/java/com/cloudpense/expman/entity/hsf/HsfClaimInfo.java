package com.cloudpense.expman.entity.hsf;

public class HsfClaimInfo {
    private int headerId;
    private int headerTypeId;
    private String subCompany;
    private String fatherDept;
    private String dept;
    private String deptCode;
    private String applicant;
    private String applicantDept;
    private String applicantComp;
    private String submitter;
    private String date;
    private double amount;
    private String status;
    private String description;
    private String supplier;
    private String supplierAc;
    private String account;

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSupplierAc() {
        return supplierAc;
    }

    public void setSupplierAc(String supplierAc) {
        this.supplierAc = supplierAc;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public String getApplicantComp() {
        return applicantComp;
    }

    public void setApplicantComp(String applicantComp) {
        this.applicantComp = applicantComp;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFatherDept() {
        return fatherDept;
    }

    public void setFatherDept(String fatherDept) {
        this.fatherDept = fatherDept;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getHeaderId() {
        return headerId;
    }

    public void setHeaderId(int headerId) {
        this.headerId = headerId;
    }

    public int getHeaderTypeId() {
        return headerTypeId;
    }

    public void setHeaderTypeId(int headerTypeId) {
        this.headerTypeId = headerTypeId;
    }

    public String getSubCompany() {
        return subCompany;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public void setSubCompany(String subCompany) {
        this.subCompany = subCompany;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getApplicant() {
        return applicant;
    }

    public void setApplicant(String applicant) {
        this.applicant = applicant;
    }

    public String getApplicantDept() {
        return applicantDept;
    }

    public void setApplicantDept(String applicantDept) {
        this.applicantDept = applicantDept;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }


}
