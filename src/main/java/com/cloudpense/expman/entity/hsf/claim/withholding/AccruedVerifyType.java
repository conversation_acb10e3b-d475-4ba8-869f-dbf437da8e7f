
package com.cloudpense.expman.entity.hsf.claim.withholding;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>accrued_verifyType complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType name="accrued_verifyType">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="jkBusitem" type="{}itemType"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "accrued_verifyType", propOrder = {
        "item"
})
public class AccruedVerifyType {

    @XmlElement(required = true)
    protected ItemType item;

    /**
     * 获取item属性的值。
     *
     * @return possible object is
     * {@link ItemType }
     */
    public ItemType getItem() {
        return item;
    }

    /**
     * 设置item属性的值。
     *
     * @param value allowed object is
     *              {@link ItemType }
     */
    public void setItem(ItemType value) {
        this.item = value;
    }

}
