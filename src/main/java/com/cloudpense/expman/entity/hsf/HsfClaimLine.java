package com.cloudpense.expman.entity.hsf;

public class HsfClaimLine {
    /**
     * 组织
     * 借款人
     * 金额
     */
    private String dept;//关联单据的branch_id对应的code
    private String user;//charge_user
    private double amount;//金额
    private int linkHeaderId;//关联单据头ID
    private int linkLineId;//关联单据行ID
    private String supplier;
    private String supplierAc;
    private String date;
    private String expTypeCode;

    public String getExpTypeCode() {
        return expTypeCode;
    }

    public void setExpTypeCode(String expTypeCode) {
        this.expTypeCode = expTypeCode;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getSupplierAc() {
        return supplierAc;
    }

    public void setSupplierAc(String supplierAc) {
        this.supplierAc = supplierAc;
    }

    public String getDept() {
        return dept;
    }

    public void setDept(String dept) {
        this.dept = dept;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public int getLinkHeaderId() {
        return linkHeaderId;
    }

    public void setLinkHeaderId(int linkHeaderId) {
        this.linkHeaderId = linkHeaderId;
    }

    public int getLinkLineId() {
        return linkLineId;
    }

    public void setLinkLineId(int linkLineId) {
        this.linkLineId = linkLineId;
    }
}