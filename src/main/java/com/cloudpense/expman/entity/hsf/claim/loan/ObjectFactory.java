
package com.cloudpense.expman.entity.hsf.claim.loan;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.cloudpense.expman.entity.hsf.claim.loan package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _Ufinterface_QNAME = new QName("", "ufinterface");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cloudpense.expman.entity.hsf.claim.loan
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link UfinterfaceType }
     */
    public UfinterfaceType createUfinterfaceType() {
        return new UfinterfaceType();
    }

    /**
     * Create an instance of {@link ItemType }
     */
    public ItemType createItemType() {
        return new ItemType();
    }

    /**
     * Create an instance of {@link BillType }
     */
    public BillType createBillType() {
        return new BillType();
    }

    /**
     * Create an instance of {@link BillheadType }
     */
    public BillheadType createBillheadType() {
        return new BillheadType();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link UfinterfaceType }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "ufinterface")
    public JAXBElement<UfinterfaceType> createUfinterface(UfinterfaceType value) {
        return new JAXBElement<UfinterfaceType>(_Ufinterface_QNAME, UfinterfaceType.class, null, value);
    }

}
