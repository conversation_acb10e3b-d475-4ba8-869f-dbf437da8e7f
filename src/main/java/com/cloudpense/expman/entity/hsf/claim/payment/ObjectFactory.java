
package com.cloudpense.expman.entity.hsf.claim.payment;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlElementDecl;
import javax.xml.bind.annotation.XmlRegistry;
import javax.xml.namespace.QName;


/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the com.cloudpense.expman.entity.hsf.claim.payment package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 */
@XmlRegistry
public class ObjectFactory {

    private final static QName _UfinterfaceBillBillheadPkGroup_QNAME = new QName("", "pk_group");
    private final static QName _UfinterfaceBillBillheadGroupnotaxDe_QNAME = new QName("", "groupnotax_de");
    private final static QName _UfinterfaceBillBillheadLocalMoney_QNAME = new QName("", "local_money");
    private final static QName _UfinterfaceBillBillheadPkTradetype_QNAME = new QName("", "pk_tradetype");
    private final static QName _UfinterfaceBillBillheadPkCurrtype_QNAME = new QName("", "pk_currtype");
    private final static QName _UfinterfaceBillBillheadBodys_QNAME = new QName("", "bodys");
    private final static QName _UfinterfaceBillBillheadPkOrg_QNAME = new QName("", "pk_org");
    private final static QName _UfinterfaceBillBillheadSyscode_QNAME = new QName("", "syscode");
    private final static QName _UfinterfaceBillBillheadPkFiorg_QNAME = new QName("", "pk_fiorg");
    private final static QName _UfinterfaceBillBillheadBillmaker_QNAME = new QName("", "billmaker");
    private final static QName _UfinterfaceBillBillheadSupplier_QNAME = new QName("", "supplier");
    private final static QName _UfinterfaceBillBillheadBilldate_QNAME = new QName("", "billdate");
    private final static QName _UfinterfaceBillBillheadBillno_QNAME = new QName("", "billno");
    private final static QName _UfinterfaceBillBillheadPkBilltype_QNAME = new QName("", "pk_billtype");
    private final static QName _UfinterfaceBillBillheadSrcSyscode_QNAME = new QName("", "src_syscode");
    private final static QName _UfinterfaceBillBillheadBodysItemNotaxDe_QNAME = new QName("", "notax_de");
    private final static QName _UfinterfaceBillBillheadBodysItemObjtype_QNAME = new QName("", "objtype");
    private final static QName _UfinterfaceBillBillheadBodysItemSrcBillid_QNAME = new QName("", "src_billid");
    private final static QName _UfinterfaceBillBillheadBodysItemTopBilltype_QNAME = new QName("", "top_billtype");
    private final static QName _UfinterfaceBillBillheadBodysItemPkRecpaytype_QNAME = new QName("", "pk_recpaytype");
    private final static QName _UfinterfaceBillBillheadBodysItemMoneyDe_QNAME = new QName("", "money_de");
    private final static QName _UfinterfaceBillBillheadBodysItemPkPayitem_QNAME = new QName("", "pk_payitem");
    private final static QName _UfinterfaceBillBillheadBodysItemPrepay_QNAME = new QName("", "prepay");
    private final static QName _UfinterfaceBillBillheadBodysItemSrcItemid_QNAME = new QName("", "src_itemid");
    private final static QName _UfinterfaceBillBillheadBodysItemSrcBilltype_QNAME = new QName("", "src_billtype");
    private final static QName _UfinterfaceBillBillheadBodysItemSrcTradetype_QNAME = new QName("", "src_tradetype");
    private final static QName _UfinterfaceBillBillheadBodysItemTopItemid_QNAME = new QName("", "top_itemid");
    private final static QName _UfinterfaceBillBillheadBodysItemTopBillid_QNAME = new QName("", "top_billid");
    private final static QName _UfinterfaceBillBillheadBodysItemTopTradetype_QNAME = new QName("", "top_tradetype");
    private final static QName _UfinterfaceBillBillheadBodysItemScomment_QNAME = new QName("", "scomment");
    private final static QName _UfinterfaceBillBillheadBodysItemContractno_QNAME = new QName("", "contractno");
    private final static QName _UfinterfaceBillBillheadBodysItemRate_QNAME = new QName("", "rate");
    private final static QName _UfinterfaceBillBillheadBodysItemDef12_QNAME = new QName("", "def12");
    private final static QName _UfinterfaceBillBillheadBodysItemDef13_QNAME = new QName("", "def13");
    private final static QName _UfinterfaceBillBillheadBodysItemDef14_QNAME = new QName("", "def14");
    private final static QName _UfinterfaceBillBillheadBodysItemDef15_QNAME = new QName("", "def15");
    private final static QName _UfinterfaceBillBillheadBodysItemDef16_QNAME = new QName("", "def16");
    private final static QName _UfinterfaceBillBillheadBodysItemDef17_QNAME = new QName("", "def17");
    private final static QName _UfinterfaceBillBillheadBodysItemSupplier_QNAME = new QName("", "supplier");
    private final static QName _UfinterfaceBillBillheadPkBusitype_QNAME = new QName("", "pk_busitype");
    private final static QName _UfinterfaceBillBillheadMoney_QNAME = new QName("", "money");
    private final static QName _UfinterfaceBillBillheadRecaccount_QNAME = new QName("", "recaccount");
    private final static QName _UfinterfaceBillBillheadObjtype_QNAME = new QName("", "objtype");

    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.cloudpense.expman.entity.hsf.claim.payment
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link Ufinterface }
     */
    public Ufinterface createUfinterface() {
        return new Ufinterface();
    }

    /**
     * Create an instance of {@link Ufinterface.Bill }
     */
    public Ufinterface.Bill createUfinterfaceBill() {
        return new Ufinterface.Bill();
    }

    /**
     * Create an instance of {@link Ufinterface.Bill.Billhead }
     */
    public Ufinterface.Bill.Billhead createUfinterfaceBillBillhead() {
        return new Ufinterface.Bill.Billhead();
    }

    /**
     * Create an instance of {@link Ufinterface.Bill.Billhead.Bodys }
     */
    public Ufinterface.Bill.Billhead.Bodys createUfinterfaceBillBillheadBodys() {
        return new Ufinterface.Bill.Billhead.Bodys();
    }

    /**
     * Create an instance of {@link Ufinterface.Bill.Billhead.Bodys.Item }
     */
    public Ufinterface.Bill.Billhead.Bodys.Item createUfinterfaceBillBillheadBodysItem() {
        return new Ufinterface.Bill.Billhead.Bodys.Item();
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_group", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkGroup(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkGroup_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    @XmlElementDecl(namespace = "", name = "recaccount", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadRecaccount(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadRecaccount_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupnotax_de", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadGroupnotaxDe(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadGroupnotaxDe_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "local_money", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadLocalMoney(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadLocalMoney_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_tradetype", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkTradetype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkTradetype_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_currtype", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkCurrtype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkCurrtype_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link Ufinterface.Bill.Billhead.Bodys }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "bodys", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<Ufinterface.Bill.Billhead.Bodys> createUfinterfaceBillBillheadBodys(Ufinterface.Bill.Billhead.Bodys value) {
        return new JAXBElement<Ufinterface.Bill.Billhead.Bodys>(_UfinterfaceBillBillheadBodys_QNAME, Ufinterface.Bill.Billhead.Bodys.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_org", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkOrg(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkOrg_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "syscode", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadSyscode(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadSyscode_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_fiorg", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkFiorg(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkFiorg_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "billmaker", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBillmaker(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBillmaker_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "supplier", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadSupplier(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadSupplier_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "billdate", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBilldate(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBilldate_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "billno", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBillno(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBillno_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_billtype", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkBilltype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkBilltype_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_syscode", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadSrcSyscode(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadSrcSyscode_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_busitype", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadPkBusitype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkBusitype_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "money", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadMoney(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadMoney_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_group", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkGroup(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkGroup_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "notax_de", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemNotaxDe(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemNotaxDe_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "groupnotax_de", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemGroupnotaxDe(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadGroupnotaxDe_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_tradetype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkTradetype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkTradetype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_currtype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkCurrtype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkCurrtype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "objtype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemObjtype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemObjtype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_billid", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemSrcBillid(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemSrcBillid_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_org", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkOrg(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkOrg_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "supplier", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemSupplier(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadSupplier_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "top_billtype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemTopBilltype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemTopBilltype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_recpaytype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkRecpaytype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemPkRecpaytype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "money_de", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemMoneyDe(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemMoneyDe_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_payitem", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkPayitem(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemPkPayitem_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "prepay", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPrepay(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemPrepay_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_itemid", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemSrcItemid(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemSrcItemid_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_fiorg", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkFiorg(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkFiorg_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "billdate", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemBilldate(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBilldate_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_billtype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemSrcBilltype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemSrcBilltype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "src_tradetype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemSrcTradetype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemSrcTradetype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "billno", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemBillno(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBillno_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "pk_billtype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemPkBilltype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadPkBilltype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "top_itemid", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemTopItemid(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemTopItemid_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "top_billid", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemTopBillid(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemTopBillid_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "top_tradetype", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemTopTradetype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemTopTradetype_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "scomment", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemScomment(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemScomment_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "contractno", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemContractno(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemContractno_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "rate", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemRate(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemRate_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def12", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef12(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef12_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def13", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef13(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef13_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def14", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef14(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef14_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def15", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef15(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef15_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def16", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef16(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef16_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "def17", scope = Ufinterface.Bill.Billhead.Bodys.Item.class)
    public JAXBElement<String> createUfinterfaceBillBillheadBodysItemDef17(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadBodysItemDef17_QNAME, String.class, Ufinterface.Bill.Billhead.Bodys.Item.class, value);
    }

    /**
     * Create an instance of {@link JAXBElement }{@code <}{@link String }{@code >}}
     */
    @XmlElementDecl(namespace = "", name = "objtype", scope = Ufinterface.Bill.Billhead.class)
    public JAXBElement<String> createUfinterfaceBillBillheadObjtype(String value) {
        return new JAXBElement<String>(_UfinterfaceBillBillheadObjtype_QNAME, String.class, Ufinterface.Bill.Billhead.class, value);
    }

}
