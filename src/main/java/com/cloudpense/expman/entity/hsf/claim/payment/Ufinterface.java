
package com.cloudpense.expman.entity.hsf.claim.payment;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>anonymous complex type的 Java 类。
 *
 * <p>以下模式片段指定包含在此类中的预期内容。
 *
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="bill">
 *           &lt;complexType>
 *             &lt;complexContent>
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                 &lt;sequence>
 *                   &lt;element name="billhead">
 *                     &lt;complexType>
 *                       &lt;complexContent>
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                           &lt;choice maxOccurs="unbounded" minOccurs="0">
 *                             &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="pk_org">
 *                               &lt;simpleType>
 *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                   &lt;enumeration value="99100000"/>
 *                                   &lt;enumeration value="D3"/>
 *                                 &lt;/restriction>
 *                               &lt;/simpleType>
 *                             &lt;/element>
 *                             &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="local_money" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="supplier" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="src_syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="billmaker" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                             &lt;element name="bodys">
 *                               &lt;complexType>
 *                                 &lt;complexContent>
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                     &lt;sequence>
 *                                       &lt;element name="item" maxOccurs="unbounded" minOccurs="0">
 *                                         &lt;complexType>
 *                                           &lt;complexContent>
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *                                               &lt;choice maxOccurs="unbounded" minOccurs="0">
 *                                                 &lt;element name="pk_payitem" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_org">
 *                                                   &lt;simpleType>
 *                                                     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
 *                                                       &lt;enumeration value="99100000"/>
 *                                                       &lt;enumeration value="D3"/>
 *                                                     &lt;/restriction>
 *                                                   &lt;/simpleType>
 *                                                 &lt;/element>
 *                                                 &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="prepay" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="objtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="money_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="notax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="top_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="top_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="src_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="src_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="src_billid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="src_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                                 &lt;element name="pk_recpaytype" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *                                               &lt;/choice>
 *                                             &lt;/restriction>
 *                                           &lt;/complexContent>
 *                                         &lt;/complexType>
 *                                       &lt;/element>
 *                                     &lt;/sequence>
 *                                   &lt;/restriction>
 *                                 &lt;/complexContent>
 *                               &lt;/complexType>
 *                             &lt;/element>
 *                           &lt;/choice>
 *                         &lt;/restriction>
 *                       &lt;/complexContent>
 *                     &lt;/complexType>
 *                   &lt;/element>
 *                 &lt;/sequence>
 *                 &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}string" />
 *               &lt;/restriction>
 *             &lt;/complexContent>
 *           &lt;/complexType>
 *         &lt;/element>
 *       &lt;/sequence>
 *       &lt;attribute name="account" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="billtype" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="businessunitcode" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="filename" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="groupcode" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="isexchange" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="orgcode" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="receiver" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="replace" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="roottag" type="{http://www.w3.org/2001/XMLSchema}string" />
 *       &lt;attribute name="sender" type="{http://www.w3.org/2001/XMLSchema}string" />
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
        "bill"
})
@XmlRootElement(name = "ufinterface")
public class Ufinterface {

    @XmlElement(required = true)
    protected Ufinterface.Bill bill;
    @XmlAttribute(name = "account")
    protected String account;
    @XmlAttribute(name = "billtype")
    protected String billtype;
    @XmlAttribute(name = "businessunitcode")
    protected String businessunitcode;
    @XmlAttribute(name = "filename")
    protected String filename;
    @XmlAttribute(name = "groupcode")
    protected String groupcode;
    @XmlAttribute(name = "isexchange")
    protected String isexchange;
    @XmlAttribute(name = "orgcode")
    protected String orgcode;
    @XmlAttribute(name = "receiver")
    protected String receiver;
    @XmlAttribute(name = "replace")
    protected String replace;
    @XmlAttribute(name = "roottag")
    protected String roottag;
    @XmlAttribute(name = "sender")
    protected String sender;
    @XmlAttribute(name = "operator")
    protected String operator;

    /**
     * 获取bill属性的值。
     *
     * @return possible object is
     * {@link Ufinterface.Bill }
     */
    public Ufinterface.Bill getBill() {
        return bill;
    }

    /**
     * 设置bill属性的值。
     *
     * @param value allowed object is
     *              {@link Ufinterface.Bill }
     */
    public void setBill(Ufinterface.Bill value) {
        this.bill = value;
    }

    /**
     * 获取account属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getAccount() {
        return account;
    }

    /**
     * 设置account属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setAccount(String value) {
        this.account = value;
    }

    /**
     * 获取billtype属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBilltype() {
        return billtype;
    }

    /**
     * 设置billtype属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBilltype(String value) {
        this.billtype = value;
    }

    /**
     * 获取businessunitcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getBusinessunitcode() {
        return businessunitcode;
    }

    /**
     * 设置businessunitcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setBusinessunitcode(String value) {
        this.businessunitcode = value;
    }

    /**
     * 获取filename属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getFilename() {
        return filename;
    }

    /**
     * 设置filename属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setFilename(String value) {
        this.filename = value;
    }

    /**
     * 获取groupcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getGroupcode() {
        return groupcode;
    }

    /**
     * 设置groupcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setGroupcode(String value) {
        this.groupcode = value;
    }

    /**
     * 获取isexchange属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getIsexchange() {
        return isexchange;
    }

    /**
     * 设置isexchange属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setIsexchange(String value) {
        this.isexchange = value;
    }

    /**
     * 获取orgcode属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getOrgcode() {
        return orgcode;
    }

    /**
     * 设置orgcode属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setOrgcode(String value) {
        this.orgcode = value;
    }

    /**
     * 获取receiver属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getReceiver() {
        return receiver;
    }

    /**
     * 设置receiver属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReceiver(String value) {
        this.receiver = value;
    }

    /**
     * 获取replace属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getReplace() {
        return replace;
    }

    /**
     * 设置replace属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setReplace(String value) {
        this.replace = value;
    }

    /**
     * 获取roottag属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getRoottag() {
        return roottag;
    }

    /**
     * 设置roottag属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setRoottag(String value) {
        this.roottag = value;
    }

    /**
     * 获取sender属性的值。
     *
     * @return possible object is
     * {@link String }
     */
    public String getSender() {
        return sender;
    }

    /**
     * 设置sender属性的值。
     *
     * @param value allowed object is
     *              {@link String }
     */
    public void setSender(String value) {
        this.sender = value;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * <p>anonymous complex type的 Java 类。
     *
     * <p>以下模式片段指定包含在此类中的预期内容。
     *
     * <pre>
     * &lt;complexType>
     *   &lt;complexContent>
     *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *       &lt;sequence>
     *         &lt;element name="billhead">
     *           &lt;complexType>
     *             &lt;complexContent>
     *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                 &lt;choice maxOccurs="unbounded" minOccurs="0">
     *                   &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="pk_org">
     *                     &lt;simpleType>
     *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                         &lt;enumeration value="99100000"/>
     *                         &lt;enumeration value="D3"/>
     *                       &lt;/restriction>
     *                     &lt;/simpleType>
     *                   &lt;/element>
     *                   &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="local_money" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="supplier" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="src_syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="billmaker" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                   &lt;element name="bodys">
     *                     &lt;complexType>
     *                       &lt;complexContent>
     *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                           &lt;sequence>
     *                             &lt;element name="item" maxOccurs="unbounded" minOccurs="0">
     *                               &lt;complexType>
     *                                 &lt;complexContent>
     *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
     *                                     &lt;choice maxOccurs="unbounded" minOccurs="0">
     *                                       &lt;element name="pk_payitem" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_org">
     *                                         &lt;simpleType>
     *                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
     *                                             &lt;enumeration value="99100000"/>
     *                                             &lt;enumeration value="D3"/>
     *                                           &lt;/restriction>
     *                                         &lt;/simpleType>
     *                                       &lt;/element>
     *                                       &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="prepay" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="objtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="money_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="notax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="top_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="top_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="src_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="src_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="src_billid" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="src_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                       &lt;element name="pk_recpaytype" type="{http://www.w3.org/2001/XMLSchema}string"/>
     *                                     &lt;/choice>
     *                                   &lt;/restriction>
     *                                 &lt;/complexContent>
     *                               &lt;/complexType>
     *                             &lt;/element>
     *                           &lt;/sequence>
     *                         &lt;/restriction>
     *                       &lt;/complexContent>
     *                     &lt;/complexType>
     *                   &lt;/element>
     *                 &lt;/choice>
     *               &lt;/restriction>
     *             &lt;/complexContent>
     *           &lt;/complexType>
     *         &lt;/element>
     *       &lt;/sequence>
     *       &lt;attribute name="id" type="{http://www.w3.org/2001/XMLSchema}string" />
     *     &lt;/restriction>
     *   &lt;/complexContent>
     * &lt;/complexType>
     * </pre>
     */
    @XmlAccessorType(XmlAccessType.FIELD)
    @XmlType(name = "", propOrder = {
            "billhead"
    })
    public static class Bill {

        @XmlElement(required = true)
        protected Ufinterface.Bill.Billhead billhead;
        @XmlAttribute(name = "id")
        protected String id;

        /**
         * 获取billhead属性的值。
         *
         * @return possible object is
         * {@link Ufinterface.Bill.Billhead }
         */
        public Ufinterface.Bill.Billhead getBillhead() {
            return billhead;
        }

        /**
         * 设置billhead属性的值。
         *
         * @param value allowed object is
         *              {@link Ufinterface.Bill.Billhead }
         */
        public void setBillhead(Ufinterface.Bill.Billhead value) {
            this.billhead = value;
        }

        /**
         * 获取id属性的值。
         *
         * @return possible object is
         * {@link String }
         */
        public String getId() {
            return id;
        }

        /**
         * 设置id属性的值。
         *
         * @param value allowed object is
         *              {@link String }
         */
        public void setId(String value) {
            this.id = value;
        }


        /**
         * <p>anonymous complex type的 Java 类。
         *
         * <p>以下模式片段指定包含在此类中的预期内容。
         *
         * <pre>
         * &lt;complexType>
         *   &lt;complexContent>
         *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *       &lt;choice maxOccurs="unbounded" minOccurs="0">
         *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="pk_org">
         *           &lt;simpleType>
         *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *               &lt;enumeration value="99100000"/>
         *               &lt;enumeration value="D3"/>
         *             &lt;/restriction>
         *           &lt;/simpleType>
         *         &lt;/element>
         *         &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="local_money" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="supplier" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="src_syscode" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="billmaker" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *         &lt;element name="bodys">
         *           &lt;complexType>
         *             &lt;complexContent>
         *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                 &lt;sequence>
         *                   &lt;element name="item" maxOccurs="unbounded" minOccurs="0">
         *                     &lt;complexType>
         *                       &lt;complexContent>
         *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
         *                           &lt;choice maxOccurs="unbounded" minOccurs="0">
         *                             &lt;element name="pk_payitem" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_org">
         *                               &lt;simpleType>
         *                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
         *                                   &lt;enumeration value="99100000"/>
         *                                   &lt;enumeration value="D3"/>
         *                                 &lt;/restriction>
         *                               &lt;/simpleType>
         *                             &lt;/element>
         *                             &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="prepay" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="objtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="money_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="notax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="top_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="top_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="src_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="src_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="src_billid" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="src_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                             &lt;element name="pk_recpaytype" type="{http://www.w3.org/2001/XMLSchema}string"/>
         *                           &lt;/choice>
         *                         &lt;/restriction>
         *                       &lt;/complexContent>
         *                     &lt;/complexType>
         *                   &lt;/element>
         *                 &lt;/sequence>
         *               &lt;/restriction>
         *             &lt;/complexContent>
         *           &lt;/complexType>
         *         &lt;/element>
         *       &lt;/choice>
         *     &lt;/restriction>
         *   &lt;/complexContent>
         * &lt;/complexType>
         * </pre>
         */
        @XmlAccessorType(XmlAccessType.FIELD)
        @XmlType(name = "", propOrder = {
                "pkGroupOrPkFiorgOrBillno"
        })
        public static class Billhead {

            @XmlElementRefs({
                    @XmlElementRef(name = "src_syscode", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "groupnotax_de", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "billdate", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "supplier", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_group", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_currtype", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "bodys", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "billno", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_org", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_tradetype", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "billmaker", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "syscode", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_billtype", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_fiorg", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "local_money", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "pk_busitype", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "money", type = JAXBElement.class, required = false),
                    @XmlElementRef(name = "recaccount", type = JAXBElement.class, required = false)
            })
            protected List<JAXBElement<?>> pkGroupOrPkFiorgOrBillno;

            /**
             * Gets the value of the pkGroupOrPkFiorgOrBillno property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the JAXB object.
             * This is why there is not a <CODE>set</CODE> method for the pkGroupOrPkFiorgOrBillno property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getPkGroupOrPkFiorgOrBillno().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link Ufinterface.Bill.Billhead.Bodys }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             * {@link JAXBElement }{@code <}{@link String }{@code >}
             */
            public List<JAXBElement<?>> getPkGroupOrPkFiorgOrBillno() {
                if (pkGroupOrPkFiorgOrBillno == null) {
                    pkGroupOrPkFiorgOrBillno = new ArrayList<JAXBElement<?>>();
                }
                return this.pkGroupOrPkFiorgOrBillno;
            }


            /**
             * <p>anonymous complex type的 Java 类。
             *
             * <p>以下模式片段指定包含在此类中的预期内容。
             *
             * <pre>
             * &lt;complexType>
             *   &lt;complexContent>
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *       &lt;sequence>
             *         &lt;element name="item" maxOccurs="unbounded" minOccurs="0">
             *           &lt;complexType>
             *             &lt;complexContent>
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
             *                 &lt;choice maxOccurs="unbounded" minOccurs="0">
             *                   &lt;element name="pk_payitem" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_org">
             *                     &lt;simpleType>
             *                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
             *                         &lt;enumeration value="99100000"/>
             *                         &lt;enumeration value="D3"/>
             *                       &lt;/restriction>
             *                     &lt;/simpleType>
             *                   &lt;/element>
             *                   &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="prepay" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="objtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="money_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="notax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="top_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="top_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="src_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="src_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="src_billid" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="src_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                   &lt;element name="pk_recpaytype" type="{http://www.w3.org/2001/XMLSchema}string"/>
             *                 &lt;/choice>
             *               &lt;/restriction>
             *             &lt;/complexContent>
             *           &lt;/complexType>
             *         &lt;/element>
             *       &lt;/sequence>
             *     &lt;/restriction>
             *   &lt;/complexContent>
             * &lt;/complexType>
             * </pre>
             */
            @XmlAccessorType(XmlAccessType.FIELD)
            @XmlType(name = "", propOrder = {
                    "item"
            })
            public static class Bodys {

                protected List<Item> item;

                /**
                 * Gets the value of the item property.
                 *
                 * <p>
                 * This accessor method returns a reference to the live list,
                 * not a snapshot. Therefore any modification you make to the
                 * returned list will be present inside the JAXB object.
                 * This is why there is not a <CODE>set</CODE> method for the item property.
                 *
                 * <p>
                 * For example, to add a new item, do as follows:
                 * <pre>
                 *    getItem().add(newItem);
                 * </pre>
                 *
                 *
                 * <p>
                 * Objects of the following type(s) are allowed in the list
                 * {@link Ufinterface.Bill.Billhead.Bodys.Item }
                 */
                public List<Item> getItem() {
                    if (item == null) {
                        item = new ArrayList<Item>();
                    }
                    return this.item;
                }


                /**
                 * <p>anonymous complex type的 Java 类。
                 *
                 * <p>以下模式片段指定包含在此类中的预期内容。
                 *
                 * <pre>
                 * &lt;complexType>
                 *   &lt;complexContent>
                 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
                 *       &lt;choice maxOccurs="unbounded" minOccurs="0">
                 *         &lt;element name="pk_payitem" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_group" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_fiorg" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="billno" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_currtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_org">
                 *           &lt;simpleType>
                 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string">
                 *               &lt;enumeration value="99100000"/>
                 *               &lt;enumeration value="D3"/>
                 *             &lt;/restriction>
                 *           &lt;/simpleType>
                 *         &lt;/element>
                 *         &lt;element name="billdate" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="groupnotax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="prepay" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="objtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="money_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="notax_de" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="top_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="top_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="src_tradetype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="src_billtype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="src_billid" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="src_itemid" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *         &lt;element name="pk_recpaytype" type="{http://www.w3.org/2001/XMLSchema}string"/>
                 *       &lt;/choice>
                 *     &lt;/restriction>
                 *   &lt;/complexContent>
                 * &lt;/complexType>
                 * </pre>
                 */
                @XmlAccessorType(XmlAccessType.FIELD)
                @XmlType(name = "", propOrder = {
                        "pkPayitemOrPkGroupOrPkFiorg"
                })
                public static class Item {

                    @XmlElementRefs({
                            @XmlElementRef(name = "pk_group", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_tradetype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "groupnotax_de", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_org", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_billtype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_recpaytype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_currtype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "billdate", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "notax_de", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "billno", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "src_itemid", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "src_billid", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_payitem", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "src_billtype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "pk_fiorg", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "src_tradetype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "prepay", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "money_de", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "top_itemid", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "top_billtype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "top_billid", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "top_tradetype", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "scomment", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "contractno", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "rate", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def12", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def13", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def14", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def15", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def16", type = JAXBElement.class, required = false),
                            @XmlElementRef(name = "def17", type = JAXBElement.class, required = false)
                    })
                    protected List<JAXBElement<String>> pkPayitemOrPkGroupOrPkFiorg;

                    /**
                     * Gets the value of the pkPayitemOrPkGroupOrPkFiorg property.
                     *
                     * <p>
                     * This accessor method returns a reference to the live list,
                     * not a snapshot. Therefore any modification you make to the
                     * returned list will be present inside the JAXB object.
                     * This is why there is not a <CODE>set</CODE> method for the pkPayitemOrPkGroupOrPkFiorg property.
                     *
                     * <p>
                     * For example, to add a new item, do as follows:
                     * <pre>
                     *    getPkPayitemOrPkGroupOrPkFiorg().add(newItem);
                     * </pre>
                     *
                     *
                     * <p>
                     * Objects of the following type(s) are allowed in the list
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     * {@link JAXBElement }{@code <}{@link String }{@code >}
                     */
                    public List<JAXBElement<String>> getPkPayitemOrPkGroupOrPkFiorg() {
                        if (pkPayitemOrPkGroupOrPkFiorg == null) {
                            pkPayitemOrPkGroupOrPkFiorg = new ArrayList<JAXBElement<String>>();
                        }
                        return this.pkPayitemOrPkGroupOrPkFiorg;
                    }

                }

            }

        }

    }

}
