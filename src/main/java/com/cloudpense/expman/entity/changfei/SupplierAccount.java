package com.cloudpense.expman.entity.changfei;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/11/6 14:50
 */
@Data
public class SupplierAccount {

    private String column2;

    private String documentNum;

    private BigDecimal totalPayAmount;

    private Integer supplierAccountId;

    private String  accountNumber;

    private String accountName;

    private String bankName;

    private String bankBranch;

    private String state;

    private String city;

    private String bankHqCode;

    private String bankCode;

    private String bankAddress;

    private String linkHeaderId;
}
