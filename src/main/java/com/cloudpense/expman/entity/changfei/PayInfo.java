package com.cloudpense.expman.entity.changfei;

import lombok.Data;

import java.math.BigDecimal;

/**
 * create by liyu 2020/05/18
 */
@Data
public class PayInfo {

    private Integer headerId;

    private String documentNum;

    private String internalType;

    private BigDecimal advanceAmount;

    private BigDecimal totalPayAmount;

    private Integer bankAccountId;
    // 单据类型
    private String typeCode;
    // column48
    private String column48;
    // 币种
    private String currencyCode;
}
