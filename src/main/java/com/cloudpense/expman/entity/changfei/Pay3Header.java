package com.cloudpense.expman.entity.changfei;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019/12/5 14:26
 */
@Data
public class Pay3Header {

    private String typeCode;

    private String documentNum;

    private String column31;

    private BigDecimal total_pay_amount;

    private String column14;

    private String column44;

    private String headerId;

    private String column7;

    private Integer branchAccountId;

    private Date actualPaymentDate;

    private String paymentType;
    
    private String columnJson;

    private String column41;
    private String headerTypeCode;

}
