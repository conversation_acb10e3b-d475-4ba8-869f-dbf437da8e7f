package com.cloudpense.expman.entity.changfei;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2019/11/13 10:08
 */
@Data
public class FaPiaoHeaderInfo {

    private String ducumentNum;

    //发票号码
    private String column36;

    //发票类型
    private String column53;

    //供应商代码
    private String supplierCode;

    //供应商名称
    private String supplierName;

    //供应商地点
    private String supplierColumn1;

    //供应商集团标识
    private String supplierColumn5;

    //供应商ERP代码
    private String supplierColumn6;

    //付款方法
    private String column7;

    //付款条件
    private String column17;

    //支出类型
    private String column13;

    //币种
    private String currencyCode;

    //提单人
    private String chargeUser;

    //事由
    private String description;

    //合同编号
    private String column32;

    //财务编辑字段
    private String column42;

    //
    private String column15;

    //
    private String column20;

    //
    private String column47;

    //头id
    private String headerId;

    //单据类型
    private String typeCode;

    private String fdColumn4;

    private String fdCode;

    // redmine-27149
    private String column30;

    private String branchColumn1;

    // redmine-27149
    private String branchColumn4;

    // redmine-27149
    private String branchColumn5;

    private Integer branchId;

    private String submitUser;

    private String columnJson;

    private String column26;

    private Integer ledger1;
    private String ledgerName;

    private String submitDepartment;

    private String submitDepartmentColumn1;

    private String chargeDepartment;

    private String chargeDepartmentColumn1;

    private String projectCode;

    private String projectColumn3;

    private Integer linkHeaderId;

    private String glStatus;
}
