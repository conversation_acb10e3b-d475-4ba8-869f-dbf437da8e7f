package com.cloudpense.expman.entity.changfei;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *  2019/11/13 14:44
 */
@Data
public class FaPiaoLineInfo {

    private Date receiptDate;

    private BigDecimal finNetAmount;

    private BigDecimal taxAmount;

    private String comments;

    private String taxCode;

    private BigDecimal receiptAmount;

    private String aliasName;

    private String drAccountCode;

    private String taxAccountCode;

    private String typeCode;

    private String currencyCode;

    private String lineId;

    private String receiptAmount2;

    private String linkHeaderId;

    private String column30;

    private String column40;

    private Integer payMethodId;

    private Integer payMethodFactor;

    private String internalType;

    private String projectCode;

    private String projectColumn2;

    private String projectColumn3;

    private String column7;

    private BigDecimal quantity;

    private String column33;
    /**
     * 是否为PG项目费用行
     */
    private String column37;
    /**
     * 补充科目相关字段
     */
    private String column38;
    private String column39;
    private String columnJson;
    /**
     * 预扣税组
     */
    private String column28;
    /**
     * 原币不含税金额
     */
    private String column23;

    private String costCenter;

    private String costCenterColumn1;
    private String costCenterEnabledFlag;

    private String column4;

    private String budgetCode;
}
