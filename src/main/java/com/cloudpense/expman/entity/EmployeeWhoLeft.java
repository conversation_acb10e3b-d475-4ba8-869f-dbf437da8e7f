package com.cloudpense.expman.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;

public class EmployeeWhoLeft {
    @JSONField(name = "employee_number")
    @JsonProperty("employee_number")
    private String employeeCode;
    @J<PERSON>NField(name = "inactive_date")
    @JsonProperty("inactive_date")
    private String inactiveDate;

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getInactiveDate() {
        return inactiveDate;
    }

    public void setInactiveDate(String inactiveDate) {
        this.inactiveDate = inactiveDate;
    }
}
