package com.cloudpense.expman.entity;

import com.cloudpense.expman.util.View;
import com.fasterxml.jackson.annotation.JsonIdentityReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;


public class ExpClaimHeader implements Serializable {

    @JsonProperty(value = "header_id")
    private int headerId;

    @JsonProperty(value = "path_id")
    private int pathId;
    @NotNull(message = "should have company_id field.")

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "document_id")
    private int documentId;

    @JsonProperty(value = "language")
    private String language;

    @JsonProperty(value = "user_account_id")
    private int userAccountId;

    @JsonProperty(value = "supplier_account_id")
    private int supplierAccountId;
    @Length(max = 64, message = "document_num length should be less than 64.")
    @JsonProperty(value = "document_num")
    private String documentNum;

    @JsonProperty(value = "link_claim")
    private ExpClaimHeader linkClaim;
    @JsonProperty(value = "total_amount")
    private double totalAmount;
    @Range(message = "advance_amount should be more than or equal to 0.")

    @JsonProperty(value = "total_claim_amount")
    private double totalClaimAmount;

    @JsonProperty(value = "advance_amount")
    private double advanceAmount;
    @Length(max = 16, message = "currency_code length should be less than 16.")

    @JsonProperty(value = "currency_code")
    private String currencyCode;

    @JsonProperty(value = "submit_date")
    private Date submitDate;
//@NotNull(message = "should have start_datetime field.")

    @JsonProperty(value = "start_datetime")
    private Date startDatetime;
//@NotNull(message = "should have end_datetime field.")

    @JsonProperty(value = "end_datetime")
    private Date endDatetime;
    @Length(max = 255, message = "length should be less than or equal to 256.")

    @JsonProperty(value = "description")
    private String description;
    @Length(max = 16, message = "length should be less than or equal to 16.")

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "fin_status")
    private String financeStatus;

    @JsonProperty(value = "created_by")
    private int createdBy;

    @JsonProperty(value = "creator_name")
    private String creatorName;

    @JsonProperty(value = "creation_date")
    private Date creationDate;

    @JsonProperty(value = "last_updated_by")
    private int lastUpdatedBy;

    @JsonProperty(value = "last_update_date")
    private Date lastUpdateDate;

    @JsonProperty(value = "cost_element")
    private Collection<FndCostElement> costElements;
    @JsonProperty(value = "claim_line")
    private Collection<ExpClaimLine> claimLines;
    @JsonProperty(value = "attachment")
    private Collection<ExpClaimAttachment> attachments;
    @JsonProperty(value = "attachments")
    private Collection<ExpClaimAttachment> allAttachments;
    @JsonProperty(value = "workflow")
    private Collection<FndWorkflowPath> fndWorkflowPaths;
    @JsonProperty(value = "finance_workflow")
    private Collection<FndWorkflowPath> fndFinanceWorkflowPaths;

    @JsonProperty(value = "validation_messages")
    private Collection<String> validationMessages;
    @JsonIgnore
    private String returnCode;
    @JsonIgnore
    private String returnMessage;

    @JsonProperty(value = "total_pay_amount")
    private double totalPayAmount;
    @JsonProperty(value = "gl_batch_id")
    private int glBatchId;

    @JsonProperty(value = "link_header_id")
    private int linkHeaderId;
    private String note;

    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "column1")
    private String column1;

    @JsonProperty(value = "column2")
    private String column2;

    @JsonProperty(value = "column3")
    private String column3;

    @JsonProperty(value = "column4")
    private String column4;

    @JsonProperty(value = "column5")
    private String column5;

    @JsonProperty(value = "column6")
    private String column6;

    @JsonProperty(value = "column7")
    private String column7;

    @JsonProperty(value = "column8")
    private String column8;

    @JsonProperty(value = "column9")
    private String column9;

    @JsonProperty(value = "column10")
    private String column10;

    @JsonProperty(value = "column11")
    private String column11;

    @JsonProperty(value = "column12")
    private String column12;

    @JsonProperty(value = "column13")
    private String column13;

    @JsonProperty(value = "column14")
    private String column14;

    @JsonProperty(value = "column15")
    private String column15;

    @JsonProperty(value = "column16")
    private String column16;

    @JsonProperty(value = "column17")
    private String column17;

    @JsonProperty(value = "column18")
    private String column18;

    @JsonProperty(value = "column19")
    private String column19;

    @JsonProperty(value = "column20")
    private String column20;

    @JsonProperty(value = "column21")
    private String column21;

    @JsonProperty(value = "column22")
    private String column22;

    @JsonProperty(value = "column23")
    private String column23;

    @JsonProperty(value = "column24")
    private String column24;

    @JsonProperty(value = "column25")
    private String column25;

    @JsonProperty(value = "column26")
    private String column26;

    @JsonProperty(value = "column27")
    private String column27;

    @JsonProperty(value = "column28")
    private String column28;

    @JsonProperty(value = "column29")
    private String column29;

    @JsonProperty(value = "column30")
    private String column30;

    @JsonProperty(value = "column31")
    private String column31;

    @JsonProperty(value = "column32")
    private String column32;

    @JsonProperty(value = "column33")
    private String column33;

    @JsonProperty(value = "column34")
    private String column34;

    @JsonProperty(value = "column35")
    private String column35;

    @JsonProperty(value = "column36")
    private String column36;

    @JsonProperty(value = "column37")
    private String column37;

    @JsonProperty(value = "column38")
    private String column38;

    @JsonProperty(value = "column39")
    private String column39;

    @JsonProperty(value = "column40")
    private String column40;

    @JsonProperty(value = "column41")
    private String column41;

    @JsonProperty(value = "column42")
    private String column42;

    @JsonProperty(value = "column43")
    private String column43;

    @JsonProperty(value = "column44")
    private String column44;

    @JsonProperty(value = "column45")
    private String column45;

    @JsonProperty(value = "column46")
    private String column46;

    @JsonProperty(value = "column47")
    private String column47;

    @JsonProperty(value = "column48")
    private String column48;

    @JsonProperty(value = "column49")
    private String column49;

    @JsonProperty(value = "column50")
    private String column50;

    @JsonProperty(value = "budget_type")
    private String budgetType;

    @JsonProperty(value = "submit_department")
    private int submitDepartment;

    @JsonProperty(value = "charge_department")
    private int chargeDepartment;

    @JsonProperty(value = "submit_user")
    private int submitUser;

    @JsonProperty(value = "charge_user")
    private int chargeUser;

    @JsonProperty(value = "invoice_flag")
    private String invoiceFlag;

    @JsonProperty(value = "header_type_id")
    private int headerTypeId;

    @JsonProperty(value = "header_type_code")
    private String headerTypeCode;

    @JsonProperty(value = "payment_date")
    private Date paymentDate;

    @JsonProperty(value = "ignore_warning")
    private String ignoreWarning;

    @JsonProperty(value = "approver_list")
    private String approverList;

    @JsonProperty(value = "project_name")
    private String projectName;

    @JsonProperty(value = "supplier_id")
    private int supplierId;

    @JsonProperty(value = "path_status")
    private String pathStatus;

    @JsonProperty(value = "internal_type")
    private String internalType;

    @JsonProperty(value = "travel_method")
    private String travelMethod;

    @JsonProperty(value = "business_purpose")
    private String businessPurpose;

    @JsonProperty(value = "leave_type")
    private String leaveType;

    @JsonProperty(value = "leave_day")
    private double leaveDay;

    @JsonProperty(value = "product_name")
    private String productName;

    @JsonProperty(value = "customer_name")
    private String customerName;

    @JsonProperty(value = "due_date")
    private Date dueDate;

    @JsonProperty(value = "reminder_date")
    private Date reminderDate;

    @JsonProperty(value = "plan_start_datetime")
    private Date planStartDatetime;

    @JsonProperty(value = "plan_end_datetime")
    private Date planEndDatetime;

    @JsonProperty(value = "link_header")
    private ExpClaimHeaderLink linkHeader;

    @JsonProperty(value = "link_header_list")
    private List<Integer> linkHeaderList;

    @JsonProperty(value = "link_header_array")
    private Collection<ExpClaimHeaderLink> linkedHeaderArray;

    @JsonProperty(value = "linked_header")
    private Collection<ExpClaimHeaderLink> linkedHeader;

    @JsonProperty(value = "link_line")
    private Collection<ExpClaimHeaderLink> linkLine;

    @JsonProperty(value = "linked_line")
    private Collection<ExpClaimHeaderLink> linkedLine;

    @JsonProperty(value = "rule_name")
    private String ruleName;

    @JsonProperty(value = "priority")
    private String priority;

    @JsonProperty(value = "child_status")
    private String childStatus;

    @JsonProperty(value = "gl_status")
    private String glStatus;

    @JsonProperty(value = "gl_message")
    private String glMessage;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "journal_num")
    private String journalNum;

    @JsonProperty(value = "vouchers")
    private String vouchers;

    @JsonProperty(value = "agent_id")
    private int agentId;

    @JsonProperty(value = "finance_description")
    private String financeDescription;

    @JsonProperty(value = "branch_id")
    private int branchId;

    @JsonProperty(value = "pay_object")
    private String payObject;

    @JsonProperty(value = "pay_user")
    private int payUser;

    @JsonProperty(value = "branch_account_id")
    private int branchAccountId;

    @JsonProperty(value = "gl_period")
    private int glPeriod;

    @JsonProperty(value = "destination_city")
    private int destinationCity;

    @JsonProperty(value = "destination_city_to")
    private int destinationCityTo;

    @JsonProperty(value = "my_vesion")
    private int myVesion;

    private String input;

    private String externalStatus;
    private String externalMessage;
    private Date externalDate;

    @JsonProperty(value = "created_by2")
    private FndUser createdBy2;

    @JsonProperty(value = "last_updated_by2")
    private FndUser lastUpdatedBy2;

    @JsonProperty(value = "company_id2")
    private FndCompany fndCompany;

    @JsonProperty(value = "external")
    private String external;

    @JsonProperty(value = "my_version")
    private int myVersion;


    public String getExternal() {
        return external;
    }

    public ExpClaimHeader setExternal(String external) {
        this.external = external;
        return this;
    }

    public FndCompany getFndCompany() {
        return fndCompany;
    }

    public ExpClaimHeader setFndCompany(FndCompany fndCompany) {
        this.fndCompany = fndCompany;
        return this;
    }

    public FndUser getLastUpdatedBy2() {
        return lastUpdatedBy2;
    }

    public ExpClaimHeader setLastUpdatedBy2(FndUser lastUpdatedBy2) {
        this.lastUpdatedBy2 = lastUpdatedBy2;
        return this;
    }

    public int getMyVersion() {
        return myVersion;
    }

    public FndUser getCreatedBy2() {
        return createdBy2;
    }

    public ExpClaimHeader setCreatedBy2(FndUser createdBy2) {
        this.createdBy2 = createdBy2;
        return this;
    }

    public int getMyVerion() {
        return myVersion;
    }

    public void setMyVersion(int myVersion) {
        this.myVersion = myVersion;
    }
    public String getHeaderTypeCode() {
        return headerTypeCode;
    }

    public void setHeaderTypeCode(String headerTypeCode) {
        this.headerTypeCode = headerTypeCode;
    }
    public int getMyVesion() {
        return myVesion;
    }

    public void setMyVesion(int myVesion) {
        this.myVesion = myVesion;
    }

    public String getExternalMessage() {
        return externalMessage;
    }

    public void setExternalMessage(String externalMessage) {
        this.externalMessage = externalMessage;
    }

    public Date getExternalDate() {
        return externalDate;
    }

    public void setExternalDate(Date externalDate) {
        this.externalDate = externalDate;
    }

    public String getExternalStatus() {
        return externalStatus;
    }

    public void setExternalStatus(String externalStatus) {
        this.externalStatus = externalStatus;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public int getDestinationCity() {
        return destinationCity;
    }

    public void setDestinationCity(int destinationCity) {
        this.destinationCity = destinationCity;
    }

    public int getDestinationCityTo() {
        return destinationCityTo;
    }

    public void setDestinationCityTo(int destinationCityTo) {
        this.destinationCityTo = destinationCityTo;
    }

    public int getHeaderId() {
        return headerId;
    }

    public void setHeaderId(int headerId) {
        this.headerId = headerId;
    }

    public int getPathId() {
        return pathId;
    }

    public void setPathId(int pathId) {
        this.pathId = pathId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public int getDocumentId() {
        return documentId;
    }

    public void setDocumentId(int documentId) {
        this.documentId = documentId;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(int userAccountId) {
        this.userAccountId = userAccountId;
    }

    public int getSupplierAccountId() {
        return supplierAccountId;
    }

    public void setSupplierAccountId(int supplierAccountId) {
        this.supplierAccountId = supplierAccountId;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public void setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
    }

    public ExpClaimHeader getLinkClaim() {
        return linkClaim;
    }

    public void setLinkClaim(ExpClaimHeader linkClaim) {
        this.linkClaim = linkClaim;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public double getTotalClaimAmount() {
        return totalClaimAmount;
    }

    public void setTotalClaimAmount(double totalClaimAmount) {
        this.totalClaimAmount = totalClaimAmount;
    }

    public double getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(double advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Date getSubmitDate() {
        return submitDate;
    }

    public void setSubmitDate(Date submitDate) {
        this.submitDate = submitDate;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFinanceStatus() {
        return financeStatus;
    }

    public void setFinanceStatus(String financeStatus) {
        this.financeStatus = financeStatus;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public int getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(int lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public Collection<FndCostElement> getCostElements() {
        return costElements;
    }

    public void setCostElements(Collection<FndCostElement> costElements) {
        this.costElements = costElements;
    }

    public Collection<ExpClaimLine> getClaimLines() {
        return claimLines;
    }

    public void setClaimLines(Collection<ExpClaimLine> claimLines) {
        this.claimLines = claimLines;
    }

    public Collection<ExpClaimAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(Collection<ExpClaimAttachment> attachments) {
        this.attachments = attachments;
    }

    public Collection<ExpClaimAttachment> getAllAttachments() {
        return allAttachments;
    }

    public void setAllAttachments(Collection<ExpClaimAttachment> allAttachments) {
        this.allAttachments = allAttachments;
    }

    public Collection<FndWorkflowPath> getFndWorkflowPaths() {
        return fndWorkflowPaths;
    }

    public void setFndWorkflowPaths(Collection<FndWorkflowPath> fndWorkflowPaths) {
        this.fndWorkflowPaths = fndWorkflowPaths;
    }

    public Collection<FndWorkflowPath> getFndFinanceWorkflowPaths() {
        return fndFinanceWorkflowPaths;
    }

    public void setFndFinanceWorkflowPaths(Collection<FndWorkflowPath> fndFinanceWorkflowPaths) {
        this.fndFinanceWorkflowPaths = fndFinanceWorkflowPaths;
    }

    public Collection<String> getValidationMessages() {
        return validationMessages;
    }

    public void setValidationMessages(Collection<String> validationMessages) {
        this.validationMessages = validationMessages;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public double getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(double totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public int getGlBatchId() {
        return glBatchId;
    }

    public void setGlBatchId(int glBatchId) {
        this.glBatchId = glBatchId;
    }

    public int getLinkHeaderId() {
        return linkHeaderId;
    }

    public void setLinkHeaderId(int linkHeaderId) {
        this.linkHeaderId = linkHeaderId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getColumn1() {
        return column1;
    }

    public void setColumn1(String column1) {
        this.column1 = column1;
    }

    public String getColumn2() {
        return column2;
    }

    public void setColumn2(String column2) {
        this.column2 = column2;
    }

    public String getColumn3() {
        return column3;
    }

    public void setColumn3(String column3) {
        this.column3 = column3;
    }

    public String getColumn4() {
        return column4;
    }

    public void setColumn4(String column4) {
        this.column4 = column4;
    }

    public String getColumn5() {
        return column5;
    }

    public void setColumn5(String column5) {
        this.column5 = column5;
    }

    public String getColumn6() {
        return column6;
    }

    public void setColumn6(String column6) {
        this.column6 = column6;
    }

    public String getColumn7() {
        return column7;
    }

    public void setColumn7(String column7) {
        this.column7 = column7;
    }

    public String getColumn8() {
        return column8;
    }

    public void setColumn8(String column8) {
        this.column8 = column8;
    }

    public String getColumn9() {
        return column9;
    }

    public void setColumn9(String column9) {
        this.column9 = column9;
    }

    public String getColumn10() {
        return column10;
    }

    public void setColumn10(String column10) {
        this.column10 = column10;
    }

    public String getColumn11() {
        return column11;
    }

    public void setColumn11(String column11) {
        this.column11 = column11;
    }

    public String getColumn12() {
        return column12;
    }

    public void setColumn12(String column12) {
        this.column12 = column12;
    }

    public String getColumn13() {
        return column13;
    }

    public void setColumn13(String column13) {
        this.column13 = column13;
    }

    public String getColumn14() {
        return column14;
    }

    public void setColumn14(String column14) {
        this.column14 = column14;
    }

    public String getColumn15() {
        return column15;
    }

    public void setColumn15(String column15) {
        this.column15 = column15;
    }

    public String getColumn16() {
        return column16;
    }

    public void setColumn16(String column16) {
        this.column16 = column16;
    }

    public String getColumn17() {
        return column17;
    }

    public void setColumn17(String column17) {
        this.column17 = column17;
    }

    public String getColumn18() {
        return column18;
    }

    public void setColumn18(String column18) {
        this.column18 = column18;
    }

    public String getColumn19() {
        return column19;
    }

    public void setColumn19(String column19) {
        this.column19 = column19;
    }

    public String getColumn20() {
        return column20;
    }

    public void setColumn20(String column20) {
        this.column20 = column20;
    }

    public String getColumn21() {
        return column21;
    }

    public void setColumn21(String column21) {
        this.column21 = column21;
    }

    public String getColumn22() {
        return column22;
    }

    public void setColumn22(String column22) {
        this.column22 = column22;
    }

    public String getColumn23() {
        return column23;
    }

    public void setColumn23(String column23) {
        this.column23 = column23;
    }

    public String getColumn24() {
        return column24;
    }

    public void setColumn24(String column24) {
        this.column24 = column24;
    }

    public String getColumn25() {
        return column25;
    }

    public void setColumn25(String column25) {
        this.column25 = column25;
    }

    public String getColumn26() {
        return column26;
    }

    public void setColumn26(String column26) {
        this.column26 = column26;
    }

    public String getColumn27() {
        return column27;
    }

    public void setColumn27(String column27) {
        this.column27 = column27;
    }

    public String getColumn28() {
        return column28;
    }

    public void setColumn28(String column28) {
        this.column28 = column28;
    }

    public String getColumn29() {
        return column29;
    }

    public void setColumn29(String column29) {
        this.column29 = column29;
    }

    public String getColumn30() {
        return column30;
    }

    public void setColumn30(String column30) {
        this.column30 = column30;
    }

    public String getColumn31() {
        return column31;
    }

    public void setColumn31(String column31) {
        this.column31 = column31;
    }

    public String getColumn32() {
        return column32;
    }

    public void setColumn32(String column32) {
        this.column32 = column32;
    }

    public String getColumn33() {
        return column33;
    }

    public void setColumn33(String column33) {
        this.column33 = column33;
    }

    public String getColumn34() {
        return column34;
    }

    public void setColumn34(String column34) {
        this.column34 = column34;
    }

    public String getColumn35() {
        return column35;
    }

    public void setColumn35(String column35) {
        this.column35 = column35;
    }

    public String getColumn36() {
        return column36;
    }

    public void setColumn36(String column36) {
        this.column36 = column36;
    }

    public String getColumn37() {
        return column37;
    }

    public void setColumn37(String column37) {
        this.column37 = column37;
    }

    public String getColumn38() {
        return column38;
    }

    public void setColumn38(String column38) {
        this.column38 = column38;
    }

    public String getColumn39() {
        return column39;
    }

    public void setColumn39(String column39) {
        this.column39 = column39;
    }

    public String getColumn40() {
        return column40;
    }

    public void setColumn40(String column40) {
        this.column40 = column40;
    }

    public String getColumn41() {
        return column41;
    }

    public void setColumn41(String column41) {
        this.column41 = column41;
    }

    public String getColumn42() {
        return column42;
    }

    public void setColumn42(String column42) {
        this.column42 = column42;
    }

    public String getColumn43() {
        return column43;
    }

    public void setColumn43(String column43) {
        this.column43 = column43;
    }

    public String getColumn44() {
        return column44;
    }

    public void setColumn44(String column44) {
        this.column44 = column44;
    }

    public String getColumn45() {
        return column45;
    }

    public void setColumn45(String column45) {
        this.column45 = column45;
    }

    public String getColumn46() {
        return column46;
    }

    public void setColumn46(String column46) {
        this.column46 = column46;
    }

    public String getColumn47() {
        return column47;
    }

    public void setColumn47(String column47) {
        this.column47 = column47;
    }

    public String getColumn48() {
        return column48;
    }

    public void setColumn48(String column48) {
        this.column48 = column48;
    }

    public String getColumn49() {
        return column49;
    }

    public void setColumn49(String column49) {
        this.column49 = column49;
    }

    public String getColumn50() {
        return column50;
    }

    public void setColumn50(String column50) {
        this.column50 = column50;
    }

    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType;
    }

    public int getSubmitDepartment() {
        return submitDepartment;
    }

    public void setSubmitDepartment(int submitDepartment) {
        this.submitDepartment = submitDepartment;
    }

    public int getChargeDepartment() {
        return chargeDepartment;
    }

    public void setChargeDepartment(int chargeDepartment) {
        this.chargeDepartment = chargeDepartment;
    }

    public int getSubmitUser() {
        return submitUser;
    }

    public void setSubmitUser(int submitUser) {
        this.submitUser = submitUser;
    }

    public int getChargeUser() {
        return chargeUser;
    }

    public void setChargeUser(int chargeUser) {
        this.chargeUser = chargeUser;
    }

    public String getInvoiceFlag() {
        return invoiceFlag;
    }

    public void setInvoiceFlag(String invoiceFlag) {
        this.invoiceFlag = invoiceFlag;
    }

    public int getHeaderTypeId() {
        return headerTypeId;
    }

    public void setHeaderTypeId(int headerTypeId) {
        this.headerTypeId = headerTypeId;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getIgnoreWarning() {
        return ignoreWarning;
    }

    public void setIgnoreWarning(String ignoreWarning) {
        this.ignoreWarning = ignoreWarning;
    }

    public String getApproverList() {
        return approverList;
    }

    public void setApproverList(String approverList) {
        this.approverList = approverList;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public int getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(int supplierId) {
        this.supplierId = supplierId;
    }

    public String getPathStatus() {
        return pathStatus;
    }

    public void setPathStatus(String pathStatus) {
        this.pathStatus = pathStatus;
    }

    public String getInternalType() {
        return internalType;
    }

    public void setInternalType(String internalType) {
        this.internalType = internalType;
    }

    public String getTravelMethod() {
        return travelMethod;
    }

    public void setTravelMethod(String travelMethod) {
        this.travelMethod = travelMethod;
    }

    public String getBusinessPurpose() {
        return businessPurpose;
    }

    public void setBusinessPurpose(String businessPurpose) {
        this.businessPurpose = businessPurpose;
    }

    public String getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(String leaveType) {
        this.leaveType = leaveType;
    }

    public double getLeaveDay() {
        return leaveDay;
    }

    public void setLeaveDay(double leaveDay) {
        this.leaveDay = leaveDay;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public Date getReminderDate() {
        return reminderDate;
    }

    public void setReminderDate(Date reminderDate) {
        this.reminderDate = reminderDate;
    }

    public Date getPlanStartDatetime() {
        return planStartDatetime;
    }

    public void setPlanStartDatetime(Date planStartDatetime) {
        this.planStartDatetime = planStartDatetime;
    }

    public Date getPlanEndDatetime() {
        return planEndDatetime;
    }

    public void setPlanEndDatetime(Date planEndDatetime) {
        this.planEndDatetime = planEndDatetime;
    }

    public ExpClaimHeaderLink getLinkHeader() {
        return linkHeader;
    }

    public void setLinkHeader(ExpClaimHeaderLink linkHeader) {
        this.linkHeader = linkHeader;
    }

    public List<Integer> getLinkHeaderList() {
        return linkHeaderList;
    }

    public void setLinkHeaderList(List<Integer> linkHeaderList) {
        this.linkHeaderList = linkHeaderList;
    }

    public Collection<ExpClaimHeaderLink> getLinkedHeaderArray() {
        return linkedHeaderArray;
    }

    public void setLinkedHeaderArray(Collection<ExpClaimHeaderLink> linkedHeaderArray) {
        this.linkedHeaderArray = linkedHeaderArray;
    }

    public Collection<ExpClaimHeaderLink> getLinkedHeader() {
        return linkedHeader;
    }

    public void setLinkedHeader(Collection<ExpClaimHeaderLink> linkedHeader) {
        this.linkedHeader = linkedHeader;
    }

    public Collection<ExpClaimHeaderLink> getLinkLine() {
        return linkLine;
    }

    public void setLinkLine(Collection<ExpClaimHeaderLink> linkLine) {
        this.linkLine = linkLine;
    }

    public Collection<ExpClaimHeaderLink> getLinkedLine() {
        return linkedLine;
    }

    public void setLinkedLine(Collection<ExpClaimHeaderLink> linkedLine) {
        this.linkedLine = linkedLine;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getChildStatus() {
        return childStatus;
    }

    public void setChildStatus(String childStatus) {
        this.childStatus = childStatus;
    }

    public String getGlStatus() {
        return glStatus;
    }

    public void setGlStatus(String glStatus) {
        this.glStatus = glStatus;
    }

    public String getGlMessage() {
        return glMessage;
    }

    public void setGlMessage(String glMessage) {
        this.glMessage = glMessage;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getJournalNum() {
        return journalNum;
    }

    public void setJournalNum(String journalNum) {
        this.journalNum = journalNum;
    }

    public String getVouchers() {
        return vouchers;
    }

    public void setVouchers(String vouchers) {
        this.vouchers = vouchers;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public String getFinanceDescription() {
        return financeDescription;
    }

    public void setFinanceDescription(String financeDescription) {
        this.financeDescription = financeDescription;
    }

    public int getBranchId() {
        return branchId;
    }

    public void setBranchId(int branchId) {
        this.branchId = branchId;
    }

    public String getPayObject() {
        return payObject;
    }

    public void setPayObject(String payObject) {
        this.payObject = payObject;
    }

    public int getPayUser() {
        return payUser;
    }

    public void setPayUser(int payUser) {
        this.payUser = payUser;
    }

    public int getBranchAccountId() {
        return branchAccountId;
    }

    public void setBranchAccountId(int branchAccountId) {
        this.branchAccountId = branchAccountId;
    }

    public int getGlPeriod() {
        return glPeriod;
    }

    public void setGlPeriod(int glPeriod) {
        this.glPeriod = glPeriod;
    }
}
