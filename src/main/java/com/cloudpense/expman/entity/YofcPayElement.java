package com.cloudpense.expman.entity;

import java.math.BigDecimal;

public class YofcPayElement {
    private BigDecimal totalPayAmount;
    private String documentNum;
    private String receiveAccountNumber;
    private String receiveAccountName;
    private String departmentCode;
    private String receiveBankName;
    private String branchBankName;
    private String externalReceiveAccountNumber;
    private String externalReceiveAccountName;
    private String externalReceiveBankName;
    private String headerTypeCode;

    public BigDecimal getTotalPayAmount() {
        return totalPayAmount;
    }

    public void setTotalPayAmount(BigDecimal totalPayAmount) {
        this.totalPayAmount = totalPayAmount;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public void setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
    }

    public String getReceiveAccountNumber() {
        return receiveAccountNumber;
    }

    public void setReceiveAccountNumber(String receiveAccountNumber) {
        this.receiveAccountNumber = receiveAccountNumber;
    }

    public String getReceiveAccountName() {
        return receiveAccountName;
    }

    public void setReceiveAccountName(String receiveAccountName) {
        this.receiveAccountName = receiveAccountName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public String getReceiveBankName() {
        return receiveBankName;
    }

    public void setReceiveBankName(String receiveBankName) {
        this.receiveBankName = receiveBankName;
    }

    public String getBranchBankName() {
        return branchBankName;
    }

    public void setBranchBankName(String branchBankName) {
        this.branchBankName = branchBankName;
    }

    public String getExternalReceiveAccountNumber() {
        return externalReceiveAccountNumber;
    }

    public void setExternalReceiveAccountNumber(String externalReceiveAccountNumber) {
        this.externalReceiveAccountNumber = externalReceiveAccountNumber;
    }

    public String getExternalReceiveAccountName() {
        return externalReceiveAccountName;
    }

    public void setExternalReceiveAccountName(String externalReceiveAccountName) {
        this.externalReceiveAccountName = externalReceiveAccountName;
    }

    public String getExternalReceiveBankName() {
        return externalReceiveBankName;
    }

    public void setExternalReceiveBankName(String externalReceiveBankName) {
        this.externalReceiveBankName = externalReceiveBankName;
    }

    public String getHeaderTypeCode() {
        return headerTypeCode;
    }

    public void setHeaderTypeCode(String headerTypeCode) {
        this.headerTypeCode = headerTypeCode;
    }
}
