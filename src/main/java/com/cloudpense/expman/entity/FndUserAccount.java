package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.io.Serializable;

/**
 * Created by web on 16/11/15.
 */
//@JsonIdentityInfo(generator = ObjectIdGenerators.PropertyGenerator.class, property = "account_number")
public class FndUserAccount implements Serializable {

    @JsonProperty(value = "account_id")
    private int accountId;

    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "account_name")
    private String accountName;

    @JsonProperty(value = "account_number")
    private String accountNumber;

    @JsonProperty(value = "bank_name")
    private String bankName;

    @JsonProperty(value = "bank_branch")
    private String bankBranch;

    @JsonProperty(value = "bank_code")
    private String bankCode;

    @JsonProperty(value = "bank_swift_id")
    private String bankSwiftId;

    @JsonProperty(value = "bank_address")
    private String bankAddress;

    @JsonProperty(value = "state")
    private String state;

    @JsonProperty(value = "city")
    private String city;

    @JsonProperty(value = "enabled_flag")
    private String enabledFlag;

    @JsonProperty(value = "primary_flag")
    private String primaryFlag;

    public int getAccountId() {
        return accountId;
    }

    public void setAccountId(int accountId) {
        this.accountId = accountId;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankBranch() {
        return bankBranch;
    }

    public void setBankBranch(String bankBranch) {
        this.bankBranch = bankBranch;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankSwiftId() {
        return bankSwiftId;
    }

    public void setBankSwiftId(String bankSwiftId) {
        this.bankSwiftId = bankSwiftId;
    }

    public String getBankAddress() {
        return bankAddress;
    }

    public void setBankAddress(String bankAddress) {
        this.bankAddress = bankAddress;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getPrimaryFlag() {
        return primaryFlag;
    }

    public void setPrimaryFlag(String primaryFlag) {
        this.primaryFlag = primaryFlag;
    }
}
