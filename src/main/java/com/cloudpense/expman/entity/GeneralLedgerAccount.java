package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by Huiyi on 2015/6/28.
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeneralLedgerAccount {

    @JsonProperty(value = "company_id")
    private int companyId;

    @JsonProperty(value = "account_id")
    private int accountId;

    @JsonProperty(value = "account_code")
    private String accountCode;

    @JsonProperty(value = "account_name")
    private String accountName;

    @JsonProperty(value = "category")
    private String category;

    @JsonProperty(value = "alias_name")
    private String aliasName;

    @JsonProperty(value = "enabled_flag")
    private String enabledFlag;

    @JsonProperty(value = "language")
    private String language;

    @JsonProperty(value = "user_id")
    private int userId;

    @JsonProperty(value = "budget_id")
    private int budgetId;

    @JsonProperty(value = "branch_id")
    private int branchId;

    @JsonProperty(value = "input")
    private String input;

    @JsonProperty(value = "return_code")
    private String returnCode;

    @JsonProperty(value = "return_message")
    private String returnMessage;

    private int batch_upload_id;

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public int getAccountId() {
        return accountId;
    }

    public void setAccountId(int accountId) {
        this.accountId = accountId;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public String getEnabledFlag() {
        return enabledFlag;
    }

    public void setEnabledFlag(String enabledFlag) {
        this.enabledFlag = enabledFlag;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getUserId() {
        return userId;
    }

    public void setUserId(int userId) {
        this.userId = userId;
    }

    public String getReturnCode() {
        return returnCode;
    }

    public void setReturnCode(String returnCode) {
        this.returnCode = returnCode;
    }

    public String getReturnMessage() {
        return returnMessage;
    }

    public void setReturnMessage(String returnMessage) {
        this.returnMessage = returnMessage;
    }

    public int getBatch_upload_id() {
        return batch_upload_id;
    }

    public void setBatch_upload_id(int batch_upload_id) {
        this.batch_upload_id = batch_upload_id;
    }

    public int getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(int budgetId) {
        this.budgetId = budgetId;
    }

    public int getBranchId() {
        return branchId;
    }

    public void setBranchId(int branchId) {
        this.branchId = branchId;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }
}
