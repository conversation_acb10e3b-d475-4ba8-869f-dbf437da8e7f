package com.cloudpense.expman.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.Date;

/**
 * Created by futurefall on 2017/5/22.
 */
public class ExpClaimHeaderLink {
    
    @JsonProperty(value = "header_id")
    private int headerId;

    
    @JsonProperty(value = "document_id")
    private int documentId;

    
    @JsonProperty(value = "advance_amount")
    private double advanceAmount;
    
    @JsonProperty(value = "user_account_id")
    private int userAccountId;

    
    @JsonProperty(value = "supplier_account_id")
    private int supplierAccountId;


    
    @JsonProperty(value = "document_num")
    private String documentNum;

    
    @JsonProperty(value = "total_amount")
    private double totalAmount;

    
    @JsonProperty(value = "start_datetime")
    private Date startDatetime;

    
    @JsonProperty(value = "end_datetime")
    private Date endDatetime;

    
    @JsonProperty(value = "description")
    private String description;

    
    @JsonProperty(value = "status")
    private String status;

    
    @JsonProperty(value = "fin_status")
    private String financeStatus;


    
    @JsonProperty(value = "header_type_id")
    private int headerTypeId;

    
    @JsonProperty(value = "created_by")
    private int createdBy;

    
    @JsonProperty(value = "link_header_id")
    private int linkHeaderId;

    public int getHeaderId() {
        return headerId;
    }

    public void setHeaderId(int headerId) {
        this.headerId = headerId;
    }

    public int getDocumentId() {
        return documentId;
    }

    public void setDocumentId(int documentId) {
        this.documentId = documentId;
    }

    public int getUserAccountId() {
        return userAccountId;
    }

    public void setUserAccountId(int userAccountId) {
        this.userAccountId = userAccountId;
    }

    public int getSupplierAccountId() {
        return supplierAccountId;
    }

    public void setSupplierAccountId(int supplierAccountId) {
        this.supplierAccountId = supplierAccountId;
    }

    public String getDocumentNum() {
        return documentNum;
    }

    public void setDocumentNum(String documentNum) {
        this.documentNum = documentNum;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Date getStartDatetime() {
        return startDatetime;
    }

    public void setStartDatetime(Date startDatetime) {
        this.startDatetime = startDatetime;
    }

    public Date getEndDatetime() {
        return endDatetime;
    }

    public void setEndDatetime(Date endDatetime) {
        this.endDatetime = endDatetime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getFinanceStatus() {
        return financeStatus;
    }

    public void setFinanceStatus(String financeStatus) {
        this.financeStatus = financeStatus;
    }

    public int getHeaderTypeId() {
        return headerTypeId;
    }

    public void setHeaderTypeId(int headerTypeId) {
        this.headerTypeId = headerTypeId;
    }

    public double getAdvanceAmount() {
        return advanceAmount;
    }

    public void setAdvanceAmount(double advanceAmount) {
        this.advanceAmount = advanceAmount;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public int getLinkHeaderId() {
        return linkHeaderId;
    }

    public void setLinkHeaderId(int linkHeaderId) {
        this.linkHeaderId = linkHeaderId;
    }
}

