package com.cloudpense.expman.entity.sap;

public class POI01 {

    private String BSART;
    private String IHREZ;
    private String EBELN;
    private String LIFNR;
    private String NAME1;
    private String BEDAT;
    private String EKORG;
    private String EKGRP;
    private String WAERS;
    private String BUKRS;
    private String UNSEZ;
    private String FREE1;
    private String FREE2;
    private String FREE3;
    private String FREE4;
    private String FREE5;
    private String IHREZ_ITEM;
    private String MATNR;
    private String TXZ01;
    private String KNTTP;
    private String EBELP;
    private String MENGE;
    private String MEINS;
    private String EINDT;
    private String WERKS;
    private String NETPR;
    private String PEINH;
    private String SAKTO;
    private String KOSTL;
    private String AUFNR;
    private String PS_PSP_PNR;
    private String ANLN1;
    private String BEDNR;
    private String KONNR;
    private String KTPNR;
    private String MWSKZ;
    private String OPER;
    private String UNSEZ02;
    private String FREE102;
    private String FREE202;
    private String FREE302;
    private String FREE402;
    private String FREE502;
    private String EKPO_MATKL;

    public String getKTPNR() {
        return KTPNR;
    }

    public void setKTPNR(String KTPNR) {
        this.KTPNR = KTPNR;
    }

    public String getBSART() {
        return BSART;
    }

    public void setBSART(String BSART) {
        this.BSART = BSART;
    }

    public String getIHREZ() {
        return IHREZ;
    }

    public void setIHREZ(String IHREZ) {
        this.IHREZ = IHREZ;
    }

    public String getEBELN() {
        return EBELN;
    }

    public void setEBELN(String EBELN) {
        this.EBELN = EBELN;
    }

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getNAME1() {
        return NAME1;
    }

    public void setNAME1(String NAME1) {
        this.NAME1 = NAME1;
    }

    public String getBEDAT() {
        return BEDAT;
    }

    public void setBEDAT(String BEDAT) {
        this.BEDAT = BEDAT;
    }

    public String getEKORG() {
        return EKORG;
    }

    public void setEKORG(String EKORG) {
        this.EKORG = EKORG;
    }

    public String getEKGRP() {
        return EKGRP;
    }

    public void setEKGRP(String EKGRP) {
        this.EKGRP = EKGRP;
    }

    public String getWAERS() {
        return WAERS;
    }

    public void setWAERS(String WAERS) {
        this.WAERS = WAERS;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getUNSEZ() {
        return UNSEZ;
    }

    public void setUNSEZ(String UNSEZ) {
        this.UNSEZ = UNSEZ;
    }

    public String getFREE1() {
        return FREE1;
    }

    public void setFREE1(String FREE1) {
        this.FREE1 = FREE1;
    }

    public String getFREE2() {
        return FREE2;
    }

    public void setFREE2(String FREE2) {
        this.FREE2 = FREE2;
    }

    public String getFREE3() {
        return FREE3;
    }

    public void setFREE3(String FREE3) {
        this.FREE3 = FREE3;
    }

    public String getFREE4() {
        return FREE4;
    }

    public void setFREE4(String FREE4) {
        this.FREE4 = FREE4;
    }

    public String getFREE5() {
        return FREE5;
    }

    public void setFREE5(String FREE5) {
        this.FREE5 = FREE5;
    }

    public String getIHREZ_ITEM() {
        return IHREZ_ITEM;
    }

    public void setIHREZ_ITEM(String IHREZ_ITEM) {
        this.IHREZ_ITEM = IHREZ_ITEM;
    }

    public String getMATNR() {
        return MATNR;
    }

    public void setMATNR(String MATNR) {
        this.MATNR = MATNR;
    }

    public String getTXZ01() {
        return TXZ01;
    }

    public void setTXZ01(String TXZ01) {
        this.TXZ01 = TXZ01;
    }

    public String getKNTTP() {
        return KNTTP;
    }

    public void setKNTTP(String KNTTP) {
        this.KNTTP = KNTTP;
    }

    public String getEBELP() {
        return EBELP;
    }

    public void setEBELP(String EBELP) {
        this.EBELP = EBELP;
    }

    public String getMENGE() {
        return MENGE;
    }

    public void setMENGE(String MENGE) {
        this.MENGE = MENGE;
    }

    public String getMEINS() {
        return MEINS;
    }

    public void setMEINS(String MEINS) {
        this.MEINS = MEINS;
    }

    public String getEINDT() {
        return EINDT;
    }

    public void setEINDT(String EINDT) {
        this.EINDT = EINDT;
    }

    public String getWERKS() {
        return WERKS;
    }

    public void setWERKS(String WERKS) {
        this.WERKS = WERKS;
    }

    public String getNETPR() {
        return NETPR;
    }

    public void setNETPR(String NETPR) {
        this.NETPR = NETPR;
    }

    public String getPEINH() {
        return PEINH;
    }

    public void setPEINH(String PEINH) {
        this.PEINH = PEINH;
    }

    public String getSAKTO() {
        return SAKTO;
    }

    public void setSAKTO(String SAKTO) {
        this.SAKTO = SAKTO;
    }

    public String getKOSTL() {
        return KOSTL;
    }

    public void setKOSTL(String KOSTL) {
        this.KOSTL = KOSTL;
    }

    public String getAUFNR() {
        return AUFNR;
    }

    public void setAUFNR(String AUFNR) {
        this.AUFNR = AUFNR;
    }

    public String getPS_PSP_PNR() {
        return PS_PSP_PNR;
    }

    public void setPS_PSP_PNR(String PS_PSP_PNR) {
        this.PS_PSP_PNR = PS_PSP_PNR;
    }

    public String getANLN1() {
        return ANLN1;
    }

    public void setANLN1(String ANLN1) {
        this.ANLN1 = ANLN1;
    }

    public String getBEDNR() {
        return BEDNR;
    }

    public void setBEDNR(String BEDNR) {
        this.BEDNR = BEDNR;
    }

    public String getKONNR() {
        return KONNR;
    }

    public void setKONNR(String KONNR) {
        this.KONNR = KONNR;
    }

    public String getMWSKZ() {
        return MWSKZ;
    }

    public void setMWSKZ(String MWSKZ) {
        this.MWSKZ = MWSKZ;
    }

    public String getOPER() {
        return OPER;
    }

    public void setOPER(String OPER) {
        this.OPER = OPER;
    }

    public String getUNSEZ02() {
        return UNSEZ02;
    }

    public void setUNSEZ02(String UNSEZ02) {
        this.UNSEZ02 = UNSEZ02;
    }

    public String getFREE102() {
        return FREE102;
    }

    public void setFREE102(String FREE102) {
        this.FREE102 = FREE102;
    }

    public String getFREE202() {
        return FREE202;
    }

    public void setFREE202(String FREE202) {
        this.FREE202 = FREE202;
    }

    public String getFREE302() {
        return FREE302;
    }

    public void setFREE302(String FREE302) {
        this.FREE302 = FREE302;
    }

    public String getFREE402() {
        return FREE402;
    }

    public void setFREE402(String FREE402) {
        this.FREE402 = FREE402;
    }

    public String getFREE502() {
        return FREE502;
    }

    public void setFREE502(String FREE502) {
        this.FREE502 = FREE502;
    }

    public String getEKPO_MATKL() {
        return EKPO_MATKL;
    }

    public void setEKPO_MATKL(String EKPO_MATKL) {
        this.EKPO_MATKL = EKPO_MATKL;
    }
}
