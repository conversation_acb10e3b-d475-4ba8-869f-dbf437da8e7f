package com.cloudpense.expman.entity.sap;

public class FMI09 {
    private String ORDERID;
    private String ORDERTYPE;
    private String ORDERDSC;
    private String BUKRS;
    private String BLDAT;
    private String BUDAT;
    private String BLART;
    private String WAERS;
    private String KURSF;
    private String XBLNR;
    private String BKTXT;
    private String BUZEI;
    private String FLAG;
    private String BSCHL;
    private String HKONT;
    private String UMSKZ;
    private String DMBTR;
    private String WRBTR;
    private String AUFNR;
    private String KOSTL;
    private String WBS;
    private String XNEGP;
    private String ZUONR;
    private String SGTXT;
    private String ZFBDT;

    public String getORDERID() {
        return ORDERID;
    }

    public void setORDERID(String ORDERID) {
        this.ORDERID = ORDERID;
    }

    public String getORDERTYPE() {
        return ORDERTYPE;
    }

    public void setORDERTYPE(String ORDERTYPE) {
        this.ORDERTYPE = ORDERTYPE;
    }

    public String getORDERDSC() {
        return ORDERDSC;
    }

    public void setORDERDSC(String ORDERDSC) {
        this.ORDERDSC = ORDERDSC;
    }

    public String getBUKRS() {
        return BUKRS;
    }

    public void setBUKRS(String BUKRS) {
        this.BUKRS = BUKRS;
    }

    public String getBLDAT() {
        return BLDAT;
    }

    public void setBLDAT(String BLDAT) {
        this.BLDAT = BLDAT;
    }

    public String getBUDAT() {
        return BUDAT;
    }

    public void setBUDAT(String BUDAT) {
        this.BUDAT = BUDAT;
    }

    public String getBLART() {
        return BLART;
    }

    public void setBLART(String BLART) {
        this.BLART = BLART;
    }

    public String getWAERS() {
        return WAERS;
    }

    public void setWAERS(String WAERS) {
        this.WAERS = WAERS;
    }

    public String getKURSF() {
        return KURSF;
    }

    public void setKURSF(String KURSF) {
        this.KURSF = KURSF;
    }

    public String getXBLNR() {
        return XBLNR;
    }

    public void setXBLNR(String XBLNR) {
        this.XBLNR = XBLNR;
    }

    public String getBKTXT() {
        return BKTXT;
    }

    public void setBKTXT(String BKTXT) {
        this.BKTXT = BKTXT;
    }

    public String getBUZEI() {
        return BUZEI;
    }

    public void setBUZEI(String BUZEI) {
        this.BUZEI = BUZEI;
    }

    public String getFLAG() {
        return FLAG;
    }

    public void setFLAG(String FLAG) {
        this.FLAG = FLAG;
    }

    public String getBSCHL() {
        return BSCHL;
    }

    public void setBSCHL(String BSCHL) {
        this.BSCHL = BSCHL;
    }

    public String getHKONT() {
        return HKONT;
    }

    public void setHKONT(String HKONT) {
        this.HKONT = HKONT;
    }

    public String getUMSKZ() {
        return UMSKZ;
    }

    public void setUMSKZ(String UMSKZ) {
        this.UMSKZ = UMSKZ;
    }

    public String getDMBTR() {
        return DMBTR;
    }

    public void setDMBTR(String DMBTR) {
        this.DMBTR = DMBTR;
    }

    public String getWRBTR() {
        return WRBTR;
    }

    public void setWRBTR(String WRBTR) {
        this.WRBTR = WRBTR;
    }

    public String getAUFNR() {
        return AUFNR;
    }

    public void setAUFNR(String AUFNR) {
        this.AUFNR = AUFNR;
    }

    public String getKOSTL() {
        return KOSTL;
    }

    public void setKOSTL(String KOSTL) {
        this.KOSTL = KOSTL;
    }

    public String getWBS() {
        return WBS;
    }

    public void setWBS(String WBS) {
        this.WBS = WBS;
    }

    public String getXNEGP() {
        return XNEGP;
    }

    public void setXNEGP(String XNEGP) {
        this.XNEGP = XNEGP;
    }

    public String getZUONR() {
        return ZUONR;
    }

    public void setZUONR(String ZUONR) {
        this.ZUONR = ZUONR;
    }

    public String getSGTXT() {
        return SGTXT;
    }

    public void setSGTXT(String SGTXT) {
        this.SGTXT = SGTXT;
    }

    public String getZFBDT() {
        return ZFBDT;
    }

    public void setZFBDT(String ZFBDT) {
        this.ZFBDT = ZFBDT;
    }
}














