package com.cloudpense.expman.entity.sap;

public class CTI01 {
    private String OPERATING_LOGO;
    private String BSART;
    private String EKKO_EBELN_OA;
    private String EKKO_EBELN_SAP;
    private String EKKO_LIFNR;
    private String PROCUREMENT;
    private String PROCUREMENT_SECTION;
    private String EKKO_KDATB;
    private String EKKO_KDATE;
    private String EKPO_NETWR;
    private String CLAUSE1;
    private String CLAUSE2;
    private String EKKO_WAERS;
    private String COMPANY_PAY_CONDITION;
    private String EKPO_EBELP;
    private String EKPO_WERKS;
    private String EKPO_KNTTP;
    private String EKPO_MATNR;
    private String EKPO_TXZ01;
    private String EKPO_MENGE;
    private String EKPO_MEINS;
    private String EKKN_SAKTO;
    private String EKKN_KOSTL;
    private String EKKN_ANLN1;
    private String EKPO_MATKL;
    private String EKPO_NETPR;
    private String EKPO_PEINH;
    private String DEMAND_TRACING;
    private String MATERIAL_NUM;

    public String getOPERATING_LOGO() {
        return OPERATING_LOGO;
    }

    public void setOPERATING_LOGO(String OPERATING_LOGO) {
        this.OPERATING_LOGO = OPERATING_LOGO;
    }

    public String getBSART() {
        return BSART;
    }

    public void setBSART(String BSART) {
        this.BSART = BSART;
    }

    public String getEKKO_EBELN_OA() {
        return EKKO_EBELN_OA;
    }

    public void setEKKO_EBELN_OA(String EKKO_EBELN_OA) {
        this.EKKO_EBELN_OA = EKKO_EBELN_OA;
    }

    public String getEKKO_EBELN_SAP() {
        return EKKO_EBELN_SAP;
    }

    public void setEKKO_EBELN_SAP(String EKKO_EBELN_SAP) {
        this.EKKO_EBELN_SAP = EKKO_EBELN_SAP;
    }

    public String getEKKO_LIFNR() {
        return EKKO_LIFNR;
    }

    public void setEKKO_LIFNR(String EKKO_LIFNR) {
        this.EKKO_LIFNR = EKKO_LIFNR;
    }

    public String getPROCUREMENT() {
        return PROCUREMENT;
    }

    public void setPROCUREMENT(String PROCUREMENT) {
        this.PROCUREMENT = PROCUREMENT;
    }

    public String getPROCUREMENT_SECTION() {
        return PROCUREMENT_SECTION;
    }

    public void setPROCUREMENT_SECTION(String PROCUREMENT_SECTION) {
        this.PROCUREMENT_SECTION = PROCUREMENT_SECTION;
    }

    public String getEKKO_KDATB() {
        return EKKO_KDATB;
    }

    public void setEKKO_KDATB(String EKKO_KDATB) {
        this.EKKO_KDATB = EKKO_KDATB;
    }

    public String getEKKO_KDATE() {
        return EKKO_KDATE;
    }

    public void setEKKO_KDATE(String EKKO_KDATE) {
        this.EKKO_KDATE = EKKO_KDATE;
    }

    public String getEKPO_NETWR() {
        return EKPO_NETWR;
    }

    public void setEKPO_NETWR(String EKPO_NETWR) {
        this.EKPO_NETWR = EKPO_NETWR;
    }

    public String getCLAUSE1() {
        return CLAUSE1;
    }

    public void setCLAUSE1(String CLAUSE1) {
        this.CLAUSE1 = CLAUSE1;
    }

    public String getCLAUSE2() {
        return CLAUSE2;
    }

    public void setCLAUSE2(String CLAUSE2) {
        this.CLAUSE2 = CLAUSE2;
    }

    public String getEKKO_WAERS() {
        return EKKO_WAERS;
    }

    public void setEKKO_WAERS(String EKKO_WAERS) {
        this.EKKO_WAERS = EKKO_WAERS;
    }

    public String getCOMPANY_PAY_CONDITION() {
        return COMPANY_PAY_CONDITION;
    }

    public void setCOMPANY_PAY_CONDITION(String COMPANY_PAY_CONDITION) {
        this.COMPANY_PAY_CONDITION = COMPANY_PAY_CONDITION;
    }

    public String getEKPO_EBELP() {
        return EKPO_EBELP;
    }

    public void setEKPO_EBELP(String EKPO_EBELP) {
        this.EKPO_EBELP = EKPO_EBELP;
    }

    public String getEKPO_WERKS() {
        return EKPO_WERKS;
    }

    public void setEKPO_WERKS(String EKPO_WERKS) {
        this.EKPO_WERKS = EKPO_WERKS;
    }

    public String getEKPO_KNTTP() {
        return EKPO_KNTTP;
    }

    public void setEKPO_KNTTP(String EKPO_KNTTP) {
        this.EKPO_KNTTP = EKPO_KNTTP;
    }

    public String getEKPO_MATNR() {
        return EKPO_MATNR;
    }

    public void setEKPO_MATNR(String EKPO_MATNR) {
        this.EKPO_MATNR = EKPO_MATNR;
    }

    public String getEKPO_TXZ01() {
        return EKPO_TXZ01;
    }

    public void setEKPO_TXZ01(String EKPO_TXZ01) {
        this.EKPO_TXZ01 = EKPO_TXZ01;
    }

    public String getEKPO_MENGE() {
        return EKPO_MENGE;
    }

    public void setEKPO_MENGE(String EKPO_MENGE) {
        this.EKPO_MENGE = EKPO_MENGE;
    }

    public String getEKPO_MEINS() {
        return EKPO_MEINS;
    }

    public void setEKPO_MEINS(String EKPO_MEINS) {
        this.EKPO_MEINS = EKPO_MEINS;
    }

    public String getEKKN_SAKTO() {
        return EKKN_SAKTO;
    }

    public void setEKKN_SAKTO(String EKKN_SAKTO) {
        this.EKKN_SAKTO = EKKN_SAKTO;
    }

    public String getEKKN_KOSTL() {
        return EKKN_KOSTL;
    }

    public void setEKKN_KOSTL(String EKKN_KOSTL) {
        this.EKKN_KOSTL = EKKN_KOSTL;
    }

    public String getEKKN_ANLN1() {
        return EKKN_ANLN1;
    }

    public void setEKKN_ANLN1(String EKKN_ANLN1) {
        this.EKKN_ANLN1 = EKKN_ANLN1;
    }

    public String getEKPO_MATKL() {
        return EKPO_MATKL;
    }

    public void setEKPO_MATKL(String EKPO_MATKL) {
        this.EKPO_MATKL = EKPO_MATKL;
    }

    public String getEKPO_NETPR() {
        return EKPO_NETPR;
    }

    public void setEKPO_NETPR(String EKPO_NETPR) {
        this.EKPO_NETPR = EKPO_NETPR;
    }

    public String getEKPO_PEINH() {
        return EKPO_PEINH;
    }

    public void setEKPO_PEINH(String EKPO_PEINH) {
        this.EKPO_PEINH = EKPO_PEINH;
    }

    public String getDEMAND_TRACING() {
        return DEMAND_TRACING;
    }

    public void setDEMAND_TRACING(String DEMAND_TRACING) {
        this.DEMAND_TRACING = DEMAND_TRACING;
    }

    public String getMATERIAL_NUM() {
        return MATERIAL_NUM;
    }

    public void setMATERIAL_NUM(String MATERIAL_NUM) {
        this.MATERIAL_NUM = MATERIAL_NUM;
    }
}
