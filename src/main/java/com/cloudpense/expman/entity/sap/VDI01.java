package com.cloudpense.expman.entity.sap;

public class VDI01 {
    private String LIFNR ;
    private String COMPANY ;
    private String ADDR1_DATA_NAME1 ;
    private String SMTP_ADDR ;
    private String MOB_NUMBER ;
    private String ADDR3_DATA_DEPARTMENT ;
    private String LFBK_BANKN ;
    private String LFBK_BANKL;
    private String BNKA_BANKA;
    private String IF_DELETE;

    public String getLIFNR() {
        return LIFNR;
    }

    public void setLIFNR(String LIFNR) {
        this.LIFNR = LIFNR;
    }

    public String getCOMPANY() {
        return COMPANY;
    }

    public void setCOMPANY(String COMPANY) {
        this.COMPANY = COMPANY;
    }

    public String getADDR1_DATA_NAME1() {
        return ADDR1_DATA_NAME1;
    }

    public void setADDR1_DATA_NAME1(String ADDR1_DATA_NAME1) {
        this.ADDR1_DATA_NAME1 = ADDR1_DATA_NAME1;
    }

    public String getSMTP_ADDR() {
        return SMTP_ADDR;
    }

    public void setSMTP_ADDR(String SMTP_ADDR) {
        this.SMTP_ADDR = SMTP_ADDR;
    }

    public String getMOB_NUMBER() {
        return MOB_NUMBER;
    }

    public void setMOB_NUMBER(String MOB_NUMBER) {
        this.MOB_NUMBER = MOB_NUMBER;
    }

    public String getADDR3_DATA_DEPARTMENT() {
        return ADDR3_DATA_DEPARTMENT;
    }

    public void setADDR3_DATA_DEPARTMENT(String ADDR3_DATA_DEPARTMENT) {
        this.ADDR3_DATA_DEPARTMENT = ADDR3_DATA_DEPARTMENT;
    }

    public String getLFBK_BANKN() {
        return LFBK_BANKN;
    }

    public void setLFBK_BANKN(String LFBK_BANKN) {
        this.LFBK_BANKN = LFBK_BANKN;
    }

    public String getLFBK_BANKL() {
        return LFBK_BANKL;
    }

    public void setLFBK_BANKL(String LFBK_BANKL) {
        this.LFBK_BANKL = LFBK_BANKL;
    }

    public String getBNKA_BANKA() {
        return BNKA_BANKA;
    }

    public void setBNKA_BANKA(String BNKA_BANKA) {
        this.BNKA_BANKA = BNKA_BANKA;
    }

    public String getIF_DELETE() {
        return IF_DELETE;
    }

    public void setIF_DELETE(String IF_DELETE) {
        this.IF_DELETE = IF_DELETE;
    }
}
