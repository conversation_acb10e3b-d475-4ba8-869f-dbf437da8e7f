package com.cloudpense.expman.entity.sap;

public class PRI01 {

    private String Type;
    private String SAPSN;
    private String NB;
    private String SN;
    private String Factory;
    private String No;
    private String Type2;
    private String Material;
    private String Item;
    private String Quantity;
    private String ApplyUnit;
    private String Price;
    private String Currency;
    private String UI;
    private String Unit;
    private String WBS;
    private String CostCenter;
    private String RequestDate;
    private String DeliveryDate;
    private String DeliverAddress;
    private String AccountCode;
    private String Asset_Number;
    private String AGREEMENT;
    private String AGMT_ITEM;
    private String ApplicantDept;
    private String Applicant;
    private String Longtext;
    private String Comments;
    private String PurchGrp;
    private String EKPO_MATKL;

    public String getAsset_Number() {
        return Asset_Number;
    }

    public void setAsset_Number(String asset_Number) {
        Asset_Number = asset_Number;
    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        Type = type;
    }

    public String getSAPSN() {
        return SAPSN;
    }

    public void setSAPSN(String SAPSN) {
        this.SAPSN = SAPSN;
    }

    public String getNB() {
        return NB;
    }

    public void setNB(String NB) {
        this.NB = NB;
    }

    public String getSN() {
        return SN;
    }

    public void setSN(String SN) {
        this.SN = SN;
    }

    public String getFactory() {
        return Factory;
    }

    public void setFactory(String factory) {
        Factory = factory;
    }

    public String getNo() {
        return No;
    }

    public void setNo(String no) {
        No = no;
    }

    public String getType2() {
        return Type2;
    }

    public void setType2(String type2) {
        Type2 = type2;
    }

    public String getMaterial() {
        return Material;
    }

    public void setMaterial(String material) {
        Material = material;
    }

    public String getItem() {
        return Item;
    }

    public void setItem(String item) {
        Item = item;
    }

    public String getQuantity() {
        return Quantity;
    }

    public void setQuantity(String quantity) {
        Quantity = quantity;
    }

    public String getApplyUnit() {
        return ApplyUnit;
    }

    public void setApplyUnit(String applyUnit) {
        ApplyUnit = applyUnit;
    }

    public String getPrice() {
        return Price;
    }

    public void setPrice(String price) {
        Price = price;
    }

    public String getCurrency() {
        return Currency;
    }

    public void setCurrency(String currency) {
        Currency = currency;
    }

    public String getUI() {
        return UI;
    }

    public void setUI(String UI) {
        this.UI = UI;
    }

    public String getUnit() {
        return Unit;
    }

    public void setUnit(String unit) {
        Unit = unit;
    }

    public String getWBS() {
        return WBS;
    }

    public void setWBS(String WBS) {
        this.WBS = WBS;
    }

    public String getCostCenter() {
        return CostCenter;
    }

    public void setCostCenter(String costCenter) {
        CostCenter = costCenter;
    }

    public String getRequestDate() {
        return RequestDate;
    }

    public void setRequestDate(String requestDate) {
        RequestDate = requestDate;
    }

    public String getDeliveryDate() {
        return DeliveryDate;
    }

    public void setDeliveryDate(String deliveryDate) {
        DeliveryDate = deliveryDate;
    }

    public String getDeliverAddress() {
        return DeliverAddress;
    }

    public void setDeliverAddress(String deliverAddress) {
        DeliverAddress = deliverAddress;
    }

    public String getAccountCode() {
        return AccountCode;
    }

    public void setAccountCode(String accountCode) {
        AccountCode = accountCode;
    }

    public String getAGREEMENT() {
        return AGREEMENT;
    }

    public void setAGREEMENT(String AGREEMENT) {
        this.AGREEMENT = AGREEMENT;
    }

    public String getAGMT_ITEM() {
        return AGMT_ITEM;
    }

    public void setAGMT_ITEM(String AGMT_ITEM) {
        this.AGMT_ITEM = AGMT_ITEM;
    }

    public String getApplicantDept() {
        return ApplicantDept;
    }

    public void setApplicantDept(String applicantDept) {
        ApplicantDept = applicantDept;
    }

    public String getApplicant() {
        return Applicant;
    }

    public void setApplicant(String applicant) {
        Applicant = applicant;
    }

    public String getLongtext() {
        return Longtext;
    }

    public void setLongtext(String longtext) {
        Longtext = longtext;
    }

    public String getComments() {
        return Comments;
    }

    public void setComments(String comments) {
        Comments = comments;
    }

    public String getPurchGrp() {
        return PurchGrp;
    }

    public void setPurchGrp(String purchGrp) {
        PurchGrp = purchGrp;
    }

    public String getEKPO_MATKL() {
        return EKPO_MATKL;
    }

    public void setEKPO_MATKL(String EKPO_MATKL) {
        this.EKPO_MATKL = EKPO_MATKL;
    }
}
