package com.cloudpense.expman.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ResourceBundle;

//@Configuration
public class RabbitmqConfig {

    private ResourceBundle rb = ResourceBundle.getBundle("mq");

    private String host = rb.getString("spring.rabbitmq.host");

    private int port = Integer.valueOf(rb.getString("spring.rabbitmq.port"));

    private String username = rb.getString("spring.rabbitmq.username");

    private String password = rb.getString("spring.rabbitmq.password");

    private String virtualhost = rb.getString("spring.rabbitmq.virtual-host");

    @Bean
    public CachingConnectionFactory connectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(host, port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setVirtualHost(virtualhost);
        return connectionFactory;
    }

    @Bean
    public RabbitTemplate simpleRabbitTemplate() {
        RabbitTemplate template = new RabbitTemplate(connectionFactory());
        return template;
    }


}
