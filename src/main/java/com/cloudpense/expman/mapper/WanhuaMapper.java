package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface WanhuaMapper {
    public List<String> glBatchCreateWanhua(GlBatchTransfer glBatchTransfer);

    public List<String> getOaPushTask(@Param(value = "companyId") int companyId);

    public List<String> getWechatPushTask(@Param(value = "companyId") int companyId);

    public List<Integer> scanWanhuaVoucherPost(@Param(value = "positionId") int positionId,
                                               @Param(value = "companyId") int companyId);

    public void commonUpdate(FndQuery fndQuery);

    public List<Map<String, String>> getBudgetTodo(@Param(value = "companyId") int companyId);

    public void setBudgetPosted(@Param(value = "lockStatus") String lockStatus,
                                @Param(value = "lockMessage") String lockMessage,
                                @Param(value = "documentNum") String documentNum,
                                @Param(value = "companyId") int companyId);

    Integer updateOaPushStatusByDocumentNums(@Param("documentNums") ArrayList<String> documentNums, @Param("status") String status);

    Integer deletePortalLog(@Param("headerIds") ArrayList<Integer> headerIds);

}
