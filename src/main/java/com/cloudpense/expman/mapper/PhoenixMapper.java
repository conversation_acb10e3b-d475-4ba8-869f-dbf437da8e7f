package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import com.cloudpense.expman.entity.PhoenixPush;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;


public interface PhoenixMapper {
    public int savePhoenixInvoice(FndQuery fndQuery);

    public Collection<String> phoenixPr(FndQuery fndQuery);

    public void updatePushStatus(PhoenixPush phoenixPush);

    public Collection<PhoenixPush> getPushTask(PhoenixPush phoenixPush);

    public List<String> glBatchCreatePhoenix(GlBatchTransfer glBatchTransfer);

    public String[] syncCompanyCode(int companyId);

    public void calculateDepartment(int companyId);

    public int getUsedLeaveDay(int submitUser);

    public void baanTest();

    @Select("SELECT type_code from exp_header_type where type_id = #{headerTypeId} limit 1 ")
    String getTypeCode(@Param("headerTypeId") String headerTypeId);

    List<Integer> getPendingDocumentIds(@Param("documentIds") List<Integer> documentIds);
}
