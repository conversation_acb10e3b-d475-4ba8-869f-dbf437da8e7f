<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.cloudpense.expman.mapper.WshhMapper">

    <!-- 查询单据头（已提交、业务审批通过、财务审核通过、审批通过和已支付） -->
    <select id="selectExpClaimHeader" resultType="com.cloudpense.expman.Request.WshhClaimPushReq">
        select h.header_id,h.document_num,h.description,h.total_amount,u.employee_number submit_user,
          d.department_code charge_department,p.project_code project_name,h.column18,h.column20,h.status
        from exp_claim_header h left join exp_header_type t on h.header_type_id = t.type_id
          left join fnd_user u on h.submit_user = u.user_id
          left join fnd_department d on h.charge_department = d.department_id
          left join pjm_project p on h.project_name = p.project_id
          where h.company_id = 16907 and t.company_id = 16907 and t.type_code = 'T001'
            and h.status in ('submitted','preapproved','approved','checked','closed')
    </select>

    <!-- 查询单据行 -->
    <select id="selectExpClaimLine" resultType="ExpClaimLine" parameterType="java.lang.Integer">
        select receipt_date receiptDate, receipt_amount receiptAmount, receipt_currency receiptCurrency,
          exchange_rate exchangeRate, tax_code_id taxCodeId, tax_amount taxAmount,fin_tax_amount finTaxAmount,
          comments, dr_account_id drAccountId, cr_account_id crAccountId, tax_account_id taxAccountId,
          fin_tax_code_id finTaxCodeId, fin_receipt_amount finReceiptAmount
        from exp_claim_line
        where header_id = #{header_id}
    </select>

    <!-- 查询单据头信息 -->
    <select id="getExpClaimHeader" resultType="Map">
        SELECT
            h.header_id headerId,
            IFNULL(h.document_num,'') documentNum,
            IFNULL(h.description,'') description,
            IFNULL(u.employee_number,'') employeeNumber,
            IFNULL(u.full_name,'') fullName,
            IFNULL(c.department_code,'') companyCode,
            IFNULL(c.department_name,'') companyName,
            IFNULL(d.department_code,'') chargeDepartment,
            REPLACE(pt.project_name,RIGHT(pt.project_name,11),'') projectName,
            IFNULL(h.column11,'') column11,
            IFNULL(h.column18,'') column18,
            IFNULL(h.column20,'') column20,
            IFNULL(h.STATUS,'') status,
            IFNULL(DATE_FORMAT(DATE_ADD(fwpp.last_update_date,INTERVAL 8 HOUR),'%Y/%m/%d'),'') preapprovedDate,
			IFNULL(DATE_FORMAT(DATE_ADD(fwpc.last_update_date,INTERVAL 8 HOUR),'%Y/%m/%d'),'') checkedDate,
			IFNULL(DATE_FORMAT(DATE_ADD(h.start_datetime,INTERVAL 8 HOUR),'%Y/%m/%d'),'') startDate,
			IFNULL(DATE_FORMAT(DATE_ADD(h.end_datetime,INTERVAL 8 HOUR),'%Y/%m/%d'),'') endDate,
			IFNULL(DATE_FORMAT(DATE_ADD(h.submit_date,INTERVAL 8 HOUR),'%Y/%m/%d'),'') submitDate,
            IFNULL(h.currency_code,'') currencyCode,
            IFNULL(h.total_claim_amount,'') totalClaimAmount,
            IFNULL(fua.bank_name,'') bankName,
            IFNULL(fua.account_number,'') accountNumber,
            IFNULL(u.user_name,'') userName,
            IFNULL(u.mobile,'') mobile,
            IFNULL(cu.full_name,'') checkedBy
        FROM
            exp_claim_header h
            LEFT JOIN exp_header_type t ON h.header_type_id = t.type_id
            LEFT JOIN fnd_user u ON h.created_by = u.user_id
            LEFT JOIN fnd_department d ON h.charge_department = d.department_id
            LEFT JOIN pjm_project p ON h.project_name = p.project_id
			LEFT JOIN pjm_project_tl pt ON h.project_name = pt.project_id and pt.`language` = 'zh_CN'
            LEFT JOIN fnd_department_v c ON h.branch_id = c.department_id and c.`language` = 'zh_CN'
            LEFT JOIN fnd_workflow_path fwpp ON h.header_id = fwpp.source_id
            LEFT JOIN fnd_workflow_path fwpc ON h.header_id = fwpc.source_id
            LEFT JOIN fnd_user cu ON fwpc.user_id = cu.user_id
			LEFT JOIN fnd_user_account fua ON u.user_id = fua.user_id and fua.enabled_flag = 'Y'
        WHERE
            h.company_id = 16907
            AND t.company_id = 16907
            AND t.type_code = 'T001'
            AND h.STATUS IN ( 'withdrawed', 'preapproved', 'approved', 'checked', 'deleted', 'cancelled')
            AND fwpp.type = 'P'
            AND fwpc.type = 'A'
            and DATE_ADD(h.last_update_date,INTERVAL 8 HOUR) BETWEEN #{pushDate} and DATE_ADD(#{pushDateTo},INTERVAL 24 HOUR) GROUP BY h.header_id
    </select>

    <!-- 查询单据行信息 -->
    <select id="getExpClaimLine" resultType="Map">
       SELECT
            l.line_id lineId,
            IFNULL(l.comments,'') comments,
            IFNULL(a.account_code,'') accountCode,
            IFNULL(t.type_code,'') typeCode,
            IFNULL(tl.type,'') typeName,
            IFNULL(l.claim_currency,'') currencyCode,
            IFNULL(l.receipt_currency,'') receiptCurrency,
            IFNULL(l.exchange_rate,0) exchangeRate,
            IFNULL(l.receipt_amount,0) receiptAmount,
            IFNULL(l.net_amount,0) netAmount,
            IFNULL(l.tax_amount,0) taxAmount,
            IFNULL(l.claim_amount,0) claimAmount,
            IFNULL(l.fin_claim_amount,0) finClaimAmount,
            IFNULL(l.fin_tax_amount,0) finTaxAmount,
            IFNULL(c.tax_code,'') finTaxCode,
            IFNULL(ta.account_code,'') finTaxAccount,
            case when l.receipt_date is not null then CONVERT_TZ(l.receipt_date,"+00:00","+08:00") else '' end receiptDate,
            IFNULL(fct.city_name,'') destinationCity,
            IFNULL(fct2.city_name,'') destinationCityTo,
            case when l.column8 is not null then CONVERT_TZ(FROM_UNIXTIME(left(l.column8, char_length(l.column8) - 3)),"+00:00","+08:00") else '' end column8,
            case when l.column11 is not null then CONVERT_TZ(FROM_UNIXTIME(left(l.column11, char_length(l.column11) - 3)),"+00:00","+08:00") else '' end column11,
            IFNULL(l.price,'') price,
            IFNULL(l.column9,'') column9,
            IFNULL((SELECT v.value_meaning FROM fnd_lov_value_v v, fnd_lov fl
                    WHERE  v.lov_id = fl.lov_id AND fl.company_id = 16907 AND fl.lov_name = 'Train Class'
                    AND    v.language = 'zh_CN' AND v.value_code = l.train_class LIMIT 1),'') trainClass
        FROM
            exp_claim_line l
            LEFT JOIN gl_account a ON a.account_id = l.dr_account_id
            LEFT JOIN exp_type t ON l.type_id = t.type_id
            LEFT JOIN exp_type_tl tl ON l.type_id = tl.type_id AND tl.LANGUAGE = 'zh_CN'
            LEFT JOIN exp_tax_code c ON l.fin_tax_code_id = c.tax_code_id
            LEFT JOIN gl_account ta ON ta.account_id = l.tax_account_id
            LEFT JOIN fnd_city_tl fct ON fct.city_id = l.destination_city and fct.language = 'zh_CN'
            LEFT JOIN fnd_city_tl fct2 ON fct2.city_id = l.destination_city_to and fct2.language = 'zh_CN'
        WHERE
            l.company_id = 16907 AND l.header_id = #{headerId}
    </select>
    <select id="selectDocument" resultMap="ExpClaimHeaderMap">
        SELECT header_id,document_num FROM exp_claim_header where header_id = #{headerId};
    </select>
    <insert id="insertExpClaimInfo">
        insert into exp_claim_info (header_id,company_id,document_num,voucher_no,creation_date,resp_code,msg)
        values(#{headerId},#{companyId},#{documentNum},#{voucherNo},now(),#{resp_code},#{msg})
    </insert>
    <resultMap
            id="ExpClaimHeaderMap"
            type="com.cloudpense.expman.entity.ExpClaimHeader">
        <result
                property="externalStatus"
                column="external_status"/>
        <result
                property="externalMessage"
                column="external_message"/>
        <result
                property="externalDate"
                column="external_date"/>
        <result
                property="headerId"
                column="header_id"/>
        <result
                property="pathId"
                column="path_id"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="documentId"
                column="document_id"/>
        <result
                property="language"
                column="language"/>
        <result
                property="userAccountId"
                column="user_account_id"/>
        <result
                property="supplierAccountId"
                column="supplier_account_id"/>
        <result
                property="documentNum"
                column="document_num"/>
        <result
                property="totalAmount"
                column="total_amount"/>
        <result
                property="totalClaimAmount"
                column="total_claim_amount"/>
        <result
                property="advanceAmount"
                column="advance_amount"/>
        <result
                property="currencyCode"
                column="currency_code"/>
        <result
                property="submitDate"
                column="submit_date"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="description"
                column="description"/>
        <result
                property="status"
                column="status"/>
        <result
                property="financeStatus"
                column="fin_status"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creatorName"
                column="creator_name"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="totalPayAmount"
                column="total_pay_amount"/>
        <result
                property="glBatchId"
                column="gl_batch_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="userId"
                column="user_id"/>
        <result
                property="budgetType"
                column="budget_type"/>
        <result
                property="submitDepartment"
                column="submit_department"/>
        <result
                property="chargeDepartment"
                column="charge_department"/>
        <result
                property="submitUser"
                column="submit_user"/>
        <result
                property="chargeUser"
                column="charge_user"/>
        <result
                property="invoiceFlag"
                column="invoice_flag"/>
        <result
                property="headerTypeId"
                column="header_type_id"/>
        <result
                property="paymentDate"
                column="payment_date"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="ignoreWarning"
                column="ignore_warning"/>
        <result
                property="approverList"
                column="approver_list"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="pathStatus"
                column="path_status"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="travelMethod"
                column="travel_method"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="leaveType"
                column="leave_type"/>
        <result
                property="leaveDay"
                column="leave_day"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="dueDate"
                column="due_date"/>
        <result
                property="reminderDate"
                column="reminder_date"/>
        <result
                property="planStartDatetime"
                column="plan_start_datetime"/>
        <result
                property="planEndDatetime"
                column="plan_end_datetime"/>
        <result
                property="ruleName"
                column="rule_name"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="childStatus"
                column="child_status"/>
        <result
                property="glStatus"
                column="gl_status"/>
        <result
                property="glMessage"
                column="gl_message"/>
        <result
                property="type"
                column="type"/>
        <result
                property="journalNum"
                column="journal_num"/>
        <result
                property="vouchers"
                column="vouchers"/>
        <result
                property="agentId"
                column="agent_id"/>
        <result
                property="financeDescription"
                column="finance_description"/>
        <result
                property="branchId"
                column="branch_id"/>
        <result
                property="payObject"
                column="pay_object"/>
        <result
                property="payUser"
                column="pay_user"/>
        <result
                property="branchAccountId"
                column="branch_account_id"/>
        <result
                property="glPeriod"
                column="gl_period"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
    </resultMap>
</mapper>