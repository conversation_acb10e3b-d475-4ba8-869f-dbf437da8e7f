<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.WanhuaMapper">

    <select id="glBatchCreateWanhua" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_wanhua(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="getOaPushTask" statementType="CALLABLE" resultType="String">
        {call zzwanhua_oa_portal(#{companyId})}
    </select>

    <select id="getWechatPushTask" statementType="CALLABLE" resultType="String">
        {call zzwanhua_wechat_portal(#{companyId})}
    </select>

    <select id="scanWanhuaVoucherPost" statementType="CALLABLE" resultType="Integer">
        select case when ech.header_id is null then 0 else ech.header_id end
        from fnd_workflow_path fwp
        left join exp_claim_header ech on fwp.source_id = ech.header_id
        where fwp.status = 'approving'
        and ech.status in ('submitted','preapproved','checked')
        and fwp.position_id = #{positionId}
        and ech.company_id = #{companyId}
        and branch_id in (select department_id from fnd_department
        where type = 'B' and column10 = 1)
        <!--column10为1的时候代表开，2代表关-->
    </select>

    <select id="commonUpdate" statementType="CALLABLE" resultType="String">
        {call zzwanhua_api_update_data(#{type}, #{companyId}, #{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <select id="getBudgetTodo" statementType="CALLABLE" resultType="Map">
        select ech.document_num,eht.type_code,ech.internal_type,ech.status from exp_claim_header ech
        left join exp_header_type eht on ech.header_type_id = eht.type_id
        where ech.status in ('approved', 'withdrawed', 'rejected')
        and (lock_status is null or (lock_status = 'locked' and lock_message not like 'POST_ERROR:%'))
        and ech.company_id = #{companyId}
--         and eht.type_code not in ('FYC004','FYC005')
--         and ech.internal_type != 'request'
--         and ech.internal_type != 'request_travel'
    </select>

    <update id="setBudgetPosted" statementType="CALLABLE">
        update exp_claim_header set
        lock_status = #{lockStatus},
        lock_message = #{lockMessage},
        column9 = #{lockMessage}
        where company_id = #{companyId}
        and document_num = #{documentNum}
        and status in ('rejected', 'withdrawed', 'approved')
    </update>
    
    <update id="updateOaPushStatusByDocumentNums">
        update exp_claim_header ech
        set ech.column29 = #{status}
        where  ech.status not in ('deleted') and ech.document_num in
        <foreach collection="documentNums" item="documentNum"
        index="index" open="(" close=")" separator=",">
        #{documentNum}
        </foreach>
    </update>

    <delete id="deletePortalLog">
        delete from zz_wanhua_portal_log where header_id in
        <foreach collection="headerIds" item="headerId"
                 index="index" open="(" close=")" separator=",">
            #{headerId}
        </foreach>
    </delete>
</mapper>