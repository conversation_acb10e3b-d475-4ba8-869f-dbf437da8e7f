<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.PhoenixMapper">
    <insert id="savePhoenixInvoice" statementType="CALLABLE">
       { call zz_phoenix_exp_claim_header_update(#{input},#{type},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </insert>

    <select id="phoenixPr" statementType="CALLABLE" resultType="String">
        {call zzphoenix_update_data(#{type}, #{companyId}, #{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <update id="updatePushStatus" statementType="CALLABLE">
        update ntf_queue_company set status = #{status} where id = #{id}
    </update>

    <select id="getPushTask" statementType="CALLABLE" resultMap="PhoenixPushMap">
        select * from ntf_queue_company where company_id = #{companyId}
        and status = #{status} and type in ('phoenix_approve_delete','phoenix_approve')
        and creation_date &lt; date_add(now(),interval 1 minute)
    </select>

    <select id="glBatchCreatePhoenix" statementType="CALLABLE" resultType="String">
        {call zzgl_batch_create_phoenix(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="syncCompanyCode" statementType="CALLABLE" resultType="String">
        select department_code from fnd_department where type = 'B'
        and company_id = #{companyId} and supervisor_id != 0
    </select>

    <update id="calculateDepartment" statementType="CALLABLE">
        {call zz_phoenix_cal_department(#{companyId})}
    </update>

    <select id="getUsedLeaveDay" statementType="CALLABLE" resultType="Integer">
        select case when sum(leave_day) is null then 0 else sum(leave_day) end
        from exp_claim_header
        where status in ('submitted', 'checked', 'preapporved')
        and submit_user = #{submitUser}
    </select>

    <select id="getPendingDocumentIds" statementType="CALLABLE" resultType="Integer">
        SELECT document_id from exp_claim_header h
        left join exp_header_type t on h.header_type_id = t.type_id
        where h.status not in ('deleted') and h.gl_status in ('pending','error')
        <if test="documentIds != null">
            and h.document_id in
            <foreach collection="documentIds" item="documentId"
                     index="index" open="(" close=")" separator=",">
                #{documentId}
            </foreach>
        </if>
    </select>

    <update id="baanTest" statementType="CALLABLE" >
       UPDATE exp_claim_header set gl_status=null,gl_message = null
       where  header_id in  (726,740,916,1283,1287,1886,2091);
    </update>

    <resultMap id="PhoenixPushMap" type="com.cloudpense.expman.entity.PhoenixPush">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="companyId" column="companyId"/>
        <result property="status" column="status"/>
        <result property="content" column="content"/>
    </resultMap>


</mapper>
