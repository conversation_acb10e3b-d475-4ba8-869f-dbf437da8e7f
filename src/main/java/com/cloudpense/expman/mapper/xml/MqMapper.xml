<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.MqMapper">
    <resultMap id="BaseResultMap" type="com.cloudpense.expman.entity.MqQueue">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="exchange" jdbcType="VARCHAR" property="exchange"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="company_id" jdbcType="INTEGER" property="companyId"/>
        <result column="header_id" jdbcType="INTEGER" property="headerId"/>
        <result column="path_id" jdbcType="INTEGER" property="pathId"/>
        <result column="header_type_id" jdbcType="INTEGER" property="headerTypeId"/>
        <result column="position_id" jdbcType="INTEGER" property="positionId"/>
        <result column="event_code" jdbcType="VARCHAR" property="eventCode"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="content" jdbcType="CHAR" property="content"/>
        <result column="mq_count" jdbcType="INTEGER" property="mqCount"/>
        <result column="biz_id" jdbcType="VARCHAR" property="bizId"/>
        <result column="creation_date" jdbcType="TIMESTAMP" property="creationDate"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
    </resultMap>

    <update id="updateMqStatus" parameterType="com.cloudpense.expman.entity.MqQueue">
        update mq_queue
        <set>
            <if test="exchange != null">
                exchange = #{exchange,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="headerId != null">
                header_id = #{headerId,jdbcType=INTEGER},
            </if>
            <if test="pathId != null">
                path_id = #{pathId,jdbcType=INTEGER},
            </if>
            <if test="headerTypeId != null">
                header_type_id = #{headerTypeId,jdbcType=INTEGER},
            </if>
            <if test="positionId != null">
                position_id = #{positionId,jdbcType=INTEGER},
            </if>
            <if test="eventCode != null">
                event_code = #{eventCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=CHAR},
            </if>
            <if test="mqCount != null">
                mq_count = #{mqCount,jdbcType=INTEGER},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=VARCHAR},
            </if>
            <if test="creationDate != null">
                creation_date = #{creationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="lastUpdateDate != null">
                last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
</mapper>