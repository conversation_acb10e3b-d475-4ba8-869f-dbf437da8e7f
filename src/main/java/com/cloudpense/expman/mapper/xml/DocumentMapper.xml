<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.cloudpense.expman.mapper.DocumentMapper">
    <resultMap
            id="ExpClaimHeaderMap"
            type="com.cloudpense.expman.entity.ExpClaimHeader">
        <result
                property="externalStatus"
                column="external_status"/>
        <result
                property="externalMessage"
                column="external_message"/>
        <result
                property="externalDate"
                column="external_date"/>
        <result
                property="headerId"
                column="header_id"/>
        <result
                property="pathId"
                column="path_id"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="documentId"
                column="document_id"/>
        <result
                property="language"
                column="language"/>
        <result
                property="userAccountId"
                column="user_account_id"/>
        <result
                property="supplierAccountId"
                column="supplier_account_id"/>
        <result
                property="documentNum"
                column="document_num"/>
        <result
                property="totalAmount"
                column="total_amount"/>
        <result
                property="totalClaimAmount"
                column="total_claim_amount"/>
        <result
                property="advanceAmount"
                column="advance_amount"/>
        <result
                property="currencyCode"
                column="currency_code"/>
        <result
                property="submitDate"
                column="submit_date"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="description"
                column="description"/>
        <result
                property="status"
                column="status"/>
        <result
                property="financeStatus"
                column="fin_status"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creatorName"
                column="creator_name"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="totalPayAmount"
                column="total_pay_amount"/>
        <result
                property="glBatchId"
                column="gl_batch_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="userId"
                column="user_id"/>
        <result
                property="budgetType"
                column="budget_type"/>
        <result
                property="submitDepartment"
                column="submit_department"/>
        <result
                property="chargeDepartment"
                column="charge_department"/>
        <result
                property="submitUser"
                column="submit_user"/>
        <result
                property="chargeUser"
                column="charge_user"/>
        <result
                property="invoiceFlag"
                column="invoice_flag"/>
        <result
                property="headerTypeId"
                column="header_type_id"/>
        <result
                property="paymentDate"
                column="payment_date"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="ignoreWarning"
                column="ignore_warning"/>
        <result
                property="approverList"
                column="approver_list"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="pathStatus"
                column="path_status"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="travelMethod"
                column="travel_method"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="leaveType"
                column="leave_type"/>
        <result
                property="leaveDay"
                column="leave_day"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="dueDate"
                column="due_date"/>
        <result
                property="reminderDate"
                column="reminder_date"/>
        <result
                property="planStartDatetime"
                column="plan_start_datetime"/>
        <result
                property="planEndDatetime"
                column="plan_end_datetime"/>
        <result
                property="ruleName"
                column="rule_name"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="childStatus"
                column="child_status"/>
        <result
                property="glStatus"
                column="gl_status"/>
        <result
                property="glMessage"
                column="gl_message"/>
        <result
                property="type"
                column="type"/>
        <result
                property="journalNum"
                column="journal_num"/>
        <result
                property="vouchers"
                column="vouchers"/>
        <result
                property="agentId"
                column="agent_id"/>
        <result
                property="financeDescription"
                column="finance_description"/>
        <result
                property="branchId"
                column="branch_id"/>
        <result
                property="payObject"
                column="pay_object"/>
        <result
                property="payUser"
                column="pay_user"/>
        <result
                property="branchAccountId"
                column="branch_account_id"/>
        <result
                property="glPeriod"
                column="gl_period"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
    </resultMap>
    <!--修改单据状态-->
    <update id="updateDocument" statementType="CALLABLE">
        {call fnd_workflow_update('EXP',#{headerId},null,#{companyId},#{userId},#{status})}
    </update>
    <!--查询状态不是close的单据-->
    <select id="selectDocument" resultType="java.lang.Integer" parameterType="ExpClaimHeader">
        SELECT header_id FROM exp_claim_header where column28 = #{column28} and STATUS not in('closed');
    </select>

</mapper>