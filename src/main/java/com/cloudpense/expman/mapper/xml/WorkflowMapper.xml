<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.WorkflowMapper">
	<select id="getToDoWorkflowCount" statementType="PREPARED" resultType="Integer">
		select count(*) cnt
		from   fnd_workflow_path p join exp_claim_header c on p.source_id = c.header_id and p.workflow_type = 'EXP' left join fnd_user u on u.user_id = c.created_by left join
		fnd_user_position up on p.position_id = up.position_id and p.user_id is null
		where  p.type in ('P','O','C','Q')
		and    p.status in ('approving','commenting','copied','answering')
		and    c.status in ('submitted','preapproved','checked','approved','closed')
		and    ifnull(p.user_id,up.user_id) in
		<foreach collection="userIds" item="userId" open =	"(" close=")" separator=",">
			#{userId}
		</foreach>
		and    p.company_id = #{companyId}
		;
    </select>

	<select id="getUserIds" statementType="PREPARED" resultType="Integer">
		select user_id from fnd_user where employee_number = #{employeeNumber} and company_id = #{companyId}
		union all
		select l.user_id from fnd_agent_log l
		where l.company_id = #{companyId} and l.agent_id in(select user_id from fnd_user where employee_number = #{employeeNumber} and company_id = #{companyId})
		and l.approve_flag = 'Y' and now() between ifnull(l.start_datetime,now()) and ifnull(l.end_datetime,now())
	</select>
</mapper>