<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.CJLRMapper">
    <insert id="cheryJLRUpdateData" statementType="CALLABLE">
       { call zzcjlr_update_data(#{type},#{companyId},#{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </insert>

    <update id="normalInsertLog" statementType="CALLABLE">
            insert into fnd_log(company_id, type, content, creation_date)
            values (#{companyId}, #{type}, #{content}, now())
    </update>
</mapper>
