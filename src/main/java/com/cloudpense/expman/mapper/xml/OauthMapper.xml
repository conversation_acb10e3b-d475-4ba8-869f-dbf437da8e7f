<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.OauthMapper">

    <update id="update">
        <!--UPDATE oauth_access_token-->
        <!--SET company_id=#{companyId}-->
        <!--WHERE user_name=#{username}-->
        INSERT INTO oauth_access_company (token_id,user_name,client_id,company_id)
        VALUES (#{tokenId},#{username},'web-client',#{companyId})
        ON DUPLICATE KEY
        UPDATE company_id = #{companyId} ,token_id = #{tokenId}
    </update>

    <select id="setCompanyId" resultType="INTEGER">
        SELECT company_id
        FROM oauth_access_company
        WHERE user_name=#{username}
        LIMIT 1
    </select>

    <update id="generateToken" statementType="CALLABLE">
        UPDATE oauth_client_credentials
        SET access_token=#{accessToken},expired_time=#{time}
        WHERE client_id=#{clientId}
    </update>

    <select id="verifyUaesToken" resultType="Integer">
        SELECT company_id from oauth_client_credentials
        WHERE access_token=#{accessToken} AND expired_time > #{time}
    </select>

    <select id="fndServer" resultType="String">
        SELECT server_name from oauth_company
        WHERE company_id=#{companyId}
    </select>

    <select id="getTokenVo" resultType="com.cloudpense.expman.Request.TokenVo" resultMap="tokenMap">
        SELECT access_token, expired_time, client_id, client_secret, company_id, grant_type FROM oauth_client_credentials
        WHERE client_id = #{clientId} AND client_secret = #{clientSecret}
    </select>

    <select id="getTokenVoByClientId" resultType="com.cloudpense.expman.Request.TokenVo" resultMap="tokenMap">
        SELECT access_token, expired_time, client_id, client_secret, company_id, grant_type FROM oauth_client_credentials
        WHERE client_id = #{clientId}
    </select>

    <select id="findUserByMobile" resultMap="FndUserMap">
        select user_id,company_id,mobile from fnd_user where company_id = #{companyId}
    </select>

    <select id="findFndUserBindingByCompanyId" resultMap="BindUserInfoMap">
        select * from fnd_user_binding where company_id = #{companyId}
    </select>

    <insert id="addFndUserBinding">
        insert into fnd_user_binding(user_id,company_id,platform,userid,mobile)
        values (#{userId},#{companyId},#{platform},#{dingUserId},#{dingMobile})
    </insert>

    <select id="findBindByMobile" resultMap="BindUserInfoMap">
        select * from fnd_user_binding
        where platform = #{platform} and mobile = #{mobile}
    </select>

    <select id="findUserNameById" resultType="String">
        select user_name from fnd_user where user_id = #{userId}
    </select>

    <select id="findServerNameByCorpId" resultType="String">
        select server_name from sso_login where corp = #{corpId}
    </select>

    <resultMap id="FndUserMap" type="com.cloudpense.expman.entity.FndUser">
        <result property="userId" column="user_id"/>
        <result property="companyId" column="company_id"/>
        <result property="mobile" column="mobile"/>
    </resultMap>

    <resultMap id="BindUserInfoMap" type="com.cloudpense.expman.entity.BindUserInfo">
        <result property="userId" column="user_id"/>
        <result property="companyId" column="company_id"/>
        <result property="platform" column="platform"/>
        <result property="bindId" column="userid"/>
        <result property="bindName" column="user_name"/>
        <result property="corpId" column="companyid"/>
        <result property="accesstoken" column="accesstoken"/>
        <result property="refreshToken" column="refreshToken"/>
        <result property="mobile" column="mobile"/>
        <result property="role" column="role"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="updateStatus" column="update_status"/>
    </resultMap>

    <resultMap id="tokenMap" type="com.cloudpense.expman.Request.TokenVo">
        <id column="client_id" jdbcType="VARCHAR" property="clientId"/>
        <result column="client_secret" jdbcType="VARCHAR" property="clientSecret"/>
        <result column="access_token" jdbcType="VARCHAR" property="accessToken"/>
        <result column="expired_time" jdbcType="TIMESTAMP" property="expiredTime"/>
        <result column="company_id" jdbcType="INTEGER" property="companyId"/>
        <result column="grant_type" jdbcType="VARCHAR" property="grantType"/>
    </resultMap>
</mapper>