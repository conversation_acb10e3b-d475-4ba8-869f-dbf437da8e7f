<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.EvcardMapper">
      <select id="evcardDataUpdate" statementType="CALLABLE" resultType="String">
           {call zzevcard_update_data(#{type}, #{companyId}, #{input},
            #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
      </select>

    <select id="evcardGetTypeByAccount" statementType="CALLABLE" resultType="String">
        select et.type_code from gl_account_rule_json garj
        left join gl_account ga on garj.account_id = ga.account_id and ga.company_id = #{companyId}
        left join exp_type et on et.type_id = garj.rule_condition ->> '$.type_id' and et.company_id = #{companyId}
        where garj.company_id = #{companyId}
        and ga.account_code = #{accountCode}
        and garj.account_event = 'EXP_DR'
        ORDER BY garj.priority limit 1
    </select>

    <select id="evcardGetPositionRecever" statementType="CALLABLE" resultType="String">
        select fu.employee_number from fnd_user_position fup
        left join fnd_user fu on fu.user_id = fup.user_id and fu.company_id = #{companyId}
        where fup.position_id = #{positionId}
        and fup.company_id = #{companyId} limit 1
    </select>

    <select id="getOaPushTask" statementType="CALLABLE" resultType="String">
        {call zzevcard_oa_task(#{companyId})}
    </select>

    <select id="evcardGetHeaderAttachments" statementType="CALLABLE" resultType="String">
        select json_object('FILE_NAME', eca.file_name, 'ATTACHMENT_URL', eca.attachment_url)
        from exp_claim_header ech left join exp_claim_attachment eca on ech.header_id = eca.claim_header_id
        where ech.header_id = #{headerId}  and eca.claim_line_id is null and eca.claim_header_id is not null
        and ech.company_id = #{companyId}
    </select>

    <select id="evcardGetLinesAttachments" statementType="CALLABLE" resultType="String">
        select json_object('FILE_NAME', eca.file_name, 'ATTACHMENT_URL', eca.attachment_url)
        from exp_claim_header ech left join exp_claim_attachment eca on ech.header_id = eca.claim_header_id
        left join exp_claim_line ecl on ecl.header_id = ech.header_id
        where ech.header_id = #{headerId} and eca.claim_line_id is not null and eca.claim_header_id is not null
        and eca.claim_line_id = ecl.line_id
        and ecl.column27 = #{lineNum}
        and ech.company_id = #{companyId}
    </select>

    <select id="evcardGetLinesInfo" statementType="CALLABLE" resultType="String">
        {call line_update_data(#{type}, #{companyId}, #{input},
            #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <select id="getLovValue" statementType="CALLABLE" resultType="String">
        select  flv.value_code from fnd_lov_value flv INNER JOIN fnd_lov fl on flv.lov_id = fl.lov_id
        where fl.company_id = #{companyId} and fl.lov_name = #{lovName} and flv.enabled_flag = 'Y'
    </select>
</mapper>