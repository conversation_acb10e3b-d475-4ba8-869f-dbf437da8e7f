<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.YofcMapper">
    <select id="yofcDataUpdate" statementType="CALLABLE" resultType="String">
        {call zzyofc_update_data(#{type}, #{companyId}, #{input},
            #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <update id="normalInsertLog" statementType="CALLABLE">
            insert into fnd_log(company_id, type, content, creation_date)
            values (#{companyId}, #{type}, #{content}, now())
    </update>

    <update id="yofcUpdateColumn50" statementType="CALLABLE">
            update exp_claim_header set column45 = #{busNbr}, column44 = #{bankAccountCode}
            where header_id =
                (select source_id from ap_payment_header
                 where header_id = #{documentId} limit 1) limit 1;
    </update>

    <select id="getDocumentGroupNum" statementType="CALLABLE" resultType="String">
        select eht.group_num from exp_header_type eht
        left join exp_claim_header ech on eht.type_id = ech.header_type_id
        left join ap_payment_header aph on aph.source_id = ech.header_id
        where aph.header_id = #{documentId} limit 1;
    </select>

    <select id="getUserIdentityColumn1" statementType="CALLABLE" resultType="String">
        select column1 from fnd_user where user_id = #{userId} limit 1;
    </select>

    <select id="getBranchAccountNumberById" statementType="CALLABLE" resultMap="FndUserAccountMap">
        SELECT account_id, company_id, account_name, account_number, bank_name, bank_branch, bank_code,
        bank_swift_id, bank_address, state, city, enabled_flag, primary_flag
        FROM fnd_company_account
        WHERE account_id = #{accountId}
    </select>

    <select id="getDocumentPayInfo" statementType="CALLABLE" resultMap="yofcDocumentMap">
        select ech.total_pay_amount total_pay_amount,
        ech.document_num document_num,
        fua.account_number receive_account_number,
        fua.account_name receive_account_name
        from
        ap_payment_header aph left join exp_claim_header ech on aph.source_id = ech.header_id
        left join fnd_user_account fua on fua.account_id = ech.user_account_id
        where aph.header_id = #{documentId} limit 1;
    </select>
    
    <select id="getPaymentVouchers" resultMap="paymentVoucher">
        SELECT
        ch.header_id, ch.document_num, ch.total_pay_amount, ht.type_code,
        ch.column45, ch.column44, ph.payment_date, d.column1 as department_column1,ch.trans_serial_num,
        ch.charge_user, u.full_name, ch.start_datetime, ch.end_datetime, cc.city_name
        FROM exp_claim_header ch
        LEFT JOIN fnd_department d ON ch.branch_id = d.department_id
        LEFT JOIN fnd_user u ON ch.charge_user = u.user_id
        LEFT JOIN ap_payment_header ph ON ph.source_id = ch.header_id
        LEFT JOIN exp_header_type ht ON ch.header_type_id = ht.type_id
        LEFT JOIN (
            SELECT
            cc.header_id,
            GROUP_CONCAT( c.city_name SEPARATOR '|' ) AS city_name
            FROM
            exp_claim_city cc
            INNER JOIN fnd_city c ON cc.city_id = c.city_id
            INNER JOIN ap_payment_header ph2 on cc.header_id = ph2.source_id
            WHERE
            ph2.header_id IN
                <foreach item="headerId" index="index" collection="headerIds"
                         open="(" separator="," close=")">
                    #{headerId}
                </foreach>
            GROUP BY cc.header_id
        ) cc ON ch.header_id = cc.header_id
        WHERE ph.header_id in
        <foreach item="headerId" index="index" collection="headerIds"
                 open="(" separator="," close=")">
            #{headerId}
        </foreach>
    </select>
    <select id="getPaymentVouchersNew" resultMap="paymentVoucher">
        SELECT
        ch.header_id, ch.document_num, ch.total_pay_amount, ht.type_code,d.department_code as branch_code,
        ch.column45, ga.account_number as column44, d.column1 as department_column1, d.column4 as department_column4,
        d.column5 as department_column5, ch.trans_serial_num,
        ch.charge_user, u.full_name, ch.start_datetime, ch.end_datetime, cc.city_name
        FROM exp_claim_header ch
        LEFT JOIN fnd_department d ON ch.branch_id = d.department_id
        LEFT JOIN fnd_user u ON ch.charge_user = u.user_id
        LEFT JOIN exp_header_type ht ON ch.header_type_id = ht.type_id
        LEFT JOIN fnd_company_account ga ON ch.branch_account_id = ga.account_id
        LEFT JOIN (
            SELECT
            cc.header_id,
            GROUP_CONCAT( c.city_name SEPARATOR '|' ) AS city_name
            FROM
            exp_claim_city cc
            INNER JOIN fnd_city c ON cc.city_id = c.city_id
            INNER JOIN exp_claim_header ech on cc.header_id = ech.link_header_id
            WHERE
            ech.document_id IN
                <foreach item="documentId" index="index" collection="documentIds"
                         open="(" separator="," close=")">
                    #{documentId}
                </foreach> and ech.`status` in ('approved','closed')
            GROUP BY cc.header_id
        ) cc ON ch.header_id = cc.header_id
        WHERE ch.header_id in
        (
        SELECT link_header_id from exp_claim_header where `status` in ('approved','closed') and  document_id in
        <foreach item="documentId" index="index" collection="documentIds"
                 open="(" separator="," close=")">
            #{documentId}
        </foreach>
        )
    </select>

    <insert id="saveNew" statementType="CALLABLE">
        { call exp_claim_header_insert(
        #{fndCompany.companyId}, #{input}, #{status,mode=OUT,jdbcType=VARCHAR}, #{createdBy2.userId}, #{agentId},
        #{headerId,mode=OUT,jdbcType=INTEGER}, #{documentNum,mode=OUT,jdbcType=VARCHAR},
        #{external, mode=OUT, jdbcType=VARCHAR},
        #{returnCode,mode=OUT,jdbcType=VARCHAR}, #{returnMessage,mode=OUT,jdbcType=VARCHAR}
        )}
    </insert>

    <update id="submitNew" statementType="CALLABLE">
        {call exp_claim_header_submit(#{headerId}, #{fndCompany.companyId}, #{ignoreWarning}, #{status}, #{userId}, #{external},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </update>


    <insert id="addApPaymentBatch" useGeneratedKeys="true" keyProperty="apPaymentBatch.batchId"  parameterType="com.cloudpense.expman.entity.changfei.ApPaymentBatch">
        insert into ap_payment_batch (company_id,payment_date,creation_date,last_update_date)
        values (#{apPaymentBatch.companyId},#{apPaymentBatch.paymentDate},now(),now())
    </insert>

    <resultMap id="yofcDocumentMap" type="com.cloudpense.expman.entity.YofcPayElement">
        <result property="totalPayAmount" column="total_pay_amount"/>
        <result property="documentNum" column="document_num"/>
        <result property="receiveAccountNumber" column="receive_account_number"/>
        <result property="receiveAccountName" column="receive_account_name"/>
    </resultMap>

    <resultMap id="paymentVoucher" type="com.cloudpense.expman.entity.YofcPaymentVoucher">
        <result property="headerId" column="header_id"/>
        <result property="documentNum" column="document_num"/>
        <result property="orgId" column="department_column1"/>
        <result property="fdColumn4" column="department_column4"/>
        <result property="fdColumn5" column="department_column5"/>
        <result property="paymentDate" column="payment_date"/>
        <result property="typeCode" column="type_code"/>
        <result property="accountCode" column="column44"/>
        <result property="lineAmount" column="total_pay_amount"/>
<!--        <result property="pipelineNumber" column="column45"/>-->
        <result property="pipelineNumber" column="trans_serial_num"/>
        <result property="chargeUserName" column="full_name"/>
        <result property="startDate" column="start_datetime"/>
        <result property="endDate" column="end_datetime"/>
        <result property="cityName" column="city_name"/>
        <result property="branchCode" column="branch_code"/>
    </resultMap>

    <resultMap id="FndUserAccountMap" type="com.cloudpense.expman.entity.FndUserAccount">
        <result property="accountId" column="account_id"/>
        <result property="companyId" column="company_id"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNumber" column="account_number"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankSwiftId" column="bank_swift_id"/>
        <result property="bankAddress" column="bank_address"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="primaryFlag" column="primary_flag"/>
    </resultMap>

    <insert id="addGlJe" useGeneratedKeys="true" keyProperty="glJeBatch.batch_id"  parameterType="com.cloudpense.expman.entity.changfei.GlJeBatch">
        insert into gl_je_batch (company_id,ledger_type,source,type,gl_date,journal_num,detail,
        <if test="userId != null">
            created_by,
        </if>
        creation_date,last_updated_by,last_update_date,ledger_id)
        values (#{companyId},1,'EXP','api',null,#{journalNum},null,
        <if test="userId != null">
            #{userId},
        </if>
        now(),#{userId},now(),#{ledgerId});
    </insert>

    <update id="updatePayStatusByDocumentIds">
        update exp_claim_header set pay_status = #{status}, pay_status_message = #{message}
        where status != 'deleted' and document_id in
        <foreach collection="documentIds" index="index" item="documentId"
                 separator="," open="(" close=")">
            #{documentId}
        </foreach>
    </update>

    <update id="updateColumnByDocumentIds">
        update exp_claim_header set column44 = #{column44}
        where status != 'deleted' and document_id in
        <foreach collection="documentIds" index="index" item="documentId"
                 separator="," open="(" close=")">
            #{documentId}
        </foreach>
    </update>
</mapper>
