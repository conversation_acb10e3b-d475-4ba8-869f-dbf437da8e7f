<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.CheryJaguarLandRoverToDoMapper">
    <select id="commonUpdate1" statementType="CALLABLE" resultType="String">
        {call zzjrcwf_api_todo(#{companyId}, #{input},
        #{Code,mode=OUT,jdbcType=VARCHAR},#{ErrorDetail,mode=OUT,jdbcType=VARCHAR},#{Message,mode=OUT,jdbcType=VARCHAR},#{Detail,mode=OUT,jdbcType=VARCHAR})}
    </select>
</mapper>