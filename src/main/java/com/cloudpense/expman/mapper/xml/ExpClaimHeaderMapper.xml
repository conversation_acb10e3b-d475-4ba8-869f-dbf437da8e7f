<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.ExpClaimHeaderMapper">

    <update id="updatebyDocumentIdAndCompanyId" parameterType="java.lang.Integer">
        UPDATE exp_claim_header INNER JOIN(select header_id, my_version
            from   exp_claim_header
            where  status in ('approved','closed')
            and	(gl_status IS NULL or gl_status='pending')
            and    document_id = #{document_id} limit 1)c
            ON exp_claim_header.header_id = c.header_id
            AND exp_claim_header.my_version=c.my_version
        set exp_claim_header.my_version =  c.my_version + 1, gl_status='generating';
    </update>
</mapper>