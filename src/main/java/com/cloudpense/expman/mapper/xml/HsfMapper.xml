<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.HsfMapper">
    <insert id="oaContractLog">
        insert into zzhsf_oa_contract_log(content, recived_time)
        values (#{con}, now())
    </insert>
    <update id="saveOAToken">
        update zzhsf_oa_token
        set token       = #{token},
            update_time = timezone(now(), 8)
        where id = 1;
    </update>


    <update
            id="updateSubCompany"
            statementType="CALLABLE">
        {call zzhsf_batch_update_company(#{value})}
    </update>
    <update
            id="updateDepartment"
            statementType="CALLABLE">
        {call zzhsf_batch_update_dept(#{value})}
    </update>
    <update
            id="updatePosition"
            statementType="CALLABLE">
        {call zzhsf_batch_update_position(#{response}, #{orgCode})}
    </update>
    <update
            id="updateUser"
            statementType="CALLABLE">
        {call zzhsf_batch_update_user(#{value})}
    </update>

    <select
            id="queryOrgCode"
            resultType="java.lang.String">
        select department_code
        from fnd_department
        where company_id = 15587
          and type = 'B';
    </select>

    <select
            id="queryNtfCompany"
            resultMap="hsfNtfMap">
        select *
        from ntf_queue_company
        where status in (0, 3, 10, 13, 14, 15)
          and (external_message != 'success' or external_message is null)
          and company_id = 15587;
    </select>
    <select
            id="queryPsnID"
            resultType="java.lang.String">
        select json_object('thirdCode', u.column3)
        from fnd_user u
        where u.user_id = #{userId}
    </select>
    <select
            id="queryUnsentClaim"
            resultMap="ExpClaimHeaderMap">
        select h.*, ifnull(left(t.type_code, 1), 'U') as type_code
        from exp_claim_header h
                 left join exp_header_type t on h.header_type_id = t.type_id
        where h.status = 'approved'
          and (h.external_status = ''
            or h.external_status = 'failed'
            or h.external_status is null)
          and h.company_id = 15587
          and h.header_type_id in
              (select type_id from exp_header_type where left(type_code, 1) in ('E', 'P', 'R', 'L', 'W'));
    </select>
    <select
            id="queryRepushClaim"
            resultMap="ExpClaimHeaderMap">
        select h.*, ifnull(left(t.type_code, 1), 'U') as type_code
        from exp_claim_header h
                 left join exp_header_type t on h.header_type_id = t.type_id
        where h.status not in ('deleted')
        <if test="documentNums != null">
            and h.document_num in
            <foreach collection="documentNums" item="documentNum"
                     index="index" open="(" close=")" separator=",">
                #{documentNum}
            </foreach>
        </if>
    </select>
    <select
            id="queryClaimLine"
            resultMap="ExpClaimLineMap">
        select *
        from exp_claim_line
        where header_id = #{value}

    </select>
    <select
            id="getOAToken"
            resultType="java.lang.String">
        select token
        from zzhsf_oa_token
        where id = 1;
    </select>
    <select
            id="queryUserExistsById"
            resultType="integer">
        select count(*)
        from fnd_user
        where column3 = #{id}
    </select>
    <update id="updateExternalStatus">
        update exp_claim_header
        set external_status=#{extStatus},
            external_message = #{extMsg},
            external_date = now()
        where header_id = #{headerId}
    </update>
    <update id="updateNtfStatus">
        update ntf_queue_company
        set status=#{status},
            content=#{content},
            external_message=#{message}
        where id = #{id}
    </update>
    <!--<update-->
    <!--id="updateExpType"-->
    <!--statementType="CALLABLE">-->
    <!--{call zzhsf_update_exp_type(#{value})}-->
    <!--</update>-->
    <!--<update-->
    <!--id="updateCusList"-->
    <!--statementType="CALLABLE">-->
    <!--{call zzhsf_update_cus_list(#{value})}-->
    <!--</update>-->
    <!--<update-->
    <!--id="updateCusItem"-->
    <!--statementType="CALLABLE">-->
    <!--{call zzhsf_update_cus_item(#{value})}-->
    <!--</update>-->
    <update
            id="updateBankAcc"
            statementType="CALLABLE">
        {call zzhsf_batch_update_account(#{value})}
    </update>
    <select
            id="queryContractAtts"
            resultType="java.lang.String">
        select a.file_name
        from exp_claim_header h
                 left join exp_claim_attachment a on a.claim_header_id = h.header_id
        where h.header_type_id = 134931
          and h.column10 = #{oaid};
    </select>
    <select
            id="queryHSFNCPsnCode"
            resultType="java.lang.String">
        select column2
        from fnd_user
        where user_id = #{userID};
    </select>
    <select
            id="queryHSFPsnCode"
            resultType="java.lang.String">
        select employee_number
        from fnd_user
        where user_id = #{userID};
    </select>
    <select
            id="queryHSFNCDeptCode"
            resultType="java.lang.String">
        select column1
        from fnd_department
        where department_id = #{deptID};
    </select>

    <select
            id="queryHSFOrgCode"
            resultType="java.lang.String">
        select department_code
        from fnd_department
        where department_id = #{deptID};</select>
    <select
            id="queryHSFDeptCode"
            resultType="java.lang.String">
        select column10
        from fnd_department
        where department_id = #{deptID};</select>
    <select
            id="queryLinkHeader"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where header_id =
              (select link_header_id from exp_claim_header_link where header_id = #{headerID})
           or header_id = (select link_header_id from exp_claim_header where header_id = #{headerID})
        limit 1;
    </select>
    <select
            id="queryClaimHeader"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where header_id = #{headerID};
    </select>
    <select
            id="queryLinkLine"
            resultMap="ExpClaimLineMap">
        select *
        from exp_claim_line
        where line_id =
              (select link_line_id from exp_claim_line where line_id = #{lineId});
    </select>
    <select
            id="getDeptOrgCode"
            resultType="java.lang.String">
        select column2
        from fnd_department
        where department_id = #{id}
    </select>
    <select
            id="queryDocNum"
            resultType="java.lang.String">
        select document_num
        from exp_claim_header
        where header_id = #{id}
    </select>
    <select
            id="getLastApprove"
            resultType="java.lang.String">
        select json_object('user', ifnull(u.employee_number, 'null'), 'date', ifnull(p.end_date, 'null'))
        from fnd_workflow_path p
                 left join fnd_user u on p.user_id = u.user_id
        where source_id = #{id}
          and sequence_num = (select max(sequence_num)
                              from fnd_workflow_path
                              where type in ('P', 'A')
                                and status = 'approved'
                                and source_id = #{id});
    </select>
    <select
            id="getBankAcconut"
            resultType="java.lang.String">
        select account_number
        from fnd_user_account
        where enabled_flag = 'Y'
          and user_id = #{id}
        limit 1
    </select>
    <select
            id="queryAccountingSubject"
            resultType="java.lang.String">
        select account_code
        from gl_account
        where account_id = #{subjectId}
    </select>
    <select
            id="queryLinkHeaderByLine"
            resultType="com.cloudpense.expman.entity.ExpClaimHeader">
        select *
        from exp_claim_header
        where header_id =
              (select link_header_id from exp_claim_line where line_id = #{lineID})
           or header_id = (select header_id
                           from exp_claim_line
                           where line_id = (select link_line_id from exp_claim_line where line_id = #{lineID}))
        limit 1;
    </select>
    <select id="querySupplierCode" resultType="java.lang.String">
        select supplier_code
        from exp_supplier
        where supplier_id = #{supplierId};
    </select>

    <select id="querySupplierAccountNum" resultType="java.lang.String">
        select account_number
        from exp_supplier_account
        where account_id = #{accountId};
    </select>

    <update
            id="saveContractNC"
            statementType="CALLABLE">
        {call zzhsf_batch_update_contract(#{json}, #{orgCode})}
    </update>
    <update
            id="freshNtfStatus"
            statementType="CALLABLE">
        {call zzhsf_ntf_delete(#{cid})}
    </update>
    <update
            id="saveContractOA"
            statementType="CALLABLE">
        {call zzhsf_update_contract(#{json}, 'O', null)}
    </update>
    <update id="disableOutdatedData" statementType="CALLABLE">
        {call zzhsf_disable_outdated_data()}
    </update>
    <insert id="insert_log">
        insert into fnd_api_log(company_id, type, content, creation_date)
        values (15587, #{type}, #{content}, now());
    </insert>
    <resultMap
            id="hsfNtfMap"
            type="com.cloudpense.expman.entity.hsf.HsfNtf">
        <id
                property="id"
                column="id"/>
        <result
                property="type"
                column="type"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="status"
                column="status"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="content"
                column="content"/>
    </resultMap>
    <resultMap
            id="ExpClaimHeaderMap"
            type="com.cloudpense.expman.entity.ExpClaimHeader">
        <id
                property="headerId"
                column="header_id"/>
        <result
                property="headerTypeCode"
                column="type_code"/>
        <result
                property="pathId"
                column="path_id"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="documentId"
                column="document_id"/>
        <result
                property="language"
                column="language"/>
        <result
                property="userAccountId"
                column="user_account_id"/>
        <result
                property="supplierAccountId"
                column="supplier_account_id"/>
        <result
                property="documentNum"
                column="document_num"/>
        <result
                property="totalAmount"
                column="total_amount"/>
        <result
                property="totalClaimAmount"
                column="total_claim_amount"/>
        <result
                property="advanceAmount"
                column="advance_amount"/>
        <result
                property="currencyCode"
                column="currency_code"/>
        <result
                property="submitDate"
                column="submit_date"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="description"
                column="description"/>
        <result
                property="status"
                column="status"/>
        <result
                property="financeStatus"
                column="fin_status"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creatorName"
                column="creator_name"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="totalPayAmount"
                column="total_pay_amount"/>
        <result
                property="glBatchId"
                column="gl_batch_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="userId"
                column="user_id"/>
        <result
                property="budgetType"
                column="budget_type"/>
        <result
                property="submitDepartment"
                column="submit_department"/>
        <result
                property="chargeDepartment"
                column="charge_department"/>
        <result
                property="submitUser"
                column="submit_user"/>
        <result
                property="chargeUser"
                column="charge_user"/>
        <result
                property="invoiceFlag"
                column="invoice_flag"/>
        <result
                property="headerTypeId"
                column="header_type_id"/>
        <result
                property="paymentDate"
                column="payment_date"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="ignoreWarning"
                column="ignore_warning"/>
        <result
                property="approverList"
                column="approver_list"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="pathStatus"
                column="path_status"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="travelMethod"
                column="travel_method"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="leaveType"
                column="leave_type"/>
        <result
                property="leaveDay"
                column="leave_day"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="dueDate"
                column="due_date"/>
        <result
                property="reminderDate"
                column="reminder_date"/>
        <result
                property="planStartDatetime"
                column="plan_start_datetime"/>
        <result
                property="planEndDatetime"
                column="plan_end_datetime"/>
        <result
                property="ruleName"
                column="rule_name"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="childStatus"
                column="child_status"/>
        <result
                property="glStatus"
                column="gl_status"/>
        <result
                property="glMessage"
                column="gl_message"/>
        <result
                property="type"
                column="type"/>
        <result
                property="journalNum"
                column="journal_num"/>
        <result
                property="vouchers"
                column="vouchers"/>
        <result
                property="agentId"
                column="agent_id"/>
        <result
                property="financeDescription"
                column="finance_description"/>
        <result
                property="branchId"
                column="branch_id"/>
        <result
                property="payObject"
                column="pay_object"/>
        <result
                property="payUser"
                column="pay_user"/>
        <result
                property="branchAccountId"
                column="branch_account_id"/>
        <result
                property="glPeriod"
                column="gl_period"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
    </resultMap>

    <resultMap
            id="ExpClaimLineMap"
            type="ExpClaimLine">
        <id
                property="lineId"
                column="line_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="receiptDate"
                column="receipt_date"/>
        <result
                property="receiptAmount"
                column="receipt_amount"/>
        <result
                property="receiptCurrency"
                column="receipt_currency"/>
        <result
                property="claimAmount"
                column="claim_amount"/>
        <result
                property="claimCurrency"
                column="claim_currency"/>
        <result
                property="exchangeRate"
                column="exchange_rate"/>
        <result
                property="comments"
                column="comments"/>
        <result
                property="receiptLocation"
                column="receipt_location"/>
        <result
                property="locationFrom"
                column="location_from"/>
        <result
                property="locationTo"
                column="location_to"/>
        <result
                property="shopName"
                column="shop_name"/>
        <result
                property="shopLocation"
                column="shop_location"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="attendeeList"
                column="attendee_list"/>
        <result
                property="route"
                column="route"/>
        <result
                property="expenseId"
                column="expense_id"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="recharge"
                column="recharge"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="flightNumber"
                column="flight_number"/>
        <result
                property="mileage"
                column="mileage"/>
        <result
                property="mileageRate"
                column="mileage_rate"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="attendeeNumber"
                column="attendee_number"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="mobileNumber"
                column="mobile_number"/>
        <result
                property="duration"
                column="duration"/>
        <result
                property="telephoneNumber"
                column="telephone_number"/>
        <result
                property="equipmentId"
                column="equipment_id"/>
        <result
                property="taxCodeId"
                column="tax_code_id"/>
        <result
                property="payAmount"
                column="pay_amount"/>
        <result
                property="taxAmount"
                column="tax_amount"/>
        <result
                property="financeComments"
                column="finance_comments"/>
        <result
                property="attachmentCount"
                column="attachment_count"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="supplierName"
                column="supplier_name"/>
        <result
                property="drAccountId"
                column="dr_account_id"/>
        <result
                property="crAccountId"
                column="cr_account_id"/>
        <result
                property="taxAccountId"
                column="tax_account_id"/>
        <result
                property="invoiceNum"
                column="invoice_num"/>
        <result
                property="invoiceCode"
                column="invoice_code"/>
        <result
                property="invoiceSerial"
                column="invoice_serial"/>
        <result
                property="invoiceType"
                column="invoice_type"/>
        <result
                property="source"
                column="source"/>
        <result
                property="finClaimAmount"
                column="fin_claim_amount"/>
        <result
                property="finTaxCodeId"
                column="fin_tax_code_id"/>
        <result
                property="finTaxAmount"
                column="fin_tax_amount"/>
        <result
                property="finNetAmount"
                column="fin_net_amount"/>
        <result
                property="var1"
                column="attachments"/>
        <result
                property="var2"
                column="details"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="tripType"
                column="trip_type"/>
        <result
                property="flightType"
                column="flight_type"/>
        <result
                property="trainType"
                column="train_type"/>
        <result
                property="timeLength"
                column="timeLength"/>
        <result
                property="departBeginDatetime"
                column="depart_begin_datetime"/>
        <result
                property="departEndDatetime"
                column="depart_end_datetime"/>
        <result
                property="departType"
                column="depart_type"/>
        <result
                property="returnBeginDatetime"
                column="return_begin_datetime"/>
        <result
                property="returnEndDatetime"
                column="return_end_datetime"/>
        <result
                property="returnType"
                column="return_type"/>
        <result
                property="price"
                column="price"/>
        <result
                property="quantity"
                column="quantity"/>
        <result
                property="flightClass"
                column="flight_class"/>
        <result
                property="trainClass"
                column="train_class"/>
        <result
                property="passengerList"
                column="passenger_list"/>
        <result
                property="approvalNumber"
                column="approval_number"/>
        <result
                property="linkLineId"
                column="link_line_id"/>
        <result
                property="standardId"
                column="standard_id"/>
        <result
                property="fromCitiesT"
                column="from_cities"/>
        <result
                property="toCitiesT"
                column="to_cities"/>
        <result
                property="returnAmount"
                column="return_amount"/>
        <result
                property="originalAmount"
                column="original_amount"/>
        <result
                property="netAmount"
                column="net_amount"/>
        <result
                property="payClaimAmount"
                column="pay_claim_amount"/>
        <result
                property="offsetDetail"
                column="offset_detail"/>
        <result
                property="finReceiptAmount"
                column="fin_receipt_amount"/>
        <result
                property="finExchangeRate"
                column="fin_exchange_rate"/>
        <result
                property="finPayAmount"
                column="fin_pay_amount"/>
        <result
                property="finPayClaimAmount"
                column="fin_pay_claim_amount"/>
        <result
                property="comExchangeRate"
                column="com_exchange_rate"/>
        <result
                property="costCenterId"
                column="cost_center_id"/>
        <result
                property="budgetId"
                column="budget_id"/>
        <result
                property="address"
                column="address"/>
        <result
                property="companyId"
                column="companyId"/>
        <association
                property="expType"
                column="type_id"
                resultMap="com.cloudpense.expman.mapper.HsfMapper.ExpTypeMap"/>
    </resultMap>

    <resultMap
            id="ExpTypeMap"
            type="ExpType">
        <id
                property="typeId"
                column="type_id"/>
        <result
                property="type"
                column="type"/>
        <result
                property="enabled"
                column="enabled"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="periodType"
                column="period_type"/>
        <result
                property="advanceDate"
                column="advance_date"/>
        <result
                property="periodDay"
                column="period_day"/>
        <result
                property="reminderDay"
                column="reminder_day"/>
        <result
                property="category"
                column="category"/>
        <result
                property="attachmentUrl"
                column="attachment_url"/>
        <result
                property="printUrl"
                column="print_url"/>
        <result
                property="printTemplate"
                column="print_template"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="language"
                column="language"/>
        <result
                property="budgetCategory"
                column="budget_category"/>
        <result
                property="requestRequiredFlag"
                column="request_required_flag"/>
        <result
                property="requestLinkNum"
                column="request_link_num"/>
        <result
                property="claimLinkNum"
                column="claim_link_num"/>
        <result
                property="description"
                column="description"/>
        <result
                property="typeCode"
                column="type_code"/>
        <result
                property="lineBlock"
                column="line_block"/>
        <result
                property="advanceBlock"
                column="advance_block"/>
        <result
                property="returnBlock"
                column="return_block"/>
        <result
                property="staffFlag"
                column="staff_flag"/>
        <result
                property="groupNum"
                column="group_num"/>
        <result
                property="groupName"
                column="group_name"/>
        <result
                property="revisionFlag"
                column="revision_flag"/>
        <result
                property="revisionName"
                column="revision_name"/>
        <result
                property="shareFlag"
                column="share_flag"/>
        <result
                property="companyId"
                column="company_id"/>
        <!--<association property="company" column="company_id"-->
        <!--resultMap="com.cloudpense.expman.mapper.FndCompanyMapper.FndCompanyMap"/>-->
        <!--
        <collection property="columns" column="type_id" ofType="ExpTypeColumn"
                    resultMap="com.cloudpense.expman.mapper.ExpTypeColumnMapper.ExpTypeColumnMap" />
        -->
    </resultMap>

    <select id="getErrorPushedClaims" resultType="java.util.Map">
        select ech.header_id, ech.document_id, ech.document_num, ech.last_update_date, ech.external_status, ech.external_message
        from exp_claim_header ech
        where last_update_date &gt;= #{startDate} and last_update_date &lt;= #{endDate} and ech.external_status = 'failed'
    </select>

</mapper>