<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.KehuaMapper">
    <select id="kehuaDataUpdate" statementType="CALLABLE" resultType="String">
          {call zzkh_update_data(#{type}, #{companyId}, #{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
      </select>

     <update id="normalInsertLog" statementType="CALLABLE">
        insert into fnd_log(company_id, type, content, creation_date)
        values (#{companyId}, #{type}, #{content}, now())
     </update>

    <update id="mailSend" statementType="CALLABLE">
        insert into ntf_queue(type,company_id,status,creation_date,content) values
        ('mail',#{companyId},0,now(),#{content})
    </update>

        <!-- 查询单据头 -->
        <select id="selectExpClaimHeader" resultType="com.cloudpense.expman.Request.KhSapVoucherInfo" parameterType="java.lang.Integer">
            SELECT
                h.header_id headerId,
                h.gl_date glDate,
                t.type_code typeCode,
                es.supplier_code supplierCode,
                est.supplier_name supplierName,
                h.document_num documentNum,
                h.supplier_id supplierId,
                h.column_json columnJson,
                c.company_code companyCode,
                h.submit_date submitDate,
                h.currency_code currencyCode,
                h.internal_type headerInternalType,
                h.column10,
                h.column49,
                h.column1,
                u.column3,
                d.department_code departmentCode,
                d.column1 cdColumn1,
                u.full_name fullName,
                u.employee_number employeeNumber,
                h.advance_amount advanceAmount,
                h.total_amount totalAmount,
                h.total_pay_amount totalPayAmount,
                h.total_pay_currency_amount totalPayCurrencyAmount,
                h.leave_type leaveType,
                pp.project_code projectName,
                su.full_name suFullName,
                h.column13,
                d2.department_code branchCode
            FROM
                exp_claim_header h
                LEFT JOIN exp_header_type t ON h.header_type_id = t.type_id
                LEFT JOIN exp_supplier es ON h.supplier_id = es.supplier_id
                LEFT JOIN exp_supplier_tl est ON h.supplier_id = est.supplier_id
                AND est.`language` = 'zh_CN'
                LEFT JOIN fnd_user u ON h.charge_user = u.user_id
                LEFT JOIN fnd_user su ON h.submit_user = su.user_id
                LEFT JOIN fnd_company c ON h.company_id = c.company_id
                LEFT JOIN fnd_department d ON h.charge_department = d.department_id
                LEFT JOIN pjm_project pp ON h.project_name = pp.project_id
                LEFT JOIN fnd_department d2 ON h.branch_id = d2.department_id
            WHERE
                h.company_id = 14870
                AND h.STATUS = 'approved'
                and h.document_id = #{documentId}
        </select>
    <!-- 查询单据行借 -->
    <select id="selectExpClaimLineDr" resultType="com.cloudpense.expman.Request.KhSapVoucherInfo"
            parameterType="java.lang.Integer">
            select
                a.account_code accountCode,
                sum(l.fin_claim_amount) finClaimAmount
            from
                exp_claim_line l
                left join gl_account a on l.dr_account_id = a.account_id
            where
                l.header_id = #{headerId}
            GROUP BY accountCode
        </select>
    <!-- 查询单据行贷 -->
    <select id="selectExpClaimLineCr" resultType="com.cloudpense.expman.Request.KhSapVoucherInfo"
            parameterType="java.lang.Integer">
            select
            a.account_code accountCode,
            sum(l.fin_claim_amount) finClaimAmount
            from
            exp_claim_line l
            left join gl_account a on l.cr_account_id = a.account_id
            where
            l.header_id = #{headerId}
            GROUP BY accountCode
        </select>

    <!-- 查询单据行 -->
    <select id="getClaimLine" resultType="com.cloudpense.expman.Request.KhSapVoucherInfo"
            parameterType="java.lang.Integer">
            select
                l.column6,
                l.column7,
                l.fin_claim_amount finClaimAmount,
                l.fin_receipt_amount finReceiptAmount,
                l.fin_tax_amount finTaxAmount,
                etc.tax_code taxCode,
                l.fin_net_amount finNetAmount,
                l.invoice_num invoiceNum,
                l.internal_type lineInternalType,
                ett.type lineType,
                j.account_code drAccountCode,
                d.account_code crAccountCode,
                s.account_code TaxAccountCode,
                l.comments,
                l.receipt_currency receiptCurrency
            from
                exp_claim_line l
                left join exp_tax_code etc on l.fin_tax_code_id = etc.tax_code_id
                left join gl_account j on l.dr_account_id = j.account_id
                left join gl_account d on l.cr_account_id = d.account_id
                left join gl_account s on l.tax_account_id = s.account_id
                left join exp_type_tl ett on l.type_id = ett.type_id
                and ett.language = 'zh_CN'
            where
                l.header_id = #{headerId}
        </select>

        <select id="getAccountName" resultType="String">
            select gat.account_name from gl_account ga
            left join gl_account_tl gat on ga.account_id = gat.account_id
            and gat.language = 'zh_CN'
            where ga.company_id = 14870 and ga.account_code = #{accountCode}
        </select>
</mapper>