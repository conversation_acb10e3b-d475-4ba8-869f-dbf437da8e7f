<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.NipponMapper">
    <update
            id="updateCompany"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_company(#{code}, #{name}, #{type}, #{taxNum}, null, 15220, 'B', '0', #{enable1},
                                         #{enable2})}
    </update>
    <update
            id="updateCostCenter"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'costcenter')}
    </update>
    <update
            id="updatePhoneLimit"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'phone')}
    </update>
    <update
            id="updateFrameOrder"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'frame')}
    </update>
    <update
            id="freshFrameStatus"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(null, 'U')}
    </update>
    <update
            id="updateSupplier"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'supplier')}
    </update>
    <update
            id="updateCustomer"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'customer')}
    </update>
    <update
            id="updateData"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, #{type})}
    </update>
    <update
            id="updateOutWork"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'out')}
    </update>
    <update
            id="updateCredit"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'credit')}
    </update>
    <update
            id="updateExRate"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_rate(#{rate}, #{update})}
    </update>
    <update
            id="updateWbs"
            statementType="CALLABLE">
        {call cpnpp.zznpp_update_data(#{json}, 'wbs')}
    </update>
    <select
            id="queryCompanyCodeList"
            resultType="java.lang.String">
        select department_code
        from cpnpp.fnd_department
        where type = 'B';
    </select>
    <select
            id="getUnsentReimClaim"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where header_type_id in (select type_id from exp_header_type_v where language = 'zh_CN' and type like '%报销%')
          and status = 'preapproved'
          and (external_status in ('failed', 'modified', 'resubmit', 'rejected') or external_status is null);
    </select>
    <select
            id="queryEmployeeNum"
            resultType="java.lang.String">
        select employee_number
        from fnd_user
        where user_id = #{id}
    </select>

    <select
            id="queryUserId"
            resultType="integer">
        select u.user_id, r.role_id
        from fnd_user u,
             fnd_user_role r
        where u.user_id = r.user_id
          and r.role_id in (3, 7)
          and u.employee_number = #{emp}
        limit 1
    </select>
    <select
            id="queryUserName"
            resultType="string">
        select user_name
        from fnd_user
        where employee_number = #{emp}
    </select>
    <select
            id="queryDeptCode"
            resultType="java.lang.String">
        select department_code
        from fnd_department
        where department_id = #{id};
    </select>
    <select
            id="queryDeptName"
            resultType="java.lang.String">
        select department_name
        from fnd_department_tl
        where department_id = #{id}
          and language = 'zh_CN';
    </select>
    <select
            id="queryDeptNameByCode"
            resultType="java.lang.String">
        SELECT
            fdt.department_name
        FROM
            fnd_department fd
            LEFT JOIN fnd_department_tl fdt ON fdt.department_id = fd.department_id
            AND fdt.LANGUAGE = 'zh_CN'
        WHERE
            fd.department_code = #{code}
    </select>
    <select
            id="queryClaimLine"
            resultMap="ExpClaimLineMap">
        select l.*, t.type_code
        from exp_claim_line l
                 left join exp_type_v t on l.type_id = t.type_id and t.language = 'zh_CN'
        where l.header_id = #{value}
    </select>
    <select
            id="queryClaimDetail"
            resultMap="ExpClaimLineMap">
        select d.*
        from exp_claim_detail d
        where d.header_id = #{value}

    </select>
    <select
            id="queryCityName"
            resultType="java.lang.String">
        select city_name
        from fnd_city_tl
        where language = 'zh_CN'
          and city_id = #{id}
    </select>
    <select
            id="queryCityNames"
            resultType="java.lang.String">
        SELECT
            city_name
        FROM
            exp_claim_city c
            LEFT JOIN fnd_city_tl t ON c.city_id = t.city_id
        WHERE
            LANGUAGE = 'zh_CN'
            AND header_id = #{headerId}
    </select>
    <select
            id="queryProjectInfo"
            resultType="java.lang.String">
        select json_object('id', project_code, 'code', column1, 'name', project_name)
        from pjm_project_v
        where company_id = 15220
          and language = 'zh_CN'
          and project_id = #{id}
    </select>
    <select
            id="queryLinkHeader"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where header_id =
              (select link_header_id from exp_claim_header where header_id = #{headerID} limit 1);
    </select>
    <select
            id="queryApproveList"
            resultType="java.lang.String">
        select json_object('ApproveLevel', p.sequence_num, 'ApproveEmployeesName', u.full_name,
                           'ApproveEmployeesNumber', u.employee_number, 'ApproveEmployeesEmail',
                           u.email_address, 'StartTime', ifnull(unix_timestamp(open_date), 0), 'OrderNumber', #{param2},
                           'TOrderNumber', #{param3})
        from fnd_workflow_path p
                 left join fnd_user u on p.user_id = u.user_id
        where p.company_id = 15220
          and type not in ('approving', 'open')
          and p.source_id = #{param1}
    </select>
    <select
            id="queryFileList"
            resultType="com.cloudpense.expman.entity.ExpClaimAttachment">
        select file_name as fileName, attachment_url as attachmentUrl, creation_date as creationDate
        from exp_claim_attachment
        where (claim_header_id = #{param1} or claim_line_id in ${param2})
          and company_id = 15220;
    </select>
    <update id="updateExternalStatus">
        update exp_claim_header
        set external_status  = #{extStatus},
            external_message = #{extMsg},
            column44 = #{extMsg},
            external_date    = now()
        where header_id = #{headerId}
    </update>
    <update id="updateSapNo">
        update exp_claim_header
        set column9 = #{sapNo}
        where header_id = #{id}
    </update>
    <update
            id="updateBudget"
            statementType="CALLABLE">
        {call zznpp_budget_update_batch(#{json})}
    </update>
    <update
            id="updateBudgetYear"
            statementType="CALLABLE">
        {call zznpp_budget_year_gen()}
    </update>
    <update id="updateWorkFlow">
        update fnd_workflow_path
        set status = 'commenting'
        where status = 'approving'
          and type = 'A'
          and company_id = 15220
          and source_id = #{claimId}
    </update>
    <update id="updateWorkFlow2">
        update fnd_workflow_path
        set status = 'approving'
        where status = 'commenting'
          and type = 'A'
          and company_id = 15220
          and source_id = #{claimId}
    </update>
    <select
            id="queryTravel"
            resultMap="ExpClaimHeaderMap">
        select h.header_id                                               as headerId,
               f.path_id                                                 as path_id,
               h.document_num                                            as documentNum,
               case h.header_type_id when 126807 then 2002 else 2003 end as column1,
               timezone(h.start_datetime, 8)                             as startDatetime,
               timezone(h.end_datetime, 8)                               as endDatetime,
               u.employee_number                                         as column2,
               h.description,
               h.status,
               h.header_id,
               case when h.last_update_date > h.external_date then 1 else 0 end as column5
        from exp_claim_header h
                 left join fnd_user u on h.charge_user = u.user_id
                 left join fnd_workflow_path f on f.source_id = h.header_id
        where h.company_id = 15220
        <if test="departmentIds != null">
            and h.branch_id in
            <foreach collection="departmentIds" item="item" open =	"(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
          and h.header_type_id in (126807, 126813)
--           and h.status in ('approved', 'submitted', 'closed')
          and h.status in ('approved', 'closed')
          and (h.external_status = 'failed' or
               h.external_status is null or
               h.last_update_date > h.external_date or
               (h.status = 'closed' and h.external_status = 'success'))
               GROUP BY h.document_num;
    </select>

    <select
            id="queryZznppCompanyGroup"
            resultType="java.lang.Integer">
        select department_id from fnd_department where enabled_flag = 'Y' and type='B';
    </select>

    <select
            id="getSAPNo"
            statementType="CALLABLE"
            parameterType="map">
        {call zznpp_company_sapno(#{dept,mode=IN,jdbcType=VARCHAR}, #{result,mode=OUT,jdbcType=VARCHAR})}
    </select>
    <select
            id="queryClaimHeaderByDocNum"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where status = 'preapproved'
          and document_num = #{docNum}
          and company_id = 15220;
    </select>
    <select
            id="queryClaimHeaderByDocNum1"
            resultMap="ExpClaimHeaderMap">
        select *
        from exp_claim_header
        where status not in ('cancelled', 'deleted', 'incomplete')
          and document_num = #{docNum}
          and company_id = 15220;
    </select>
    <select
            id="queryWorkFlow2"
            resultType="java.lang.Integer">
        select path_id
        from fnd_workflow_path
        where status like '%ing'
          and type = 'A'
          and source_id = #{claimId}
    </select>

    <insert
            id="saveWithFinanceInfoNew"
            statementType="CALLABLE">
        { call zzexp_claim_finance_approve_new(
                #{documentId}, #{userId}, #{agentId}, #{companyId}, #{language}, #{input},
                #{returnCode,mode=OUT,jdbcType=VARCHAR},
                #{returnMessage,mode=OUT,jdbcType=VARCHAR}
            )}
    </insert>

    <select
            id="queryNtfCompany"
            resultMap="hsfNtfMap">
        select *
        from ntf_queue_company
        where status in (0)
          and type = 'nippon_approve'
          and company_id = 15220
    </select>

    <select
            id="queryNtfCompanyEmail"
            resultMap="hsfNtfMap">
        select *
        from ntf_queue_company
        where status in (0, 3, 10, 13, 14, 15)
          and type = 'nippon_mail'
          and company_id = 15220;
    </select>

    <select
            id="queryClaimInvoice"
            resultType="int">
        select receipt_id
        from exp_receipt
        where expense_id in (select expense_id from exp_claim_line where header_id = #{headerId});
    </select>
    <select
            id="qureyGlp"
            resultType="java.lang.String">
        select date_format(start_date, '%Y%m')
        from gl_period
        where period_id = #{id}
    </select>
    <select
            id="queryLastApprovedTime"
            resultType="java.util.Date">
        select max(open_date)
        from fnd_workflow_path
        where source_id = #{id}
    </select>
    <select
            id="queryHeaderToUpdateSendStatus"
            resultType="java.lang.String">
        select column9
        from exp_claim_header
        where header_type_id in (126805, 126806, 126810)
          and column9 is not null
          and column9 != ''
          and last_update_date between date_add(now(), interval -2 week) and now();
    </select>
    <select
            id="queryTravelPush"
            resultType="java.lang.String">
        select enable_flag
        from zz_update_time
        where type = 'nippon_last_push'
        limit 1
    </select>
    <select
            id="queryTravelPushByGroup"
            resultType="java.lang.String">
        select enabled_flag
        from zznpp_absense_sync
        where business_unit = #{businessGroup}
    </select>
    <select
            id="queryPayMethod"
            resultType="java.lang.Integer">
        select pay_method_id
        from exp_expense
        where expense_id = (select expense_id from exp_claim_line where line_id = #{lineId});
    </select>
    <select
            id="queryLineInvoice"
            resultType="java.lang.String">
        select json_object('code', invoice_num, 'tax', tax_amount)
        from exp_receipt
        where invoice_type = '01'
          and expense_id in (select expense_id from exp_claim_line where line_id = #{lineId})
    </select>
    <select
            id="queryLineInvoiceOther"
            resultType="java.lang.String">
         select json_object('code', r.invoice_num, 'tax', l.tax_amount)
        from exp_receipt r LEFT JOIN exp_claim_line l  on l.expense_id = r.expense_id
        where r.invoice_type != '01' and l.tax_amount > 0
        and l.line_id = #{lineId} GROUP BY line_id
    </select>
    <select id="queryUserFlag" resultType="java.lang.String">
        select column50
        from fnd_user
        where employee_number = #{empNo};
    </select>
    <select id="getUpdatedInvoice" resultType="java.lang.String">
        SELECT concat('{"invoiceList": [', group_concat(
                json_object(
                        'invoicekey', temp.receipt_id,
                        'status', case
                                      when temp.status = 0 then '未使用'
                                      when temp.status = 1 then '已使用'
                                      when temp.status = 2 then '已锁定'
                                      when temp.status = -1 then '已删除'
                                      else '未知状态' end,
                        'chargeuser', (select employee_number
                                       from fnd_user
                                       where user_id = ifnull(temp.c1, temp.c2)),
                        'source_file',temp.source_file
                    )), ']}') from
            (
                select r.receipt_id,r.status,e.created_by as c1,r.created_by as c2,r.source_file,e.last_update_date l1,r.												last_update_date l2
                from exp_receipt r
                         left join exp_expense e on r.expense_id = e.expense_id
                where
                    e.last_update_date > date_sub(now(), interval 11 minute)
                union all
                select r.receipt_id,r.status,e.created_by as c1,r.created_by as c2,r.source_file,e.last_update_date l1,r.												last_update_date l2
                from exp_receipt r
                         left join exp_expense e on r.expense_id = e.expense_id
                where
                    r.last_update_date > date_sub(now(), interval 11 minute)) temp
        WHERE ifnull(temp.l1, temp.l2) > date_sub(now(), interval 11 minute);
    </select>
    <update
            id="freshNtfStatus"
            statementType="CALLABLE">
        {call zznippon_ntf_delete(#{cid})}
    </update>
    <update id="updateNtfStatus">
        update ntf_queue_company
        set status=#{status},
            content=#{content}
        where id = #{id}
    </update>
    <delete id="deleteNtf">
        delete
        from ntf_queue_company
        where id = #{id}
    </delete>
    <update id="updateNipponStatus">
        update exp_claim_header
        set column10        = #{finStatus},
            external_status = #{external}
        where header_id = #{id}
          and company_id = 15220;
    </update>
    <update id="updateReimStatus">
        update exp_claim_header
        set external_status = 'modified'
        where header_id in (select h1.header_id
                            from (select h.header_id
                                  from exp_claim_header h,
                                       fnd_workflow_path p
                                  where h.header_id = p.source_id
                                    and p.status = 'Approving'
                                    and p.type = 'A'
                                    and h.external_status = 'modifying') h1)
          and header_type_id in (select type_id from exp_header_type_v where language = 'zh_CN' and type like '%报销%');
    </update>
    <update id="updateSendStatus">
        update exp_claim_header
        set column11=#{11}
        where column9 = #{9}
          and company_id = 15220
    </update>
    <update
            id="rejectHeader"
            statementType="CALLABLE">
        {call zznpp_reject_header(#{headerId}, #{tips})}
    </update>
    <update id="toggleTravelPush">
        update zz_update_time
        set enable_flag = #{status}
        where type = 'nippon_last_push'
    </update>
    <update id="toggleTravelPushByGroup">
        update zznpp_absense_sync
        set enabled_flag = #{status}
        where business_unit = #{businessGroup}
    </update>
    <update id="unlockReceipt">
        update exp_receipt
        set status = 0
        where expense_id in (select expense_id from exp_claim_line where header_id = #{headerId})
    </update>
    <update id="updateClaimCredit">
        update exp_claim_header
        set column17 = (case
                            when (select column25 from fnd_user where user_id = #{userId} limit 1) = '02' then '◉'
                            else '' end)
        where header_id = #{headerId}
    </update>
    <update id="updatePostingDate">
        update exp_claim_header
        set column20 = if(#{type} = 'I', #{date}, null)
        where header_id = #{header_id}
    </update>
    <insert id="updateFndWorkflowPath" statementType="CALLABLE">
        {
        call fnd_workflow_approve( #{pathId}, #{note}, #{userId}, #{clientId}, #{language}, #{status},
        #{returnCode,mode=OUT,jdbcType=VARCHAR}, #{returnMessage,mode=OUT,jdbcType=VARCHAR})
        }
    </insert>
    <select id="queryLovValueList" resultType="java.lang.String">
        select flv.value_code from fnd_lov fl, fnd_lov_value flv
        where flv.lov_id = fl.lov_id and fl.lov_name = #{lovName} and flv.enabled_flag = 'Y'
    </select>
    <resultMap
            id="hsfNtfMap"
            type="com.cloudpense.expman.entity.hsf.HsfNtf">
        <id
                property="id"
                column="id"/>
        <result
                property="type"
                column="type"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="status"
                column="status"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="content"
                column="content"/>
    </resultMap>

    <resultMap
            id="ExpClaimHeaderMap"
            type="com.cloudpense.expman.entity.ExpClaimHeader">
        <result
                property="externalStatus"
                column="external_status"/>
        <result
                property="externalMessage"
                column="external_message"/>
        <result
                property="externalDate"
                column="external_date"/>
        <result
                property="headerId"
                column="header_id"/>
        <result
                property="pathId"
                column="path_id"/>
        <result
                property="companyId"
                column="company_id"/>
        <result
                property="documentId"
                column="document_id"/>
        <result
                property="language"
                column="language"/>
        <result
                property="userAccountId"
                column="user_account_id"/>
        <result
                property="supplierAccountId"
                column="supplier_account_id"/>
        <result
                property="documentNum"
                column="document_num"/>
        <result
                property="totalAmount"
                column="total_amount"/>
        <result
                property="totalClaimAmount"
                column="total_claim_amount"/>
        <result
                property="advanceAmount"
                column="advance_amount"/>
        <result
                property="currencyCode"
                column="currency_code"/>
        <result
                property="submitDate"
                column="submit_date"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="description"
                column="description"/>
        <result
                property="status"
                column="status"/>
        <result
                property="financeStatus"
                column="fin_status"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creatorName"
                column="creator_name"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="totalPayAmount"
                column="total_pay_amount"/>
        <result
                property="glBatchId"
                column="gl_batch_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="userId"
                column="user_id"/>
        <result
                property="budgetType"
                column="budget_type"/>
        <result
                property="submitDepartment"
                column="submit_department"/>
        <result
                property="chargeDepartment"
                column="charge_department"/>
        <result
                property="submitUser"
                column="submit_user"/>
        <result
                property="chargeUser"
                column="charge_user"/>
        <result
                property="invoiceFlag"
                column="invoice_flag"/>
        <result
                property="headerTypeId"
                column="header_type_id"/>
        <result
                property="paymentDate"
                column="payment_date"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="ignoreWarning"
                column="ignore_warning"/>
        <result
                property="approverList"
                column="approver_list"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="pathStatus"
                column="path_status"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="travelMethod"
                column="travel_method"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="leaveType"
                column="leave_type"/>
        <result
                property="leaveDay"
                column="leave_day"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="dueDate"
                column="due_date"/>
        <result
                property="reminderDate"
                column="reminder_date"/>
        <result
                property="planStartDatetime"
                column="plan_start_datetime"/>
        <result
                property="planEndDatetime"
                column="plan_end_datetime"/>
        <result
                property="ruleName"
                column="rule_name"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="childStatus"
                column="child_status"/>
        <result
                property="glStatus"
                column="gl_status"/>
        <result
                property="glMessage"
                column="gl_message"/>
        <result
                property="type"
                column="type"/>
        <result
                property="journalNum"
                column="journal_num"/>
        <result
                property="vouchers"
                column="vouchers"/>
        <result
                property="agentId"
                column="agent_id"/>
        <result
                property="financeDescription"
                column="finance_description"/>
        <result
                property="branchId"
                column="branch_id"/>
        <result
                property="payObject"
                column="pay_object"/>
        <result
                property="payUser"
                column="pay_user"/>
        <result
                property="branchAccountId"
                column="branch_account_id"/>
        <result
                property="glPeriod"
                column="gl_period"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
    </resultMap>

    <resultMap
            id="ExpClaimLineMap"
            type="ExpClaimLine">
        <id
                property="lineId"
                column="line_id"/>
        <result
                property="linkHeaderId"
                column="link_header_id"/>
        <result
                property="receiptDate"
                column="receipt_date"/>
        <result
                property="receiptAmount"
                column="receipt_amount"/>
        <result
                property="receiptCurrency"
                column="receipt_currency"/>
        <result
                property="claimAmount"
                column="claim_amount"/>
        <result
                property="claimCurrency"
                column="claim_currency"/>
        <result
                property="exchangeRate"
                column="exchange_rate"/>
        <result
                property="comments"
                column="comments"/>
        <result
                property="receiptLocation"
                column="receipt_location"/>
        <result
                property="locationFrom"
                column="location_from"/>
        <result
                property="locationTo"
                column="location_to"/>
        <result
                property="shopName"
                column="shop_name"/>
        <result
                property="shopLocation"
                column="shop_location"/>
        <result
                property="startDatetime"
                column="start_datetime"/>
        <result
                property="endDatetime"
                column="end_datetime"/>
        <result
                property="attendeeList"
                column="attendee_list"/>
        <result
                property="expenseId"
                column="expense_id"/>
        <result
                property="payMethodId"
                column="pay_method_id"/>
        <result
                property="column1"
                column="column1"/>
        <result
                property="column2"
                column="column2"/>
        <result
                property="column3"
                column="column3"/>
        <result
                property="column4"
                column="column4"/>
        <result
                property="column5"
                column="column5"/>
        <result
                property="column6"
                column="column6"/>
        <result
                property="column7"
                column="column7"/>
        <result
                property="column8"
                column="column8"/>
        <result
                property="column9"
                column="column9"/>
        <result
                property="column10"
                column="column10"/>
        <result
                property="column11"
                column="column11"/>
        <result
                property="column12"
                column="column12"/>
        <result
                property="column13"
                column="column13"/>
        <result
                property="column14"
                column="column14"/>
        <result
                property="column15"
                column="column15"/>
        <result
                property="column16"
                column="column16"/>
        <result
                property="column17"
                column="column17"/>
        <result
                property="column18"
                column="column18"/>
        <result
                property="column19"
                column="column19"/>
        <result
                property="column20"
                column="column20"/>
        <result
                property="column21"
                column="column21"/>
        <result
                property="column22"
                column="column22"/>
        <result
                property="column23"
                column="column23"/>
        <result
                property="column24"
                column="column24"/>
        <result
                property="column25"
                column="column25"/>
        <result
                property="column26"
                column="column26"/>
        <result
                property="column27"
                column="column27"/>
        <result
                property="column28"
                column="column28"/>
        <result
                property="column29"
                column="column29"/>
        <result
                property="column30"
                column="column30"/>
        <result
                property="column31"
                column="column31"/>
        <result
                property="column32"
                column="column32"/>
        <result
                property="column33"
                column="column33"/>
        <result
                property="column34"
                column="column34"/>
        <result
                property="column35"
                column="column35"/>
        <result
                property="column36"
                column="column36"/>
        <result
                property="column37"
                column="column37"/>
        <result
                property="column38"
                column="column38"/>
        <result
                property="column39"
                column="column39"/>
        <result
                property="column40"
                column="column40"/>
        <result
                property="column41"
                column="column41"/>
        <result
                property="column42"
                column="column42"/>
        <result
                property="column43"
                column="column43"/>
        <result
                property="column44"
                column="column44"/>
        <result
                property="column45"
                column="column45"/>
        <result
                property="column46"
                column="column46"/>
        <result
                property="column47"
                column="column47"/>
        <result
                property="column48"
                column="column48"/>
        <result
                property="column49"
                column="column49"/>
        <result
                property="column50"
                column="column50"/>
        <result
                property="productName"
                column="product_name"/>
        <result
                property="recharge"
                column="recharge"/>
        <result
                property="destinationCity"
                column="destination_city"/>
        <result
                property="destinationCityTo"
                column="destination_city_to"/>
        <result
                property="createdBy"
                column="created_by"/>
        <result
                property="creationDate"
                column="creation_date"/>
        <result
                property="lastUpdatedBy"
                column="last_updated_by"/>
        <result
                property="lastUpdateDate"
                column="last_update_date"/>
        <result
                property="flightNumber"
                column="flight_number"/>
        <result
                property="mileage"
                column="mileage"/>
        <result
                property="mileageRate"
                column="mileage_rate"/>
        <result
                property="supplierId"
                column="supplier_id"/>
        <result
                property="attendeeNumber"
                column="attendee_number"/>
        <result
                property="businessPurpose"
                column="business_purpose"/>
        <result
                property="mobileNumber"
                column="mobile_number"/>
        <result
                property="duration"
                column="duration"/>
        <result
                property="telephoneNumber"
                column="telephone_number"/>
        <result
                property="equipmentId"
                column="equipment_id"/>
        <result
                property="taxCodeId"
                column="tax_code_id"/>
        <result
                property="payAmount"
                column="pay_amount"/>
        <result
                property="taxAmount"
                column="tax_amount"/>
        <result
                property="financeComments"
                column="finance_comments"/>
        <result
                property="attachmentCount"
                column="attachment_count"/>
        <result
                property="projectName"
                column="project_name"/>
        <result
                property="customerName"
                column="customer_name"/>
        <result
                property="supplierName"
                column="supplier_name"/>
        <result
                property="drAccountId"
                column="dr_account_id"/>
        <result
                property="crAccountId"
                column="cr_account_id"/>
        <result
                property="taxAccountId"
                column="tax_account_id"/>
        <result
                property="invoiceNum"
                column="invoice_num"/>
        <result
                property="invoiceCode"
                column="invoice_code"/>
        <result
                property="invoiceSerial"
                column="invoice_serial"/>
        <result
                property="invoiceType"
                column="invoice_type"/>
        <result
                property="source"
                column="source"/>
        <result
                property="finClaimAmount"
                column="fin_claim_amount"/>
        <result
                property="finTaxCodeId"
                column="fin_tax_code_id"/>
        <result
                property="finTaxAmount"
                column="fin_tax_amount"/>
        <result
                property="finNetAmount"
                column="fin_net_amount"/>
        <result
                property="var1"
                column="attachments"/>
        <result
                property="var2"
                column="details"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="tripType"
                column="trip_type"/>
        <result
                property="flightType"
                column="flight_type"/>
        <result
                property="trainType"
                column="train_type"/>
        <result
                property="timeLength"
                column="timeLength"/>
        <result
                property="departBeginDatetime"
                column="depart_begin_datetime"/>
        <result
                property="departEndDatetime"
                column="depart_end_datetime"/>
        <result
                property="departType"
                column="depart_type"/>
        <result
                property="returnBeginDatetime"
                column="return_begin_datetime"/>
        <result
                property="returnEndDatetime"
                column="return_end_datetime"/>
        <result
                property="returnType"
                column="return_type"/>
        <result
                property="price"
                column="price"/>
        <result
                property="quantity"
                column="quantity"/>
        <result
                property="flightClass"
                column="flight_class"/>
        <result
                property="trainClass"
                column="train_class"/>
        <result
                property="passengerList"
                column="passenger_list"/>
        <result
                property="approvalNumber"
                column="approval_number"/>
        <result
                property="linkLineId"
                column="link_line_id"/>
        <result
                property="standardId"
                column="standard_id"/>
        <result
                property="fromCitiesT"
                column="from_cities"/>
        <result
                property="toCitiesT"
                column="to_cities"/>
        <result
                property="returnAmount"
                column="return_amount"/>
        <result
                property="originalAmount"
                column="original_amount"/>
        <result
                property="netAmount"
                column="net_amount"/>
        <result
                property="payClaimAmount"
                column="pay_claim_amount"/>
        <result
                property="offsetDetail"
                column="offset_detail"/>
        <result
                property="finReceiptAmount"
                column="fin_receipt_amount"/>
        <result
                property="finExchangeRate"
                column="fin_exchange_rate"/>
        <result
                property="finPayAmount"
                column="fin_pay_amount"/>
        <result
                property="finPayClaimAmount"
                column="fin_pay_claim_amount"/>
        <result
                property="comExchangeRate"
                column="com_exchange_rate"/>
        <result
                property="costCenterId"
                column="cost_center_id"/>
        <result
                property="budgetId"
                column="budget_id"/>
        <result
                property="address"
                column="address"/>
        <result
                property="companyId"
                column="companyId"/>
        <result
                property="budgetPeriod"
                column="budget_period"/>
        <association
                property="expType"
                column="type_id"
                resultMap="ExpTypeMap"/>
    </resultMap>

    <resultMap
            id="ExpTypeMap"
            type="com.cloudpense.expman.entity.ExpType">
        <id
                property="typeId"
                column="type_id"/>
        <result
                property="type"
                column="type"/>
        <result
                property="enabled"
                column="enabled"/>
        <result
                property="priority"
                column="priority"/>
        <result
                property="periodType"
                column="period_type"/>
        <result
                property="advanceDate"
                column="advance_date"/>
        <result
                property="periodDay"
                column="period_day"/>
        <result
                property="reminderDay"
                column="reminder_day"/>
        <result
                property="category"
                column="category"/>
        <result
                property="attachmentUrl"
                column="attachment_url"/>
        <result
                property="printUrl"
                column="print_url"/>
        <result
                property="printTemplate"
                column="print_template"/>
        <result
                property="internalType"
                column="internal_type"/>
        <result
                property="language"
                column="language"/>
        <result
                property="budgetCategory"
                column="budget_category"/>
        <result
                property="requestRequiredFlag"
                column="request_required_flag"/>
        <result
                property="requestLinkNum"
                column="request_link_num"/>
        <result
                property="claimLinkNum"
                column="claim_link_num"/>
        <result
                property="description"
                column="description"/>
        <result
                property="typeCode"
                column="type_code"/>
        <result
                property="lineBlock"
                column="line_block"/>
        <result
                property="advanceBlock"
                column="advance_block"/>
        <result
                property="returnBlock"
                column="return_block"/>
        <result
                property="staffFlag"
                column="staff_flag"/>
        <result
                property="groupNum"
                column="group_num"/>
        <result
                property="groupName"
                column="group_name"/>
        <result
                property="revisionFlag"
                column="revision_flag"/>
        <result
                property="revisionName"
                column="revision_name"/>
        <result
                property="shareFlag"
                column="share_flag"/>
        <result
                property="companyId"
                column="company_id"/>
        <!--<association property="company" column="company_id"-->
        <!--resultMap="com.cloudpense.expman.mapper.FndCompanyMapper.FndCompanyMap"/>-->
        <!--
        <collection property="columns" column="type_id" ofType="ExpTypeColumn"
                    resultMap="com.cloudpense.expman.mapper.ExpTypeColumnMapper.ExpTypeColumnMap" />
        -->
    </resultMap>

    <select id="deptLeaderRequestJsonArray" resultType="java.lang.String">
        select flv.column10 from fnd_lov fl ,fnd_lov_value flv
        where  fl.lov_id=flv.lov_id
          and fl.lov_name='DOA Approval Level Mapping JSON'
          and flv.value_code='json_value'
    </select>

    <update id="updateEmployeeWhoLeft" parameterType="com.cloudpense.expman.entity.EmployeeWhoLeft">
        update fnd_user
        set inactive_date=str_to_date(#{inactiveDate},'%Y-%m-%d')
        where employee_number=#{employeeCode}
    </update>
    <update id="updateEmployeeWhoLeftInactive" parameterType="com.cloudpense.expman.entity.EmployeeWhoLeft">
        update fnd_user
        set user_name     = concat(user_name, '_inactive'),
            email_address = concat(email_address, '_inactive'),
            column40      = 'Y'
        where employee_number=#{employeeCode}
          and inactive_date &lt; sysdate()
          and column40 is null
          and char_length(inactive_date) &lt; 56
          and char_length(email_address) &lt; 247;
    </update>

</mapper>
