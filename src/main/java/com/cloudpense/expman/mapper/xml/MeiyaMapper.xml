<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.MeiyaMapper">

    <select id="glBatchCreateMeiya" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_meiya(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="getDingTalkPushTasks" statementType="CALLABLE" resultType="string">
        {call meiya_dingtalk_portal(#{companyId})}
    </select>

    <select id="getBindUserInfoByUserId" resultMap="BindUserInfoMap">
        select * from fnd_user_binding
        where company_id = #{companyId}
        and platform = #{platform}
        and user_id = #{userId}
    </select>

    <select id="getUserIdsByPositionId" resultType="Integer">
        select user_id from fnd_user_position
        where company_id = #{companyId}
        and position_id = #{positionId}
    </select>
    <select id="findBindByMobile" resultType="com.cloudpense.expman.entity.BindUserInfo">
            select user_id as userId from fnd_user_binding
            where platform = #{platform} and mobile = #{mobile}

    </select>

    <select id="findUserNameById" resultType="String">
        select user_name from fnd_user where user_id = #{userId}
    </select>

    <update id="updateGlStatusByDocumentId">
        update exp_claim_header
        set gl_status = #{glStatus}, gl_message = #{glMessage}
        where document_id = #{documentId} and status != 'deleted'
    </update>

    <resultMap id="BindUserInfoMap" type="com.cloudpense.expman.entity.BindUserInfo">
        <result property="userId" column="user_id"/>
        <result property="companyId" column="company_id"/>
        <result property="platform" column="platform"/>
        <result property="bindId" column="userid"/>
        <result property="bindName" column="user_name"/>
        <result property="corpId" column="companyid"/>
        <result property="accesstoken" column="accesstoken"/>
        <result property="refreshToken" column="refreshToken"/>
        <result property="mobile" column="mobile"/>
        <result property="role" column="role"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="expiresIn" column="expires_in"/>
        <result property="updateStatus" column="update_status"/>
    </resultMap>

</mapper>