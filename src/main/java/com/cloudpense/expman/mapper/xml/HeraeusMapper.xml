<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.HeraeusMapper">

    <select id="glBatchCreateHeraeus2" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_heraeus_22180(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="glBatchCreateHeraeus1" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_heraeus_17239(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="glBatchCreateHeraeus" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_heraeus(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <update id="updateHeraeusEmployee" statementType="CALLABLE">
        {call zzheraeus_update_sap_17239(#{type},#{companyId},#{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </update>
    <select id="getDepartmentCodeByCompanyIdAndDocumentId" resultType="java.lang.String">
        select distinct fd.department_code from exp_claim_header ech, fnd_department fd
        where fd.department_id = ech.branch_id and ech.company_id = #{companyId}
            and ech.document_id = #{documentId};
    </select>

    <select id="getDocumentNumByDocumentId" resultType="java.lang.String">
        select distinct document_num from exp_claim_header where document_id = #{documentId}
    </select>


    <resultMap
            id="ExpClaimLineMap"
            type="ExpClaimLine">
        <id
                property="lineId"
                column="line_id"/>

        <result
                property="taxAccountId"
                column="tax_account_id"/>
        <result
                property="finTaxCodeId"
                column="fin_tax_code_id"/>
        <result
                property="finTaxAmount"
                column="fin_tax_amount"/>
        <result
                property="companyId"
                column="companyId"/>
    </resultMap>


    <select
            id="queryClaimLineTax"
            resultMap="ExpClaimLineMap">
        select l.tax_account_id, l.fin_tax_amount
        from exp_claim_line l
                 left join exp_claim_header h on l.header_id=h.header_id
        where h.document_id = #{value}
          and  h.status in ('approved','closed');
    </select>

    <!--header attachments -->
    <select id="getHeaderAttachments" resultType="java.util.Map">
        SELECT
            eca.attachment_url,
            ech.document_num
        FROM
            exp_claim_header AS ech
                LEFT JOIN exp_claim_attachment AS eca ON ech.header_id = eca.claim_header_id
        WHERE
            ech.document_id = #{documentId}
          AND eca.attachment_url IS NOT NULL
          AND eca.claim_line_id IS NULL
          AND ech.STATUS IN ('approved','closed');
    </select>

    <!--line attachments -->
    <select id="getLineAttachments" resultType="java.util.Map">
        SELECT
            eca.claim_header_id as header_id,
            eca.claim_line_id as line_id,
            eca.attachment_url,
            eca.file_name,
            etl.type,
            ech.document_num
        FROM
            exp_claim_header AS ech
                JOIN exp_claim_attachment AS eca ON ech.header_id = eca.claim_header_id
                JOIN exp_claim_line AS ecl ON ecl.header_id = ech.header_id
                JOIN exp_type_tl AS etl ON ecl.type_id = etl.type_id
        WHERE
            ech.document_id = #{documentId}
          AND eca.claim_line_id IS NOT NULL
          AND eca.attachment_url IS NOT NULL
          AND ech.STATUS IN ( 'approved', 'closed' )
          AND etl.language = 'zh_TW'
          AND eca.claim_line_id = ecl.line_id
    </select>

    <!--line receipt-->
    <select id="getLineReceipts" resultType="java.util.Map">
        SELECT
            ech.header_id,
            ecl.line_id,
            etl.type,
            ech.document_num,
            er.invoice_attachments -> '$[*].attachment_url' as attachments_url
        FROM
            exp_claim_line AS ecl
                JOIN exp_receipt AS er ON ecl.expense_id = er.expense_id
                JOIN exp_claim_header AS ech ON ech.header_id = ecl.header_id
                JOIN exp_type_tl AS etl ON ecl.type_id = etl.type_id
        WHERE
            invoice_attachments IS NOT NULL
          AND ech.document_id = #{documentId}
          AND etl.language = 'zh_TW'
          AND ech.STATUS IN ( 'approved', 'closed' );
    </select>

    <update id="updateGlStatusAndGlMessage">
        UPDATE exp_claim_header
        SET gl_status = #{gl_status} ,
            gl_message = #{gl_message}
        WHERE
            document_id = #{document_id}
          AND status IN ( 'approved', 'closed' );
    </update>

</mapper>