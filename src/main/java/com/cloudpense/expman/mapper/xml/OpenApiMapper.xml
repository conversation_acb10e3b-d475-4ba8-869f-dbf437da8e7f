<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.OpenApiMapper">
    <select id="commonUpdate" statementType="CALLABLE" resultType="String">
        {call zzopen_api_update_data(#{type}, #{companyId}, #{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

    <insert id="cacheExpAttachmentName" statementType="CALLABLE">
        insert into exp_attachment (company_id, user_id, source, file_name, creation_date)
        values (#{companyId}, #{userId}, #{source}, #{fileName}, now())
    </insert>
</mapper>