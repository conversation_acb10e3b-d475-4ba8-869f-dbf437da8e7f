<?mybatis version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cloudpense.expman.mapper.SapMapper">

    <select id="glBatchCreateCJLR" statementType="CALLABLE" resultType="string">
        {call zzgl_batch_create_cjlr(#{type}, #{input}, #{companyId}, #{userId}, #{language})}
    </select>

    <select id="getProofInfoByCJLR" parameterType="java.util.List" resultType="String">
        select json_object(
        "fee_type",ett.type,
        "document_num",ech.document_num,
        "company_code",fd2.department_code,
        "department_code",fd1.department_code,
        "user_cost_center_code",fd3.department_code,
        "cost_center_code",fd.department_code,
        "employee_number",fu.employee_number,
        "employee_full_name",fut.full_name,
        "document_date",ech.submit_date + interval 8 hour,
        "document_currency",ech.currency_code,
        "document_type",ehtt.type,
        "cr_account",a1.account_code,
        "dr_account",a2.account_code,
        "tax_account",a3.account_code,
        "charge_user_number",fu2.employee_number,
        "charge_user_name",fu2.full_name,
        "dr_amount",ecl.fin_net_amount,
        "cr_amount",ecl.fin_claim_amount,
        "tax_amount",ecl.fin_tax_amount,
        "start_datetime",ech.start_datetime + interval 8 hour,
        "end_datetime",ech.end_datetime + interval 8 hour,
        "project_name",ppt.project_name,
        "column29",ecl.column29,
        "column30",ecl.column30,
        "column3",ecl.column3,
        "column31",ecl.column31,
        "previous_type_code", (select eht.type_code from exp_claim_header ech1 left join exp_header_type eht on eht.type_id = ech1.header_type_id where ech1.header_id = ech.link_header_id),
        "type_code", eht.type_code,
        "previous_document_number", (select ech2.document_num from exp_claim_header ech2 where ech2.header_id = ech.link_header_id),
        "previous_header_column28", (select ech3.column28 from exp_claim_header ech3 where ech3.header_id = ech.link_header_id),
        "previous_header_column10", (select ech3.column10 from exp_claim_header ech3 where ech3.header_id = ech.link_header_id)
        )
        from exp_claim_line ecl
        left join exp_claim_header ech on ech.header_id = ecl.header_id
        left join exp_header_type eht on eht.type_id = ech.header_type_id
        left join fnd_user fu on fu.user_id = ecl.created_by
        left join fnd_company fc on fc.company_id = ecl.company_id
        left join fnd_user_tl fut on fut.user_id = ecl.created_by and fut.language = "zh_CN"
        left join fnd_department fd on fd.department_id = ech.charge_department
        left join fnd_department fd1 on fd1.department_id = ech.submit_department
        left join fnd_department fd3 on fd3.department_id = fu.cost_center_id
        left join exp_header_type_tl ehtt on ehtt.type_id = ech.header_type_id and ehtt.language = "zh_CN"
        left join gl_account_v a1 on ecl.cr_account_id = a1.account_id and a1.`language` = "zh_CN"
        left join gl_account_v a2 on ecl.dr_account_id = a2.account_id and a2.`language` = "zh_CN"
        left join gl_account_v a3 on ecl.tax_account_id = a3.account_id and a3.`language` = "zh_CN"
        left join fnd_user fu2 on fu2.user_id = ech.charge_user
        left join fnd_department_v fdv on fdv.department_id = ech.charge_department and fdv.`language`='zh_CN'
        left join fnd_department fd2 on fd2.department_id = fdv.branch_company_id and fd2.type = 'B'
        left join pjm_project_tl ppt on (ppt.project_id = ech.project_name or ppt.project_id = ecl.project_name) and ppt.`language`='zh_CN'
        left join exp_type_tl ett on ett.type_id = ecl.type_id and ett.`language` = "zh_CN"
        where ech.document_id = #{documentId}
        and ech.`status` in ('approved','closed')
        and ecl.pay_method_id != 44671
    </select>

    <update id="normalInsertLog" statementType="CALLABLE">
        insert into fnd_log(company_id, type, content, creation_date)
        values (#{companyId}, #{type}, #{content}, now())
    </update>

    <select id="getExpClaimHeader" statementType="CALLABLE" resultMap="ExpClaimHeaderMap">
        select * from exp_claim_header where header_id = #{headerId}
    </select>

    <select id="getExpClaimLine" statementType="CALLABLE" resultMap="ExpClaimLineMap">
        select ecl.*, etv.* from exp_claim_line ecl
        left join exp_type_v etv on ecl.type_id = etv.type_id
        where ecl.line_id = #{lineId}
        and etv.language=#{language}
    </select>

    <select id="findAllByHeaderIdWithFinanceInfo" resultMap="ExpClaimLineMap">
        SELECT ecl.*, etv.*
        FROM exp_claim_line ecl
        LEFT JOIN exp_claim_attachment eca
        ON line_id = claim_line_id
        LEFT JOIN fnd_user_tl fut
        ON fut.user_id=ecl.created_by
        left join exp_type_v etv
        on ecl.type_id = etv.type_id
        WHERE header_id = #{headerId}
        AND fut.language=#{language}
        AND etv.language=#{language}
    </select>

    <select id="getWorkflowPathByPositionId" statementType="CALLABLE" resultMap="FndWorkflowPathMap">
        select fwp.* from fnd_workflow_path fwp
        left join exp_claim_header ech on fwp.source_id = ech.header_id
        where ech.company_id = #{companyId} and fwp.position_id = #{positionId}
        and fwp.status = #{status} and ech.status in ('submitted', 'checked')
    </select>

    <select id="getDocumentByHeaderType" statementType="CALLABLE" resultMap="ExpClaimHeaderMap">
        select * from exp_claim_header where header_type_id = #{headerTypeId}
        and company_id = #{companyId} and status = 'approved'
    </select>

    <select id="getDocumentByDocumentNum" statementType="CALLABLE" resultMap="ExpClaimHeaderMap">
        select * from exp_claim_header where document_num = #{documentNum}
        and company_id = #{companyId} and status = 'approved' limit 1
    </select>

    <select id="getUserById" statementType="CALLABLE" resultMap="SystemUserMap">
        select * from fnd_user_position_v
        where user_id = #{userId}
        and language=#{language}
    </select>

    <select id="getAllUsersByCompany" statementType="CALLABLE" resultMap="SystemUserMap">
        select * from fnd_user_position_v
        where company_id = #{companyId}
        and language=#{language}
    </select>

    <select id="getExpTypeById" statementType="CALLABLE" resultMap="ExpTypeMap">
        select * from exp_type_v where type_id=#{typeId} and language= #{language}
    </select>

    <select id="getDepartById" statementType="CALLABLE" resultMap="FndDepartMap">
        select * from fnd_department_v where department_id = #{departmentId} and language = #{language}
    </select>

    <update id="updateWorkflowStatus" statementType="CALLABLE">
        update fnd_workflow_path set status = #{status} where path_id = #{pathId}
    </update>

    <update id="updateSapData" statementType="CALLABLE">
        {call zzsq_update_sap(#{type}, #{companyId}, #{input}, #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </update>

    <select id="getSupplierById" statementType="CALLABLE" resultMap="FndSupplierMap">
        SELECT * FROM exp_supplier_v
        WHERE supplier_id = #{supplierId}
        AND language=#{language}
    </select>

    <select id="getGlAccountById" statementType="CALLABLE" resultMap="GeneralLedgerAccountMap">
        select * from gl_account_v
        where account_id = #{accountId}
        AND language=#{language}
    </select>

    <insert id="glBatchCreate2" statementType="CALLABLE">
        { call gl_batch_create(
        #{type}, #{companyId}, #{input}, #{attachmentUrl}, #{userId},#{batchId,mode=OUT,jdbcType=INTEGER},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR}
        )}
    </insert>

    <select id="getTobeGeneratedExp" statementType="CALLABLE" resultMap="ExpClaimHeaderMap">
        select * from exp_claim_header where gl_status is null and company_id = #{companyId}
        and status in ('approved','closed')
        and internal_type = 'claim'
    </select>

    <select id="getTaxCodeFromDesc" statementType="CALLABLE" resultType="String">
        select description from fnd_lov_value_tl
        where value_id = (select value_id
        from fnd_lov_value where lov_id = (select lov_id
        from fnd_lov where company_id = #{companyId} and lov_name = #{lovName})
        and value_code = #{valueCode}) and language = #{language}
    </select>

    <select id="findUserAccounts" resultMap="FndUserAccountMap">
        SELECT user_id, account_id, company_id, account_name, account_number, bank_name, bank_branch, bank_code,
        bank_swift_id, bank_address, state, city, enabled_flag, primary_flag
        FROM fnd_user_account
        WHERE user_id = #{userId} and company_id = #{companyId}
    </select>

    <resultMap id="FndUserAccountMap" type="com.cloudpense.expman.entity.FndUserAccount">
        <result property="accountId" column="account_id"/>
        <result property="userId" column="user_id"/>
        <result property="companyId" column="company_id"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNumber" column="account_number"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankSwiftId" column="bank_swift_id"/>
        <result property="bankAddress" column="bank_address"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="primaryFlag" column="primary_flag"/>
    </resultMap>

    <resultMap id="SystemUserMap" type="SystemUser">
        <id property="userId" column="user_id"/>
        <result property="departmentId" column="department_id"/>
        <result property="companyId" column="company_id"/>
        <result property="emailAddress" column="email_address"/>
        <result property="fullName" column="full_name"/>
        <result property="mobile" column="mobile"/>
        <result property="password" column="password"/>
        <result property="userName" column="user_name"/>
        <result property="gender" column="gender"/>
        <result property="employeeNumber" column="employee_number"/>
        <result property="enabled" column="enabled"/>
    </resultMap>

    <resultMap id="FndDepartMap" type="FndDepart">
        <result property="departmentId" column="department_id"/>
        <result property="departmentName" column="department_name"/>
        <result property="companyId" column="company_id"/>
        <result property="supervisorId" column="supervisor_id"/>
        <result property="sequenceNum" column="sequence_num"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="ownerId" column="owner_id"/>
        <result property="approvalLevel" column="approval_level"/>
        <result property="language" column="language"/>
        <result property="ownerName" column="owner_name"/>
        <result property="ledger" column="ledger"/>
        <result property="taxNumber" column="tax_number"/>
        <result property="taxAddress" column="tax_address"/>
        <result property="bankName" column="bank_name"/>
        <result property="accountNumber" column="account_number"/>
        <result property="type" column="type"/>
        <result property="departmentCode" column="department_code"/>
        <result property="address" column="address"/>
        <result property="phone" column="phone"/>
        <result property="emailAddress" column="email_address"/>
        <result property="currencyCode" column="currency_code"/>
        <result property="costCenterFlag" column="cost_center_flag"/>
        <result property="branchCompanyId" column="branch_company_id"/>
        <result property="accountsT" column="accounts"/>
        <result property="description" column="description"/>
        <result property="companyName" column="company_name"/>
        <result property="costCenterFlag" column="cost_center_flag"/>
    </resultMap>

    <resultMap id="ExpClaimHeaderMap" type="com.cloudpense.expman.entity.ExpClaimHeader">
        <result property="pathId" column="path_id"/>
        <result property="headerId" column="header_id"/>
        <result property="internalType" column="internal_type"/>
        <result property="travelMethod" column="travel_method"/>
        <result property="businessPurpose" column="business_purpose"/>
        <result property="leaveType" column="leave_type"/>
        <result property="leaveDay" column="leave_day"/>
        <result property="productName" column="product_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="dueDate" column="due_date"/>
        <result property="reminderDate" column="reminder_date"/>
        <result property="planStartDatetime" column="plan_start_datetime"/>
        <result property="planEndDatetime" column="plan_end_datetime"/>
        <result property="financeStatus" column="fin_status"/>
        <result property="userId" column="user_id"/>
        <!--<result property="source" column="source"/>-->
        <result property="sourceId" column="sourceId"/>
        <result property="sourceDocumentId" column="source_document_id"/>
        <result property="documentId" column="document_id"/>
        <result property="documentNum" column="document_num"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="advanceAmount" column="advance_amount"/>
        <result property="currencyCode" column="currency_code"/>
        <result property="submitDate" column="submit_date"/>
        <result property="startDatetime" column="start_datetime"/>
        <result property="endDatetime" column="end_datetime"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="creatorName" column="creator"/>
        <result property="totalPayAmount" column="total_pay_amount"/>
        <result property="glBatchId" column="gl_batch_id"/>
        <result property="linkHeaderId" column="link_header_id"/>
        <result property="sourceDocumentNum" column="source_document_num"/>
        <result property="projectName" column="project_name"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="priority" column="priority"/>
        <result property="ruleName" column="rule_name"/>
        <result property="column1" column="column1"/>
        <result property="column2" column="column2"/>
        <result property="column3" column="column3"/>
        <result property="column4" column="column4"/>
        <result property="column5" column="column5"/>
        <result property="column6" column="column6"/>
        <result property="column7" column="column7"/>
        <result property="column8" column="column8"/>
        <result property="column9" column="column9"/>
        <result property="column10" column="column10"/>
        <result property="column11" column="column11"/>
        <result property="column12" column="column12"/>
        <result property="column13" column="column13"/>
        <result property="column14" column="column14"/>
        <result property="column15" column="column15"/>
        <result property="column16" column="column16"/>
        <result property="column17" column="column17"/>
        <result property="column18" column="column18"/>
        <result property="column19" column="column19"/>
        <result property="column20" column="column20"/>
        <result property="column21" column="column21"/>
        <result property="column22" column="column22"/>
        <result property="column23" column="column23"/>
        <result property="column24" column="column24"/>
        <result property="column25" column="column25"/>
        <result property="column26" column="column26"/>
        <result property="column27" column="column27"/>
        <result property="column28" column="column28"/>
        <result property="column29" column="column29"/>
        <result property="column30" column="column30"/>
        <result property="column31" column="column31"/>
        <result property="column32" column="column32"/>
        <result property="column33" column="column33"/>
        <result property="column34" column="column34"/>
        <result property="column35" column="column35"/>
        <result property="column36" column="column36"/>
        <result property="column37" column="column37"/>
        <result property="column38" column="column38"/>
        <result property="column39" column="column39"/>
        <result property="column40" column="column40"/>
        <result property="column41" column="column41"/>
        <result property="column42" column="column42"/>
        <result property="column43" column="column43"/>
        <result property="column44" column="column44"/>
        <result property="column45" column="column45"/>
        <result property="column46" column="column46"/>
        <result property="column47" column="column47"/>
        <result property="column48" column="column48"/>
        <result property="column49" column="column49"/>
        <result property="column50" column="column50"/>
        <result property="budgetType" column="budget_type"/>
        <result property="submitDepartment" column="submit_department"/>
        <result property="chargeDepartment" column="charge_department"/>
        <result property="submitUser" column="submit_user"/>
        <result property="chargeUser" column="charge_user"/>
        <result property="invoiceFlag" column="invoice_flag"/>
        <result property="headerTypeId" column="header_type_id"/>
        <result property="paymentDate" column="payment_date"/>
        <result property="userAccountId" column="user_account_id"/>
        <result property="pathStatus" column="path_status"/>
        <result property="childStatus" column="child_status"/>
        <result property="supplierAccountId" column="supplier_account_id"/>
        <result property="glStatus" column="gl_status"/>
        <result property="glMessage" column="gl_message"/>
        <result property="type" column="type"/>
        <result property="journalNum" column="journal_num"/>
        <result property="vouchers" column="vouchers"/>
        <result property="agentId" column="agent_id"/>
        <result property="financeDescription" column="finance_description"/>
        <result property="revisionId" column="revision_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="payObject" column="pay_object"/>
        <result property="payUser" column="pay_user"/>
        <result property="branchAccountId" column="branch_account_id"/>
        <result property="glPeriod" column="gl_period"/>
        <result property="companyId" column="company_id"/>
        <result property="createdBy" column="created_by"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <!--<collection property="costElements" column="cost_element_id"-->
                    <!--resultMap="com.cloudpense.expman.mapper.FndCostElementMapper.FndCostElementMap"/>-->
        <!--<collection property="attachments" column="attachment_id"-->
                    <!--resultMap="com.cloudpense.expman.mapper.ExpClaimAttachmentMapper.ExpClaimAttachmentMap"/>-->
    </resultMap>

    <resultMap id="ExpClaimLineMap" type="ExpClaimLine">
        <id property="lineId" column="line_id"/>
        <result property="linkHeaderId" column="link_header_id"/>
        <result property="receiptDate" column="receipt_date"/>
        <result property="receiptAmount" column="receipt_amount"/>
        <result property="receiptCurrency" column="receipt_currency"/>
        <result property="claimAmount" column="claim_amount"/>
        <result property="claimCurrency" column="claim_currency"/>
        <result property="exchangeRate" column="exchange_rate"/>
        <result property="comments" column="comments"/>
        <result property="receiptLocation" column="receipt_location"/>
        <result property="locationFrom" column="location_from"/>
        <result property="locationTo" column="location_to"/>
        <result property="shopName" column="shop_name"/>
        <result property="shopLocation" column="shop_location"/>
        <result property="startDatetime" column="start_datetime"/>
        <result property="endDatetime" column="end_datetime"/>
        <result property="attendeeList" column="attendee_list"/>
        <result property="route" column="route"/>
        <result property="expenseId" column="expense_id"/>
        <result property="column1" column="column1"/>
        <result property="column2" column="column2"/>
        <result property="column3" column="column3"/>
        <result property="column4" column="column4"/>
        <result property="column5" column="column5"/>
        <result property="column6" column="column6"/>
        <result property="column7" column="column7"/>
        <result property="column8" column="column8"/>
        <result property="column9" column="column9"/>
        <result property="column10" column="column10"/>
        <result property="column11" column="column11"/>
        <result property="column12" column="column12"/>
        <result property="column13" column="column13"/>
        <result property="column14" column="column14"/>
        <result property="column15" column="column15"/>
        <result property="column16" column="column16"/>
        <result property="column17" column="column17"/>
        <result property="column18" column="column18"/>
        <result property="column19" column="column19"/>
        <result property="column20" column="column20"/>
        <result property="column21" column="column21"/>
        <result property="column22" column="column22"/>
        <result property="column23" column="column23"/>
        <result property="column24" column="column24"/>
        <result property="column25" column="column25"/>
        <result property="column26" column="column26"/>
        <result property="column27" column="column27"/>
        <result property="column28" column="column28"/>
        <result property="column29" column="column29"/>
        <result property="column30" column="column30"/>
        <result property="column31" column="column31"/>
        <result property="column32" column="column32"/>
        <result property="column33" column="column33"/>
        <result property="column34" column="column34"/>
        <result property="column35" column="column35"/>
        <result property="column36" column="column36"/>
        <result property="column37" column="column37"/>
        <result property="column38" column="column38"/>
        <result property="column39" column="column39"/>
        <result property="column40" column="column40"/>
        <result property="column41" column="column41"/>
        <result property="column42" column="column42"/>
        <result property="column43" column="column43"/>
        <result property="column44" column="column44"/>
        <result property="column45" column="column45"/>
        <result property="column46" column="column46"/>
        <result property="column47" column="column47"/>
        <result property="column48" column="column48"/>
        <result property="column49" column="column49"/>
        <result property="column50" column="column50"/>
        <result property="productName" column="product_name"/>
        <result property="recharge" column="recharge"/>
        <result property="destinationCity" column="destination_city"/>
        <result property="destinationCityTo" column="destination_city_to"/>
        <result property="createdBy" column="created_by"/>
        <result property="creationDate" column="creation_date"/>
        <result property="lastUpdatedBy" column="last_updated_by"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="flightNumber" column="flight_number"/>
        <result property="mileage" column="mileage"/>
        <result property="mileageRate" column="mileage_rate"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="attendeeNumber" column="attendee_number"/>
        <result property="businessPurpose" column="business_purpose"/>
        <result property="mobileNumber" column="mobile_number"/>
        <result property="duration" column="duration"/>
        <result property="telephoneNumber" column="telephone_number"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="taxCodeId" column="tax_code_id"/>
        <result property="payAmount" column="pay_amount"/>
        <result property="taxAmount" column="tax_amount"/>
        <result property="financeComments" column="finance_comments"/>
        <result property="attachmentCount" column="attachment_count"/>
        <result property="projectName" column="project_name"/>
        <result property="customerName" column="customer_name"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="drAccountId" column="dr_account_id"/>
        <result property="crAccountId" column="cr_account_id"/>
        <result property="taxAccountId" column="tax_account_id"/>
        <result property="invoiceNum" column="invoice_num"/>
        <result property="invoiceCode" column="invoice_code"/>
        <result property="invoiceSerial" column="invoice_serial"/>
        <result property="invoiceType" column="invoice_type"/>
        <result property="source" column="source"/>
        <result property="finClaimAmount" column="fin_claim_amount"/>
        <result property="finTaxCodeId" column="fin_tax_code_id"/>
        <result property="finTaxAmount" column="fin_tax_amount"/>
        <result property="finNetAmount" column="fin_net_amount"/>
        <result property="var1" column="attachments"/>
        <result property="var2" column="details"/>
        <result property="internalType" column="internal_type"/>
        <result property="tripType" column="trip_type"/>
        <result property="flightType" column="flight_type"/>
        <result property="trainType" column="train_type"/>
        <result property="timeLength" column="timeLength"/>
        <result property="departBeginDatetime" column="depart_begin_datetime"/>
        <result property="departEndDatetime" column="depart_end_datetime"/>
        <result property="departType" column="depart_type"/>
        <result property="returnBeginDatetime" column="return_begin_datetime"/>
        <result property="returnEndDatetime" column="return_end_datetime"/>
        <result property="returnType" column="return_type"/>
        <result property="price" column="price"/>
        <result property="quantity" column="quantity"/>
        <result property="flightClass" column="flight_class"/>
        <result property="trainClass" column="train_class"/>
        <result property="passengerList" column="passenger_list"/>
        <result property="approvalNumber" column="approval_number"/>
        <result property="linkLineId" column="link_line_id"/>
        <result property="standardId" column="standard_id"/>
        <result property="fromCitiesT" column="from_cities"/>
        <result property="toCitiesT" column="to_cities"/>
        <result property="returnAmount" column="return_amount"/>
        <result property="originalAmount" column="original_amount"/>
        <result property="netAmount" column="net_amount"/>
        <result property="payClaimAmount" column="pay_claim_amount"/>
        <result property="offsetDetail" column="offset_detail"/>
        <result property="finReceiptAmount" column="fin_receipt_amount"/>
        <result property="finExchangeRate" column="fin_exchange_rate"/>
        <result property="finPayAmount" column="fin_pay_amount"/>
        <result property="finPayClaimAmount" column="fin_pay_claim_amount"/>
        <result property="comExchangeRate" column="com_exchange_rate"/>
        <result property="costCenterId" column="cost_center_id"/>
        <result property="budgetId" column="budget_id"/>
        <result property="address" column="address"/>
        <result property="companyId" column="companyId"/>
        <association property="expType" column="type_id"
                     resultMap="com.cloudpense.expman.mapper.SapMapper.ExpTypeMap"/>
    </resultMap>

    <resultMap id="ExpTypeMap" type="ExpType">
        <id property="typeId" column="type_id"/>
        <result property="type" column="type"/>
        <result property="enabled" column="enabled"/>
        <result property="priority" column="priority"/>
        <result property="periodType" column="period_type"/>
        <result property="advanceDate" column="advance_date"/>
        <result property="periodDay" column="period_day"/>
        <result property="reminderDay" column="reminder_day"/>
        <result property="category" column="category"/>
        <result property="attachmentUrl" column="attachment_url"/>
        <result property="printUrl" column="print_url" />
        <result property="printTemplate" column="print_template" />
        <result property="internalType" column="internal_type"/>
        <result property="language" column="language"/>
        <result property="budgetCategory" column="budget_category"/>
        <result property="requestRequiredFlag" column="request_required_flag"/>
        <result property="requestLinkNum" column="request_link_num"/>
        <result property="claimLinkNum" column="claim_link_num"/>
        <result property="description" column="description"/>
        <result property="typeCode" column="type_code"/>
        <result property="lineBlock" column="line_block"/>
        <result property="advanceBlock" column="advance_block"/>
        <result property="returnBlock" column="return_block"/>
        <result property="staffFlag" column="staff_flag"/>
        <result property="groupNum" column="group_num"/>
        <result property="groupName" column="group_name"/>
        <result property="revisionFlag" column="revision_flag"/>
        <result property="revisionName" column="revision_name"/>
        <result property="shareFlag" column="share_flag"/>
        <result property="companyId" column="company_id"/>
        <!--<association property="company" column="company_id"-->
                     <!--resultMap="com.cloudpense.expman.mapper.FndCompanyMapper.FndCompanyMap"/>-->
        <!--
        <collection property="columns" column="type_id" ofType="ExpTypeColumn"
                    resultMap="com.cloudpense.expman.mapper.ExpTypeColumnMapper.ExpTypeColumnMap" />
        -->
    </resultMap>

    <resultMap id="FndWorkflowPathMap" type="FndWorkflowPath">
        <id property="workflowId" column="workflow_id"/>
        <result property="pathId" column="path_id"/>
        <result property="workflowType" column="workflow_type"/>
        <result property="sequenceNum" column="sequence_num"/>
        <result property="status" column="status"/>
        <result property="sourceId" column="source_id"/>
        <result property="positionId" column="position_id"/>
        <result property="positionName" column="position_name"/>
        <result property="position" column="position_user"/>
        <result property="userName" column="user_name"/>
        <result property="note" column="note"/>
        <result property="openDate" column="open_date"/>
        <result property="endDate" column="end_date"/>
        <result property="userId" column="user_id"/>
        <result property="userPositionId" column="user_position_id"/>
        <result property="type" column="type"/>
        <result property="userPositionName" column="user_position_name"/>
        <result property="language" column="language"/>
        <result property="parentId" column="parent_id"/>
        <result property="agentId" column="agent_id"/>
        <result property="question" column="question"/>
        <result property="function" column="function"/>
        <result property="value" column="value"/>
        <result property="valueJson" column="value_json"/>
        <result property="functionName" column="function_name"/>
        <result property="createdBy" column="created_by"/>
    </resultMap>

    <resultMap id="FndSupplierMap" type="FndSupplier">
        <id property="supplierId" column="supplier_id"/>
        <result property="companyId" column="company_id"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankCode" column="bank_code"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNumber" column="account_number"/>
        <result property="bankAddress" column="bank_address"/>
        <result property="city" column="city"/>
        <result property="state" column="state"/>
        <result property="country" column="country"/>
        <result property="postalCode" column="postal_code"/>
        <result property="bankSwiftId" column="bank_swift_id"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="contactAddress" column="contact_address"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="language" column="language"/>
        <result property="contactPostalCode" column="contact_postal_code"/>
        <result property="column1" column="column1"/>
        <result property="column2" column="column2"/>
        <result property="column3" column="column3"/>
        <result property="column4" column="column4"/>
        <result property="column5" column="column5"/>
        <result property="column6" column="column6"/>
        <result property="column7" column="column7"/>
        <result property="column8" column="column8"/>
        <result property="column9" column="column9"/>
        <result property="column10" column="column10"/>
        <result property="column11" column="column11"/>
        <result property="column12" column="column12"/>
        <result property="column13" column="column13"/>
        <result property="column14" column="column14"/>
        <result property="column15" column="column15"/>
        <result property="column16" column="column16"/>
        <result property="column17" column="column17"/>
        <result property="column18" column="column18"/>
        <result property="column19" column="column19"/>
        <result property="column20" column="column20"/>
        <result property="column21" column="column21"/>
        <result property="column22" column="column22"/>
        <result property="column23" column="column23"/>
        <result property="column24" column="column24"/>
        <result property="column25" column="column25"/>
        <result property="column26" column="column26"/>
        <result property="column27" column="column27"/>
        <result property="column28" column="column28"/>
        <result property="column29" column="column29"/>
        <result property="column30" column="column30"/>
        <result property="column31" column="column31"/>
        <result property="column32" column="column32"/>
        <result property="column33" column="column33"/>
        <result property="column34" column="column34"/>
        <result property="column35" column="column35"/>
        <result property="column36" column="column36"/>
        <result property="column37" column="column37"/>
        <result property="column38" column="column38"/>
        <result property="column39" column="column39"/>
        <result property="column40" column="column40"/>
        <result property="column41" column="column41"/>
        <result property="column42" column="column42"/>
        <result property="column43" column="column43"/>
        <result property="column44" column="column44"/>
        <result property="column45" column="column45"/>
        <result property="column46" column="column46"/>
        <result property="column47" column="column47"/>
        <result property="column48" column="column48"/>
        <result property="column49" column="column49"/>
        <result property="column50" column="column50"/>
    </resultMap>

    <resultMap id="FndSupplierAccountMap" type="com.cloudpense.expman.entity.FndSupplierAccount">
        <result property="accountId" column="account_id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="companyId" column="company_id"/>
        <result property="accountName" column="account_name"/>
        <result property="accountNumber" column="account_number"/>
        <result property="bankName" column="bank_name"/>
        <result property="bankBranch" column="bank_branch"/>
        <result property="bankCode" column="bank_code"/>
        <result property="bankSwiftId" column="bank_swift_id"/>
        <result property="bankAddress" column="bank_address"/>
        <result property="state" column="state"/>
        <result property="city" column="city"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="primaryFlag" column="primary_flag"/>
    </resultMap>

    <resultMap id="GeneralLedgerAccountMap" type="GeneralLedgerAccount">
        <result property="companyId" column="company_id"/>
        <result property="accountId" column="account_id"/>
        <result property="branchId" column="branch_id"/>
        <result property="accountCode" column="account_code"/>
        <result property="accountName" column="account_name"/>
        <result property="category" column="category"/>
        <result property="aliasName" column="alias_name"/>
        <result property="language" column="language"/>
        <result property="userId" column="user_id"/>
        <result property="enabledFlag" column="enabled_flag"/>
        <result property="invoiceFlag" column="invoice_flag"/>
        <result property="accountEvent" column="account_event"/>
        <result property="drAccountId" column="dr_account_id"/>
        <result property="crAccountId" column="cr_account_id"/>
        <result property="budgetId" column="budget_id"/>
    </resultMap>

</mapper>