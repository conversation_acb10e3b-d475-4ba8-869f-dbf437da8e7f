<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudpense.expman.mapper.YfMapper">
    <select id="yfPaperGenerating" statementType="CALLABLE" resultType="String">
        {call zzyf_update_data(#{type}, #{companyId}, #{input},
        #{returnCode,mode=OUT,jdbcType=VARCHAR},#{returnMessage,mode=OUT,jdbcType=VARCHAR})}
    </select>

</mapper>