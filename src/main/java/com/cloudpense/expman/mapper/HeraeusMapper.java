package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.BindUserInfo;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.GlBatchTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface HeraeusMapper {

    List<String> glBatchCreateHeraeus2(GlBatchTransfer glBatchTransfer);

    List<String> glBatchCreateHeraeus1(GlBatchTransfer glBatchTransfer);

    List<String> glBatchCreateHeraeus(GlBatchTransfer glBatchTransfer);

    void updateHeraeusEmployee(FndQuery fndQuery);

    List<String> getDepartmentCodeByCompanyIdAndDocumentId(@Param("companyId") Integer company_id,@Param("documentId") Integer documentId);

    String getDocumentNumByDocumentId(int documentId);

    ArrayList<ExpClaimLine> queryClaimLineTax(int documentId);

    List<Map<String, String>> getHeaderAttachments(Integer documentId);

    List<Map<String, Object>> getLineAttachments(Integer documentId);

    List<Map<String, Object>> getLineReceipts(Integer documentId);

    void updateGlStatusAndGlMessage(@Param("gl_status") String glStatus, @Param("gl_message") String glMessage, @Param("document_id") Integer documentId);

}
