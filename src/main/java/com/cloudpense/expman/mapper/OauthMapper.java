package com.cloudpense.expman.mapper;

import com.cloudpense.expman.Request.TokenVo;
import com.cloudpense.expman.entity.BindUserInfo;
import com.cloudpense.expman.entity.FndUser;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;

/**
 * Created by futurefall on 2016/3/29.
 */
public interface OauthMapper {
    public void update(@Param(value = "companyId") int companyId,
                       @Param(value = "username") String username,
                       @Param(value = "tokenId") String tokenId);

    public Integer setCompanyId(String username);

    public void generateToken(@Param(value = "clientId")String clientId,
                              @Param(value = "accessToken")String accessToken,
                              @Param(value = "time") Date time
    );

    public Integer verifyUaesToken(@Param(value = "accessToken")String accessToken,
                                   @Param(value = "time") Date time);

    public String fndServer(@Param(value = "companyId")int companyId);


    TokenVo getTokenVo(@Param(value = "clientId") String clientId,
                       @Param(value = "clientSecret") String clientSecret);

    TokenVo getTokenVoByClientId(@Param(value = "clientId") String clientId);

    public Collection<FndUser> findUserByMobile(@Param("companyId") int companyId);

    public Collection<BindUserInfo> findFndUserBindingByCompanyId(@Param("companyId") int companyId);

    public void addFndUserBinding(@Param("userId") int userId,
                                  @Param("companyId") int companyId,
                                  @Param("platform") String platform,
                                  @Param("dingUserId") String dingUserId,
                                  @Param("dingMobile") String dingMobile);

    public BindUserInfo findBindByMobile(@Param("platform") String platform,@Param("mobile") String mobile);

    public String findUserNameById(@Param("userId") int userId);

    public String findServerNameByCorpId(@Param("corpId") String corpId);

}
