package com.cloudpense.expman.mapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 */
public interface LovMapper {

    @Select("select lov_id from fnd_lov where lov_name = #{lovName} limit 1")
    Integer getLovId(@Param("lovName") String lovName, @Param("companyId") int companyId);

    @Select("select enabled_flag from fnd_lov_value where lov_id = #{lovId} and value_code = #{valueCode} limit 1")
    String getValueEnableFlag(@Param("lovId") Integer lovId, @Param("valueCode") String valueCode);

    @Update("update fnd_lov_value set enabled_flag = #{enabledFlag} where lov_id = #{lovId} and value_code = #{valueCode}")
    void setValueEnableFlag(@Param("lovId") Integer lovId, @Param("valueCode") String valueCode,  @Param("enabledFlag") String enabledFlag);

    @Update("insert into fnd_lov_value(lov_id, company_id, value_code, enabled_flag) " +
            "values(#{lovId}, #{companyId}, #{valueCode}, #{enabledFlag})")
    void insertValue(@Param("lovId") Integer lovId, @Param("companyId") Integer companyId,
                     @Param("valueCode") String valueCode, @Param("enabledFlag") String enabledFlag);

}