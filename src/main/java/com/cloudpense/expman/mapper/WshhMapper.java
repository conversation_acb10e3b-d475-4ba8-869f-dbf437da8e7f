package com.cloudpense.expman.mapper;

import com.cloudpense.expman.Request.WshhClaimPushReq;
import com.cloudpense.expman.Request.WshhSapReturnReq;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文思海辉mapper
 */
public interface WshhMapper {

    //查询单据头
    public List<WshhClaimPushReq> selectExpClaimHeader();

    //查询单据行
    public List<ExpClaimLine> selectExpClaimLine(int headerId);

    //查询单据头信息
    public List<Map<String, Object>> getExpClaimHeader(@Param(value = "pushDate") Date pushDate, @Param(value = "pushDateTo") Date pushDateTo);

    //查询单据行信息
    public List<Map<String, Object>> getExpClaimLine(int headerId);

    //查询单据
    public ExpClaimHeader selectDocument(int headerId);

    //查询单据
    public void insertExpClaimInfo(WshhSapReturnReq wshhSapReturnReq);

}
