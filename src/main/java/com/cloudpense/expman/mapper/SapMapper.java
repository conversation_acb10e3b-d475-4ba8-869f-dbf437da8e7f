package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.*;
import org.apache.ibatis.annotations.Param;
import sun.text.resources.et.CollationData_et;

import java.util.Collection;
import java.util.List;

public interface  SapMapper {

    public List<String> glBatchCreateCJLR(GlBatchTransfer glBatchTransfer);

    //公司支付不推送，即：pay_method_id != 44671
    public List<String> getProofInfoByCJLR(@Param("documentId")int documentId);

    public void normalInsertLog(@Param("companyId") int companyId,
                                @Param("type") String type,
                                @Param("content") String content);

    public ExpClaimHeader getExpClaimHeader(@Param("headerId") int headerId);

    public ExpClaimLine getExpClaimLine(@Param("lineId") int lineId,
                                        @Param(value = "language") String language);

    public Collection<ExpClaimLine> findAllByHeaderIdWithFinanceInfo(@Param(value = "headerId") int headerId, @Param(value = "language") String language);

    public Collection<FndWorkflowPath> getWorkflowPathByPositionId(@Param("companyId") int companyId,
                                                              @Param("positionId") int positionId,
                                                              @Param("status") String status);

    public ExpType getExpTypeById(@Param("typeId") int typeId,
                                  @Param(value = "language") String language);

    public FndDepart getDepartById(@Param("departmentId") int departmentId,
                                   @Param(value = "language") String language);

    public void updateWorkflowStatus(@Param("pathId") int pathId,
                                     @Param("status") String status);

    public void updateSapData(FndQuery fndQuery);

    public FndSupplier getSupplierById(@Param("supplierId") int supplierId,
                                       @Param("language") String language);

    public GeneralLedgerAccount getGlAccountById(@Param("accountId") int accountId,
                                                 @Param("language") String language);

    public SystemUser getUserById(@Param("userId") int userId,
                                  @Param("language") String language);

    public Collection<SystemUser> getAllUsersByCompany(@Param("companyId") int companyId,
                                                       @Param("language") String language);

    public void glBatchCreate2(GlBatchTransfer glBatchTransfer);

    public Collection<ExpClaimHeader> getTobeGeneratedExp(@Param("companyId") int companyId);

    public String getTaxCodeFromDesc(@Param("lovName") String lovName,
                                     @Param("valueCode") String valueCode,
                                     @Param("companyId") int companyId,
                                     @Param("language") String language);

    public Collection<FndUserAccount> findUserAccounts(@Param(value = "userId") int useId, @Param(value = "companyId") int companyId);

    public Collection<ExpClaimHeader> getDocumentByHeaderType(@Param("headerTypeId") int headerTypeId,
                                                              @Param("companyId") int companyId);

    public ExpClaimHeader getDocumentByDocumentNum(@Param("documentNum") String documentNum,
                                                              @Param("companyId") int companyId);
}
