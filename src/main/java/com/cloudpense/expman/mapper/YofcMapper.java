package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.*;
import com.cloudpense.expman.entity.changfei.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface YofcMapper {
    /**
     *长飞
     */
    String yofcDataUpdate(FndQuery fndQuery);

    void normalInsertLog(@Param("companyId") int companyId,
                                @Param("type") String type,
                                @Param("content") String content);

    void yofcUpdateColumn50(@Param("busNbr") String busNbr,
                                   @Param("bankAccountCode") String bankAccountCode,
                                   @Param("documentId") int documentId);

    String getDocumentGroupNum(@Param("documentId") int documentId);

    String getUserIdentityColumn1(@Param("userId") int userId);

    String getUserEmployeeNumber(@Param("userId") int userId);

    @Select("select ${columnName} from fnd_user where user_id = #{userId}")
    String getUserIdentityColumn(@Param("columnName") String columnName, @Param("userId") int userId);

    @Update("update fnd_user set ${columnName} = #{column} where user_id = #{userId}")
    void UpdateUserIdentityColumn(@Param("columnName") String columnName, @Param("column") String column, @Param("userId") int userId);

    FndUserAccount getBranchAccountNumberById(@Param("accountId") int accountId);

    YofcPayElement getDocumentPayInfo(@Param("documentId") int documentId);

    List<YofcPaymentVoucher> getPaymentVouchers(@Param("headerIds") List<Integer> headerIds);

    @Select("SELECT type_code as typeCode ,document_num as documentNum from exp_claim_header ech  LEFT JOIN exp_header_type  eht on ech.header_type_id = eht.type_id where  ech.`status` = 'approved' and ech.document_id = #{documentId}")
    TypeNum getTypeCodeByDocumentId(@Param("documentId")int documentId);

    @Select("SELECT document_id as documentId from exp_claim_header ech where ech.header_id = #{headerId} and ech.company_id = 16895 limit 1")
    Integer getDocumentIdByHeaderId(@Param("headerId") Integer headerId);

    @Select("select fd.column2,ech.document_num as documentNum,ech.link_header_id  as linkHeaderId,ech.total_pay_amount as totalPayAmount,ech.supplier_account_id as supplierAccountId,esa.account_number as accountNumber ,esa.account_name as accountName,esa.bank_name as bankName,esa.bank_branch as bankBranch,esa.state,esa.city,\n" +
            "esa.bank_code as bankCode,esa.bank_address as bankAddress " +
            "from exp_claim_header ech  left join fnd_department fd on ech.branch_id = fd.department_id  " +
            "left join exp_supplier_account esa on esa.account_id = ech.supplier_account_id " +
            "where ech.document_id = #{documentId} and ech.`status` = 'approved'")
    SupplierAccount yofcgetSupplierAccount(@Param("documentId")int documentId);

    @Select("select supplier_name as supplierName from exp_supplier_v where supplier_id = #{supplierId} and language = 'zh_CN' limit 1")
    Map yofcGetSupplier(@Param("supplierId") Integer supplierId);

    @Select("select bank_abbreviation from fnd_bank where bank_hq_code =#{bankHqCode} and bank_name like '%${bankName}%'")
    String getAbbreviation(@Param("bankHqCode") String bankHqCode,@Param("bankName") String bankName);

    @Select("select ech.column46 as refnbr,ech.trans_serial_num as busnbr,ech.document_num as documentNum,ech.header_id as headerId, 1 as type " +
            "from exp_claim_header ech " +
            "left join exp_header_type  eht on ech.header_type_id = eht.type_id " +
            "where ech.pay_status = 'paying' and ech.`status` in ('approved','closed') and ech.company_id = '16895' and eht.type_code = 'PAY003' " +
            "and ech.column46 is not null and ech.trans_serial_num is not null"
           )
    List<ErPaySta> yofcgetPayingDoc();

    @Select("select ech.column46 as refnbr,ech.trans_serial_num as busnbr,ech.document_num as documentNum,.ech.header_id as headerId  from exp_claim_header ech  LEFT JOIN exp_header_type  eht on ech.header_type_id = eht.type_id  where ech.pay_status = 'paid' and ech.`status` in ('approved','closed') and ech.actual_payment_date is null and ech.company_id = '16895' and eht.type_code = 'PAY003'")
    List<ErPaySta> yofcgetPayedDoc();

    @Select(" update exp_claim_header set column31 = #{column31}, trans_serial_num = #{busNbr}, column44 = #{bankAccountCode},column46 = #{refnbr},pay_status = 'paying' where  `status` = 'approved' and document_id = #{documentId}")
    void yofcUpdateColumn45(@Param("busNbr") String busNbr,@Param("bankAccountCode") String bankAccountCode,@Param("documentId") Integer documentId,@Param("refnbr") String refnbr,@Param("column31") String date);

    @Select("SELECT group_num from exp_claim_header ech  LEFT JOIN exp_header_type  eht on ech.header_type_id = eht.type_id where `status` in ('closed','approved') and document_id = #{documentId}")
    String yofcgetGroupByDocumentId(@Param("documentId")String documentId);

    @Select("SELECT eht.type_code as headerTypeCode, group_num as groupNum, link_header_id as linkHeaderId, document_num as documentNum " +
            "from exp_claim_header ech " +
            "LEFT JOIN exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where ech.status in ('closed','approved') and ech.document_id = #{documentId}")
    ClaimHeader yofcGetClaimHeaderByDocumentId(@Param("documentId")String documentId);

    @Select("SELECT eht.type_code as headerTypeCode, group_num as groupNum," +
            "link_header_id as linkHeaderId, column47 as column47 " +
            "from exp_claim_header ech " +
            "LEFT JOIN exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where ech.header_id = #{headerId}")
    ClaimHeader yofcGetClaimHeaderByHeaderId(@Param("headerId") Integer headerId);

    @Select({"<script>",
             "select eht.type_code as headerTypeCode, group_num as groupNum,link_header_id as linkHeaderId, column47 as column47, document_num as documentNum",
             "from exp_claim_header ech",
             "left join exp_header_type eht on ech.header_type_id = eht.type_id",
             "where ech.header_id in",
             "<foreach collection='headerIds' item='headerId' open='(' separator=',' close=')'>",
             "#{headerId}",
             "</foreach>",
             "</script>"})
    List<ClaimHeader> yofcGetClaimHeaderListByHeaderIds(@Param("headerIds") List<Integer> headerIds);

    @Select("select ech.column30,ech.column15,ech.column20,ech.column47,ech.column36,ech.column7,ech.column17,ech.column13,ech.column26,ech.column_json as columnJson," +
            "fd.column4 as fdColumn4,fd.department_code as fdCode, fd.column1 as branchColumn1,fd.column4 as branchColumn4,fd.column5 as branchColumn5," +
            "ech.currency_code as currencyCode,ech.document_num as ducumentNum,ech.column32," +
            "ech.column42,ech.description,fu.full_name as chargeUser,ech.header_id as headerId,ech.link_header_id as linkHeaderId,eht.type_code as typeCode," +
            "es.supplier_code as supplierCode,es.supplier_name as supplierName,es.column5 as supplierColumn5,es.column6 as supplierColumn6," +
            "ech.branch_id as branchId,fu2.full_name as submitUser,ech.ledger1,gl.ledger_name as ledgerName," +
            "fd1.department_code as submitDepartment,fd1.column1 as submitDepartmentColumn1, fd2.department_code as chargeDepartment,fd2.column1 as chargeDepartmentColumn1, pp.project_code as projectCode, pp.column3 as projectColumn3," +
            "ech.gl_status as glStatus " +
            "from exp_claim_header ech " +
            "left join exp_supplier_v es on ech.supplier_id = es.supplier_id and es.language = 'zh_CN' " +
            "left join fnd_user_v fu on ech.charge_user = fu.user_id and fu.language = 'zh_CN' " +
            "left join fnd_user_v fu2 on ech.submit_user = fu2.user_id and fu2.language = 'zh_CN' " +
            "left join fnd_department fd on ech.branch_id = fd.department_id " +
            "left join fnd_department fd1 on ech.submit_department = fd1.department_id " +
            "left join fnd_department fd2 on ech.charge_department = fd2.department_id " +
            "left join exp_header_type eht on ech.header_type_id = eht.type_id " +
            "left join gl_ledger gl on gl.ledger_id = ech.ledger1 " +
            "left join pjm_project pp on pp.project_id = ech.project_name " +
            "where ech.`status` in ('closed','approved') and ech.document_id = #{documentId}")
    List<FaPiaoHeaderInfo> yofcPostHeaderData(@Param("documentId") String documentId);

    @Select("select et.internal_type as internalType, ecl.pay_method_id as payMethodId, ecl.column30 as column30, " +
            "ecl.column40 as column40,ecl.column4 as column4,ecl.receipt_date as receiptDate,ecl.fin_net_amount as finNetAmount, " +
            "ecl.fin_tax_amount as taxAmount,ecl.comments,etc.tax_code as taxCode, " +
            "ecl.fin_receipt_amount as receiptAmount,gav.alias_name as aliasName, ecl.receipt_amount as receiptAmount2, " +
            "ecl.link_header_id as linkHeaderId, ga.account_code as drAccountCode, taxga.account_code as taxAccountCode, " +
            "epm.factor as payMethodFactor, et.type_code as typeCode,ecl.receipt_currency as currencyCode, " +
            "ecl.line_id as lineId, pp.project_code as projectCode, pp.column2 as projectColumn2, pp.column3 as projectColumn3, ecl.column7 as column7, ecl.quantity as quantity, " +
            "ecl.column33 as column33, ecl.column37 as column37, ecl.column38 as column38, ecl.column39 as column39, " +
            "ecl.column_json as columnJson, ecl.column28 as column28, ecl.column23 as column23, fd.department_code as costCenter, " +
            "fd.column1 as costCenterColumn1,fd.enabled_flag as costCenterEnabledFlag, gba.budget_code as budgetCode "+
            "from exp_claim_line ecl " +
            "left join exp_tax_code etc on ecl.fin_tax_code_id = etc.tax_code_id " +
            "left join gl_account_v gav  on gav.account_id = ecl.dr_account_id and gav.`language` = 'zh_CN' " +
            "left join gl_account ga on ga.account_id = ecl.dr_account_id " +
            "left join gl_account taxga on taxga.account_id = ecl.tax_account_id " +
            "left join exp_type et on et.type_id = ecl.type_id " +
            "left join exp_pay_method epm on epm.method_id = ecl.pay_method_id " +
            "left join pjm_project pp on pp.project_id = ecl.project_name " +
            "left join fnd_department fd on ecl.cost_center_id = fd.department_id " +
            "left join gl_budget_account gba on ecl.budget_id = gba.budget_id " +
            "where header_id = #{headerId} order by ecl.line_id")
    List<FaPiaoLineInfo> yofcPostLineData(@Param("headerId")String  headerId);


    @Select("select ecl.column37,ecl.column38,ecl.column39,ecl.column_json as columnJson,ecl.fin_net_amount as finNetAmount," +
            "pp.column3,pp.project_code as projectCode,gav.account_code as drAccountCode " +
            "from exp_claim_line ecl " +
            "left join pjm_project pp on pp.project_id = ecl.project_name " +
            "left join gl_account_v gav on gav.account_id = ecl.dr_account_id and gav.`language` = 'zh_CN' " +
            "where ecl.line_id = #{lineId}")
    FaPiaoLineInfo yofcQueryLineByLineId(@Param("lineId")String  lineId);



    @Select("SELECT fl.description FROM `fnd_lov_value` fv left join `fnd_lov_value_tl` fl on fv.value_id = fl.value_id where`language` = 'zh_CN' and fv.value_code = #{type} ")
    List<String> yofcCol7Descri(@Param("type") String column7);

    @Select("select flvt.value_id as valueId, flvt.value_meaning as valueMeaning, flvt.description, flv.column1 " +
            "from fnd_lov_value flv " +
            "join fnd_lov fl on fl.lov_id = flv.lov_id " +
            "left join fnd_lov_value_tl flvt on flvt.value_id = flv.value_id " +
            "where fl.lov_name = #{lovName} and flv.value_code = #{valueCode} and `language` = 'zh_CN' limit 1")
    FndLovValue yofcLovValue(@Param("lovName") String lovName, @Param("valueCode") String valueCode);

    @Select("select flvt.value_id as valueId, flv.value_code as valueCode, flvt.value_meaning as valueMeaning, flvt.description, flv.column1 " +
            "from fnd_lov_value flv " +
            "join fnd_lov fl on fl.lov_id = flv.lov_id " +
            "left join fnd_lov_value_tl flvt on flvt.value_id = flv.value_id " +
            "where fl.lov_name = #{lovName} and `language` = 'zh_CN' and flv.enabled_flag = 'Y'")
    List<FndLovValue> yofcLovValues(@Param("lovName") String lovName);

    @Update("update fnd_lov_value_tl set value_meaning = #{valueMeaning} " +
            "where value_id = #{valueId} and language = #{language}")
    void yofcUpdateLovValueMeaning(@Param("valueId") Integer valueId, @Param("valueMeaning") String valueMeaning, @Param("language") String language);

    @Select("select pp.project_code from exp_claim_line ecl left join pjm_project pp on pp.project_id = ecl.project_name where ecl.line_id = #{lineId}")
    List<String> yofcProjectCode(@Param("lineId") String lineId);

    @Select("select pp.column2,pp.column4,pp.column6,pp.column10 from pjm_project pp " +
            "where pp.project_code = #{projectCode} limit 1")
    ProjectInfo yofcProjectInfo(@Param("projectCode") String projectCode);

    @Select("select fd.column1  from exp_claim_header ech left join  fnd_department fd on fd.department_id = ech.charge_department where ech.header_id = #{headerId}")
    String yofcErpCode(@Param("headerId") String headerId);

    @Select("select pp.column2 from exp_claim_line ecl left join pjm_project pp on pp.project_id = ecl.project_name where ecl.line_id = #{lineId}")
    String yofcCostCode(@Param("lineId") String lineId);

    @Select("select pp.column4 from exp_claim_line ecl left join pjm_project pp on pp.project_id = ecl.project_name where ecl.line_id = #{lineId}")
    String yofcProjectColumn4(@Param("lineId") String lineId);

    @Select("select fuc.column1  from exp_claim_header ech  left join fnd_user fuc on fuc.user_id = ech.charge_user where ech.header_id = #{headerId}")
    String yofcUserColumn2(@Param("headerId") String headerId);

    @Select("select pp.column3 from exp_claim_header ech left join pjm_project pp on pp.project_id = ech.project_name where ech.header_id = #{headerId}")
    String yofcPPColumn3(@Param("headerId") String headerId);

    @Select("select fuc.employee_number as employeeNumber, fuc.column2 as column2 from exp_claim_header ech  left join fnd_user fuc on fuc.user_id = ech.charge_user where ech.header_id = #{headerId}")
    UserInfo yofcuserEmploy(@Param("headerId") String headerId);

    @Select("select fuc.employee_number as employeeNumber, fuc.column2 as column2 from exp_claim_line ecl " +
            "left join fnd_user fuc on fuc.user_id = ecl.charge_user where ecl.line_id = #{lineId}")
    UserInfo yofcLineUserEmploy(@Param("lineId") String lineId);


    @Select("select column45 from exp_claim_header where `status` = 'approved' and document_id = #{documentId}")
    String yofcIsPostFuKuan(@Param("documentId") Integer documentId);

    @Update("update exp_claim_header set pay_status = #{status} where header_id = #{headerId}")
    void updatePayStatus(@Param("headerId") String headerId,@Param("status") String status);

    @Update("update exp_claim_header set trans_serial_num = null where header_id = #{headerId}")
    void deleteTransSerialNum(@Param("headerId") String headerId);

    @Update("update exp_claim_header set pay_status = #{status}, actual_payment_date = #{paymentDate} where header_id = #{headerId}")
    void updatePayResult(@Param("headerId") String headerId, @Param("status") String status, @Param("paymentDate") Date paymentDate);

    @Update("update exp_claim_header set pay_status = #{status}, pay_status_message = #{message} where document_num = #{documentNum} and status != 'deleted'")
    void updatePayStatusByDocumentNum(@Param("documentNum") String documentNum, @Param("status") String status, @Param("message") String message);


    @Select("select column_json from exp_claim_header where header_id = #{headerId}")
    String yofcgetColumn54(@Param("headerId")String headerId);

    @Update("update exp_claim_header set column_json = #{columnJson} where header_id = #{headerId}")
    void updateColumnJson(@Param("headerId")String headerId,@Param("columnJson") String columnJson);

    @Update("update exp_claim_header set column_json = #{columnJson} where header_id = #{headerId}")
    void updateColumn16(@Param("headerId")String headerId,@Param("column16") String column16, @Param("columnJson") String columnJson);

    @Select(" select fd.column1 from exp_claim_line ecl left join fnd_department fd  on ecl.cost_center_id = fd.department_id where ecl.line_id = #{lineId}")
    String yofcErpCodeLine(@Param("lineId") String lineId);

    @Select("select pp.column3 from exp_claim_line ecl left join pjm_project pp on pp.project_id = ecl.project_name where ecl.line_id = #{lineId}")
    String yofcPPColumn3Line(@Param("lineId") String lineId);

    @Select("SELECT fl.value_meaning FROM `fnd_lov_value` fv left join `fnd_lov_value_tl` fl on fv.value_id = fl.value_id where`language` = 'zh_CN' and fv.value_code = #{type} ")
    List<String> yofcCol13Name(@Param("type") String type);

//    @Select("<script>"
//            +"<insert id=\"addGlJe\" useGeneratedKeys=\"true\" keyProperty=\"glJeBatch.batch_id\"  parameterType=\"com.cloudpense.standard.model.GlJeBatch\">\n" +
//            "        insert into gl_je_batch (company_id,ledger_type,source,type,gl_date,journal_num,detail,\n" +
//            "        <if test=\"userId != null\">\n" +
//            "            created_by,\n" +
//            "        </if>\n" +
//            "        creation_date,last_updated_by,last_update_date,ledger_id)\n" +
//            "        values (#{companyId},1,'EXP','api',null,#{journalNum},null,\n" +
//            "        <if test=\"userId != null\">\n" +
//            "            #{userId},\n" +
//            "        </if>\n" +
//            "        now(),#{userId},now(),0);\n" +
//            "    </insert>"
//            + "</script>")
//    int addGlJe(@Param("companyId") String companyId, @Param("journalNum") String journalNum, @Param("userId") String userId,@Param("glJeBatch") GlJeBatch glJeBatch);
//
//    @Insert("insert into gl_je_batch_assign(company_id,batch_id,header_id) values (#{companyId},#{batchId},#{headerId})")
//    void addGlJeBatchAssign(@Param("companyId") String companyId,@Param("batchId") Integer batchId,@Param("headerId") String headerId);

    @Update("update exp_claim_header h " +
            "set    h.journal_num = #{journalNum}, h.gl_status = 'generated', h.last_update_date = now(), h.last_updated_by = #{userId}, h.gl_date = #{glDate} " +
            "where  h.company_id = #{companyId} " +
            "and    h.header_id = #{headerId} " +
            "and    h.status in ('approved','closed') limit 1")
    void updateByVoucher(@Param("journalNum") String journalNum,
                         @Param("glDate") String glDate,
                         @Param("userId") String userId,
                         @Param("companyId") String companyId,
                         @Param("headerId") String headerId);

    @Select("select type_code as headerTypeCode,link_header_id as linkHeaderId,column47,document_num as documentNum," +
            "document_id as documentId " +
            "from exp_claim_header ech " +
            "left join exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where ech.`status` in ('closed','approved') and actual_payment_date is not null " +
            "and ech.header_id = #{headerId}")
    ClaimHeader yofcTypeCode(@Param("headerId") Integer headerId);

    @Select("select document_num as documentNum,total_pay_currency_amount as total_pay_amount,header_id as headerId, " +
            "branch_account_id as branchAccountId,actual_payment_date as actualPaymentDate, payment_type as paymentType, " +
            "column_json as columnJson, column14,column31,column41,column44 " +
            "from exp_claim_header where `status` in ('closed','approved') and document_id = #{documentId} limit 1")
    Pay3Header yofcHeaderPay(@Param("documentId") Integer documentId);

    @Update("update exp_claim_header set status = 'closed' where header_id = #{headerId}")
    void closeClaim(@Param("headerId") String headerId);

    @Select("select column31 from exp_claim_header where header_id = #{headerId}")
    String getColumn31(@Param("headerId") String headerId);

    int addApPaymentBatch(@Param("apPaymentBatch") ApPaymentBatch apPaymentBatch);

    @Update("update exp_claim_header set ap_batch_id = #{batchId},gl_status = 'pending' where header_id = #{headerId}")
    void updateHeaderBatchId(@Param("headerId")String headerId,@Param("batchId") Integer batchId);

    @Insert("insert into gl_je_pay_batch_assign (company_id,batch_id,header_id,last_update_date)\n" +
            "        values (#{companyId},#{batchId},#{headerId},now())")
    void addGlJePayBatchAssign(@Param("companyId") int companyId,@Param("batchId") Integer batchId,@Param("headerId") String headerId);


    //前序单据支付信息
    @Select("select ech.total_pay_amount as totalPayAmount,ech.document_num as documentNum,fua.account_number as receiveAccountNumber," +
            "fua.account_name as receiveAccountName,fua.bank_name as receiveBankName,eht.type_code as headerTypeCode " +
            "from exp_claim_header ech " +
            "join exp_header_type eht on eht.type_id = ech.header_type_id " +
            "left join fnd_user_account fua on fua.account_id = ech.user_account_id " +
            "where ech.header_id in (select link_header_id from exp_claim_header where `status` in ('approved','closed') and document_id = #{documentId}) limit 1")
    YofcPayElement getDocumentPayInfo2(@Param("documentId") int documentId);

    //当前单据支付信息
    @Select("select ech.total_pay_amount as totalPayAmount,ech.document_num as documentNum,fua.account_number as receiveAccountNumber," +
            "fua.account_name as receiveAccountName,fd.department_code as departmentCode,fua.bank_name as receiveBankName," +
            "fca.bank_name as branchBankName,epa.account_number as externalReceiveAccountNumber,epa.account_name as externalReceiveAccountName," +
            "epa.bank_name as externalReceiveBankName " +
            "from exp_claim_header ech " +
            "left join fnd_user_account fua on fua.account_id = ech.user_account_id " +
            "left join external_person_account epa on epa.account_id = ech.external_person_account_id " +
            "left join fnd_department fd on fd.department_id = ech.branch_id " +
            "left join fnd_company_account fca on fca.account_id = ech.branch_account_id " +
            "where ech.status in ('approved','closed') and document_id = #{documentId} limit 1")
    YofcPayElement getPayInfo(@Param("documentId") int documentId);

    @Select("SELECT link_header_id from exp_claim_header where `status` in ('approved','closed') and document_id = #{documentId}")
    Integer getLinkBydocumentId(@Param("documentId") Integer documentId);

    @Select("select link_header_id from exp_claim_header_link where header_id = #{headerId}")
    List<Integer> getLinkHeaderIdByHeaderId(@Param("headerId") Integer headerId);

    @Select("update exp_claim_header set trans_serial_num = #{busNbr}, column44 = #{bankAccountCode} where header_id = #{headerId}")
    void yofcUpdateColumn50New(@Param("busNbr") String busNbr, @Param("bankAccountCode") String bankAccountCode, @Param("headerId") Integer headerId);

    @Select("select ech.column46 as refnbr,ech.trans_serial_num as busnbr,ech.document_num as documentNum,ech.header_id as headerId, 2 as type " +
            "from exp_claim_header ech " +
            "left join exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where ech.pay_status = 'paying' and ech.`status` in ('approved','closed') and ech.company_id = '16895' and eht.type_code = 'PAY001' " +
            "and ech.column46 is not null and ech.trans_serial_num is not null"
           )
    List<ErPaySta> yofcgetPayingSiDoc();

    @Select("select ech.column46 as refnbr,ech.trans_serial_num as busnbr,ech.document_num as documentNum,ech.header_id as headerId, 2 as type " +
            "from exp_claim_header ech " +
            "left join exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where ech.pay_status = 'paid' and ech.`status` in ('approved','closed') and ech.company_id = '16895' and eht.type_code = 'PAY001' " +
            "and ech.actual_payment_date is null and ech.column46 is not null and ech.trans_serial_num is not null"
           )
    List<ErPaySta> yofcgetPaidSiDoc();

    @Select("update exp_claim_header set trans_serial_num = #{busNbr}, column44 = #{bankAccountCode}, column46 = #{refnbr}, pay_status = 'paying' " +
            "where `status` in ('approved','closed') and document_id = #{apDocumentId}")
    void yofcUpdatePayColumn50(@Param("busNbr") String busNbr, @Param("refnbr") String refnbr, @Param("bankAccountCode") String bankAccountCode, @Param("apDocumentId") int apDocumentId);

    @Select("select trans_serial_num from exp_claim_header where `status` in ('approved','closed') and document_id = #{documentId}")
    String getTransSerialNum(@Param("documentId") int documentId);

    List<YofcPaymentVoucher> getPaymentVouchersNew(@Param("documentIds") List<Integer> documentIds);

    @Select("select ech.header_id as headerId, ech.column_json as columnJson, eht.type_code as typeCode," +
            "ech.document_num as documentNum, ech.document_num as orignlDocumentNum, ech.total_pay_amount as totalPayAmount " +
            "from exp_claim_header ech " +
            "left join exp_header_type eht on ech.header_type_id = eht.type_id " +
            "where eht.type_code in ('T001','T002','T003','RQ01','RQ02','RQ03','RQ04','RQ05','RQ06','RQ07') " +
            "and ech.`status` = 'approved' " +
            "and not exists(select ech2.header_id from exp_claim_header ech2 where ech2.link_header_id = " +
            "ech.header_id and ech2.`status` not in ('cancelled','deleted')) " +
            "and header_id not in (98654,102197)")
    List<ClaimCopyInfo> getCopyClaim();

    @Select("select type_id from exp_header_type where type_code = #{typeCode} limit 1\n")
    String gettypeId(@Param("typeCode") String typeCode);

    @Select("select user_id from fnd_workflow_path  where source_id = #{headerId} and type = 'A'  ORDER BY sequence_num  desc limit 1")
    String getworkFlowUser(@Param("headerId") String headerId);

    int saveNew( ExpClaimHeader ech);


    void  submitNew(ExpClaimHeader ech);

    @Select("SELECT ech.document_num as documentNum,eht.type_code as typeCode,ech.header_id as headerId,\n" +
            "column14,column44,column31,column7\n" +
            "FROM exp_claim_header ech left join exp_header_type eht \n" +
            "on ech.header_type_id = eht.type_id \n" +
            "where header_id = (select link_header_id from exp_claim_header where header_id = #{headerId})")
    Pay3Header getLinkPay3Header(@Param("headerId") String headerId);

    @Select("select document_num from exp_claim_header where header_id in (select link_header_id from exp_claim_header where header_id = #{headerId})")
    String getFrontDocument(@Param("headerId") String headerId);

    @Select("select column14 from exp_claim_header where header_id in (select link_header_id from exp_claim_header where header_id = #{headerId}) limit 1")
    String yofcFrontColumn14(@Param("headerId") String headerId);

    @Update("update exp_claim_line set fin_receipt_amount = #{updateLine.finReceiptAmount},fin_exchange_rate = #{updateLine.finExchangeRate}," +
            "fin_pay_amount = #{updateLine.finPayAmount},fin_pay_claim_amount = #{updateLine.finPayClaimAmount}," +
            "fin_pay_currency_amount = #{updateLine.finPayCurrencyAmount}, fin_tax_amount=#{updateLine.finTaxAmount}," +
            "fin_tax_code_id=#{updateLine.finTaxCodeId},fin_claim_amount=#{updateLine.finClaimAmount},fin_net_amount=#{updateLine.finNetAmount}" +
            "where column40 = #{updateLine.column40} and company_id = 16895")
    void updateLine(@Param("updateLine") UpdateLine updateLine);

    @Update("update exp_claim_header " +
            "set total_pay_amount = #{totalPayAmount}, total_pay_currency_amount = #{totalPayCurrencyAmount} " +
            "where document_num = #{documentNum}")
    void updateTotalPayAmount(@Param("totalPayAmount") String totalPayAmount,
                              @Param("totalPayCurrencyAmount") String totalPayCurrencyAmount,
                              @Param("documentNum") String documentNum);

    @Select("SELECT document_num \n" +
            "from exp_claim_header  ech\n" +
            "where ech.header_id in (select link_header_id from exp_claim_header where document_num = #{documentNum} and `status` in ('approved','closed')) LIMIT 1")
    String getFrontDocumentNum(@Param("documentNum") String documentNum);

    @Select("select type_code from exp_header_type where type_id in (\n" +
            "select header_type_id from exp_claim_header ech\n" +
            "where ech.header_id in(select link_header_id from exp_claim_header where header_id = #{headerId})\n" +
            "UNION\n" +
            "select header_type_id from exp_claim_header ech\n" +
            "where ech.header_id in(select link_header_id from exp_claim_line where header_id = #{headerId})\n" +
            ")")
    List<String> getFrontTypes(@Param("headerId") String headerId);

    @Select("select document_num from exp_claim_header where header_id = #{headerId}")
    String getdDocumentNumById(@Param("headerId") String headerId);

    @Select("SELECT column_json from exp_claim_header where header_id = #{headerId}")
    String  yofcColumnJson(@Param("headerId") String headerId);

    void addGlJe(@Param("companyId") String companyId,
                 @Param("journalNum") String journalNum,
                 @Param("userId") String userId,
                 @Param("glJeBatch") GlJeBatch glJeBatch,
                 @Param("ledgerId") Integer ledgerId);

    @Insert("insert into gl_je_batch_assign(company_id,batch_id,header_id) values (#{companyId},#{batchId},#{headerId})")
    void addBatch(@Param("companyId") String companyId,@Param("batchId") Integer batchId,@Param("headerId") String headerId);

    @Select("SELECT column47 from exp_claim_header where header_id = #{headerId} LIMIT 1 ")
    String getColumn47(@Param("headerId") String headerId);

    @Select("SELECT DISTINCT er.invoice_num\n" +
            "        FROM exp_claim_header eh right join exp_claim_line el on eh.header_id = el.header_id\n" +
            "        right join exp_expense ee on el.line_id = ee.line_id right join exp_receipt er\n" +
            "        on ee.expense_id = er.expense_id where eh.document_id = #{documentId}")
    List<String> getInvoiceNums(@Param("documentId") String documentId);

    @Select("select ech.header_id as headerId, ech.document_num as documentNum, ech.internal_type as internalType, " +
            "ech.advance_amount as advanceAmount, ech.total_pay_amount as totalPayAmount, " +
            "ech.column48 as column48, eht.type_code as typeCode " +
            "from exp_claim_header ech, exp_header_type eht where ech.header_type_id = eht.type_id " +
            "and ech.header_id = #{headerId} and ech.company_id = 16895 limit 1 ")
    PayInfo getPaymentInfo(@Param("headerId") String headerId);

    @Select("select link_header_id from exp_claim_header where header_id = #{headerId} and company_id = 16895 limit 1")
    String getLinkHeaderId(@Param("headerId") String headerId);

    @Select("select link_header_id as linkHeaderId from exp_claim_header where header_id = #{headerId} and company_id = 16895 " +
            "union " +
            "select link_header_id as linkHeaderId from exp_claim_line where header_id = #{headerId} and company_id = 16895 " +
            "union " +
            "select link_header_id as linkHeaderId from exp_claim_header_link where header_id = #{headerId} and company_id = 16895")
    List<Integer> getLinkHeaderIds(@Param("companyId") Integer companyId, @Param("headerId") String headerId);

    @Select("select header_id as headerId,branch_account_id  as bankAccountId,currency_code as currencyCode " +
            "from exp_claim_header " +
            "where link_header_id = #{headerId} and company_id = 16895 limit 1")
    PayInfo getNextHeaderId(@Param("headerId") Integer headerId);

    @Select("select SUM(total_pay_amount) from exp_claim_header where header_id in " +
            "(select header_id from exp_claim_header where link_header_id = #{headerId}) and company_id = 16895 and status = 'closed' limit 1")
    BigDecimal getSumTotalPayAmount(@Param("headerId") String headerId);

    @Select("SELECT fu.full_name as fullName,fu.column1 FROM exp_claim_header ech left join fnd_workflow_path fwp on ech.header_id = fwp.source_id \n" +
            "left join fnd_user fu on fwp.user_id = fu.user_id \n" +
            "where header_id = #{headerId} and ech.`status` != 'deleted' and fwp.type = 'A'  \n" +
            "Order By fwp.path_id ASC limit 1")
    UserInfo getFinanceUser(@Param("headerId") String headerId);

    @Select("select ga.account_code from exp_claim_line ecl left join gl_account ga on ecl.dr_account_id = ga.account_id " +
            "where ecl.header_id = #{headerId} limit 1")
    String getLineFirstDrAccount(@Param("headerId") Integer headerId);

    void updatePayStatusByDocumentIds(@Param("documentIds") List<String> documentIds, @Param("status") String status, @Param("message") String message);

    void updateColumnByDocumentIds(@Param("documentIds") List<String> documentIds, @Param("column44") String column44);

    @Select("select fd.column1 from cpcfg.fnd_department_tl fdt, cpcfg.fnd_department fd\n" +
            "where fdt.department_id = fd.department_id and fdt.department_name in('公司_公共','Public')" +
            "and fdt.language = 'zh_CN' and fd.supervisor_id = #{departmentId} limit 1")
    String getGsggColumn1(@Param("departmentId") Integer departmentId);

    @Select("select ech.header_id as headerId, eht.type_code as typeCode, ech.document_num as documentNum, " +
            "ech.supplier_id as supplierId, ech.branch_id as branchId, ech.column_json as columnJson, " +
            "ech.gl_status as glStatus " +
            "from cpcfg.exp_claim_header ech, cpcfg.exp_header_type eht " +
            "where ech.header_type_id = eht.type_id and ech.document_id = #{documentId} and ech.status <> 'deleted' limit 1")
    Map getTypeByDocumentId(@Param("documentId") Integer documentId);

    @Select("select column4, column5, department_code as departmentCode,currency_code as currencyCode " +
            "from fnd_department " +
            "where department_id=#{branchId} " +
            "limit 1")
    BranchInfo getBranchById(@Param("branchId") Integer branchId);
    @Select("select column4, column5, department_code as departmentCode,currency_code as currencyCode " +
            "from fnd_department " +
            "where department_code=#{code} " +
            "limit 1")
    BranchInfo getBranchByCode(@Param("code") String code);

    @Select("select ecl.pay_method_id as payMethodId from cpcfg.exp_claim_line ecl where ecl.header_id = #{headerId} and pay_method_id <> 48849 limit 1")
    Integer payMethodExists(@Param("headerId") Integer headerId);

    @Update("update cpcfg.exp_claim_header set gl_status = #{glStatus} where header_id = #{headerId}")
    void updateGlStatus(@Param("headerId") Integer headerId, @Param("glStatus") String glStatus);

    @Update("update cpcfg.exp_claim_header set gl_status = #{glStatus}, gl_message = #{glMessage} where document_id = #{documentId} and status <> 'deleted'")
    void updateGlMessageByDocumentId(@Param("documentId") Integer documentId, @Param("glStatus") String glStatus, @Param("glMessage") String glMessage);

    @Select("select eht.type_code as typeCode, fu.full_name as submitUser,ech.column13 as column13 " +
            "from cpcfg.exp_claim_header ech " +
            "left join cpcfg.exp_header_type eht on eht.type_id = ech.header_type_id " +
            "left join cpcfg.fnd_user fu on fu.user_id = ech.submit_user " +
            "where ech.header_id = #{headerId}")
    FaPiaoLinkHeaderInfo getLinkHeader(@Param("headerId") Integer headerId);

    @Update("update cpcfg.exp_claim_header set journal_num = #{journalNum} where document_id = #{documentId} and status <> 'deleted'")
    void updateJournalNum(@Param("documentId") Integer documentId, @Param("journalNum") String journalNum);

}
