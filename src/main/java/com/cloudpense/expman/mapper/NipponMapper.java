package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.EmployeeWhoLeft;
import com.cloudpense.expman.entity.ExpClaimAttachment;
import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.hsf.HsfNtf;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface NipponMapper {

    public void updateCompany(@Param("code") String code, @Param("name") String name, @Param("type") String type, @Param("taxNum") String taxNum, @Param("enable1") String enable1, @Param("enable2") String enable2);

    public void updateCostCenter(@Param("json") String json);

    public void updateFrameOrder(@Param("json") String json);

    public void updateSupplier(@Param("json") String json);

    public void updateCustomer(@Param("json") String json);

    public void updatePhoneLimit(@Param("json") String json);

    public void updateOutWork(@Param("json") String json);

    public void updateCredit(@Param("json") String json);

    public void updateWbs(@Param("json") String json);

    public void freshFrameStatus();

    public ArrayList<String> queryCompanyCodeList();

    public void updateData(@Param("json") String json, @Param("type") String type);

    public void updateBudget(@Param("json") String json);

    public void updateBudgetYear();

    public void updateExRate(@Param("rate") String rate, @Param("update") String update);

    public void getSAPNo(Map<String, String> id);

    public ArrayList<ExpClaimHeader> getUnsentReimClaim();

    public String queryEmployeeNum(int id);

    public Integer queryUserId(String emp);

    public String queryUserName(String emp);

    public String queryDeptCode(int id);

    public String queryDeptName(int id);

    public ArrayList<ExpClaimLine> queryClaimLine(int claimId);

    public ArrayList<ExpClaimLine> queryClaimDetail(int claimId);

    public String queryCityName(int id);

    public List<String> queryCityNames(int headerId);

    public String queryProjectInfo(int id);

    public ArrayList<ExpClaimHeader> queryTravel(@Param("departmentIds") List<Integer> departmentIds);

    public void rejectHeader(@Param("headerId") int headerId, @Param("tips") String tips);

    public ExpClaimHeader queryLinkHeader(int headerID);

    public ExpClaimHeader queryClaimHeaderByDocNum(String docNum);

    public ExpClaimHeader queryClaimHeaderByDocNum1(String docNum);

    public ArrayList<String> queryApproveList(int claimId, String sapNo, String docNum);

    public ArrayList<ExpClaimAttachment> queryFileList(int header, String lines);

    public void updateExternalStatus(@Param("headerId") int headerId,
                                     @Param("extStatus") String extStatus,
                                     @Param("extMsg") String extMsg);

    public void updateSapNo(@Param("id") int headerId, @Param("sapNo") String sapNo);

    public void updateWorkFlow(int claimId);

    public void updateWorkFlow2(int claimId);

    public Integer queryWorkFlow2(int claimId);

    public int saveWithFinanceInfoNew(ExpClaimHeader ech);

    public ArrayList<HsfNtf> queryNtfCompany();

    public ArrayList<HsfNtf> queryNtfCompanyEmail();

    public void freshNtfStatus(@Param("cid") int cid);

    public void deleteNtf(@Param("id") int id);

    public void updateNtfStatus(@Param("id") Integer id, @Param("status") Integer status, @Param("content") String content);

    public void updateReimStatus();

    public ArrayList<Integer> queryClaimInvoice(int headerId);

    public void updateNipponStatus(@Param("id") int id, @Param("finStatus") String finStatus, @Param("SendStatus") String sendStatus, @Param("external") String externalStatus);

    public String qureyGlp(@Param("id") int id);

    public Date queryLastApprovedTime(int id);

    public List<String> queryHeaderToUpdateSendStatus();

    public void updateSendStatus(@Param("9") String sapNo, @Param("11") String status);

    public void toggleTravelPush(@Param("status") String status);

    public void toggleTravelPushByGroup(@Param("status") String status, @Param("businessGroup") String businessGroup);

    public String queryTravelPush();

    public String queryTravelPushByGroup(String businessGroup);

    public Integer queryPayMethod(@Param("lineId") int lineId);

    public List<String> queryLineInvoice(@Param("lineId") int lineId);

    public List<String> queryLineInvoiceOther(@Param("lineId") int lineId);

    String getUpdatedInvoice();

    String queryUserFlag(@Param("empNo") String empNo);

    void unlockReceipt(@Param("headerId") int headerId);

    void updateClaimCredit(@Param("userId") int userId, @Param("headerId") int headerId);

    void updatePostingDate(@Param("header_id") int header_id, @Param("type") String type, @Param("date") String date);

    //审批
    void updateFndWorkflowPath(@Param("pathId") int pathId, @Param("note") String note, @Param("userId") int userId, @Param("clientId") int clientId, @Param("language") String language, @Param("status") String status, @Param("returnCode") String returnCode, @Param("returnMessage") String returnMessage);

    //查询成本中心名称
    String queryDeptNameByCode(String costCenterCodeKey);

    List<Integer> queryZznppCompanyGroup();

    List<String> queryLovValueList(String lovName);

    String deptLeaderRequestJsonArray();

    void updateEmployeeWhoLeft(EmployeeWhoLeft employeeWhoLeft);
    void updateEmployeeWhoLeftInactive(EmployeeWhoLeft employeeWhoLeft);
}
