package com.cloudpense.expman.mapper;

import com.cloudpense.expman.Request.KhSapVoucherInfo;
import com.cloudpense.expman.entity.FndQuery;
import com.cloudpense.expman.entity.kehua.FndLovValue;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface KehuaMapper {
    /**
     * 科华数据更新
     */
    public String kehuaDataUpdate(FndQuery fndQuery);

    /**
     * 错误信息记录
     * @param companyId 公司代码
     * @param type 类型
     * @param content 错误内容
     */
    public void normalInsertLog(@Param("companyId") int companyId,
                                @Param("type") String type,
                                @Param("content") String content);

    /**
     * 发送邮件提醒
     * @param companyId 公司代码
     * @param content 邮件内容
     */
    public void mailSend(@Param("companyId") int companyId,
                         @Param("content") String content);

    //查询单据信息（凭证）
    public KhSapVoucherInfo selectExpClaimHeader(int documentId);

    //查询单据行（凭证）借
    public List<KhSapVoucherInfo> selectExpClaimLineDr(int headerId);

    //查询单据行（凭证）贷
    public List<KhSapVoucherInfo> selectExpClaimLineCr(int headerId);

    /**
     * 获取单据行信息
     *
     * @param headerId
     * @return
     */
    List<KhSapVoucherInfo> getClaimLine(int headerId);

    /**
     * 根据科目编码获取科目描述
     * @return
     */
    String getAccountName(@Param("accountCode") String accountCode);

    @Select("select lov_id from fnd_lov fl " +
            "where fl.company_id = 14870 and fl.lov_name = #{lovName} limit 1")
    Integer queryLovIdByName(@Param("lovName") String lovName);
    @Select("select value_id as valueId, column10 from fnd_lov_value flv " +
            "where flv.lov_id = #{lovId} and flv.value_code = #{valueCode} limit 1")
    FndLovValue queryLovValueByCode(@Param("lovId") Integer lovId, @Param("valueCode") String valueCode);
    @Update("insert fnd_lov_value(lov_id, company_id, value_code, enabled_flag, column10, creation_date, last_update_date) " +
            "values(#{lovId},14870,#{valueCode},'Y',#{column10},now(),now())")
    void insertLovValue(@Param("lovId") Integer lovId, @Param("valueCode") String valueCode, @Param("column10") String column10);
    @Update("insert fnd_lov_value_tl(value_id, value_meaning, description, language, last_update_date) " +
            "select value_id, #{valueMeaning}, #{description}, #{language}, now() " +
            "from fnd_lov_value where lov_id = #{lovId} and value_code = #{valueCode} limit 1")
    void insertLovValueTl(@Param("lovId") Integer lovId, @Param("valueCode") String valueCode, @Param("valueMeaning") String valueMeaning,
                          @Param("description") String description, @Param("language") String language);
    @Update("update fnd_lov_value flv set flv.column10 = #{column10} " +
            "where value_id = #{valueId}")
    void updateLovColumnById(@Param("valueId") Integer valueId, @Param("column10") String column10);
    @Select("select document_num from exp_claim_header where header_id in(select link_header_id from exp_claim_line where header_id = #{headerId} and internal_type = 'advance')")
    List<String> queryAdvanceLinkDocumentNumList(@Param("headerId") Integer headerId);
}
