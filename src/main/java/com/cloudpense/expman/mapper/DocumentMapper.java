package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.ExpClaimHeader;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 修改单据状态mapper
 */
public interface DocumentMapper {

    //修改单据状态
    public void updateDocument(@Param("headerId") int headerId, @Param("companyId") int companyId, @Param("userId") int userId, @Param("status") String status);

    //查询状态不是close的单据
    public List<Integer> selectDocument(ExpClaimHeader expClaimHeader);
}
