package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.shangfa.OnBusinessUser;
import com.cloudpense.expman.entity.shangfa.VoucherHeader;
import com.cloudpense.expman.entity.shangfa.VoucherLine;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/20 15:45
 */
public interface ShangfaMapper {


    @Select("SELECT fu.employee_number as employeeNumber,eht.type_code as typeCode,ech.start_datetime as startDatetime," +
            "ech.end_datetime as endDatetime,ech.document_num as documentNum,ech.header_id as headerId,ech.external_status as externalStatus,ech.external_message as externalMessage " +
            " from exp_claim_header ech \n" +
            "LEFT JOIN exp_header_type  eht on ech.header_type_id = eht.type_id\n" +
            "left join fnd_user fu on ech.submit_user = fu.user_id\n" +
            "where ech.`status` = 'approved' and eht.type_code in ('SF001','SF036','SF031','SF037','SF005','SF039','SF012','SF042','SF014','SF043','SF022','SF045')")
    List<OnBusinessUser> shangfaHeaderUser();


    @Select("select fu.employee_number as employeeNumber,type_code as typeCode, ecl.line_id as lineId from exp_claim_line ecl  \n" +
            "left join exp_type et on et.type_id = ecl.type_id\n" +
            "left join fnd_user fu on fu.user_id = ecl.charge_user\n" +
            "where ecl.header_id = #{headerId}")
    List<OnBusinessUser> shangfaLineUser(@Param("headerId") Integer headerId);

    @Select("select ech.header_id as headerId,ech.document_num as documentNum,eht.type_code as typeCode,ech.description,ech.column12,ech.column3,ech.column35," +
            "ech.currency_code as currencyCode,fd.department_code as departmentCode," +
            "pp.project_code as projectCode,pp.project_id as projectId " +
            " from exp_claim_header ech \n" +
            "LEFT JOIN exp_header_type  eht on ech.header_type_id = eht.type_id\n" +
            "LEFT JOIN fnd_department fd on fd.department_id = ech.branch_id\n" +
            "LEFT JOIN pjm_project pp on pp.project_id = ech.project_name\n" +
            "where ech.`status` in ('approved','closed') and ech.document_id = #{documentId}")
    VoucherHeader shangfaVoucherHeader(@Param("documentId")Integer documentId);

    @Select("select ecl.fin_net_amount as finNetAmount,ecl.fin_claim_amount as finClaimAmount,ecl.fin_tax_amount as finTaxAmount,es.supplier_code as suppplierCode,\n" +
            "gldr.account_code as drAccountCode,glcr.account_code as crAccountCode,\n" +
            "gltax.account_code as taxAccountCode,\n" +
            "ecl.original_amount as originalAmount,ecl.start_datetime as startDateTime,ecl.end_datetime as endDateTime,\n" +
            "ecl.receipt_currency as receiptCurrency,fd.department_code as departmentCode,pp.project_code as projectCode," +
            "pp.project_id as projectId,ecl.column41  \n" +
            "from exp_claim_line ecl\n" +
            "left join gl_account gldr  on gldr.account_id = ecl.dr_account_id\n" +
            "left join gl_account glcr  on glcr.account_id = ecl.cr_account_id\n" +
            "left join gl_account gltax on gltax.account_id = ecl.tax_account_id\n" +
            "left join exp_type et on et.type_id = ecl.type_id\n" +
            "left join fnd_department fd on ecl.cost_center_id = fd.department_id\n" +
            "left join exp_supplier es on ecl.supplier_id = es.supplier_id\n" +
            "left join pjm_project pp on pp.project_id = ecl.project_name \n" +
            "where ecl.header_id = #{headerId}")
    List<VoucherLine> shangfaVoucherLine(@Param("headerId") Integer headerId);

    @Select("select external_status from exp_claim_header where header_id = #{headerId} ")
    String shangfaGetExternalStatus(@Param("headerId") Integer headerId);

    @Select("select external_message from exp_claim_header where header_id = #{headerId}")
    String shangfaGetExternalMessage(@Param("headerId") Integer headerId);

    @Update("update exp_claim_header set external_status = #{status},external_message = #{message} where header_id = #{headerId}")
    void shangfaUpdateExternal(@Param("status") String status,@Param("message") String message,@Param("headerId") Integer headerId);

    @Select("select full_name from fnd_user where user_id = #{userId} limit 1")
    String shangfaGetUserName(@Param("userId") String userId);

    @Select("select count(claim_header_id) from exp_claim_attachment where claim_header_id = #{headerId}")
    Integer shagnfaGetAttachmentsCount(@Param("headerId") Integer headerId);

    @Select("select JSON_LENGTH (invoice_attachments) as exp from exp_claim_line as exc left join exp_receipt as er on exc.expense_id = er.expense_id where exc.header_id = #{headerId}")
    List<Integer> getInvoiceAttachment(@Param("headerId") Integer headerId);

    @Select("select parent_id from pjm_project where project_id = #{projectId}")
    String shangfaGetParent(@Param("projectId") String projectId);

    @Select("select project_code from pjm_project where project_id = #{projectId}")
    String shangfaGetProjectCode(@Param("projectId") String projectId);

    @Select(" select save_cal_btn\n" +
            "        from   exp_header_type\n" +
            "        where  type_id = #{headerTypeId}\n" +
            "        and    company_id = #{companyId}\n" +
            "        limit 1")
    String findSaveCalBtn(@Param(value = "headerTypeId") Integer headerTypeId,
                          @Param(value = "companyId") Integer companyId);

    @Select(" select type_id\n" +
            "        from   exp_type\n" +
            "        where  type_code = #{TypeCode}\n" +
            "        and    company_id = 16894\n" +
            "        limit 1")
    Integer findTypeIdSSF(@Param(value = "TypeCode") String TypeCode);

    @Select(" select detail\n" +
            "        from   fnd_expense_standard_level\n" +
            "        where  standard_id = (select standard_id from fnd_expense_standard where company_id = #{companyId} and internal_type = 'flight')\n" +
            "        and    company_id = #{companyId}\n" +
            "        and    enabled_flag = 'Y'\n" +
            "        limit 1")
    String findallowanceDetail(@Param(value = "companyId") Integer companyId);

    @Select("  select value_code\n" +
            "        from   fnd_lov_value_v\n" +
            "        where  value_id = #{airlineName}\n" +
            "        and    company_id = #{companyId}\n" +
            "        and    language = #{language}\n" +
            "        limit 1")
    String findFlightValueCide(@Param(value = "airlineName") Integer airlineName,
                               @Param(value = "companyId") Integer companyId,
                               @Param(value = "language") String language);

    @Select("select text from sf_voucher_des")
    List<String> shangfaVoucherDes();

    @Select("select document_id  from exp_claim_header where  document_num = #{documentNum}")
    List<Integer> getDocumentId(@Param("documentNum") String documentNum);

}
