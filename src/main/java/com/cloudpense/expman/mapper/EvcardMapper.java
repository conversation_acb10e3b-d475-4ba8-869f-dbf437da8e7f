package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.FndQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EvcardMapper {
    /**
     * Evcard
     */
    public List<String> evcardDataUpdate(FndQuery fndQuery);

    public String evcardGetTypeByAccount(@Param("companyId") int companyId,
                                         @Param("accountCode") String accountCode);

    public String evcardGetPositionRecever(@Param("companyId") int companyId,
                                         @Param("positionId") int positionId);

    public List<String> getOaPushTask(@Param(value = "companyId") int companyId);

    public List<String> evcardGetHeaderAttachments(@Param("companyId") int companyId,
                                                  @Param("headerId") int headerId);

    public List<String> evcardGetLinesAttachments(@Param("companyId") int companyId,
                                                  @Param("headerId") int headerId,
                                                  @Param("lineNum") String lineNum);
    public List<String> evcardGetLinesInfo(FndQuery fndQuery);

    public List<String> getLovValue(@Param("companyId") int companyId, @Param("lovName") String lovName);
}
