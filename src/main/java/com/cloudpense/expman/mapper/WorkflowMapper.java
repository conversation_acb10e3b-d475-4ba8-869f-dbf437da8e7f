package com.cloudpense.expman.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface WorkflowMapper {
    /**
     * 获取待办任务数
     * @param companyId      企业ID
     * @param userIds        员工号
     * @return Integer 待办任务数量
     */
    Integer getToDoWorkflowCount(@Param("companyId") int companyId, @Param("userIds")  List<Integer> userIds);

    /**
     * 获取用户ID列表
     * @param companyId      企业ID
     * @param employeeNumber 员工号
     * @return List 获取用户ID列表
     */
    List<Integer> getUserIds(@Param("companyId") int companyId,@Param("employeeNumber")  String employeeNumber);
}
