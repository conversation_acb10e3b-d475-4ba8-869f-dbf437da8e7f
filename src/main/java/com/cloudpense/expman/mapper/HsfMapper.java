package com.cloudpense.expman.mapper;


import com.cloudpense.expman.entity.ExpClaimHeader;
import com.cloudpense.expman.entity.ExpClaimLine;
import com.cloudpense.expman.entity.hsf.HsfNtf;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public interface HsfMapper {

    public void updateSubCompany(String jsonString);

    public void updateDepartment(String jsonString);

    public void updateUser(String jsonString);

//    public void updateExpType(String jsonString);
//
//    public void updateCusList(String jsonString);
//
//    public void updateCusItem(String jsonString);

    public void updateBankAcc(String jsonString);

    public ArrayList<ExpClaimHeader> queryUnsentClaim();

    ArrayList<ExpClaimHeader> queryRepushClaim(@Param(value = "documentNums") List<String> documentNums);

    public String queryDocNum(int id);

    public String queryHSFPsnCode(int userID);

    public String queryHSFNCPsnCode(int userID);

    public String queryHSFOrgCode(int deptID);

    public String queryHSFDeptCode(int deptID);

    public String queryHSFNCDeptCode(int deptID);

    public ExpClaimHeader queryLinkHeader(int headerID);

    public ExpClaimHeader queryLinkHeaderByLine(int lineID);

    public ExpClaimHeader queryClaimHeader(int headerID);

    public ArrayList<ExpClaimLine> queryClaimLine(int claimId);

    public ArrayList<String> queryContractAtts(@Param("oaid") String oaid);

    public void saveContractNC(@Param("json") String json, @Param("orgCode") String orgCode);

    public void saveContractOA(@Param("json") String json);

    public ArrayList<String> queryOrgCode();

    public ArrayList<HsfNtf> queryNtfCompany();

    public void saveOAToken(@Param("token") String token);

    public String getOAToken();

    public String queryPsnID(@Param("userId") String userId);

    public void freshNtfStatus(@Param("cid") int cid);

    public void updateNtfStatus(@Param("id") Integer id, @Param("status") Integer status, @Param("content") String content, @Param("message") String message);

    public int queryUserExistsById(@Param("id") String id);

    public void updateExternalStatus(@Param("headerId") int headerId,
                                     @Param("extStatus") String extStatus,
                                     @Param("extMsg") String extMsg);

    public void updatePosition(@Param("response") String response,
                               @Param("orgCode") String orgCode);

    public String getDeptOrgCode(@Param("id") int id);

    public String getLastApprove(int id);

    public String getBankAcconut(int id);

    public ExpClaimLine queryLinkLine(int lineId);

    public void oaContractLog(String con);

    public String queryAccountingSubject(@Param("subjectId") int subjectId);

    String querySupplierCode(int supplierId);

    void disableOutdatedData();

    public void insert_log(@Param("type") String type, @Param("content") String content);

    List<Map<String, String>> getErrorPushedClaims(@Param("startDate") String startDate, @Param("endDate") String endDate);

    String querySupplierAccountNum(int accountId);// 获取供应商银行账号
}
