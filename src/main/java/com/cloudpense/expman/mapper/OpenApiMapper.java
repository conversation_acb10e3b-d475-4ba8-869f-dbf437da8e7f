package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.FndQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OpenApiMapper {
    public List<String> commonUpdate(FndQuery fndQuery);

    public void cacheExpAttachmentName(@Param(value = "companyId") int companyId,
                                       @Param(value = "userId") int userId,
                                       @Param(value = "source") String source,
                                       @Param(value = "fileName") String fileName);
}
