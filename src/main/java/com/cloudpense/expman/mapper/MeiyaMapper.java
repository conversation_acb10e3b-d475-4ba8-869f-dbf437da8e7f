package com.cloudpense.expman.mapper;

import com.cloudpense.expman.entity.BindUserInfo;
import com.cloudpense.expman.entity.GlBatchTransfer;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface MeiyaMapper {
    public List<String> glBatchCreateMeiya(GlBatchTransfer glBatchTransfer);

    public Collection<String> getDingTalkPushTasks(@Param("companyId") int companyId);

    public BindUserInfo getBindUserInfoByUserId(@Param("companyId") int companyId,
                                                @Param("platform") String platform,
                                                @Param("userId") int userId);

    public Collection<Integer> getUserIdsByPositionId(@Param("companyId") int companyId,
                                                      @Param("positionId") int positionId);

    public void updateGlStatusByDocumentId(@Param("documentId") Integer documentId,
                                           @Param("glStatus") String glStatus,
                                           @Param("glMessage") String glMessage);

    public BindUserInfo findBindByMobile(@Param("platform") String platform,@Param("mobile") String mobile);

    public String findUserNameById(@Param("userId") int userId);
}
