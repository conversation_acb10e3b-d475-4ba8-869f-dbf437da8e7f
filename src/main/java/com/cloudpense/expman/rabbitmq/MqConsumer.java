package com.cloudpense.expman.rabbitmq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.dataSource.CustomerContextHolder;
import com.cloudpense.expman.entity.MqQueue;
import com.cloudpense.expman.mapper.MqMapper;
import com.cloudpense.expman.service.YofcService;
import com.rabbitmq.client.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 生产者
 *
 * <AUTHOR>
 */
@Component
public class MqConsumer {

    private static ResourceBundle RB = ResourceBundle.getBundle("mq");
    private static String host = RB.getString("spring.rabbitmq.host");
    private static Integer port = Integer.valueOf(RB.getString("spring.rabbitmq.port"));
    private static String username = RB.getString("spring.rabbitmq.username");
    private static String password = RB.getString("spring.rabbitmq.password");
    private static String virtualHost = RB.getString("spring.rabbitmq.virtual-host");
    private static Integer mqChannel = Integer.valueOf(RB.getString("mq.channel"));
    private static String exchange = RB.getString("exchange");
    private static String queueName = RB.getString("queue.name");

    private static Logger logger = LoggerFactory.getLogger(MqConsumer.class);

    @Autowired
    private YofcService yofcService;

    @Autowired
    private MqMapper mqMapper;

    public void init() throws Exception {
        /*//执行的步骤
        // 创建一个ConnectionFactory
        ConnectionFactory connectionFactory = new ConnectionFactory();

        //设置连接rabbitmq的连接信息
        //设置连接的地址
        connectionFactory.setHost(host);
        //设置端口号
        connectionFactory.setPort(port);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        //关键所在，指定线程池
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        connectionFactory.setSharedExecutor(executorService);
        //设置自动重连标志
        connectionFactory.setAutomaticRecoveryEnabled(true);
        connectionFactory.setNetworkRecoveryInterval(10*1000);// 设置每10s重试一次
        connectionFactory.setTopologyRecoveryEnabled(false);// 设置不重新声明交换器，队列等信息

        //通过连接工厂创建连接
        Connection connection = connectionFactory.newConnection();
        //通过连接创建一个channel信道
        Channel channel = connection.createChannel(mqChannel);


        //声明一个队列

         * 参数详解:
         * 	参数一:队列名称 queue
         *  参数二:是否是持久化,持久化的含义就是当设定持久化之后，当我们的服务重新启动的时候当前的队列已让存在  durable
         *  参数三:代表的是读栈的方式 ,想要进行顺序的读取信息就可以设定为true,当前定义的队列connection中的channel是共享的，其他的connection是访问不到的
         *                当conneciton.close的时候，queue被删除  exclusive
         *  参数四:自动删除   autoDelete

        channel.queueDeclare(queueName, true, false, false, null);

        //创建消费者,并获取到消息和输出消息
        Consumer consumer = new DefaultConsumer(channel) {
            @Override
            public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                       byte[] body) throws IOException {
                String message = new String(body, StandardCharsets.UTF_8);
                String LOG_KEY = "长飞cbs支付==";
                logger.info("{}收到消息={}", LOG_KEY, message);
                CustomerContextHolder.setCustomerType(CustomerContextHolder.CFG);
                Map mapObj = JSONObject.parseObject(message, Map.class);
                int id = (int) mapObj.get("id");
                try {
                    String documentIds = mapObj.get("headerIdList").toString();
                    List<Integer> documentIdList = JSONObject.parseArray(documentIds, Integer.class);
                    JSONObject inputJson = new JSONObject();
                    inputJson.put("document_id", documentIdList);
                    inputJson.put("account_id", mapObj.get("accountId"));
                    inputJson.put("mqId", id);
                    int userId = (int) mapObj.get("userId");
                    String result = yofcService.cbsPayStart(inputJson, userId).toJSONString();
                    logger.info("{}支付结果result={}", LOG_KEY, result);
                } catch (Exception e) {
                    logger.info("{}出现未知异常：{}", LOG_KEY, e.getMessage(), e);
                    MqQueue mqQueue = new MqQueue();
                    mqQueue.setId(id);
                    mqQueue.setStatus(3);
                    mqMapper.updateMqStatus(mqQueue);
                }
            }
        };
        //设置信道的属性

         * 属性的详解:
         *	参数一:当前的信道使用的哪一个队列，是通过routing key进行设置的  queue
         *  参数二:是否自动签收(接受消息) atuoAck
         *  参数三:具体的消费者对象

        channel.basicConsume(queueName, true, consumer);

        //关闭相关的连接
        //channel.close();
        //connection.close();*/
    }

}
