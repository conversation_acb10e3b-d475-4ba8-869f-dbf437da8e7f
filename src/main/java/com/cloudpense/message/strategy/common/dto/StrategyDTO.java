package com.cloudpense.message.strategy.common.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/27 11:27
 */
@Data
public class StrategyDTO implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 策略code
     */
    private String strategyCode;
    /**
     * 策略消息id，strategyCode加strategyId需唯一，建议用雪花算法
     */
    private Long strategyId;

    private Integer retry;

    private String strategyName;

    //用来给定制(其他渠道)存放id的字段
    private Integer mqId;

}
