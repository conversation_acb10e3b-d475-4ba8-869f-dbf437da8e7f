package com.cloudpense.message.strategy.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/27 11:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DocumentMQStrategyDTO extends StrategyDTO implements Serializable {
    private static final long serialVersionUID = -1L;
    private Long headerId;
    private Long pathId;
    private Long parentId;
    private String documentNum;
    private String status;
}
