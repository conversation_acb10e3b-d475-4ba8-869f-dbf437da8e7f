<?mybatis version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:security="http://www.springframework.org/schema/security"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context 
						http://www.springframework.org/schema/context/spring-context.xsd
				 		http://www.springframework.org/schema/mvc 
				 		http://www.springframework.org/schema/mvc/spring-mvc.xsd
				 		http://www.springframework.org/schema/aop
	                    http://www.springframework.org/schema/aop/spring-aop.xsd
						http://www.springframework.org/schema/security 
						http://www.springframework.org/schema/security/spring-security.xsd">

    <!--<mvc:annotation-driven validator="validator">-->
        <!--<mvc:argument-resolvers>-->
            <!--<bean class="com.cloudpense.expman.annotation.CurrentLocaleHandlerMethodArgumentResolver"/>-->
            <!--<bean class="com.cloudpense.expman.annotation.CurrentUserHandlerMethodArgumentResolver"/>-->
        <!--</mvc:argument-resolvers>-->
    <!--</mvc:annotation-driven>-->
    <mvc:annotation-driven />

    <context:component-scan base-package="com.cloudpense.expman.controller"></context:component-scan>
    <aop:aspectj-autoproxy/>

    <!--<security:global-method-security-->
            <!--secured-annotations="enabled" jsr250-annotations="enabled"/>-->


    <!-- **************************************************************** -->
    <!-- CONVERSION SERVICE -->
    <!-- Standard Spring formatting-enabled implementation -->
    <!-- **************************************************************** -->
    <!-- <bean id="conversionService" class="org.springframework.format.support.FormattingConversionServiceFactoryBean">
        <property name="formatters"> <set> <bean class="cn.edu.sjtu.se.dclab.seedstarter.VarietyFormatter"
        /> <bean class="cn.edu.sjtu.se.dclab.seedstarter.DateFormatter" /> </set>
        </property> </bean> -->

    <!-- **************************************************************** -->
    <!-- 完成请求和注解POJO的映射 -->
    <!-- **************************************************************** -->
    <bean
            class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping"/>

    <bean id="compositeExceptionResolver"
          class="org.springframework.web.servlet.handler.HandlerExceptionResolverComposite">
        <property name="order" value="0"/>
        <property name="exceptionResolvers">
            <list>
                <ref bean="exceptionHandlerExceptionResolver"/>
                <ref bean="restExceptionResolver"/>
            </list>
        </property>
    </bean>


    <!--<bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">-->
        <!--<property name="maxUploadSize">-->
            <!--<value>1048576</value>-->
        <!--</property>-->
        <!--<property name="defaultEncoding">-->
            <!--<value>UTF-8</value>-->
        <!--</property>-->
    <!--</bean>-->
    <!-- 多部分文件上传 -->
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="104857600" />
        <property name="maxInMemorySize" value="4096" />
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>

    <bean id="restExceptionResolver"
          class="cz.jirutka.spring.exhandler.RestHandlerExceptionResolverFactoryBean">
        <property name="messageSource" ref="httpErrorMessageSource"/>
        <property name="defaultContentType" value="application/json"/>
        <property name="exceptionHandlers">
            <map>
                <entry key="org.springframework.dao.EmptyResultDataAccessException" value="404"/>
                <entry key="com.cloudpense.expman.exception.EntityNotFoundException" value="404"/>
                <entry key="com.cloudpense.expman.exception.EntityNotFoundException2" value="424"/>
                <entry key="com.cloudpense.expman.exception.FormatException" value="422"/>
                <entry key="com.cloudpense.expman.exception.UnauthorizedException" value="405"/>
                <entry key="com.cloudpense.expman.exception.PasswordException" value="400"/>
                <entry key="com.cloudpense.expman.exception.UnsupportedFileTypeException" value="400"/>
                <entry key="com.cloudpense.expman.exception.IncorrectDataException" value="400"/>
                <entry key="com.cloudpense.expman.exception.ValidationException" value="400"/>
                <entry key="org.springframework.security.core.userdetails.UsernameNotFoundException" value="400"/>
                <!--<entry key="com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException" value="500"/>-->
                <entry key="java.sql.SQLException" value="500"/>
                <entry key="com.cloudpense.expman.exception.SqlException" value="410"/>
            </map>
        </property>
    </bean>

    <bean id="exceptionHandlerExceptionResolver"
          class="org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver"/>

    <!--<bean id="validator"-->
          <!--class="org.springframework.validation.beanvalidation.LocalValidatorFactoryBean">-->
        <!--<property name="providerClass" value="org.hibernate.validator.HibernateValidator"/>-->
    <!--</bean>-->
</beans>