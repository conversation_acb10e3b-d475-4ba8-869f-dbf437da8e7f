<?mybatis version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC
        "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>

    <!--<settings>-->
    <!--<setting name="callSettersOnNulls" value="true"/>-->
    <!--</settings>-->
    <typeAliases>
        <typeAlias alias="ExpClaimHeader" type="com.cloudpense.expman.entity.ExpClaimHeader"/>
        <typeAlias alias="ExpClaimAttachment" type="com.cloudpense.expman.entity.ExpClaimAttachment"/>
        <typeAlias alias="ExpClaimLine" type="com.cloudpense.expman.entity.ExpClaimLine"/>
        <typeAlias alias="ExpType" type="com.cloudpense.expman.entity.ExpType"/>
        <typeAlias alias="FndCostElement" type="com.cloudpense.expman.entity.FndCostElement"/>
        <typeAlias alias="FndWorkflowPath" type="com.cloudpense.expman.entity.FndWorkflowPath"/>
        <typeAlias type="com.cloudpense.expman.entity.FndDepart" alias="FndDepart"/>
        <typeAlias type="com.cloudpense.expman.entity.FndSupplier" alias="FndSupplier"/>
        <typeAlias alias="GeneralLedgerAccount" type="com.cloudpense.expman.entity.GeneralLedgerAccount"/>
        <typeAlias alias="FndUser" type="com.cloudpense.expman.entity.FndUser"/>
        <typeAlias type="com.cloudpense.expman.entity.FndDepart" alias="FndDepart"/>
        <typeAlias type="com.cloudpense.expman.entity.SystemUser" alias="SystemUser"/>
    </typeAliases>


</configuration>  