<?mybatis version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:task="http://www.springframework.org/schema/task"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd
						http://www.springframework.org/schema/tx
						http://www.springframework.org/schema/tx/spring-tx.xsd
	                    http://www.springframework.org/schema/task
		                http://www.springframework.org/schema/task/spring-task-3.1.xsd  ">

    <context:component-scan base-package="com"/>
    <context:annotation-config/>

    <tx:annotation-driven transaction-manager="transactionManager"/>

    <!--  persistence database configurations -->
    <!-- <context:property-placeholder location="classpath*:/application.properties"/> -->

    <!--timer task option-->
    <task:annotation-driven/>
    <!--<bean id="DidiAuto" class="com.cloudpense.expman.entity.DidiAuto"></bean>-->
    <!--<bean id="CtripAuto" class="com.cloudpense.expman.entity.view.Ctrip"></bean>-->
    <task:scheduler id="scheduler" pool-size="30"/>
    <task:scheduled-tasks scheduler="scheduler">
        <!-- start from 5th, conduct every three seconds -->
        <!--<task:scheduled ref="CtripAuto" method="getOrderList" cron="0 0/30 * * * ?"/>-->
        <!--<task:scheduled ref="CtripAuto" method="updateHuazhuList" cron="0 0/30 * * * ?"/>-->
        <task:scheduled ref="SAPController" method="PhoenixPostAll" cron="0/30 * * * * ?"/>
        <task:scheduled ref="SAPController" method="PhoenixFetchBi" cron="0 10 5,18 * * ?"/>
        <task:scheduled ref="SAPController" method="PhoenixFetchBi" cron="0 20 7 * * ?"/>
        <task:scheduled ref="SAPController" method="PhoenixIportalDelete" cron="0/30 * * * * ?"/>
        <task:scheduled ref="phoenixController" method="phoenixBAANUpdateRules" cron="0 25 19 * * ?"/>
        <task:scheduled ref="SAPController" method="PhoenixFetchBiSupplier" cron="0 30 22 * * ?"/>
        <task:scheduled ref="SAPController" method="MeiyaVoucherReceive" cron="0 0 18 * * ?"/>
        <!--延锋抛报销单每2分钟扫描一次-->
        <task:scheduled ref="YFController" method="yfPaperGenerating" cron="0 0/2 * * * ?"/>
        <!--立邦单据和消息推送-->
        <!--task:scheduled ref="nipponController" method="ntfPush" cron="0 0/1 * * * ?"/>
        <task:scheduled ref="nipponController" method="reimPush" cron="0 0/1 * * * ?"/>
        <task:scheduled ref="nipponController" method="invoicePush" cron="0 0/10 * * * ?"/>
        <task:scheduled ref="nipponController" method="dailySync" cron="0 45 19 * * ?"/ -->
        <!--农垦定时任务 UTC+8每天凌晨2点同步数据 即UTC 18:00-->
        <!--<task:scheduled ref="HSFController" method="getOAToken" cron="0 0/10 * * * ?"/>
        <task:scheduled ref="HSFController" method="claimPush" cron="0 0/5 * * * ?"/>
        <task:scheduled ref="HSFController" method="ntfPush" cron="0/30 * * * * ?"/>
        <task:scheduled ref="HSFController" method="updateData" cron="0 15 20 * * ?"/>-->
        <!--农垦定时任务end-->
        <!--科华工单排期-->
        <!--task:scheduled ref="kehuaController" method="orderScheduled" cron="0 0/10 * * * ?"/-->
        <!--科华合同接口定时任务-->
        <!--task:scheduled ref="kehuaController" method="khContract" cron="0 0 0/1 * * ?"/-->
        <!--万华预算同步-->
        <task:scheduled ref="wanhuaController" method="doAutoPostBudget" cron="0/30 * * * * ?"/>
        <task:scheduled ref="wanhuaController" method="pushOaTasks" cron="0/25 * * * * ?"/>
        <task:scheduled ref="wanhuaController" method="getIndexNum" cron="0 0/20 * * * ?"/>
        <task:scheduled ref="wanhuaController" method="postInvoice" cron="0 0 16 * * ?"/>
        <task:scheduled ref="wanhuaController" method="getInvoice" cron="0 0 12 * * ?"/>

        <!--evcard-->
        <!--<task:scheduled ref="evcardController" method="autoPostDocument" cron="0 0/1 * * * ?"/>
        <task:scheduled ref="evcardController" method="autoPostPr" cron="0 0/1 * * * ?"/>
        <task:scheduled ref="evcardController" method="evcardPushTask" cron="0/30 * * * * ?"/>-->

        <!--长飞-->
        <!--<task:scheduled ref="yofcController" method="cbsPayWaiStatus" cron="0 0/5 * * * ?"/>
        <task:scheduled ref="yofcController" method="cbsPayStatus" cron="0 0/5 * * * ?"/>
        <task:scheduled ref="yofcController" method="copyClaim" cron="30 0/2 * * * ?"/>-->

    </task:scheduled-tasks>

    <bean
            class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations" value="classpath:application.properties"/>
    </bean>

    <bean id="consumer" name="consumer" class="com.cloudpense.expman.rabbitmq.MqConsumer" scope="singleton"
          init-method="init">
    </bean>

    <bean id="cpprd" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db.driver}"></property>
        <property name="jdbcUrl" value="${db.url}"></property>
        <property name="username" value="${db.username}"></property>
        <property name="password" value="${db.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="40"/>
        <property name="minimumIdle" value="2"/>
    </bean>


    <bean id="cpuae" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db2.driver}"></property>
        <property name="jdbcUrl" value="${db2.url}"></property>
        <property name="username" value="${db2.username}"></property>
        <property name="password" value="${db2.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="8"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cprmy" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db3.driver}"></property>
        <property name="jdbcUrl" value="${db3.url}"></property>
        <property name="username" value="${db3.username}"></property>
        <property name="password" value="${db3.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpdcj" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db4.driver}"></property>
        <property name="jdbcUrl" value="${db4.url}"></property>
        <property name="username" value="${db4.username}"></property>
        <property name="password" value="${db4.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cppxc" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db5.driver}"></property>
        <property name="jdbcUrl" value="${db5.url}"></property>
        <property name="username" value="${db5.username}"></property>
        <property name="password" value="${db5.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cphsf" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${db6.driver}"></property>
        <property name="jdbcUrl" value="${db6.url}"></property>
        <property name="username" value="${db6.username}"></property>
        <property name="password" value="${db6.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="20"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpnpp" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbnpp.driver}"></property>
        <property name="jdbcUrl" value="${dbnpp.url}"></property>
        <property name="username" value="${dbnpp.username}"></property>
        <property name="password" value="${dbnpp.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="30"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpwhx" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbwhx.driver}"></property>
        <property name="jdbcUrl" value="${dbwhx.url}"></property>
        <property name="username" value="${dbwhx.username}"></property>
        <property name="password" value="${dbwhx.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpjrc" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbcjlr.driver}"></property>
        <property name="jdbcUrl" value="${dbcjlr.url}"></property>
        <property name="username" value="${dbcjlr.username}"></property>
        <property name="password" value="${dbcjlr.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpcfg" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbcfg.driver}"></property>
        <property name="jdbcUrl" value="${dbcfg.url}"></property>
        <property name="username" value="${dbcfg.username}"></property>
        <property name="password" value="${dbcfg.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="10"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cplgg" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dblgg.driver}"></property>
        <property name="jdbcUrl" value="${dblgg.url}"></property>
        <property name="username" value="${dblgg.username}"></property>
        <property name="password" value="${dblgg.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpevc" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbevc.driver}"></property>
        <property name="jdbcUrl" value="${dbevc.url}"></property>
        <property name="username" value="${dbevc.username}"></property>
        <property name="password" value="${dbevc.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpshw" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbshw.driver}"></property>
        <property name="jdbcUrl" value="${dbshw.url}"></property>
        <property name="username" value="${dbshw.username}"></property>
        <property name="password" value="${dbshw.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpcea" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${dbcea.driver}"></property>
        <property name="jdbcUrl" value="${dbcea.url}"></property>
        <property name="username" value="${dbcea.username}"></property>
        <property name="password" value="${dbcea.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="5"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpkhb" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${cpkhb.driver}"></property>
        <property name="jdbcUrl" value="${cpkhb.url}"></property>
        <property name="username" value="${cpkhb.username}"></property>
        <property name="password" value="${cpkhb.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="20"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cphrstw" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${cphrstw.driver}"></property>
        <property name="jdbcUrl" value="${cphrstw.url}"></property>
        <property name="username" value="${cphrstw.username}"></property>
        <property name="password" value="${cphrstw.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="20"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="cpprd03" class="com.zaxxer.hikari.HikariDataSource"
          destroy-method="close">
        <property name="driverClassName" value="${cpprd03.driver}"></property>
        <property name="jdbcUrl" value="${cpprd03.url}"></property>
        <property name="username" value="${cpprd03.username}"></property>
        <property name="password" value="${cpprd03.password}"></property>
        <property name="connectionTimeout" value="25000"/>
        <property name="idleTimeout" value="30000"/>
        <property name="maxLifetime" value="570000"/>
        <property name="maximumPoolSize" value="40"/>
        <property name="minimumIdle" value="2"/>
    </bean>

    <bean id="dynamicDataSource" class="com.cloudpense.expman.dataSource.DynamicDataSource">
        <property name="targetDataSources">
            <map key-type="java.lang.String">
                <entry value-ref="cpprd" key="cpprd"></entry>
                <entry value-ref="cpuae" key="cpuae"></entry>
                <entry value-ref="cprmy" key="cprmy"></entry>
                <entry value-ref="cpdcj" key="cpdcj"></entry>
                <entry value-ref="cppxc" key="cppxc"></entry>
                <entry value-ref="cphsf" key="cphsf"></entry>
                <entry value-ref="cpnpp" key="cpnpp"></entry>
                <entry value-ref="cpwhx" key="cpwhx"></entry>
                <entry value-ref="cpjrc" key="cpjrc"></entry>
                <entry value-ref="cpcfg" key="cpcfg"></entry>
                <entry value-ref="cplgg" key="cplgg"></entry>
                <entry value-ref="cpevc" key="cpevc"></entry>
                <entry value-ref="cpshw" key="cpshw"></entry>
                <entry value-ref="cpcea" key="cpcea"></entry>
                <entry value-ref="cpkhb" key="cpkhb"></entry>
                <entry value-ref="cphrstw" key="cphrstw"></entry>
                <entry value-ref="cpprd03" key="cpprd03"></entry>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="cpprd">
        </property>
    </bean>

    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dynamicDataSource"/>
    </bean>


    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource"/>
        <property name="configLocation" value="classpath:mybatis-config.xml"/>
        <property name="mapperLocations" value="classpath:com/cloudpense/expman/mapper/xml/*.xml"/>
    </bean>

    <!-- scan mapper annotations automatically and let them be autowired -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.cloudpense.expman.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <bean id="httpErrorMessageSource"
          class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <property name="basename" value="classpath:/com/cloudpense/expman/exception/messages/messages"/>
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>
</beans>