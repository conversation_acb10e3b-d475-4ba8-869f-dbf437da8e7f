<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jaxws="http://cxf.apache.org/jaxws"
       xmlns:cxf="http://cxf.apache.org/core"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://cxf.apache.org/core http://cxf.apache.org/schemas/core.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
        http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
        http://www.springframework.org/schema/util
        http://cxf.apache.org/jaxws http://cxf.apache.org/jaxws ">

    <!--<import resource="classpath:META-INF/cxf/cxf.xml" />-->
    <!--<import resource="classpath:META-INF/cxf/cxf-extension-soap.xml" />-->
    <!--<import resource="classpath:META-INF/cxf/cxf-servlet.xml" />-->

    <jaxws:endpoint
            id="abroadApplication"
            implementor="com.cloudpense.expman.webService.phoenix.AbroadApplicationImpl"
            address="/abroadApplication">
        <jaxws:inInterceptors>
            <bean class="com.cloudpense.expman.webService.phoenix.Interceptor.PhoenixAuthInterceptor"/>
        </jaxws:inInterceptors>
    </jaxws:endpoint>

    <jaxws:endpoint
            id="indirectPurchaseApplication"
            implementor="com.cloudpense.expman.webService.phoenix.IndirectPurchaseApplicationImpl"
            address="/indirectPurchaseApplication">
        <jaxws:inInterceptors>
            <bean class="com.cloudpense.expman.webService.phoenix.Interceptor.PhoenixAuthInterceptor"/>
        </jaxws:inInterceptors>
    </jaxws:endpoint>

    <jaxws:endpoint
            id="promotionalToolsApplication"
            implementor="com.cloudpense.expman.webService.phoenix.PromotionalToolsApplicationImpl"
            address="/promotionalToolsApplication">
        <jaxws:inInterceptors>
            <bean class="com.cloudpense.expman.webService.phoenix.Interceptor.PhoenixAuthInterceptor"/>
        </jaxws:inInterceptors>
    </jaxws:endpoint>

    <!--奇瑞-捷豹路虎，主数据-->
    <jaxws:endpoint
            id="EmployeeData"
            implementor="com.cloudpense.expman.webService.cjlr.EmployeeDataImpl"
            address="/cjlr/employeeData">
        <jaxws:inInterceptors>
            <bean class="com.cloudpense.expman.webService.cjlr.Interceptor.CjlrAuthInterceptor"/>
        </jaxws:inInterceptors>
    </jaxws:endpoint>
    <jaxws:endpoint
            id="ClaimStatusUpdate"
            implementor="com.cloudpense.expman.webService.nippon.server.ClaimStatusUpdateImpl"
            address="/nippon/claim">
    </jaxws:endpoint>
</beans>