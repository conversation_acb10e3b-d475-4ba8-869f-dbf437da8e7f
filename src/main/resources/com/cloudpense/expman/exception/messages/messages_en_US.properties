# fnd_document_create
101001=Invalid Company
101002=Invalid Document Name
101003=Document name already exist
101004=Initial value must be positive integer
101005=Increment must be positive integer
101006=Length must be between 5 and 20

# fnd_user_verify_create
102001=Invalid email_address

# fnd_user_verify
103001=Invalid link, please reverify email

# fnd_user_pass_update
104001=Inavlid user, password reset failed

#fnd_user_create
105001=Email user name can't be null
105002=Password can't be null
105003=User name already exists, please change
105004=Email already exists, please change
105005=Verification key can't be null

#fnd_code_verify
107001
107002

#fnd_company_admin_create
108002=Company name is already registered
108003=Invalid user
108004=Company already created

#fnd_department_create
109001=Department name can't be null
109002=Invalid Company
10903=Invalid Superior Department
109004=Invalid Department Owner
109005=Department Owner name already exists

#fnd_department_user_create
110001=User name can't be null
110002=Verification key can't be null
110003=User name already exists
110004=Invalid Company
110005=Invalid Department

#fnd_boss_create
111001=User name can't be null
111002=Verification key can't be null
111003=User name already exists
111004=Invalid Company
111005=Root deparment not exists

#fnd_user_role_assign
112001=Your company service has expired, please renew
112002=Invalid User
112003=Invalid Role

#fnd_update_department
113001=Department Name can't be Null
113002=Your company service has expired, please renew
113003=Invalid Supervisor Department
113004=Invalid Department Owner
113005=Invalid Department
113006=Invalid Language

#fnd_update_user
114001=User name can't be null
114002=User name already exists
114003=Email address already exists
114004=Your company service has expired, please renew
114005=Invalid Department
114006=Invalid User
114007=Invalid Gender
114008=Invalid Birthday
114009=Active users exceeds limit, please expand value-added service
114010=Invalid Level
114011=Invalid Date of Joining
114012=Invalid Resignation Date

#fnd_delete_department
115001=Invalid Department
115003=Department which have employee assigned canât be deleted
115002=My company canât be deleted

#fnd_set_user_department
116001=Invalid User
116002=Invalid Department

#fnd_enable_user
117001=Invalid Status Flag
117002=Invalid User
117003=Active users exceeds limit, please expand value-added service

#fnd_company_info_update
118001=Company name can't be null
118002=Company name already exists

#fnd_company_currency_update
119001=Invalid Company Currency
119002=Invalid Rate Method

#fnd_user_update
120001
120002
120003
120004

#fnd_workflow_approve
121001=Status must be approved or rejected
121002=No available Source document
121003=No available workflow
121004=Currnet sequence wrong
121005=No available position or user
121006=Current status is not open
121007=Not the right person to approve
121008=Not the right position to approve
121009=Invalid approver
121010=Note is requied when reject

#fnd_role_update
125001=Your company service has expired, please renew
125002=Invalid User
125003=Invalid Role ID
125004=Duplicate Role Name

#fnd_role_menu_update
126001=Your company service has expired, please renew
126002=Invalid User
126003=Invalid Role ID
126004=Invalid Web Menu

#fnd_role_privilege_update
127001=Your company service has expired, please renew
127002=Invalid User
127003=Invalid Role ID
127004=Invalid Web Menu
127005=Invalid Privilege

#fnd_finance_department_update
130001
130002
130003

#fnd_level_update
134001=Your company service has expired, please renew
134002=Invalid User
134003=Invalid level
134004=Invalid level Id

#fnd_exchange_rate_update
135001=Your company service has expired, please renew
135002=Invalid User
135003=Invalid Currency
135004=Invalid Rate
135005=Invalid Date

#fnd_currencu_rate_update
136001=Your company service has expired, please renew
136002=Invalid Finance User
136003=Invalid Currency Code
136004=Invalid Rate

#fnd_company_printer_update
140001=Invalid Company
140002=Invalid Admin User
140003=Invalid Printer
140004=Printer address already exists

#fnd_company_printer_delete
141001=Invalid Company
141002=Invalid Admin User
141003=Invalid Printer

#fnd_company_max_user_update
150001=Invalid Company
150002=Max user less than current value

#fnd_lov_value_update
160001=Your company service has expired, please renew
160002=Invalid User
160003=Invalid Value ID
160004=Invalid LOV ID
160005=Duplicate Value Code
160006

#fnd_budget_type_update
170001=Your company service has expired, please renew
170002=Invalid User
170003=Invalid Budget Type
170004=Invalid Level Id

#fnd_budget_type_assignment
180001=Your company service has expired, please renew
180002=Invalid User
180003=Invalid Budget Type
180004=Invalid Request Type

#fnd_profile_value_set
190001=Your company service has expired, please renew
190002=Invalid User
190003=Invalid Profile
190004=Invalid Value

#fnd_profile_value_update
191001=Invalid Company
191002=Invalid User
191003=Invalid Profile
192004=Invalid Value

# trv_request_insert
201001=Invalid Status
201002=Invalid User
201003=Your company service has expired, please renew
201004=Invalid Type
201005=Document number can't be generated
201006=Invalid Start Datetime
201007=Invalid End Datetime
201008=End date can't be less than start date

# trv_request_city_insert
202001=Your company service has expired, please renew
202002=Invalid City
202003=Invalid datetime format
202004=Invalid Travel Method

# trv_request_cost_insert
203001=Your company service has expired, please renew
203002=Invalid Cost Element
203003=Invalid Budget Type

# trv_request_update
204001=Invalid Status
204002=Invalid User
204003=Your company service has expired, please renew
204004=Invalid Type
204005=Document not exists
204006=Invalid Start Datetime
204007=Invalid End Datetime
204008=End datet can't be less than start date

#trv_request_delete
205001=Invalid Company
205002=Invalid User
205003=Document not exists
205004=Document status not allow delete

#trv_request_withdraw
206001=Your company service has expired, please renew
206002=Invalid User
206003=Document not exists
206004=Document status not allow withdraw

# trv_type_insert
210001=Your company service has expired, please renew

# trv_type_update
211001=Your company service has expired, please renew
211002=Invalid Language
211003=Type you updated not exists

# trv_type_column_insert
212001=Your company service has expired, please renew
212002=Invalid Type

# trv_type_column_update
213001=Your company service has expired, please renew
213002=Invalid Type
213003=Invalid Language
213004=Column you updated not exists

#trv_type_enable
214001=Invalid User
214002=Invalid Type

#trv_type_column_enable
215001=Invalid User
215002=Invalid Column

# trv_exp_type_assign
216001=Your company service has expired, please renew
216002=Invalid User
216003=Invalid Request Type
216004=Invalid Expense Type

#trv_book_insert
220001=Your company service has expired, please renew
220002=Invalid User
220003=Invalid Book Id
220004=Invalid Start Date
220005=Invalid End Date
220006=Invalid Room
220007=Invalid Travel Plan
220008=Invalid Status
220009=Invalid Status

#trv_hotel_update
230001=Invalid Name
230002=Invalid City
230003=Invalid Hotel
230004=Invalid Hotel

#trv_hotel_room_update
240001=Invalid Name
240002=Invalid Hotel
240003=Invalid Room
240004=Invalid Hotel

#trv_room_daily_update
250001=Invalid Room
250002=Invalid Date

# exp_claim_header_insert
301001=Invalid Status
301002=Invalid User
301003=Your company service has expired, please renew
301004=Invalid Submit Date
301005=Document number can't be generated
301006=Invalid Start Datetime
301007=Invalid End Datetime
301008=End date can't be less than start date
301009=Invalid Travel Request
301010
301011
301012
301013=Invalid User Account
301014=Invalid Supplier Account

# exp_claim_city_insert
302001=Your company service has expired, please renew
302002=Invalid City
302003=Invalid datetime format

# exp_claim_cost_insert
303001=Your company service has expired, please renew
303002=Invalid Cost Element

# exp_claim_line_insert
304001=Invalid User
304002=Your company service has expired, please renew
304003=Invalid Type
304004=Invalid Receipt Date
304005=Invalid Pay Method

# exp_claim_header_update
305001=Invalid Status
305002=Invalid User
305003=Your company service has expired, please renew
305004=Invalid Submit Date
305005=Document not exists
305006=Invalid Start Datetime
305007=Invalid End Datetime
305008=End datet can't be less than start date
305009=Invalid Travel Request
305010=Line amount not equal total amount

#exp_claim_header_delete
306001=Invalid Company
306002=Invalid User
306003=Document not exists
306004=Document status not allow delete

#exp_claim_header_withdraw
307001=Your company service has expired, please renew
307002=Invalid User
307003=Document not exists
307004=Document status not allow withdraw
307005=Approved document not allow withdraw

# exp_claim_finance_approve
308001=Your company service has expired, please renew
308002=Document not exists
308003=Document status is not approved
308004=Invalid user

# exp_claim_line_finance_update
309001=Invalid Line
309002=Invalid Tax Code
309003=Invalid Debit Account
309004=Invalid Credit Account

# exp_type_insert
310001=Your company service has expired, please renew

# exp_type_update
311001=Your company service has expired, please renew
311002=Invalid Language
311003=Type you updated not exists

# exp_type_column_insert
312001=Your company service has expired, please renew
312002=Invalid Type

# exp_type_column_update
313001=Your company service has expired, please renew
313002=Invalid Type
313003=Invalid Language
313004=Column you updated not exists

#exp_supplier_update
314001=Your company service has expired, please renew
314002=Invalid User
314003=Invalid Supplier Name
314004=Invalid Account

# exp_type_enable
315001=Invalid User
315002=Invalid Type
315003=Invalid Budget Category

#exp_type_column_enable
316001=Invalid User
316002=Invalid Column

#exp_tax_code_update
317001=Your company service has expired, please renew
317002=Invalid User
317003=Tax code already exists
317004=Invalid Tax Code ID

#exp_type_assign
318001=Your company service has expired, please renew
318002=Invalid User
318003=Invalid Header Type
318004=Invalid Line Type

#exp_budget_amount_update
320001=Your company service has expired, please renew
320002=Invalid User
320003=Invalid Dimension
320004=Invalid Department
320005=Invalid Project
320006=Invalid Budget Category
320007=Invalid Budget Period
320008=Invalid Amount ID

#exp_budget_category_update
321001=Your company service has expired, please renew
321002=Invalid User
321003=Invalid Category ID
321004=Invalid Language

#exp_budget_control_update
322001=Your company service has expired, please renew
322002=Invalid User
322003=Invalid Dimension
322004=Invalid Department
322005=Invalid Project
322006=Invalid Budget Category
322007=Invalid Budget Period
322008=Invalid Control ID
322009=Invalid Control Type

#gl_batch_create
401001=Your company service has expired, please renew
401002=Invalid GL Date
401003=Invalid User
401004=Invalid Document List

#gl_account_rule_insert
402001=Your company service has expired, please renew
402002=Invalid Department
402003=Invalid Position
402004=Invalid Expense Type
402005=Invalid Debit Account
402006=Invalid Credit Account
402007=Invalid Finance User
402008=Duplicate Account Rule

#gl_account_rule_delete
403001=Invalid Finance User

#gl_account_insert
404001=Your company service has expired, please renew
404002=Account not exists
404003=Invalid Finance User
404004=Invalid Budget Account

#gl_batch_delete
405001=Your company service has expired, please renew
405002=Invalid GL Batch
405003=Invalid Finance User

# upload image
487600=Invalid size

# print reimbursement
487601=Unauthorized access


500001=cost element amount doesn't match
500002=claim amount doesn't match

#ap_payment_update
501001=Your company service has expired, please renew
501002=Invalid Finance User
501003=Invalid Payment ID
501004=Invalid Status

#ntf_notification_update
601001=Your company service has expired, please renew
601002=Invalid Admin User
601003=Invalid Notification ID
601004=Invalid Condition Type
601005=Invalid Period Type
601006=Invalid Event Name

#pjm_project_update
701001=Your company service has expired, please renew
701002=Invalid Language
701003=Invalid User
701004=Invalid Project ID
701005=Invalid Department
701006=Duplicate Project Code

#fnd_update_json
900001=Your company service has expired, please renew
900002=Invalid User
900003=Invalid ID

#null point error
997001=Invalid Value

