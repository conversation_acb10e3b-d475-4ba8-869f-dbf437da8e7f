com.cloudpense.expman.exception.EntityNotFoundException.title=Entity not found.
com.cloudpense.expman.exception.EntityNotFoundException.detail=#{ex.message}

com.cloudpense.expman.exception.EntityNotFoundException2.title=Entity not found.
com.cloudpense.expman.exception.EntityNotFoundException2.detail=#{ex.message}

com.cloudpense.expman.exception.FormatException.title=Input error.
com.cloudpense.expman.exception.FormatException.detail=#{ex.message}

com.cloudpense.expman.exception.UnauthorizedException.title=Authorization error.
com.cloudpense.expman.exception.UnauthorizedException.detail=#{ex.message}

com.cloudpense.expman.exception.PasswordException.title=Password error.
com.cloudpense.expman.exception.PasswordException.detail=#{ex.message}

com.cloudpense.expman.exception.UnsupportedFileTypeException.title=Unsupported file type.
com.cloudpense.expman.exception.UnsupportedFileTypeException.detail=#{ex.message}

com.cloudpense.expman.exception.IncorrectDataException.title=Incorrect data
com.cloudpense.expman.exception.IncorrectDataException.detail=#{ex.message}

com.cloudpense.expman.exception.ValidationException.title=Validation error.
com.cloudpense.expman.exception.ValidationException.detail=#{ex.message}

org.springframework.security.core.userdetails.UsernameNotFoundException.title=Incorrect user info
org.springframework.security.core.userdetails.UsernameNotFoundException.detail=#{ex.message}

java.sql.SQLException.title=Internal database error.
java.sql.SQLException.detail=#{ex.message}

com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException.title=Internal database error.
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException.detail=#{ex.message}