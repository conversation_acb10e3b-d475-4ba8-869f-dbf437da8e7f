<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://java.sun.com/xml/ns/javaee"
		 xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
							 http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
		  					 version="3.0">

	<display-name>iExpenseSqftp</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath:spring-core.xml,
			classpath:spring-security.xml,
			classpath:spring-ws.xml
		</param-value>
	</context-param>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>

	<filter>
		<filter-name>CharacterEncodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>UTF-8</param-value>
		</init-param>
		<init-param>
			<param-name>forceEncoding</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>

	<filter-mapping>
		<filter-name>CharacterEncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>CORS</filter-name>
		<filter-class>com.thetransactioncompany.cors.CORSFilter</filter-class>
		<init-param>
			<param-name>cors.allowOrigin</param-name>
			<param-value>*</param-value>
		</init-param>
		<init-param>
			<param-name>cors.supportedMethods</param-name>
			<param-value>GET,POST,HEAD,OPTIONS,PUT,DELETE</param-value>
		</init-param>
		<init-param>
			<param-name>cors.allowed.headers</param-name>
			<param-value>Content-Type,X-Requested-With,accept,Origin,Access-Control-Request-Method,Access-Control-Request-Headers</param-value>
		</init-param>
		<init-param>
			<param-name>cors.exposed.headers</param-name>
			<param-value>Access-Control-Allow-Origin,Access-Control-Allow-Credentials</param-value>
		</init-param>
		<init-param>
			<param-name>cors.support.credentials</param-name>
			<param-value>true</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>CORS</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<!--<filter>-->
		<!--<filter-name>CAS Filter</filter-name>-->
		<!--<filter-class>edu.yale.its.tp.cas.client.filter</filter-class>-->
		<!--<init-param>-->
			<!--<param-name>edu.yale.its.tp.cas.client.filter.loginUrl</param-name>-->
			<!--<param-value> https://www.yuanjutong.com/cas/login</param-value>-->
		<!--</init-param>-->
		<!--<init-param>-->
			<!--<param-name>edu.yale.its.tp.cas.client.filter.validateUrl</param-name>-->
			<!--<param-value> http://172.20.1.45:8088/cas/serviceValidate</param-value>-->
		<!--</init-param>-->
		<!--<init-param>-->
			<!--<param-name>edu.yale.its.tp.cas.client.filter.serverName</param-name>-->
			<!--<param-value>https://www.yuanjutong.com</param-value>-->
		<!--</init-param>-->
	<!--</filter>-->
	<!--<filter-mapping>-->
		<!--<filter-name>CAS Filter</filter-name>-->
		<!--<url-pattern>/cas/*</url-pattern>-->
	<!--</filter-mapping>-->

	<servlet>
		<servlet-name>springmvc</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:spring-servlet.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>

	<servlet-mapping>
		<servlet-name>springmvc</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<!-- cxf webservice-->
	<servlet>
		<servlet-name>CXFServlet</servlet-name>
		<servlet-class>org.apache.cxf.transport.servlet.CXFServlet</servlet-class>
		<load-on-startup>2</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>CXFServlet</servlet-name>
		<url-pattern>/ws/*</url-pattern>
	</servlet-mapping>
</web-app>
