package com.cloudpense.expman.it;

import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.util.IntegrationTestBase2;
import com.cloudpense.expman.util.JsonResourceLoader;
import org.junit.Test;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Created by Huiyi on 2015/3/9.
 */
public class ThirdITTest extends IntegrationTestBase2 {

    @Test
    public void testDingTalk() throws Exception {
        mockMvc.perform(post("/dingtalk/callback?signature=ff9ff626d82d22adbe5bd1a37e57b6db22c5a048&timestamp=1444705739079&nonce=fb0BKFM6")
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("dingtalk")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testLogin() throws Exception {
        mockMvc.perform(post("/dingtalk/login")
                .content(JsonResourceLoader.getData("fastjson")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testCode() throws Exception {
        mockMvc.perform(post("/phoenix/hrpost")
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("third")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testDate() throws Exception {
        mockMvc.perform(get("/ingage/order"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testWechatQy() throws Exception {
        mockMvc.perform(get("/wechat/qywx"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testWechatQy2() throws Exception {
        mockMvc.perform(post("/meiya/doc"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testNormal() throws Exception {
        mockMvc.perform(post("/meiya/voucher/receive")
                .content(JsonResourceLoader.getData("fastjson")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testDepartment() throws Exception {
        mockMvc.perform(get("/dingtalk/department"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testTmp() throws Exception {
        mockMvc.perform(post("phoenix/ibmpapprove")
                .content(JsonResourceLoader.getData("fastjson")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testCount() throws Exception {
        mockMvc.perform(get("/count"))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testDynamic() throws Exception {
        mockMvc.perform(get("/dingtalk/message")
                .contentType(CommonConstants.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void testOffice() throws Exception {
        mockMvc.perform(get("/test/office")
                .contentType(CommonConstants.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void testValidateCode() throws Exception {
        mockMvc.perform(get("/apitry/v1/apply/validateCode")
                .contentType("image/jpeg"))
                .andExpect(status().isOk());
    }


    @Test
    public void testSignIn() throws Exception {
        mockMvc.perform(post("/reset")
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("login")))
                .andExpect(status().isOk());
    }

    @Test
    public void testIngageUser() throws Exception {
        mockMvc.perform(post("/ingage/user")
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("ingage_user")))
                .andExpect(status().isOk());
    }

    @Test
    public void testorderScheduled() throws Exception {
        mockMvc.perform(post("/kehua/orderScheduled")
                .contentType("application/json")
                .content(JsonResourceLoader.getData("ingage_user")))
                .andExpect(status().isOk());
    }

    @Test
    public void hetPushTask() throws Exception {
        mockMvc.perform(post("/het/hetPushTask")
                .contentType("application/json")
                .content(JsonResourceLoader.getData("ingage_user")))
                .andExpect(status().isOk());
    }

}
