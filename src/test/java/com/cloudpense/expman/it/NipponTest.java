package com.cloudpense.expman.it;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloudpense.expman.util.IntegrationTestBase2;
import org.junit.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created by ye.yang on 2018/12/8.
 */
public class NipponTest extends IntegrationTestBase2 {
    @Test
    public void fullTest() throws Exception {
//        mockMvc.perform(post("/nippon/comsynccall"));
//        mockMvc.perform(post("/nippon/ccsynccall"));
//        mockMvc.perform(post("/nippon/synccall/position"));
//        mockMvc.perform(post("/nippon/aplsynccall"));
//        mockMvc.perform(post("/nippon/synccall/employee"));
//        mockMvc.perform(post("/nippon/synccall/partjob"));
//        mockMvc.perform(post("/nippon/framesynccallf"));
//        mockMvc.perform(post("/nippon/pelsynccall"));
//        mockMvc.perform(post("/nippon/outsynccall"));
//        mockMvc.perform(post("/nippon/creditsynccall"));
//        mockMvc.perform(post("/nippon/ratesynccall"));
//        mockMvc.perform(post("/nippon/wbssynccall"));
//        mockMvc.perform(post("/nippon/meetsynccall"));
//        mockMvc.perform(post("/nippon/intosynccall"));
//        mockMvc.perform(post("/nippon/trainsynccallf"));
//        mockMvc.perform(post("/nippon/trainsynccall"));
//        mockMvc.perform(post("/nippon/reimpush"));
//        mockMvc.perform(post("/nippon/ntf/push"));
//        mockMvc.perform(post("/nippon/travelpushcall"));
//        mockMvc.perform(post("/nippon/freshStatus"));
//        mockMvc.perform(post("/nippon/budgetsynccall"));
        mockMvc.perform(post("/nippon/invoicepushcall"));
    }

    @Test
    public void testComSync() throws Exception {
        mockMvc.perform(post("/nippon/comsynccall"));
    }

    @Test
    public void testCCSync() throws Exception {
        mockMvc.perform(post("/nippon/ccsynccall"));
    }

    @Test
    public void testSAPNo() throws Exception {
        mockMvc.perform(post("/nippon//getSAPNo/60142")).andDo(print());
    }

    @Test
    public void testPELSync() throws Exception {
        mockMvc.perform(post("/nippon/suppliersynccall"));
    }

    @Test
    public void testFFOS() throws Exception {
        mockMvc.perform(post("/nippon/freshFOS"));
    }

    @Test
    public void testContractSave() throws Exception {
        mockMvc.perform(post("/nippon/auth")
                .content("{\"mob\":1,\"code\":\"WeiXin_6229f6dbaedc4784bbec15ff8894d222\"}".getBytes()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testEncrypt() throws Exception {
        mockMvc.perform(get("/hsf/oa/token?id=640121199201019572")).andDo(print());
    }

    @Test
    public void updateUser() {
        String url = "http://beep2.nipponpaint.com.cn:8080/NipponData/api/employee";
        RestTemplate restTemplate = new RestTemplate();
        int page = 100;
        HttpHeaders headers = new HttpHeaders();
        String requestBody = "{\"page\":" + page + "}";
        //定义请求参数类型，这里用json所以是MediaType.APPLICATION_JSON
        headers.setContentType(MediaType.APPLICATION_JSON);
        //RestTemplate带参传的时候要用HttpEntity<?>对象传递

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

        String result = restTemplate.postForObject(url, request, String.class);
        JSONObject resultArray = JSON.parseObject(result);
    }
}
