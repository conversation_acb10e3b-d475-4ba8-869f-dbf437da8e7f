package com.cloudpense.expman.it;

import com.cloudpense.expman.util.Constants.CommonConstants;
import com.cloudpense.expman.util.IntegrationTestBase2;
import com.cloudpense.expman.util.JsonResourceLoader;
import org.junit.Test;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Created by futurefall on 2015/10/30.
 */
public class BiQueryITTest extends IntegrationTestBase2 {
    @Test
    public void testBi() throws Exception {
        mockMvc.perform(post("/api/v1/bi?access_token=" + token)
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("b")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testBiHeader() throws Exception {
        mockMvc.perform(post("/api/v1/bi/header?access_token=" + token)
                .contentType(CommonConstants.APPLICATION_JSON)
                .content(JsonResourceLoader.getData("bi_header")))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testFindOne() throws Exception {
        mockMvc.perform(get("/api/v1/bi/header/25355?access_token=" + token))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void test() throws Exception {
        mockMvc.perform(get("/api/v1/bi?access_token=" + token))
                .andDo(print())
                .andExpect(status().isOk());
    }

    @Test
    public void testBiCondition() throws Exception {
        mockMvc.perform(get("/sap/scan?type=FMI02"))
                .andDo(print())
                .andExpect(status().isOk());
    }
}
