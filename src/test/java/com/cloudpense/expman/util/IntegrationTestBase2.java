package com.cloudpense.expman.util;

import org.junit.Before;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
//import org.springframework.security.web.FilterChainProxy;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.transaction.TransactionConfiguration;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;

/**
 * Created by <PERSON><PERSON> on 2015/3/4.
 */
@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@ContextConfiguration(locations = {
        "classpath:spring-core.xml",
        "classpath:spring-servlet.xml",
        "classpath:spring-security.xml"
})
@TransactionConfiguration(defaultRollback = true)
@Transactional
public class IntegrationTestBase2 {

    @Autowired
    protected WebApplicationContext wac;

    protected MockMvc mockMvc;

    protected static String token;

    @Before
    public void setup() {
        mockMvc = MockMvcBuilders.webAppContextSetup(wac).build();

//        token = getToken("<EMAIL>", "admin");


//        System.out.println(token);

    }

//    protected String getToken(String username, String password) {
//        String result = null;
//        int index = 0;
//
//        try {
//            result = mockMvc.perform(post("/oauth/token").contentType(MediaType.APPLICATION_FORM_URLENCODED)
//                    .param("username", username)
//                    .param("password", password)
//                    .param("grant_type", "password")
//                    .param("client_id", "ios-client")
//                    .param("client_secret", "ios-client"))
//                    .andReturn().getResponse().getContentAsString();
//
////            result = mockMvc.perform(post("/oauth/token").contentType(MediaType.APPLICATION_FORM_URLENCODED)
////                    .param("username", username)
////                    .param("password", password)
////                    .param("grant_type", "password")
////                    .param("client_id", "IC85VW0S")
////                    .param("client_secret", "f4SXS4tuj69zFivB"))
////                    .andReturn().getResponse().getContentAsString();
//
//
//            index = result.indexOf("access_token") + 15;
//
//        } catch (Exception ex) {
//            return null;
//        }
//        System.out.println(result);
//        return result.substring(index, result.indexOf("\"", index));
//    }
}
